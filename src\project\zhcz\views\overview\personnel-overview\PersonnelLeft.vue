<template>
  <div class="personnel-left" style="color: #fff">
    <BoxHeader :title="title">
      <template #right>
        <Select
          v-model:value="personType"
          style="width: 160px"
          @change="handleSludgeChange"
          class="big-screen-select"
          popupClassName="big-screen-select-dropdown"
        >
          <SelectOption v-for="item in personTypList" :value="item.value" :key="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
    </BoxHeader>
    <!-- <template v-if="dataLoading">
      <div style="background: unset"></div>
    </template> -->
    <template v-if="dataLoading">
      <OnGuard
        v-for="(item, index) in dataList"
        :key="index"
        :data="item"
        :title="`${item.title}`"
      />
    </template>
    <template v-else>
      <OnGuard
        v-for="(item, index) in mockPersonnelData"
        :key="index"
        :data="item"
        :title="`${item.title}`"
      />
    </template>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onUnmounted } from 'vue';
  import dayjs from 'dayjs';
  import { orderBy } from 'lodash-es';
  import BoxHeader from '../components/box-container/BoxHeader.vue';
  import { getPlantByDate, getPlanTime } from '/@zhcz/api/scheduling';
  import { mockPersonnelData } from './data';
  import { useEmitt } from '/@/hooks/web/useEmitt';
  import { getAllUserList, getDeptList } from '/@zhcz/api/warehouse';
  import { Select, SelectOption } from 'ant-design-vue';
  import OnGuard from './components/on-guard/index.vue';

  const dataList = ref<{ title: string; list: any[] }[]>([]);
  const dataLoading = ref<Boolean>(true);
  const classTypeList = ref<{ label: string; value: string }[]>([]);
  const assayClassTypeList = ref<{ label: string; value: string }[]>([]);
  const allUser = ref<Recordable[]>([]);
  const title = ref('值班排班');
  const personType = ref('1');
  const personTypList = [
    {
      label: '值班排班',
      value: '1',
    },
    {
      label: '化验排班',
      value: '2',
    },
  ];
  function handleSludgeChange() {
    if (personType.value === '1') {
      getPersonData();
      title.value = '值班排班';
    } else if (personType.value === '2') {
      getAssayPersonData();
      title.value = '化验排班';
    }
  }
  // 值班排班班次
  const getPlanTimeList = async () => {
    const ret = await getPlanTime(1);
    if (ret) {
      const morning = {
        label: '早班',
        value: ret.morningStartTime + '-' + ret.morningEndTime,
      };
      const afternoon = {
        label: '中班',
        value: ret.afternoonStartTime + '-' + ret.afternoonEndTime,
      };
      const evening = {
        label: '晚班',
        value: ret.eveningStartTime + '-' + ret.eveningEndTime,
      };
      classTypeList.value = [morning, afternoon, evening];
    } else {
      classTypeList.value = [];
    }
  };

  // 化验排班班次
  const getAssayPlanTimeList = async () => {
    const ret = await getPlanTime(2);
    if (ret) {
      const morning = {
        label: '早班',
        value: ret.morningStartTime + '-' + ret.morningEndTime,
      };
      const afternoon = {
        label: '中班',
        value: ret.afternoonStartTime + '-' + ret.afternoonEndTime,
      };
      const evening = {
        label: '晚班',
        value: ret.eveningStartTime + '-' + ret.eveningEndTime,
      };
      assayClassTypeList.value = [morning, afternoon, evening];
    } else {
      assayClassTypeList.value = [];
    }
  };

  const getAvator = (id) => {
    return allUser.value.find((item) => item.userId === id)?.avatar;
  };

  const getAllUsers = async () => {
    allUser.value = await getAllUserList();
  };

  const allDept = ref<Recordable[]>([]);
  async function getAllDept() {
    const dept = await getDeptList();
    allDept.value = dept;
  }

  function getDeptName(id: string) {
    const user = allUser.value.find((item) => item.userId === id);
    const dept = allDept.value.find((item) => item.deptId === user?.deptId);

    return dept?.name ?? '';
  }

  // 值班排班人员
  const getPersonData = async () => {
    const params = {
      type: 1, // 值班排班
      startTime: dayjs().format('YYYY-MM-DD'),
      endTime: dayjs().format('YYYY-MM-DD'),
    };
    await getPlanTimeList();
    const ret = await getPlantByDate(params);

    // dataList.value = dataList.value.filter((item) => item.title !== '值班排班');
    dataList.value = [];
    if (ret && classTypeList.value.length) {
      dataLoading.value = true;
      const currentData = ret.scheduleDayInfoList.find(
        (item) => dayjs(item.scheduleTime).format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD'),
      );

      if (!currentData) return;
      const dataVal = orderBy(currentData.schedulePersonInfoList, ['classType']).map((item) => {
        let typeName = '';
        let dateOrder = '';
        if (item.classType === 1) {
          typeName = '早班';
          const currentClassType = classTypeList.value.find((item) => item.label.includes('早班'));
          dateOrder = currentClassType ? currentClassType.value : '';
        } else if (item.classType === 2) {
          typeName = '中班';
          const currentClassType = classTypeList.value.find((item) => item.label.includes('中班'));
          dateOrder = currentClassType ? currentClassType.value : '';
        } else if (item.classType === 3) {
          typeName = '晚班';
          const currentClassType = classTypeList.value.find((item) => item.label.includes('晚班'));
          dateOrder = currentClassType ? currentClassType.value : '';
        }

        return {
          classType: item.classType,
          typeName,
          dateOrder,
          leaderId: item.leaderId,
          leaderName: item.leaderName,
          leaderAvatar: getAvator(item.leaderId),
          identity: getDeptName(item.leaderId),
          persons: item.scheduleMemberList
            ? item.scheduleMemberList.map((item) => {
                return {
                  name: item.memberName,
                  id: item.memberId,
                  avatar: getAvator(item.memberId),
                  identity: getDeptName(item.memberId),
                };
              })
            : [],
        };
      });

      dataList.value.push({
        title: '值班排班',
        list: dataVal,
      });
    } else if (!ret && classTypeList.value.length) {
      dataLoading.value = false;
      const dataVal = mockPersonnelData[0].list.map((item) => {
        const currentClass = classTypeList.value.find((v) => item.typeName.includes(v.label));
        item.dateOrder = currentClass ? currentClass.value : '';
        return item;
      });
      dataList.value.push({
        title: '值班排班',
        list: dataVal,
      });
    }
  };

  // 化验排班人员
  const getAssayPersonData = async () => {
    const params = {
      type: 2, // 化验排班
      startTime: dayjs().format('YYYY-MM-DD'),
      endTime: dayjs().format('YYYY-MM-DD'),
    };
    await getAssayPlanTimeList();
    const ret = await getPlantByDate(params);
    // dataList.value = dataList.value.filter((item) => item.title !== '化验排班');
    dataList.value = [];
    if (ret && assayClassTypeList.value.length) {
      dataLoading.value = true;
      const currentData = ret.scheduleDayInfoList.find(
        (item) => dayjs(item.scheduleTime).format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD'),
      );

      if (!currentData) return;
      const dataVal = orderBy(currentData.schedulePersonInfoList, ['classType']).map((item) => {
        let typeName = '';
        let dateOrder = '';
        if (item.classType === 1) {
          typeName = '早班';
          const currentClassType = assayClassTypeList.value.find((item) =>
            item.label.includes('早班'),
          );
          dateOrder = currentClassType ? currentClassType.value : '';
        } else if (item.classType === 2) {
          typeName = '中班';
          const currentClassType = assayClassTypeList.value.find((item) =>
            item.label.includes('中班'),
          );
          dateOrder = currentClassType ? currentClassType.value : '';
        } else if (item.classType === 3) {
          typeName = '晚班';
          const currentClassType = assayClassTypeList.value.find((item) =>
            item.label.includes('晚班'),
          );
          dateOrder = currentClassType ? currentClassType.value : '';
        }

        return {
          classType: item.classType,
          typeName,
          dateOrder,
          leaderId: item.leaderId,
          leaderName: item.leaderName,
          leaderAvatar: getAvator(item.leaderId),
          identity: getDeptName(item.leaderId),
          persons: item.scheduleMemberList
            ? item.scheduleMemberList.map((item) => {
                return {
                  name: item.memberName,
                  id: item.memberId,
                  avatar: getAvator(item.memberId),
                  identity: getDeptName(item.memberId),
                };
              })
            : [],
        };
      });
      dataList.value.push({
        title: '化验排班',
        list: dataVal,
      });
    } else if (!ret && classTypeList.value.length) {
      dataLoading.value = false;
      const dataVal = mockPersonnelData[1].list.map((item) => {
        const currentClass = classTypeList.value.find((v) => item.typeName.includes(v.label));
        item.dateOrder = currentClass ? currentClass.value : '';
        return item;
      });

      dataList.value.push({
        title: '化验排班',
        list: dataVal,
      });
    }
  };
  const getData = async () => {
    await getAllUsers();
    await getAllDept();
    await getPersonData();
    // await getAssayPersonData();
  };

  getData();

  const { emitter } = useEmitt();
  emitter.on('bi:change-factory', () => {
    getData();
  });

  onUnmounted(() => {
    emitter.off('bi:change-factory');
  });
</script>

<style lang="less" scoped>
  .personnel-left {
    height: 100%;
    background: linear-gradient(160deg, rgba(0, 63, 137, 0.04) 1%, rgba(31, 132, 250, 0.24) 97%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid;
    border-image: linear-gradient(
        180deg,
        rgba(151.1946925520897, 208.2876169681549, 255, 0),
        rgba(151.00000619888306, 208.0000028014183, 255, 0.4000000059604645)
      )
      1 1;

    :deep(.ant-select-selection-item) {
      color: #fff;
    }

    .person-left {
      height: 100%;
      width: 100%;
      position: relative;
    }
  }
</style>
