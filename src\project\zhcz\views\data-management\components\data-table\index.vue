<template>
  <div
    class="data-table"
    :style="{ paddingBottom: mergeRow.length ? '' : '14px' }"
    :class="{ hasMergeRow: mergeRow.length }"
  >
    <div
      class="table-wrapper"
      @scroll="onWrapperScroll"
      ref="wrapperRef"
      :class="{ auto: data.length === 1 }"
    >
      <div class="table-inner">
        <TableHeader :data="columns" :timeColumnWidth="timeColumnWidth" />
        <TableBody :data="data" :dataTimeType="dataTimeType" :timeColumnWidth="timeColumnWidth" />
        <div
          class="absolute right-0 top-0 h-full flex justify-center items-center"
          :style="{ width: `calc(100% - ${timeColumnWidth} - 16px)` }"
          v-if="data.length === 1"
        >
          <HEmpty />
        </div>
      </div>
    </div>
    <div class="data-table-merge-row" v-if="mergeRow.length">
      <div class="merge-row-wrapper" @scroll="onMergeRowScroll" ref="mergeRowRef">
        <div class="content">
          <span class="merge-item cursor-default">合计</span>
          <span class="merge-item" v-for="(item, index) in mergeRow" :key="index">
            <Tooltip placement="bottom">
              <template #title>
                {{ getMergeValue(item) }}
              </template>
              <div class="cursor-default overflow-hidden text-ellipsis whitespace-nowrap">
                {{ getMergeValue(item) }}
              </div>
            </Tooltip>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PropType, ref } from 'vue';
  import TableHeader from './TableHeader.vue';
  import TableBody from './table-body/index.vue';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import morning from '../../assets/images/morning.png';
  import type { Columns, Data } from './types';
  import { truncateAndConvert } from '/@zhcz/utils/number';
  import { Tooltip } from 'ant-design-vue';

  defineProps({
    timeColumnWidth: {
      type: String,
      default: '201px',
    },
    columns: {
      type: Array as PropType<Columns[]>,
      default: () => [
        {
          title: '小时',
          type: 'time',
          data: [],
        },
        ...Array.from({ length: 9 }, () => {
          return {
            title: '生化池1#鼓风机正向电量（kW·h）',
            type: 'index',
          };
        }),
      ],
    },
    data: {
      type: Array as PropType<Data[]>,
      default: () => [
        {
          title: '小时',
          type: 'time',
          data: [
            {
              icon: morning,
              time: '01:00',
            },
            {
              icon: morning,
              time: '02:00',
            },
            {
              icon: morning,
              time: '03:00',
            },
            {
              icon: morning,
              time: '04:00',
            },
            {
              icon: morning,
              time: '05:00',
            },
            {
              icon: morning,
              time: '06:00',
            },
            {
              icon: morning,
              time: '07:00',
            },
            {
              icon: morning,
              time: '08:00',
            },
            {
              icon: morning,
              time: '09:00',
            },
          ],
        },
        ...Array.from({ length: 9 }, () => {
          return {
            title: '生化池1#鼓风机正向电量（kW·h）',
            type: 'index',
            data: Array.from({ length: 9 }, () => {
              return {
                value: '1234.00',
                status: 'blur',
              };
            }),
          };
        }),
      ],
    },
    dataTimeType: {
      type: Number,
      default: Infinity,
    },
    dataIndex: {
      type: Number,
      default: 1,
    },
    // 合计行
    mergeRow: {
      type: Array as PropType<Recordable[]>,
      default: () => [],
    },
  });

  const wrapperRef = ref();
  const mergeRowRef = ref();

  const onWrapperScroll = (e) => {
    const scrollLeft = e.target.scrollLeft;
    if (mergeRowRef.value && mergeRowRef.value.scrollLeft != scrollLeft) {
      mergeRowRef.value.scrollLeft = scrollLeft;
    }
  };

  const onMergeRowScroll = (e) => {
    const scrollLeft = e.target.scrollLeft;
    if (wrapperRef.value && wrapperRef.value.scrollLeft != scrollLeft) {
      wrapperRef.value.scrollLeft = scrollLeft;
    }
  };
  const getMergeValue = ({ val, mergerName }: Recordable) => {
    if (!isNaN(Number(val)) && val !== '') {
      val = truncateAndConvert(Number(val), 2);
      mergerName && (val += ` (${mergerName})`);
      return val;
    } else {
      return '-';
    }
  };
</script>

<style lang="less" scoped>
  .data-table {
    padding: 0 16px;
    overflow: hidden;
    height: 100%;
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    background: #ffffff;

    &::before {
      content: '';
      height: calc(100% - 14px);
      left: calc(16px + v-bind(timeColumnWidth));
      background-color: #e9e9e9;
      width: 1px;
      position: absolute;
      z-index: 1;
    }

    &.hasMergeRow::before {
      height: calc(100% - 56px);
    }

    .table-wrapper {
      width: 100%;
      flex: 1;
      overflow: auto;
      border-radius: 4px 4px 0 0;
      z-index: 2;

      &.auto {
        width: fit-content;
        max-width: 100%;
      }

      &::-webkit-scrollbar {
        background-color: @scrollbar;
        width: 0;
        height: 8px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: @scrollbar-thumb;
        transition: background 0.3s;

        &:hover {
          background-color: @scrollbar-thumb-hover;
        }
      }

      .table-inner {
        width: 100%;
        display: table;
        border-collapse: collapse;
        border-radius: 8px 8px 0px 0px;
      }
    }

    .data-table-merge-row {
      margin: 0 -16px;
      background-color: #ffffff;
      height: 56px;
      z-index: 2;
      box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.08);
      flex-shrink: 0;
      border-radius: 0 0 4px 0;
      padding: 0 16px;

      .merge-row-wrapper {
        overflow-x: auto;
        height: 100%;

        &::-webkit-scrollbar {
          display: none;
        }

        .content {
          display: flex;
          width: fit-content;
          height: 100%;

          .merge-item {
            width: 170px;
            box-sizing: content-box;
            height: 100%;
            padding: 0 16px 0 24px;
            display: flex;
            align-items: center;
            background-color: #ffffff;
            font-size: 14px;
            font-weight: bold;
            color: #333;

            &:first-of-type {
              border-right: #e9e9e9 1px solid;
              position: sticky;
              left: 0;
              top: 0;
              z-index: 2;
              width: calc(v-bind(timeColumnWidth) - 40px);
            }
          }
        }
      }
    }
  }
</style>
