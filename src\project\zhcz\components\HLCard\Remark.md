# HLCard 组件

## 文件路径

```
/src/project/aoa/components/HLCard/
├── index.ts                  # 组件导出文件
├── src/
│   ├── index.vue             # HLCard 主组件
│   ├── CardHeader.vue        # 卡片头部组件
│   ├── CardBody.vue          # 卡片主体组件
│   └── data.ts               # 组件配置数据
```

## 组件说明

HLCard 是一个可定制的卡片容器组件，具有以下特点：

1. 模块化设计：分为卡片头部 (CardHeader) 和卡片主体 (CardBody) 两个子组件
2. 灵活的插槽系统：提供多种具名插槽，支持自定义内容
3. 主题支持：支持明暗两种主题模式 (light, Dark, screenColor)
4. 样式可定制：提供多种 prop 用于自定义样式

## 组件 API

### HLCard Props

| 属性名     | 类型                   | 默认值  | 描述                                             |
| ---------- | ---------------------- | ------- | ------------------------------------------------ |
| className  | String                 | ''      | 卡片的额外类名                                   |
| styleData  | Object (CSSProperties) | {}      | 卡片的样式对象                                   |
| themeColor | String                 | 'light' | 主题颜色，可选值: 'Dark', 'light', 'screenColor' |

### CardHeader Props

| 属性名 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| title | String | - | 卡片标题 |
| line | Boolean | true | 是否显示底部分割线 |
| headerStyleDate | Object (CSSProperties) | {} | 卡片头部的样式对象 |
| headerClassName | String | '' | 卡片头部的额外类名 |
| themeColor | String | 'light' | 主题颜色，可选值: 'Dark', 'light', 'screenColor' |

### CardBody Props

| 属性名 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| bodyClassName | String | '' | 卡片主体的额外类名 |
| bodyStyleDate | Object (CSSProperties) | {} | 卡片主体的样式对象 |
| themeColor | String | 'light' | 主题颜色，可选值: 'Dark', 'light', 'screenColor' |

## 插槽

### HLCard 插槽

- 默认插槽：卡片的整体内容

### CardHeader 插槽

- `headerLeftBefore`：标题前的内容
- `headerLeftAfter`：标题后的内容
- `headerRight`：卡片头部右侧的内容

### CardBody 插槽

- `defaultBody`：默认主体内容
- `bodyTop`：主体顶部内容
- `bodyBottom`：主体底部内容

## 使用示例

### 基础用法

```vue
<template>
  <HLCard>
    <template #headerLeftBefore>
      <Icon icon="mdi:card" />
    </template>

    <template #headerRight>
      <Button>操作按钮</Button>
    </template>

    <template #defaultBody>
      <p>卡片内容区域</p>
    </template>
  </HLCard>
</template>

<script lang="ts" setup>
  import { HLCard } from '/@/project/aoa/components/HLCard';
</script>
```

### 自定义样式

```vue
<template>
  <HLCard className="custom-card" :styleData="{ borderRadius: '8px' }" themeColor="Dark">
    <template #headerLeftBefore>
      <Icon icon="mdi:card" />
    </template>

    <template #headerRight>
      <Button>操作按钮</Button>
    </template>

    <template #defaultBody>
      <p>卡片内容区域</p>
    </template>
  </HLCard>
</template>

<script lang="ts" setup>
  import { HLCard } from '/@/project/aoa/components/HLCard';
</script>
```

### 分别使用子组件

```vue
<template>
  <div class="custom-layout">
    <CardHeader title="我的卡片" :line="false">
      <template #headerRight>
        <Button>操作</Button>
      </template>
    </CardHeader>

    <CardBody>
      <template #defaultBody>
        <p>自定义内容</p>
      </template>
    </CardBody>
  </div>
</template>

<script lang="ts" setup>
  import { CardHeader, CardBody } from '/@/project/aoa/components/HLCard';
</script>
```

## 注意事项

1. 使用时需要先导入组件：`import { HLCard, CardHeader, CardBody } from '/@/project/aoa/components/HLCard'`
2. 样式已经在组件内部使用 scoped 隔离，不会影响全局样式
3. 可以通过 className 和 styleData 属性自定义样式
4. 支持三种主题模式：light（默认）、Dark 和 screenColor
