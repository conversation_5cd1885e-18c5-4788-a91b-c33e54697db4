<template>
  <PageWrapper dense contentFullHeight>
    <div
      class="card-wrapper px-4 pb-4 gap-y-12px w-full h-full overflow-y-hidden grid grid-rows-[minmax(230px,3.5fr)_6.5fr]"
    >
      <Card style="" :bodyStyle="{ height: 'calc(100% - 49px)', overflow: 'hidden' }">
        <template #title>
          <span class="mr-12">数据趋势图</span>
        </template>
        <template #extra>
          <RadioGroup v-model:value="twoDays" @change="onRadioChange" class="my-2">
            <RadioButton v-for="(item, index) in dayOptions" :key="index" :value="item">
              {{ item }}
            </RadioButton>
          </RadioGroup>
          <!-- <Select
            class="ml-4 my-2"
            v-model:value="indictor"
            style="width: 180px"
            :options="selectOptions"
            :allow-clear="false"
            @change="onSelectChange"
          /> -->
        </template>
        <LineChart
          v-if="showEcharts"
          ref="chartRef"
          :data="chartData"
          :option="chartOptions"
          :type="type"
          :echartTitle="`${selectedIndictorName}趋势`"
        />
        <HEmpty class="h-full h-empty" v-else />
      </Card>
      <div class="card-item overflow-x-hidden overflow-y-auto" style="">
        <Row :gutter="[12, 12]" class="h-full">
          <Col span="8" class="overflow-hidden" data-resource-code="scls_sz">
            <Card
              title="水质"
              class="h-full"
              :bodyStyle="{ height: 'calc(100% - 49px)', overflow: 'hidden' }"
            >
              <template #extra>
                <div class="flex gap-4 my-2">
                  <RadioGroup
                    class="flex-shrink-0"
                    v-model:value="dayAndMonth1"
                    @change="onDayAndMonthChange1($event, false)"
                  >
                    <RadioButton
                      v-for="(item, index) in dayAndMonthOptions"
                      :key="index"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </RadioButton>
                  </RadioGroup>
                  <DatePicker
                    style="width: 130px !important"
                    v-model:value="date1"
                    :allow-clear="false"
                    :picker="dayAndMonth1 === indicatorType.小时数据 ? 'date' : 'month'"
                    :disabledDate="disabledDate"
                    @change="onDayAndMonthChange1($event)"
                  />
                </div>
              </template>
              <CardIndicator
                :list="cardList1"
                :isShowPage="isShowPage"
                :week-and-month="dayAndMonth1 === indicatorType.小时数据 ? '周' : '月'"
                :index="0"
                :decimalPlaces="2"
                :selectedIndictor="indictor"
                @view-chart="onViewChart1"
              />
            </Card>
          </Col>
          <Col span="8" class="overflow-hidden" data-resource-code="scls_dl">
            <Card
              title="电量"
              class="h-full"
              :bodyStyle="{ height: 'calc(100% - 49px)', overflow: 'hidden' }"
            >
              <template #extra>
                <div class="flex gap-4 my-2">
                  <RadioGroup
                    class="flex-shrink-0"
                    v-model:value="dayAndMonth2"
                    @change="onDayAndMonthChange2($event, false)"
                  >
                    <RadioButton
                      v-for="(item, index) in dayAndMonthOptions"
                      :key="index"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </RadioButton>
                  </RadioGroup>
                  <DatePicker
                    style="width: 130px !important"
                    v-model:value="date2"
                    :allow-clear="false"
                    :picker="dayAndMonth2 === indicatorType.小时数据 ? 'date' : 'month'"
                    :disabledDate="disabledDate"
                    @change="onDayAndMonthChange2($event)"
                  />
                </div>
              </template>
              <CardIndicator
                :list="cardList2"
                :isShowPage="isShowPage"
                :week-and-month="dayAndMonth2 === indicatorType.小时数据 ? '周' : '月'"
                :index="1"
                :selectedIndictor="indictor"
                @view-chart="onViewChart2"
              />
            </Card>
          </Col>
          <Col span="8" class="overflow-hidden" data-resource-code="scls_sl">
            <Card
              title="流量"
              class="h-full"
              :bodyStyle="{ height: 'calc(100% - 49px)', overflow: 'hidden' }"
            >
              <template #extra>
                <div class="flex gap-4 my-2">
                  <RadioGroup
                    class="flex-shrink-0"
                    v-model:value="dayAndMonth3"
                    @change="onDayAndMonthChange3($event, false)"
                  >
                    <RadioButton
                      v-for="(item, index) in dayAndMonthOptions"
                      :key="index"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </RadioButton>
                  </RadioGroup>
                  <DatePicker
                    style="width: 130px !important"
                    v-model:value="date3"
                    :allow-clear="false"
                    :picker="dayAndMonth3 === indicatorType.小时数据 ? 'date' : 'month'"
                    :disabledDate="disabledDate"
                    @change="onDayAndMonthChange3($event)"
                  />
                </div>
              </template>
              <CardIndicator
                :list="cardList3"
                :isShowPage="isShowPage"
                :week-and-month="dayAndMonth3 === indicatorType.小时数据 ? '周' : '月'"
                :index="2"
                :decimalPlaces="0"
                :selectedIndictor="indictor"
                @view-chart="onViewChart3"
              />
            </Card>
          </Col>
          <Col span="8" class="overflow-hidden" data-resource-code="scls_yl">
            <Card
              title="压力"
              class="h-full"
              :bodyStyle="{ height: 'calc(100% - 49px)', overflow: 'hidden' }"
            >
              <template #extra>
                <div class="flex gap-4 my-2">
                  <RadioGroup
                    class="flex-shrink-0"
                    v-model:value="dayAndMonth4"
                    @change="onDayAndMonthChange4($event, false)"
                  >
                    <RadioButton
                      v-for="(item, index) in dayAndMonthOptions"
                      :key="index"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </RadioButton>
                  </RadioGroup>
                  <DatePicker
                    style="width: 130px !important"
                    v-model:value="date4"
                    :allow-clear="false"
                    :picker="dayAndMonth4 === indicatorType.小时数据 ? 'date' : 'month'"
                    :disabledDate="disabledDate"
                    @change="onDayAndMonthChange4($event)"
                  />
                </div>
              </template>
              <CardIndicator
                :list="cardList4"
                :isShowPage="isShowPage"
                :week-and-month="dayAndMonth4 === indicatorType.小时数据 ? '周' : '月'"
                :index="3"
                :decimalPlaces="2"
                :selectedIndictor="indictor"
                @view-chart="onViewChart4"
              />
            </Card>
          </Col>
          <Col span="8" class="overflow-hidden" data-resource-code="scls_yh">
            <Card
              title="药耗"
              class="h-full"
              :bodyStyle="{ height: 'calc(100% - 49px)', overflow: 'hidden' }"
            >
              <template #extra>
                <div class="flex gap-4 my-2">
                  <RadioGroup
                    class="flex-shrink-0"
                    v-model:value="dayAndMonth5"
                    @change="onDayAndMonthChange5($event, false)"
                  >
                    <RadioButton
                      v-for="(item, index) in dayAndMonthOptions"
                      :key="index"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </RadioButton>
                  </RadioGroup>
                  <DatePicker
                    style="width: 130px !important"
                    v-model:value="date5"
                    :allow-clear="false"
                    :picker="dayAndMonth5 === indicatorType.小时数据 ? 'date' : 'month'"
                    :disabledDate="disabledDate"
                    @change="onDayAndMonthChange5($event)"
                  />
                </div>
              </template>
              <CardIndicator
                :list="cardList5"
                :isShowPage="isShowPage"
                :week-and-month="dayAndMonth5 === indicatorType.小时数据 ? '周' : '月'"
                :index="4"
                :selectedIndictor="indictor"
                @view-chart="onViewChart5"
              />
            </Card>
          </Col>
          <Col span="8" class="overflow-hidden">
            <div class="w-full h-full flex items-center justify-center no-more">
              没有更多数据了～
            </div>
          </Col>
        </Row>
      </div>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import dayjs from 'dayjs';
  import { Card, Row, Col, RadioGroup, RadioButton, DatePicker } from 'ant-design-vue';
  import { PageWrapper } from '/@/components/Page';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import LineChart from './LineChart.vue';
  import CardIndicator from './CardIndicator.vue';
  import {
    getIndexListApi,
    getProductHistoryDataApi,
    getIndicatorStatisticsDataApi,
  } from '/@zhcz/api/process-production/production-history';
  import type { ChartData } from './typing';
  import type {
    QueryProductHistory,
    IProductHistory,
    QueryIndicatorStatistics,
    IIndicatorStatistics,
  } from '/@zhcz/api/process-production/production-history/model';
  import { indicatorType, indicatorCategory } from '/@zhcz/enums/indicator';
  import { getFactoryId } from '/@zhcz/utils/factory';

  defineOptions({
    name: 'ProcessProductionProductionHistory',
  });

  const factoryId = getFactoryId();

  // '上月-当月', '上月-前月'
  const dayOptions = ref(['昨日-今日', '昨日-前日']);
  const twoDays = ref('昨日-今日');
  const type = ref('天');

  const chartRef = ref<typeof LineChart>();

  const indictor = ref('');
  const selectOptions = ref<Recordable[]>([]);
  const selectedIndictorName = ref('');

  async function getIndexList() {
    const data = await getIndexListApi();
    selectOptions.value = data.map((item) => ({
      value: item.code,
      label: item.name,
    }));
    indictor.value = `${selectOptions.value[0]?.value}&${dayAndMonth1.value}`;
  }

  const dateType = ref(indicatorType.小时数据);
  function getQueryDataChart(date?: dayjs.Dayjs) {
    let startDateStr1 = '';
    let startDateStr2 = '';
    let endDateStr1 = '';
    let endDateStr2 = '';

    const startDateFormat = 'YYYY-MM-DD 00:00:00';
    const endDateFormat = 'YYYY-MM-DD 23:59:59';
    if (twoDays.value === '昨日-今日') {
      startDateStr1 = dayjs(date).subtract(1, 'day').format(startDateFormat);
      endDateStr1 = dayjs(date).subtract(1, 'day').format(endDateFormat);
      startDateStr2 = dayjs(date).format(startDateFormat);
      endDateStr2 = dayjs(date).format(endDateFormat);
    } else if (twoDays.value === '昨日-前日') {
      startDateStr1 = dayjs(date).subtract(1, 'day').format(startDateFormat);
      endDateStr1 = dayjs(date).subtract(1, 'day').format(endDateFormat);
      startDateStr2 = dayjs(date).subtract(2, 'day').format(startDateFormat);
      endDateStr2 = dayjs(date).subtract(2, 'day').format(endDateFormat);
    } else if (twoDays.value === '上月-当月') {
      startDateStr1 = dayjs(date).subtract(1, 'months').startOf('months').format(startDateFormat);
      endDateStr1 = dayjs(date).subtract(1, 'months').endOf('months').format(endDateFormat);
      startDateStr2 = dayjs(date).startOf('months').format(startDateFormat);
      endDateStr2 = dayjs(date).endOf('months').format(endDateFormat);
    } else if (twoDays.value === '上月-前月') {
      startDateStr1 = dayjs(date).subtract(1, 'months').startOf('months').format(startDateFormat);
      endDateStr1 = dayjs(date).subtract(1, 'months').endOf('months').format(endDateFormat);
      startDateStr2 = dayjs(date).subtract(2, 'months').startOf('months').format(startDateFormat);
      endDateStr2 = dayjs(date).subtract(2, 'months').endOf('months').format(endDateFormat);
    }

    return {
      startDateTime1: dayjs(startDateStr1).valueOf(),
      endDateTime1: dayjs(endDateStr1).valueOf(),
      startDateTime2: dayjs(startDateStr2).valueOf(),
      endDateTime2: dayjs(endDateStr2).valueOf(),
    };
  }

  function getQueryProductHistory(date?: dayjs.Dayjs) {
    const code = (indictor.value || '').split('&')[0];
    const selectedItem = selectOptions.value.find((item) => item.value === code);

    const indecatorCodeList = selectedItem
      ? [
          {
            indexName: selectedItem.label as string,
            indexCode: selectedItem.value as string,
            unit: '',
          },
        ]
      : [];

    const dateParams = getQueryDataChart(date);
    const query1: QueryProductHistory = {
      factoryId: factoryId as string,
      indecatorCodeList,
      startDateTime: dateParams.startDateTime1,
      endDateTime: dateParams.endDateTime1,
      timeType: dateType.value,
      resourceInterfaceId: 3,
    };

    const query2: QueryProductHistory = {
      factoryId: factoryId as string,
      indecatorCodeList,
      startDateTime: dateParams.startDateTime2,
      endDateTime: dateParams.endDateTime2,
      timeType: dateType.value,
      resourceInterfaceId: 3,
    };

    return [query1, query2];
  }

  const chartData = ref<ChartData[]>([]);
  const chartOptions = {
    grid: {
      left: '1%',
      right: '1%',
      bottom: 0,
      containLabel: true,
    },
  };

  const showEcharts = computed(() => {
    return chartData.value.some((item) => item.data.some((data) => data.value));
  });

  async function getProductHistoryData() {
    let date = dayjs();
    // 取选中指标的日期
    const cardList = [cardList1.value, cardList2.value, cardList3.value];
    cardList.forEach((item) => {
      const findItem = item.find((i) => i.indicatorCode === indictor.value);
      if (findItem) {
        date =
          item === cardList1.value
            ? date1.value
            : item === cardList2.value
            ? date2.value
            : date3.value;
      }
    });
    const params = getQueryProductHistory(date);
    const promises = [getProductHistoryDataApi(params[0]), getProductHistoryDataApi(params[1])];
    let result: IProductHistory[][] = [];
    await Promise.allSettled(promises).then((results: Recordable[]) => {
      const values = results.map((i) => i.value);

      result = values.filter(Boolean);
    });
    chartData.value = result.map((item, index) => {
      selectedIndictorName.value = item[0].codeRecord[0].indexTitie;
      let indexName = '';
      if (twoDays.value) {
        // indexName = `${twoDays.value.split('-')[index]}${item[0].codeRecord[0].indexTitie}`;
        indexName = `${twoDays.value.split('-')[index]}`;
      } else {
        indexName = `${dayjs(item[0].provideTime).format(legendDateStr.value)} ${
          item[0].codeRecord[0].indexTitie
        }`;
      }

      return {
        indexName,
        indexCode: item[0].codeRecord[0].indicatorCode,
        unit: item[0].codeRecord[0].unit,
        data: item.map((item) => {
          return {
            datetime: item.provideTime,
            value: item.codeRecord[0].val,
          };
        }),
      };
    });
  }

  const cardList1 = ref<IIndicatorStatistics[]>([]);
  const cardList2 = ref<IIndicatorStatistics[]>([]);
  const cardList3 = ref<IIndicatorStatistics[]>([]);
  const cardList4 = ref<IIndicatorStatistics[]>([]);
  const cardList5 = ref<IIndicatorStatistics[]>([]);
  async function getIndicatorStatisticsData(
    codeType: indicatorCategory,
    date: {
      startDateTime: number;
      endDateTime: number;
    },
    timeType: indicatorType,
  ) {
    const params: QueryIndicatorStatistics = {
      factoryId: factoryId as string,
      codeType,
      startDateTime: date.startDateTime,
      endDateTime: date.endDateTime,
      timeType,
      resourceInterfaceId: 12,
    };
    const data = await getIndicatorStatisticsDataApi(params);

    return data.map((item) => {
      return {
        ...item,
        indicatorCode: `${item.indicatorCode}&${timeType}`,
      };
    });
  }

  function getQueryDateCard(date: dayjs.Dayjs, timeType: indicatorType) {
    let startDateTime = 0;
    let endDateTime = 0;

    if (timeType === indicatorType.小时数据) {
      startDateTime = dayjs(date.format('YYYY-MM-DD 00:00:00')).valueOf();
      endDateTime = dayjs(date.format('YYYY-MM-DD 23:59:59')).valueOf();
    } else if (timeType === indicatorType.天数据) {
      startDateTime = dayjs(date.startOf('months').format('YYYY-MM-DD 00:00:00')).valueOf();
      endDateTime = dayjs(date.endOf('months').format('YYYY-MM-DD 23:59:59')).valueOf();
    }

    return {
      startDateTime,
      endDateTime,
    };
  }
  // 获取每部分的最大长度，每部分展示四条，都小于四条则不显示分页
  const maxLength = ref(0);
  async function getData() {
    await getIndexList();
    await getProductHistoryData();

    const promises = [
      getIndicatorStatisticsData(
        indicatorCategory.水质,
        getQueryDateCard(date1.value, dayAndMonth1.value),
        dayAndMonth1.value,
      ),
      getIndicatorStatisticsData(
        indicatorCategory.电量,
        getQueryDateCard(date2.value, dayAndMonth2.value),
        dayAndMonth2.value,
      ),
      getIndicatorStatisticsData(
        indicatorCategory.流量,
        getQueryDateCard(date3.value, dayAndMonth3.value),
        dayAndMonth3.value,
      ),
      getIndicatorStatisticsData(
        indicatorCategory.压力,
        getQueryDateCard(date4.value, dayAndMonth4.value),
        dayAndMonth4.value,
      ),
      getIndicatorStatisticsData(
        indicatorCategory.药耗,
        getQueryDateCard(date5.value, dayAndMonth5.value),
        dayAndMonth5.value,
      ),
    ];
    await Promise.allSettled(promises).then((results: Recordable[]) => {
      const values = results.map((i) => i.value);
      cardList1.value = values[0] || [];
      cardList2.value = values[1] || [];
      cardList3.value = values[2] || [];
      cardList4.value = values[3] || [];
      cardList5.value = values[4] || [];
    });
    maxLength.value = Math.max(
      cardList1.value.length,
      cardList2.value.length,
      cardList3.value.length,
      cardList4.value.length,
      cardList5.value.length,
    );
  }
  const isShowPage = computed(() => {
    return maxLength.value > 4;
  });

  getData();

  async function onRadioChange() {
    await getProductHistoryData();
    chartRef.value && chartRef.value.renderEcharts();
  }

  // async function onSelectChange() {
  //   await getProductHistoryData();
  //   chartRef.value && chartRef.value.renderEcharts();
  // }

  const dayAndMonthOptions = [
    { label: '日', value: indicatorType.小时数据 },
    { label: '月', value: indicatorType.天数据 },
  ];
  const dayAndMonth1 = ref(dayAndMonthOptions[0].value);
  const dayAndMonth2 = ref(dayAndMonthOptions[0].value);
  const dayAndMonth3 = ref(dayAndMonthOptions[0].value);
  const dayAndMonth4 = ref(dayAndMonthOptions[0].value);
  const dayAndMonth5 = ref(dayAndMonthOptions[0].value);
  const date1 = ref(dayjs());
  const date2 = ref(dayjs());
  const date3 = ref(dayjs());
  const date4 = ref(dayjs());
  const date5 = ref(dayjs());
  const disabledDate = (current: dayjs.Dayjs) => {
    return current && current > dayjs().endOf('day');
  };
  // 只有本tab下的日月切换才会触发图渲染

  function getCurrentRecord(data) {
    if (data.length < 1) return;
    let prefixIndector = indictor.value.slice(0, -2);
    return data.find((i) => i.indicatorCode.includes(prefixIndector));
  }
  async function onDayAndMonthChange1(_) {
    const data = await getIndicatorStatisticsData(
      indicatorCategory.水质,
      getQueryDateCard(date1.value, dayAndMonth1.value),
      dayAndMonth1.value,
    );
    cardList1.value = data;
    let record = getCurrentRecord(data);
    if (record) {
      onViewChart1(record);
    }
  }
  async function onDayAndMonthChange2(_) {
    const data = await getIndicatorStatisticsData(
      indicatorCategory.电量,
      getQueryDateCard(date2.value, dayAndMonth2.value),
      dayAndMonth2.value,
    );
    cardList2.value = data;
    let record = getCurrentRecord(data);
    if (record) {
      onViewChart2(record);
    }
  }
  async function onDayAndMonthChange3(_) {
    const data = await getIndicatorStatisticsData(
      indicatorCategory.流量,
      getQueryDateCard(date3.value, dayAndMonth3.value),
      dayAndMonth3.value,
    );

    cardList3.value = data;
    let record = getCurrentRecord(data);
    if (record) {
      onViewChart3(record);
    }
  }
  async function onDayAndMonthChange4(_) {
    const data = await getIndicatorStatisticsData(
      indicatorCategory.压力,
      getQueryDateCard(date4.value, dayAndMonth4.value),
      dayAndMonth4.value,
    );

    cardList4.value = data;
    let record = getCurrentRecord(data);
    if (record) {
      onViewChart4(record);
    }
  }
  async function onDayAndMonthChange5(_) {
    const data = await getIndicatorStatisticsData(
      indicatorCategory.药耗,
      getQueryDateCard(date5.value, dayAndMonth5.value),
      dayAndMonth5.value,
    );

    cardList5.value = data;
    let record = getCurrentRecord(data);
    if (record) {
      onViewChart5(record);
    }
  }

  const legendDateStr = ref('');
  async function onViewChart1(record: IIndicatorStatistics) {
    if (dayAndMonth1.value === indicatorType.天数据) {
      dayOptions.value = ['上月-当月', '上月-前月'];
      type.value = '月';
      twoDays.value = '上月-当月';
    }

    if (dayAndMonth1.value === indicatorType.小时数据) {
      dayOptions.value = ['昨日-今日', '昨日-前日'];
      type.value = '天';
      twoDays.value = '昨日-今日';
    }

    legendDateStr.value = dayAndMonth1.value === indicatorType.小时数据 ? 'YYYY-MM-DD' : 'YYYY-MM';
    indictor.value = record.indicatorCode;
    dateType.value = dayAndMonth1.value;
    await getProductHistoryData();
    chartRef.value && chartRef.value.renderEcharts();
  }

  async function onViewChart2(record: IIndicatorStatistics) {
    if (dayAndMonth2.value === indicatorType.天数据) {
      dayOptions.value = ['上月-当月', '上月-前月'];
      type.value = '月';
      twoDays.value = '上月-当月';
    }

    if (dayAndMonth2.value === indicatorType.小时数据) {
      dayOptions.value = ['昨日-今日', '昨日-前日'];
      type.value = '天';
      twoDays.value = '昨日-今日';
    }

    legendDateStr.value = dayAndMonth2.value === indicatorType.小时数据 ? 'YYYY-MM-DD' : 'YYYY-MM';
    indictor.value = record.indicatorCode;
    dateType.value = dayAndMonth2.value;
    await getProductHistoryData();
    chartRef.value && chartRef.value.renderEcharts();
  }

  async function onViewChart3(record: IIndicatorStatistics) {
    if (dayAndMonth3.value === indicatorType.天数据) {
      dayOptions.value = ['上月-当月', '上月-前月'];
      type.value = '月';
      twoDays.value = '上月-当月';
    }

    if (dayAndMonth3.value === indicatorType.小时数据) {
      dayOptions.value = ['昨日-今日', '昨日-前日'];
      type.value = '天';
      twoDays.value = '昨日-今日';
    }

    legendDateStr.value = dayAndMonth3.value === indicatorType.小时数据 ? 'YYYY-MM-DD' : 'YYYY-MM';
    indictor.value = record.indicatorCode;
    dateType.value = dayAndMonth3.value;
    await getProductHistoryData();
    chartRef.value && chartRef.value.renderEcharts();
  }

  async function onViewChart4(record: IIndicatorStatistics) {
    if (dayAndMonth4.value === indicatorType.天数据) {
      dayOptions.value = ['上月-当月', '上月-前月'];
      type.value = '月';
      twoDays.value = '上月-当月';
    }

    if (dayAndMonth4.value === indicatorType.小时数据) {
      dayOptions.value = ['昨日-今日', '昨日-前日'];
      type.value = '天';
      twoDays.value = '昨日-今日';
    }

    legendDateStr.value = dayAndMonth4.value === indicatorType.小时数据 ? 'YYYY-MM-DD' : 'YYYY-MM';
    indictor.value = record.indicatorCode;
    dateType.value = dayAndMonth4.value;
    await getProductHistoryData();
    chartRef.value && chartRef.value.renderEcharts();
  }

  async function onViewChart5(record: IIndicatorStatistics) {
    if (dayAndMonth5.value === indicatorType.天数据) {
      dayOptions.value = ['上月-当月', '上月-前月'];
      type.value = '月';
      twoDays.value = '上月-当月';
    }

    if (dayAndMonth5.value === indicatorType.小时数据) {
      dayOptions.value = ['昨日-今日', '昨日-前日'];
      type.value = '天';
      twoDays.value = '昨日-今日';
    }

    legendDateStr.value = dayAndMonth5.value === indicatorType.小时数据 ? 'YYYY-MM-DD' : 'YYYY-MM';
    indictor.value = record.indicatorCode;
    dateType.value = dayAndMonth5.value;
    await getProductHistoryData();
    chartRef.value && chartRef.value.renderEcharts();
  }
</script>

<style lang="less" scoped>
  .card-item {
    // height: calc(100% - 372px);
    overflow-y: auto;
    overflow-x: hidden;
  }

  :deep(.ant-card) {
    .ant-pagination .ant-pagination-disabled:hover {
      background-color: transparent !important;
    }

    .ant-card-head {
      min-height: auto;
      padding: 0 16px;
      border-color: #e9e9e9;

      .ant-card-head-title {
        color: #333333;

        & > span {
          color: #333333;
        }
      }
    }

    .ant-card-body {
      padding: 16px 16px 16px;

      .h-empty {
        .ant-empty {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
        }
      }
    }
  }

  :deep(.ant-radio-group .ant-radio-button-wrapper .ant-radio-button) {
    color: #333333;
  }

  .no-more {
    color: @text-color-defined;
  }
</style>
