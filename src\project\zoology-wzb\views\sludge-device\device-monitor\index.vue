<template>
  <div :class="prefixCls" class="w-full h-full overflow-hidden">
    <div v-show="!showViewPage" class="device-monitor-page" :class="isEdit ? 'edit_page' : ''">
      <div class="header">
        <BasicForm @register="registerForm" />
      </div>
      <div class="content">
        <div class="list-container">
          <!-- 列表内容区 -->
          <template v-if="dataList.length">
            <div class="list">
              <Row :gutter="[16, 16]">
                <Col :xs="12" :xl="8" :xxl="6" v-for="item in dataList" :key="item.id">
                  <div :class="['item', { error: item.equipmentStatus == 3 }]">
                    <!-- 信息卡片 -->
                    <InfoCard @click="handleDetail(item)" :value="item" :schemas="infoCardSchemas">
                      <template #equipmentName>
                        <div class="name">
                          <Tooltip>
                            <template #title>
                              <div class="tip-title">{{ item.equipmentName }}</div>
                            </template>
                            {{ item.equipmentName }}
                          </Tooltip>
                        </div>
                      </template>
                    </InfoCard>
                    <!-- 悬浮在右上角的状态 -->
                    <div class="fixed-status-box">
                      <div :class="['tag-item', getStatusClass(item.equipmentStatus)]">
                        {{ getOptionLabel(equipmentStatusOptions, item.equipmentStatus) }}
                      </div>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
            <div class="pagetion">
              <Pagination
                v-model:current="pagination.current"
                v-model:page-size="pagination.pageSize"
                :total="pagination.total"
                :page-size-options="pageSizeOptions"
                size="small"
                showSizeChanger
                :showQuickJumper="pagination.total / pagination.pageSize > 1"
                :show-total="(total) => `共${total}条`"
                @change="handleChangePage"
                @showSizeChange="handleChangePage"
              />
            </div>
          </template>
          <template v-else>
            <div class="empty">
              <HEmpty />
            </div>
          </template>
        </div>
      </div>
    </div>
    <!-- 详情页 -->
    <div class="view-page" v-show="showViewPage">
      <DetailPage v-if="showViewPage" @back="backPage" :equipmentId="equipmentId" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import DetailPage from './components/DetailPage/DetailPage.vue';
  import { Tooltip } from 'ant-design-vue';
  import InfoCard from './components/InfoCard/InfoCard.vue';
  import { Row, Col, Pagination } from 'ant-design-vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import HEmpty from '/@/components/HEmpty';
  import {
    searchFormSchema,
    infoCardSchemas,
    equipmentStatusOptions,
    getOptionLabel,
  } from './data';
  import { ref, reactive } from 'vue';
  import { getEevReturnDomain } from '/@/utils/file/url';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { SearchEquipmentMonitorApi } from '/@zoology-wzb/api/device';
  const { prefixCls } = useDesign('sludge-device-monitor');

  const pageSizeOptions = ref<string[]>(['12', '24', '36', '48', '60']);
  const pagination = reactive({
    current: 1,
    pageSize: 12,
    total: 0,
  });

  const isEdit = ref(false);
  const dataList = ref<any[]>([]); // 设备列表数据

  const [registerForm, { getFieldsValue, setFieldsValue }] = useForm({
    actionColOptions: {
      span: 6,
    },
    schemas: searchFormSchema,
    showActionButtonGroup: true,
    submitFunc: async () => {
      isEdit.value = false;
      await getData();
    },
    resetFunc: async () => {
      pagination.current = 1;
      isEdit.value = false;
      await setFieldsValue({
        name: undefined,
        level: undefined,
        status: undefined,
      });
      await getData();
    },
  });

  const equipmentId = ref(); //设备id
  const showViewPage = ref(false);
  const handleDetail = (v) => {
    equipmentId.value = v.id;
    showViewPage.value = true;
  };
  /* 返回 */
  const backPage = () => {
    showViewPage.value = false;
    equipmentId.value = null;
  };
  /* 更改分页配置 */
  const handleChangePage = (page, pageSize) => {
    pagination.current = page;
    pagination.pageSize = pageSize;
    getData();
  };

  const statusLIst = ['success', 'info', 'error', 'offline']; //状态class
  const getStatusClass = (status) => {
    console.log(statusLIst[status - 1]);
    return statusLIst[status - 1];
  };
  /* 获取列表数据 */
  const getData = async () => {
    const values = getFieldsValue();
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      ...values,
      salesStatusKey: 1,
    };
    const res = await SearchEquipmentMonitorApi(params);
    pagination.total = res.total;
    const list = res.records.map((i) => {
      if (i.thumbnailUrl) {
        i.thumbnailUrl = getEevReturnDomain(i.thumbnailUrl);
        i.picUrl = i.thumbnailUrl;
      }
      return {
        ...i,
        checked: false,
      };
    });
    dataList.value = list;
  };
  getData();
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-sludge-device-monitor';

  .@{prefix-cls} {
    padding: 0 16px 16px;

    .device-monitor-page {
      padding: 16px;
      height: 100%;
      border-radius: 4px;
      background-color: #fff;

      &.edit_page {
        height: calc(100% - 56px);
      }

      .header {
        position: relative;

        &:after {
          position: absolute;
          content: '';
          width: calc(100% + 32px);
          left: -16px;
          bottom: 0;
          height: 1px;
          background: #e9e9e9;
        }

        :deep(.ant-form) {
          .ant-form-item {
            margin-bottom: 16px;
          }
        }
      }

      .content {
        padding-top: 16px;
        height: calc(100% - 48px);

        .list-container {
          height: calc(100% - 48px);

          .empty {
            height: calc(100% + 48px);
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .list {
            overflow-y: auto;
            overflow-x: hidden;
            height: 100%;
            padding-right: 8px;
            width: calc(100% + 16px);

            .item {
              position: relative;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #d9d9d9;
              cursor: pointer;
              // overflow: hidden;

              &:hover {
                border-color: var(--theme-color);
              }

              &.error {
                box-shadow: inset 0px 0px 8px 0px rgba(255, 43, 43, 0.56);
                border: 1px solid #ff522b;
              }

              .name {
                font-weight: 600;
                font-size: 16px;
                line-height: 1;
                color: #333333;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
              }

              .fixed-status-box {
                position: absolute;
                right: -1px;
                top: -1px;
                border-radius: 0px 4px 0px 8px;

                .tag-item {
                  font-size: 12px;
                  padding: 4px 8px;
                  line-height: 1;
                  color: #333;
                  background-color: #e9e9e9;
                  border: 1px solid rgba(51, 51, 51, 0.44);
                  border-radius: 0px 4px 0px 8px;

                  &.success {
                    color: #4db803;
                    background-color: rgba(77, 184, 3, 0.12);
                    border-color: rgba(77, 184, 3, 0.56);
                  }

                  &.error {
                    color: #ff2e2e;
                    background-color: rgba(255, 46, 46, 0.12);
                    border-color: rgba(255, 46, 46, 0.56);
                  }

                  &.offline {
                    color: #6f0000;
                    background-color: rgba(111, 0, 0, 0.08);
                    border-color: rgba(111, 0, 0, 0.44);
                  }
                }
              }
            }
          }

          .pagetion {
            margin-top: 16px;
          }
        }
      }
    }

    .view-page {
      height: 100%;
      padding-bottom: 16px;
    }
  }
</style>
