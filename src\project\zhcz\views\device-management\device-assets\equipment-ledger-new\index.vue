<template>
  <div class="device-page">
    <div class="device-resource-page" v-show="!showViewPage && !showEditPage">
      <div class="page-content">
        <div class="tree-container" :class="{ 'hide-tree-container': !isShowTree }">
          <div class="tit-content">
            <div class="tit">设备地址</div>
            <div class="add-btn">
              <Dropdown overlayClassName="custom-dropdown-tree" :trigger="['hover']">
                <div> <PlusOutlined />新增</div>
                <template #overlay>
                  <Menu>
                    <MenuItem @click="handleAddWaterLine">添加水线</MenuItem>
                    <MenuItem
                      @click="
                        handleAddTechnology(factoryId, {
                          type: 1,
                          parentType: null,
                          parentId: null,
                          uniqueId: factoryId,
                          id: factoryId,
                        })
                      "
                    >
                      添加工艺段
                    </MenuItem>
                  </Menu>
                </template>
              </Dropdown>
            </div>
          </div>
          <div class="tree-content">
            <BasicTree
              combo
              search
              defaultExpandAll
              placeholder="请输入"
              treeWrapperClassName="h-[calc(100%-52px)] overflow-auto"
              :clickRowToExpand="false"
              :treeData="treeData"
              :fieldNames="treeFieldNames"
              @select="handleSelectNode"
              ref="asyncExpandTreeRef"
              emptyClass="mt-[50%]"
            >
              <template #title="node">
                <div
                  class="flex items-center justify-between flex-1"
                  @mouseenter="handleHoverNode(node)"
                  @mouseleave="hoverNode = null"
                >
                  <div style="overflow: hidden; text-overflow: ellipsis">
                    <Tooltip :title="node.nodeName">
                      {{ node.nodeName }}
                    </Tooltip>
                  </div>
                  <Dropdown overlayClassName="custom-dropdown-tree" :trigger="['hover']">
                    <div v-show="hoverNode && hoverNode.virtualId === node.virtualId">
                      <div class="flex">
                        <Icon size="16" color="var(--theme-color)" icon="icon-park-outline:more" />
                      </div>
                    </div>
                    <template #overlay>
                      <Menu @mouseenter="handleHoverNode(node)" @mouseleave="hoverNode = null">
                        <MenuItem
                          @click="handleAddTechnology(node.uniqueId, node)"
                          v-if="node.type === 1 || node.type === 2 || node.type === 3"
                          >添加工艺段</MenuItem
                        >
                        <MenuItem
                          @click="handleEditTree(node.uniqueId, node)"
                          v-if="node.type !== 3"
                          >编辑</MenuItem
                        >
                        <Popconfirm
                          title="是否确认删除"
                          ok-text="确认"
                          cancel-text="取消"
                          @confirm="handleDeleteTree(node.uniqueId, node)"
                          v-if="node.type !== 3"
                        >
                          <MenuItem>删除</MenuItem>
                        </Popconfirm>
                      </Menu>
                    </template>
                  </Dropdown>
                </div>
              </template>
            </BasicTree>
          </div>
        </div>
        <div
          class="toggle-tree"
          @click="handleChangeTreeShow"
          :style="{
            width: isShowTree ? '0' : '24px',
            borderRight: isShowTree ? 'none' : '1px solid #e9e9e9',
          }"
        >
          <div
            class="toggle-tree-btn"
            :style="{
              left: isShowTree ? '-24px' : '0',
              borderRadius: isShowTree ? '4px 0px 0px 4px' : '0px 4px 4px 0px',
              background: isShowTree ? '' : 'var(--theme-color)',
              borderColor: isShowTree ? '' : 'var(--theme-color)',
            }"
          >
            <Icon
              :icon="isShowTree ? 'icon-park-outline:left' : 'icon-park-outline:right'"
              v-show="isShowTree"
              :color="isShowTree ? 'rgba(51, 51, 51, 0.65)' : 'rgb(255,255,255)'"
            />
          </div>
        </div>
        <div ref="tableBoxRef" class="table-box">
          <Ledger
            ref="ledgerRef"
            :treeNodeType="treeNodeType"
            :treeNodeId="treeNodeId"
            :parentType="parentType"
            :parentId="parentId"
            :rootNodeId="factoryId"
            :id="id"
            :treeIsLoading="treeIsLoading"
            @viewPage="viewPage"
            @editPage="editPage"
            @resetTree="resetTree"
          />
        </div>
      </div>
      <WaterLineModal @success="getTreeData" @register="registerWaterLineModal" />
      <TechnologyModal @success="getTreeData" @register="registerTechnologyModal" />
    </div>
    <div class="view-page" v-if="showViewPage">
      <ModulePage :equipmentId="viewPageId" :equipmentName="viewPageEqName" @back="backPage" />
    </div>
    <div class="view-page" v-if="showEditPage">
      <EditPage
        :stationId="factoryId"
        :equipmentId="editEqId"
        :copy="editIsCopy"
        @back="editBackPage"
        @success="editSuccess"
      />
    </div>
  </div>
</template>

<script lang="ts" setup name="DeviceManagementDeviceAssetsEquipmentLedgerNew">
  import { ref, onMounted, nextTick, unref } from 'vue';
  import Icon from '/@/components/Icon';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Dropdown, Menu, MenuItem, Popconfirm, Tooltip } from 'ant-design-vue';
  import { BasicTree, TreeActionType } from '/@/components/Tree';
  import { useLoading } from '/@/components/Loading';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import {
    getTechnologyUnitTreeNew,
    deleteWaterLine,
    deleteTechnology,
  } from '/@zhcz/api/device-management';
  import { useUserStore } from '/@/store/modules/user';
  import { createLocalStorage } from '/@/utils/cache';
  import { FACTORY_KEY } from '/@/enums/cacheEnum';
  import WaterLineModal from './components/WaterLineModal.vue';
  import TechnologyModal from './components/TechnologyModal.vue';
  import Ledger from './modules/ledger/index.vue';
  import EditPage from './modules/ledger/EditPage.vue';
  import ModulePage from './modules/index.vue';

  defineOptions({
    name: 'DeviceManagementDeviceAssetsEquipmentLedgerNew',
  });

  const [openFullLoading, closeFullLoading] = useLoading({});
  const treeFieldNames = {
    title: 'name',
    key: 'virtualId',
    children: 'children',
  };
  const treeData = ref<any[]>([]);
  const treeRawData = ref<any[]>([]);
  const selectedKeys = ref<string[]>([]);
  const isShowTree = ref(true);
  const tableBoxRef = ref<null | HTMLElement>(null);
  const hoverNode = ref();
  const tabActiveKey = ref(1);
  const treeNodeType = ref(0);
  const treeNodeId = ref<number | null>(0);
  const parentType = ref(0);
  const parentId = ref(0);
  const asyncExpandTreeRef = ref<Nullable<TreeActionType>>(null);

  const id = ref(0);
  const treeIsLoading = ref(false);

  const { createMessage } = useMessage();
  const ls = createLocalStorage();
  const userStore = useUserStore();

  const factoryId = ref(ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId);

  const [registerWaterLineModal, { openModal: openWaterLineModal }] = useModal();
  const [registerTechnologyModal, { openModal: openTechnologyModal }] = useModal();

  const handleChangeTreeShow = () => {
    isShowTree.value = !isShowTree.value;
    if (tableBoxRef.value) {
      tableBoxRef.value.style.width = isShowTree.value ? 'calc(100% - 248px)' : 'calc(100% - 24px)';
    }
  };

  const handleHoverNode = (node) => {
    hoverNode.value = node;
  };

  // 切换树
  const handleSelectNode = (keys, e) => {
    selectedKeys.value = keys;
    treeNodeId.value = e.selected ? Number(e.node.uniqueId) : null;
    treeNodeType.value = e.selected ? e.node.type : 1;
    parentId.value = e.selected ? e.node.parentId : Number(e.node.rootNodeId);
    parentType.value = e.selected ? e.node.parentType : 1;
    id.value = e.selected ? e.node.id : Number(e.node.rootNodeId);
    if (e.node.type !== 5) {
      tabActiveKey.value = 1;
    }
  };

  const renameNodeName = (tree) => {
    return tree.map((node) => {
      const newNode = { ...node, nodeName: node.name };
      delete newNode.name;

      // 递归处理子节点
      if (newNode.children) {
        newNode.children = renameNodeName(newNode.children);
      }

      return newNode;
    });
  };

  // 获取树数据
  const getTreeData = async () => {
    openFullLoading();
    const data = await getTechnologyUnitTreeNew({ showTuTypeFlag: true, hideEqpFlag: true });
    treeData.value = renameNodeName(data);
    treeRawData.value = renameNodeName(data);
    treeIsLoading.value = data && data.length ? true : false;
    treeNodeId.value = null;
    treeNodeType.value = 1;
    parentId.value = treeData.value[0]?.rootNodeId;
    parentType.value = 1;
    id.value = treeData.value[0]?.rootNodeId;
    closeFullLoading();

    nextTick(() => {
      unref(asyncExpandTreeRef)?.expandAll(true);
    });
  };

  const resetTree = () => {
    selectedKeys.value = [];
    treeNodeId.value = null;
    treeNodeType.value = 1;
    parentId.value = treeData.value[0]?.rootNodeId;
    parentType.value = 1;
    id.value = treeData.value[0]?.rootNodeId;
  };

  // 新增水线
  const handleAddWaterLine = () => {
    console.log('新增水线-水厂id', factoryId.value);
    const stationId = factoryId.value;
    openWaterLineModal(true, {
      isEdit: false,
      stationId,
    });
  };

  // 编辑水线
  const handleEditWaterLine = (uniqueId, node) => {
    console.log('编辑水线', uniqueId, node);
    const stationId = factoryId.value;
    openWaterLineModal(true, {
      isEdit: true,
      id: node.uniqueId,
      stationId,
    });
  };

  // 删除水线
  const handleDeleteWaterLine = async (uniqueId) => {
    console.log('删除水线', uniqueId);
    await deleteWaterLine({ id: uniqueId });
    if (uniqueId == treeNodeId.value) {
      selectedKeys.value = treeData.value.length ? [treeData.value[0]?.virtualId] : [];
      treeNodeId.value = treeData.value[0]?.uniqueId || 0;
      treeNodeType.value = treeData.value[0]?.type || 0;
      parentId.value = treeData.value[0]?.parentId;
      parentType.value = treeData.value[0]?.parentType;
      id.value = treeData.value[0]?.id;
    }
    createMessage.success('删除成功');
    getTreeData();
  };

  // 新增工艺段
  const handleAddTechnology = (uniqueId, node) => {
    console.log('新增工艺段', uniqueId, node);
    const parentId = node.type === 3 ? node.parentId : node.uniqueId;
    const parentType = node.type === 3 ? node.parentType : node.type;
    const unitCode = node.type === 3 ? node.id : '';

    openTechnologyModal(true, {
      isEdit: false,
      parentId,
      parentType,
      unitCode,
    });
  };

  // 编辑工艺段
  const handleEditTechnology = (id, node) => {
    console.log('编辑工艺段', id, node);
    openTechnologyModal(true, {
      id,
      isEdit: true,
    });
  };

  // 删除工艺段
  const handleDeleteTechnology = async (uniqueId) => {
    console.log('删除工艺段', uniqueId);
    await deleteTechnology({ id: uniqueId });
    if (uniqueId == treeNodeId.value) {
      selectedKeys.value = treeData.value.length ? [treeData.value[0]?.virtualId] : [];
      treeNodeId.value = treeData.value[0]?.uniqueId || 0;
      treeNodeType.value = treeData.value[0]?.type || 0;
      parentId.value = treeData.value[0]?.parentId;
      parentType.value = treeData.value[0]?.parentType;
      id.value = treeData.value[0]?.id;
    }
    createMessage.success('删除成功');
    getTreeData();
  };

  // 编辑树
  const handleEditTree = (uniqueId, node) => {
    console.log('编辑树', uniqueId, node);
    switch (node.type) {
      case 2:
        handleEditWaterLine(uniqueId, node);
        break;
      case 4:
        handleEditTechnology(uniqueId, node);
        break;
    }
  };

  // 删除树
  const handleDeleteTree = async (uniqueId, node) => {
    console.log('删除树', uniqueId, node);
    if (node.children && node.children.length > 0) {
      createMessage.warning('存在子级，不允许删除');
      return;
    }

    // type: 1 -> 水厂  2 -> 水线 3-> 工艺单元类型(无法编辑，删除) 4 -> 工艺段 5 -> 设备
    switch (node.type) {
      case 2:
        handleDeleteWaterLine(uniqueId);
        break;
      case 4:
        handleDeleteTechnology(uniqueId);
        break;
    }
  };

  const ledgerRef = ref();

  const viewPageId = ref();
  const viewPageEqName = ref('');
  const editEqId = ref();
  const editIsCopy = ref(false);
  const showViewPage = ref(false);
  const showEditPage = ref(false);

  const viewPage = ({ id, equipmentName }) => {
    viewPageId.value = id;
    viewPageEqName.value = equipmentName;
    showViewPage.value = true;
  };

  const editPage = ({ id, copy }) => {
    editEqId.value = id;
    editIsCopy.value = copy;
    console.log('编辑设备', id);
    showEditPage.value = true;
  };

  // 返回
  const backPage = () => {
    viewPageId.value = null;
    editEqId.value = null;
    editIsCopy.value = false;
    showViewPage.value = false;
    showEditPage.value = false;
  };

  // 从编辑页返回
  const editBackPage = () => {
    viewPageId.value = null;
    editEqId.value = null;
    showViewPage.value = false;
    showEditPage.value = false;
  };

  // 编辑成功
  const editSuccess = () => {
    editBackPage();
    ledgerRef.value.reload();
  };

  onMounted(() => {
    getTreeData();
  });
</script>

<style lang="less" scoped>
  .device-resource-page {
    padding: 0 16px 16px;

    height: 100%;
    width: 100%;

    .page-content {
      overflow: hidden;
      height: 100%;
      display: flex;
      width: 100%;
    }

    .tree-container {
      background: #fff;
      width: 248px;
      flex-shrink: 0;
      border-right: 1px solid #e9e9e9;
      transition: all 0.3s;
      border-radius: 4px;

      .tit-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        padding: 0 12px;
        border-bottom: 1px solid #e9e9e9;
        white-space: nowrap;

        .tit {
          font-size: 16px;
          font-weight: 600;
        }

        .add-btn {
          color: @theme-color;
          cursor: pointer;

          .anticon-plus {
            margin-right: 4px;
          }
        }
      }

      .tree-filter {
        padding: 12px 12px 0;

        :deep(.ant-select) {
          width: 100%;
        }
      }

      .tree-content {
        padding-top: 16px;
        height: calc(100% - 48px);
        overflow-x: hidden;
        overflow-y: hidden;

        :deep(.vben-tree) {
          .vben-tree-header {
            border-bottom: none;
          }
        }
      }
    }

    .hide-tree-container {
      width: 0;
      padding: 0;
      border-right: none;
      margin-right: 0;

      .tit-content,
      .tree-content {
        display: none;
      }
    }

    .toggle-tree {
      position: relative;
      width: 24px;
      height: 100%;
      background: #fff;
      border-right: 1px solid #e9e9e9;
      cursor: pointer;

      .toggle-tree-btn {
        position: absolute;
        top: 50%;
        width: 24px;
        height: 32px;
        border-radius: 4px 0px 0px 4px;
        border: 1px solid #e9e9e9;
        transform: translateY(-50%);
        cursor: pointer;
        text-align: center;
        line-height: 30px;
        z-index: 999;
        background: white;
        transition: all 0.3s;

        .anticon {
          margin: 0;
        }
      }
    }

    .table-box {
      position: relative;
      flex: 1;
      border-radius: 4px;
      width: calc(100% - 248px);

      :deep(.ant-tabs) {
        height: 100%;

        .ant-tabs-nav {
          border-radius: 4px 4px 0 0;
          padding: 0 16px;
          background-color: #fff;

          .ant-tabs-tab {
            padding: 13px 0;
          }
        }

        .ant-tabs-content {
          height: 100%;
          // padding: 0 16px;
        }
      }
    }
  }

  .search-container {
    padding: 12px 12px 0;

    :deep(.ant-input-affix-wrapper) {
      .ant-input-prefix img {
        width: 14px;
      }
    }
  }

  .device-page {
    height: 100%;
    width: 100%;

    .view-page {
      height: 100%;
      padding: 0 16px 16px;
    }
  }
</style>
<style lang="less">
  .custom-dropdown-tree {
    .ant-dropdown-menu-submenu {
      .ant-dropdown-menu-submenu-title {
        height: 44px;
        line-height: 44px;
        padding: 0 16px;
        padding-inline-end: 24px;
      }
    }
  }
</style>
