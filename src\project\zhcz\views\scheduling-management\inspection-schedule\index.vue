<!-- 排班计划 -->
<template>
  <div class="page-container">
    <div class="content-container">
      <Row :gutter="[24, 16]">
        <Col v-for="(item, index) in dataList" :key="item.id">
          <div class="item-conatiner">
            <div class="header-box">
              <div class="title-box">
                <img :src="scheduleLogo" width="32" />
                <div>{{ filterPlanType(item.type) }}</div>
              </div>
              <div class="oper-wrapper">
                <div
                  class="oper-box"
                  @mouseenter="
                    KykFlag = true;
                    KykIndexVal = index;
                  "
                  @mouseleave="
                    KykFlag = false;
                    KykIndexVal = index;
                  "
                  @click="KykHandle(item)"
                >
                  <span>查看</span>
                </div>
                <div
                  class="oper-box"
                  @mouseenter="
                    editFlag = true;
                    indexVal = index;
                  "
                  @mouseleave="
                    editFlag = false;
                    indexVal = index;
                  "
                  @click="handleEdit(item)"
                >
                  <img
                    :src="editFlag && indexVal === index ? editSvg : editSvgDefault"
                    class="open_svg"
                    alt="Logo"
                  />
                  <span>编辑</span>
                </div>
              </div>
            </div>
            <div class="detail-box">
              <div>
                <span class="label">周期：</span>
                <span class="">{{ item.cycle || '-' }}</span>
              </div>
              <div>
                <span class="label">开始时间：</span>
                <span class="">{{ item.planPeriod ? item.planPeriod : '-' }}</span>
              </div>
              <div>
                <span class="label">更新时间：</span>
                <span class="">{{
                  item.lastModificationTime ? item.lastModificationTime : '-'
                }}</span>
              </div>
              <div class="remark">
                <span class="label">备注：</span>
                <span class="value">
                  <Tooltip>
                    <template #title>{{ item.remark ? item.remark : '-' }}</template>
                    {{ item.remark ? item.remark : '-' }}
                  </Tooltip></span
                >
              </div>
            </div>
          </div>
        </Col>
      </Row>
    </div>
    <EditModal @success="getData" @register="registerEditModal" />
  </div>
</template>
<script lang="ts">
  export default { name: 'SchedulingManagementInspectionSchedule' };
</script>
<script lang="ts" setup name="SchedulingManagementInspectionSchedule">
  import { ref } from 'vue';
  // import { BasicForm, useForm } from '/@/components/Form';
  import { Tooltip, Row, Col } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  import dayjs from 'dayjs';
  import editSvg from '../assets/images/edit.svg';
  import editSvgDefault from '../assets/images/edit_default.svg';
  import { useLoading } from '/@/components/Loading';
  import { getPlanList } from '/@zhcz/api/scheduling';
  import EditModal from './components/EditModal.vue';
  import scheduleLogo from '../assets/images/title.svg';
  import { planTypeOptions } from '../data';

  const [openFullLoading, closeFullLoading] = useLoading({});

  const [registerEditModal, { openModal: openEditModal }] = useModal();

  const editFlag = ref(false);
  const KykFlag = ref(false);
  const KykIndexVal = ref(0);
  const indexVal = ref(0);
  // 编辑
  const handleEdit = async (item) => {
    openFullLoading();
    closeFullLoading();
    const dateArr = item.planPeriod.split('~');
    const diff = dayjs(dateArr[1]).diff(dateArr[0], 'day') + 1;
    const endTime = dayjs(dateArr[0]).add(diff / item.cycle - 1, 'day');

    openEditModal(true, {
      isEdit: true,
      endTime: dateArr[1],
      baseForm: {
        factoryId: item.factoryId,
        id: item.id,
        name: item.name,
        type: item.type,
        startTime: dayjs(dateArr[0]),
        endTime: endTime,
        cycle: item.cycle,
        remark: item.remark,
      },
    });
  };
  // 查看
  const KykHandle = async (item) => {
    openFullLoading();
    closeFullLoading();
    const dateArr = item.planPeriod.split('~');
    const diff = dayjs(dateArr[1]).diff(dateArr[0], 'day') + 1;
    const endTime = dayjs(dateArr[0]).add(diff / item.cycle - 1, 'day');

    openEditModal(true, {
      isEdit: false,
      isKyk: true,
      endTime: dateArr[1],
      baseForm: {
        factoryId: item.factoryId,
        id: item.id,
        name: item.name,
        type: item.type,
        startTime: dayjs(dateArr[0]),
        endTime: endTime,
        cycle: item.cycle,
        remark: item.remark,
      },
    });
  };
  const filterPlanType = (type) => {
    return planTypeOptions.find((item) => item.value === type)?.label;
  };

  const dataList: any = ref([
    {
      id: null,
      type: 1,
      name: '值班',
      planPeriod: '',
      remark: '',
      lastAlreadyCycleDay: '',
      cycle: null,
    },
    {
      id: null,
      type: 2,
      name: '化验',
      planPeriod: '',
      remark: '',
      lastAlreadyCycleDay: '',
      cycle: null,
    },
  ]);

  const getData = async () => {
    const params = {
      pageIndex: 1,
      pageSize: 10,
    };
    const data = await getPlanList(params);
    dataList.value = dataList.value.map((item) => {
      data.records.map((i) => {
        if (i.type === item.type) {
          item = {
            ...i,
            startDate: `${dayjs(item.createTime).format('YYYY-MM-DD')}`,
          };
        }
      });
      return item;
    });
  };

  getData();
</script>

<style scoped lang="less">
  .page-container {
    padding: 0px 16px 16px;
    height: 100%;
    // background: #ffffff;
    background-size: 100%;

    .search-form {
      padding: 0 16px;
      height: 64px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      background-color: #fcfcfc;

      :deep(.ant-form) {
        width: 100%;

        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }

    .content-container {
      padding: 24px;
      // display: flex;
      // padding: 16px;
      // & > div {
      //   width: 404px;
      //   margin: 16px 16px 0 0;
      //   border-radius: 8px;
      // }
      border-radius: 4px;
      height: 100%;
      overflow: auto;
      background: #ffffff;
      // background: url('../assets/images/pbjh_bg.svg') center center no-repeat,
      //   linear-gradient(321deg, #ffffff 0%, #ffffff 100%);
      background-size: 100%;

      .add-button {
        color: var(--theme-color);
        text-align: center;
        border: dashed 1px var(--theme-color);
        position: relative;
        cursor: pointer;
        user-select: none;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        line-height: 1;
        gap: 12px 0;
        min-height: 250px;
        height: 100%;
        border-radius: 8px;

        .icon {
          position: relative;
          width: 32px;
          height: 32px;

          &::before {
            content: '';
            display: inline-block;
            width: 32px;
            height: 2px;
            background-color: var(--theme-color);
            position: absolute;
            left: calc(50% - 16px);
            top: calc(50% - 1px);
          }

          &::after {
            content: '';
            display: inline-block;
            width: 2px;
            height: 32px;
            background-color: var(--theme-color);
            position: absolute;
            left: calc(50% - 1px);
            top: calc(50% - 16px);
          }
        }
      }

      .item-conatiner {
        height: 360px;
        width: 520px;
        border: 1px solid #d9d9d9;
        display: flex;
        flex-direction: column;
        background: url('../assets/images/bj_default.svg') center center;
        background-size: cover;
        // padding: 24px;
        border-radius: 4px;

        &:hover {
          background: url('../assets/images/bj_default.svg'),
            linear-gradient(135deg, #ffffff 0%, var(--theme-color-4p) 100%);
          border: 1px solid var(--theme-color-32p);
        }

        .header-box {
          background: unset;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 64px;
          border-bottom: 1px solid #d9d9d9;
          padding: 20px 16px;

          .title-box {
            display: flex;
            align-items: center;

            & > div {
              font-size: 18px;
              color: #333333;
              font-weight: 600;
              margin-left: 8px;
            }
          }

          .oper-wrapper {
            display: flex;
            align-items: center;
            gap: 0 12px;

            .oper-box {
              display: flex;
              align-items: center;
              justify-content: center;
              color: @theme-color;
              cursor: pointer;
              font-size: 14px;
              // padding: 8px 12px;
              width: 72px;
              height: 32px;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid @theme-color;
              background: #ffffff;

              .open_svg {
                width: 16px;
                height: 16px;
                margin-right: 4px;
                color: @theme-color;
              }

              &:hover {
                color: #ffffff;
                background: @theme-color;

                .open_svg {
                  color: #fff;
                }
              }

              span {
                margin-right: 4px;
              }
            }
          }
        }

        .detail-box {
          display: flex;
          flex-direction: column;
          padding: 24px 20px;
          font-size: 16px;
          flex: 1;
          // margin-top: 16px;
          // background: rgba(43, 99, 161, 0.03);
          border-radius: 2px;

          & > div {
            height: 14px;
            line-height: 14px;
            margin-bottom: 24px;

            &:last-child {
              margin-bottom: 0;
            }

            & > span:nth-child(2) {
              color: #333;
              font-size: 16px;
            }
          }

          .label {
            color: #999999;
          }

          .remark,
          .title {
            display: flex;

            .value {
              flex: 1;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
</style>
