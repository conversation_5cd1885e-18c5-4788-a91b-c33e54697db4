<template>
  <span
    :class="`${prefixCls}__extra-favorites`"
    :style="{ color: findFavoritesPage() ? '#FC7C22' : '', display: 'none' }"
    @click="addOrDeleteFavorites"
  >
    <IconButton tooltip="收藏" :icon="getIcon" :size="16" hoverSize="100%" hoverColor="none" />
  </span>
</template>

<script lang="ts" setup>
  import { computed, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { useZHCZMenuStore } from '/@zhcz/store/modules/menu';
  import { useUserStore } from '/@/store/modules/user';
  import { createLocalStorage } from '/@/utils/cache';
  import { FACTORY_KEY } from '/@/enums/cacheEnum';
  import { IconButton } from '/@/components/Button';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { getFavoritesList, addFavorites, deleteFavorites } from '/@zhcz/api/admin/menu';
  import type { QueryAddFavoritesListParams } from '/@zhcz/api/admin/model/menu';
  import { getCurrentUserFactoryList } from '/@zhcz/api/patrol';

  const { prefixCls } = useDesign('multiple-tabs-content');

  const router = useRouter();
  const { currentRoute } = router;

  const { createMessage } = useMessage();

  const zhczMenuStore = useZHCZMenuStore();

  const favoritesMenus = computed(() => zhczMenuStore.getFavoritesMenus);

  function findFavoritesPage() {
    return favoritesMenus.value.find((item) => item.path === currentRoute.value.path);
  }

  const getIcon = computed(() => {
    return findFavoritesPage() ? 'icon-park-solid:star' : 'icon-park-outline:star';
  });

  // 收藏或取消收藏
  async function addOrDeleteFavorites() {
    const page = findFavoritesPage();
    if (page) {
      await deleteFavorites(page.id);
      createMessage.success('取消收藏成功');
    } else {
      // 收藏
      const params: QueryAddFavoritesListParams = {
        name: currentRoute.value.meta.title,
        path: currentRoute.value.path,
      };
      await addFavorites(params);
      createMessage.success('收藏成功');
    }
    const favoritesList = await getFavoritesList();
    zhczMenuStore.setFavoritesMenus(favoritesList);
  }

  const userStore = useUserStore();
  const ls = createLocalStorage();
  const factoryId = computed(() => userStore.getCurrentFactoryId);

  const getFactoryList = async () => {
    const res = await getCurrentUserFactoryList();
    const currentFactoryId = ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId;
    if (res?.bindSourceUniqueId) {
      // 匹配是否启用
      let setId = '-1';
      const findIndex = res.factoryInfoList.findIndex(
        (item) => item.factoryId === currentFactoryId,
      );
      if (findIndex < 0) {
        setId = res.bindSourceUniqueId;
      } else {
        setId = factoryId.value;
      }
      userStore.setFactoryId(setId);
      userStore.setUserInfo({
        factoryInfoList: res.factoryInfoList,
        bindSourceUniqueId: setId,
        ...userStore.getUserInfo,
      });
    } else {
      userStore.setFactoryId('-1');
      userStore.setGLobalSource({ factoryId: '-1' });
    }
  };

  watch(
    () => router,
    async () => {
      await getFactoryList();
    },
    {
      deep: true,
      immediate: true,
    },
  );

  // 收集厂站id做为业务字段，提供给平台传递参数
  watch(
    () => factoryId.value,
    async (val) => {
      val && userStore.setGLobalSource({ factory_id: val, factoryId: val });
    },
    {
      immediate: true,
    },
  );
</script>

<style lang="less" scoped></style>
