<template>
  <div class="main-wrapper">
    <div class="process-draw-wrapper">
      <IndicatorList />
      <div class="process-draw-inner-wrapper">
        <ProcessDiagram :flowId="flowId" v-if="flowId" :translate="{ x: 30, y: 0 }" />
      </div>
    </div>
    <div class="card-wrapper">
      <CardLeft title="原水总进水量" style="flex: 1" />
      <CardCenter title="清水池液位" style="flex: 1" />
      <CardRight title="报警信息" style="flex: 1" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useRoute } from 'vue-router';
  import IndicatorList from './IndicatorList.vue';
  import CardLeft from './CardLeft.vue';
  import CardCenter from './CardCenter.vue';
  import CardRight from './CardRight.vue';

  import ProcessDiagram from '/@water-balance/components/BasicProcessDiagram/index.vue';

  const route = useRoute();

  const flowId = computed(() => {
    return route.meta.flowId ?? '';
  });
</script>

<style lang="less" scoped>
  .main-wrapper {
    width: 100%;
    height: 100%;
    padding: 0 16px 16px 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow: hidden;

    .process-draw-wrapper {
      padding: 16px;
      display: flex;
      flex-direction: column;
      flex: 1 0;
      // min-height: 700px;
      background: linear-gradient(180deg, #e8f2f9 0%, #f5f8fd 100%);
      border-radius: 8px 8px 8px 8px;
      overflow: hidden;

      .process-draw-inner-wrapper {
        flex: 1;
        overflow: hidden;
      }
    }

    .card-wrapper {
      height: 30%;
      display: flex;
      gap: 12px;
      overflow: hidden;
    }
  }
</style>
