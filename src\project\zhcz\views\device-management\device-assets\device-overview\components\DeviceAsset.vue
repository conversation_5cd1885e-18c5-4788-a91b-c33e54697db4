<template>
  <Card :bordered="false" dis-hover title="设备资产">
    <template #extra>
      <div class="extra-wrap">
        <Select
          placeholder="请选择"
          @change="handleTypeChange"
          v-model:value="formData.type"
          :dropdownMatchSelectWidth="false"
          class=""
        >
          <SelectOption v-for="item in options" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
      </div>
    </template>
    <div
      class="h-full grid grid-cols-4 grid-rows-[56px_1fr] gap-x-17px gap-y-12px"
      v-loading="loading"
      v-show="!isEmpty"
    >
      <div class="item">
        <span class="icon bg-[#0B62CBFF]"></span><span>设备总数</span
        ><span class="value">{{ data.totalNum }}</span>
      </div>
      <div class="item">
        <span class="icon bg-[#22CD80FF]"></span><span>启用</span
        ><span class="value">{{ data.runningNum }}</span>
      </div>
      <div class="item">
        <span class="icon bg-[#FF2C2CFF]"></span><span>停用</span
        ><span class="value">{{ data.stoppedNum }}</span>
      </div>
      <div class="item">
        <span class="icon bg-[#FF8C2EFF]"></span><span>维修</span
        ><span class="value">{{ data.maintenanceNum }}</span>
      </div>
      <div class="col-span-4" ref="chartRef" style="height: 100%"></div>
    </div>

    <div v-show="isEmpty" class="empty-box">
      <HEmpty />
    </div>
  </Card>
</template>

<script setup>
  import { Card, Select, SelectOption } from 'ant-design-vue';
  import { ref, reactive, onMounted } from 'vue';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import { searchEqpAssetsApi } from '/@zhcz/api/device-management';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { roundAndConvert } from '/@zhcz/utils/number';

  let options = [
    { value: '0', label: '工艺段' },
    { value: '1', label: '类别' },
  ];
  let formData = reactive({
    type: options[0].value,
  });

  const chartRef = ref(null);

  defineProps({
    sourceData: { type: Array, default: () => [] },
  });

  let getParams = () => {
    let params = {
      type: formData.type,
    };
    return params;
  };
  let data = reactive({
    totalNum: 0,
    runningNum: 0,
    stoppedNum: 0,
    maintenanceNum: 0,
  });
  let loading = ref(true);
  async function getData() {
    let params = getParams();
    loading.value = true;
    let res;
    try {
      res = (await searchEqpAssetsApi(params)) || {};
    } finally {
      loading.value = false;
      data.totalNum = res.totalNum || 0;
      data.runningNum = res.runningNum || 0;
      data.stoppedNum = res.stoppedNum || 0;
      data.maintenanceNum = res.maintenanceNum || 0;

      renderChart(res);
    }
  }

  let isEmpty = ref(false);
  function renderChart(data) {
    let { lableList, recordlist } = data;
    if (!lableList?.length) {
      isEmpty.value = true;
      return;
    } else {
      isEmpty.value = false;
    }

    // 顺序展示
    lableList && lableList.reverse();
    recordlist.forEach(({ value }) => value && value.reverse());
    // 确定颜色
    let legend = ['启用', '维修', '停用'];
    recordlist = legend.map((name) => recordlist.find((i) => i.name === name));
    // 去除0数据
    let valueArr = recordlist.map(({ value }) => value);
    for (let i = valueArr[0].length; i >= 0; i--) {
      let total = valueArr.reduce((total, currentValue) => {
        return total + currentValue[i];
      }, 0);
      if (total === 0) {
        lableList.splice(i, 1);
        recordlist.forEach(({ value }) => value.splice(i, 1));
      }
    }

    let color = ['#22CD80CC', '#FF8C2ECC', '#FF2C2CCC'];

    const option = {
      animation: false,
      color,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(255, 255, 255, 1)',
        textStyle: {
          color: '#999',
          fontSize: 14,
          lineHeight: 28,
          height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
        formatter: (params) => {
          let { name } = params[0] || {};
          let str = `<div style='display: grid; grid-template-columns: 15px 1fr auto;'>`;
          params.forEach((item) => {
            str += `
                        <span>${item.marker}</span>
                        <span style='color: #333;'>${item.seriesName}</span>
                        <div style='color: #333; margin-left: 15px; font-weight: 600; justify-self: end;'>
                           <span>${item.value ?? '-'}</span><span>${'个'}</span>
                        </div>`;
          });

          let nameStr = name ? `<div style="color: #333">${name}</div>` : '';
          str = `${nameStr}${str}</div>`;
          return str;
        },
      },
      legend: {
        show: false,
        icon: 'circle',
        itemWidth: 8,
        itemHeight: 8,
        itemGap: 24,
        textStyle: {
          fontSize: 14,
          color: '#333333',
        },
      },
      grid: {
        top: '0',
        left: 0,
        right: 10,
        bottom: 0,
        containLabel: true,
      },
      xAxis: [
        {
          type: 'value',
          boundaryGap: true,
          axisLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#666',
              fontSize: 14,
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisPointer: {
            type: 'shadow',
          },
        },
      ],
      // dataZoom: [
      // {
      //   type: 'inside',
      //   orient: 'vertical',
      //   start: 0,
      //   end: 40,
      // },
      // {
      //   start: 0,
      //   end: 40,
      // },
      // ],
      yAxis: [
        {
          type: 'category',
          data: lableList,
          // name: `单位（%）`,
          name: null,
          nameTextStyle: {
            color: '#666',
            fontSize: 14,
            align: 'left',
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            align: 'right',
            interval: 0,
            textStyle: {
              color: '#666',
              fontSize: 14,
            },
          },
        },
      ],
      series: recordlist.map((i, index) => {
        return {
          name: i.name,
          type: 'bar',
          barMaxWidth: 16,
          stack: 'total',
          color: color[index % 3],
          data: i.value.map((value) => (value == null ? null : roundAndConvert(Number(value), 0))),
        };
      }),
    };
    let instance = getInstance();
    instance?.clear();
    setOptions(option);
  }

  function handleTypeChange() {
    getData();
  }

  let setOptions;
  let getInstance;
  // let echarts;
  onMounted(() => {
    getData();
    ({ setOptions, getInstance } = useECharts(chartRef));
  });
</script>

<style lang="less" scoped>
  .item {
    display: grid;
    grid-template-columns: 14px 1fr 60px;
    align-items: center;
    border-radius: 4px 4px 4px 4px;
    padding: 0 16px;
    background-color: #0b62cb14;

    .icon {
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
    }

    .value {
      font-size: 16px;
      font-weight: 600;
      justify-self: end;
    }
  }
</style>
