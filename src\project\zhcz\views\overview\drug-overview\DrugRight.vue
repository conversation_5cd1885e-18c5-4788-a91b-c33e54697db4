<template>
  <div class="drug-right">
    <BigBoxContainer>
      <BoxContainer
        style="height: calc(48% - 4px)"
        :data-resource-code="indexList?.index_RT[0]?.groupCode"
      >
        <template #header>
          <BoxHeader :title="title1">
            <template #right>
              <!-- 日药耗比例，变更为药耗比例，增加原料矾、氢氧化钠、活性炭的比例，同时支持日、月、年过滤 -->
              <!-- <DatePicker
                v-model:value="drugProportionDate"
                class="date-picker big-screen-date-picker"
                popupClassName="big-screen-date-picker-dropdown"
                style="width: 120px !important"
                placeholder="选择日期"
                :allowClear="false"
                :showToday="false"
                :disabledDate="disabledDate"
                @change="getDrugProportionData"
              /> -->
              <div class="select-box">
                <Select
                  v-model:value="sludgeTime2"
                  style="width: 5rem"
                  @change="handleSludgeChange2"
                  class="big-screen-select"
                  popupClassName="big-screen-select-dropdown"
                >
                  <SelectOption v-for="item in dataList2" :value="item.value" :key="item.value">
                    {{ item.label }}
                  </SelectOption>
                </Select>
                <DatePicker
                  v-model:value="drugProportionDate"
                  :picker="getPicker2"
                  class="date-picker big-screen-date-picker"
                  popupClassName="big-screen-date-picker-dropdown"
                  style="width: 126px !important"
                  placeholder="选择日期"
                  :allowClear="false"
                  :showToday="false"
                  :disabledDate="disabledDate"
                  @change="getDrugProportionData"
                />
              </div>
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div class="content-pie">
            <!-- <template v-if="drugPropoLoad">
              <div style="background: unset"></div>
            </template> -->
            <template v-if="!drugPropoLoad">
              <DrugProportion :dataList="drugProportionData" />
            </template>
            <template v-else>
              <DataEmpty />
            </template>
          </div>
        </template>
      </BoxContainer>
      <BoxContainer
        style="height: calc(52% - 4px)"
        :data-resource-code="indexList?.index_RB[0]?.groupCode"
      >
        <template #header>
          <!-- 用药统计（近半年）取消掉，位置用来摆放药耗曲线图，药耗曲线图腾出来的位置，用于上下单元增加的药种类 -->
          <BoxHeader title="药耗数据曲线">
            <template #right>
              <!-- 能耗数据，变更为能耗趋势数据，近30天下拉框，变更为近一周、近一月、近一年过滤 -->
              <Select
                v-model:value="dateType"
                style="width: 120px"
                @change="handleEnergyDateChange"
                class="big-screen-select"
                popupClassName="big-screen-select-dropdown"
              >
                <SelectOption v-for="item in timeList" :value="item.value" :key="item.value">
                  {{ item.label }}
                </SelectOption>
              </Select>
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div style="height: 100%; width: 100%; padding-top: 16px; position: relative">
            <!-- <template v-if="drugCostDataLoad">
              <div style="background: unset"></div>
            </template> -->
            <template v-if="!drugCostDataLoad">
              <ProduceDataChart :data="drugCostData" :psHeight="100" />
            </template>
            <template v-else>
              <DataEmpty />
            </template>
          </div>
        </template>
      </BoxContainer>
    </BigBoxContainer>
  </div>
</template>

<script lang="ts" setup name="EquipmentLeft">
  import { ref, computed, watch } from 'vue';
  import dayjs from 'dayjs';
  import { useIndexList, useTitleList } from '../hooks';
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import { toDecimalFloor } from '../utils';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { roundAndConvertCheckNullAndUnDef } from '/@zhcz/utils/number';
  import { mockChartData } from './data.js';
  // import { mockDrugCostData } from './data';
  import { flatMap } from 'lodash-es';
  import BigBoxContainer from '../components/box-container/BigBoxContainer.vue';
  import BoxContainer from '../components/box-container/index.vue';
  import BoxHeader from '../components/box-container/BoxHeader.vue';
  import ProduceDataChart from '../components/echarts/ProduceDataChart.vue';
  import DrugProportion from './components/echarts/DrugProportion.vue';
  import DataEmpty from '../components/data-empty/index.vue';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';

  // import { useEmbeddedParams } from '/@zhcz/hooks/useEmbeddedParams';
  const dateType = ref('7');
  const timeList = [
    {
      value: '7',
      label: '近一周',
    },
    {
      value: '30',
      label: '近一月',
    },
    {
      value: '365',
      label: '近一年',
    },
  ];
  const sludgeTime2 = ref<null | string>(null);
  const dataList2 = ref<{ label: string; value: string }[]>([]);
  async function getTimeList2() {
    const res = await listSenceGroupByParent({
      groupCode: 'DrugConsumptionProportion',
      factoryId: 1,
      platformld: 1,
    });
    if (Object.keys(res).length) {
      sludgeTime2.value = Object.keys(res)[0];
      dataList2.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      const picker =
        pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
      if (picker === 'date') {
        drugProportionDate.value = dayjs().subtract(1, 'day');
      } else if (picker === 'month') {
        drugProportionDate.value = dayjs().subtract(1, picker);
      } else {
        drugProportionDate.value = dayjs().subtract(0, picker);
      }
      getDrugProportionData();
    }
  }
  getTimeList2();
  enum pickerType {
    '日' = 'date',
    '月' = 'month',
    '年' = 'year',
  }
  const getPicker2 = computed(() => {
    if (sludgeTime2.value) {
      return pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    } else {
      return pickerType['日'];
    }
  });
  const handleSludgeChange2 = () => {
    const picker =
      pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    if (picker === 'date') {
      drugProportionDate.value = dayjs().subtract(1, 'day');
    } else if (picker === 'month') {
      drugProportionDate.value = dayjs().subtract(1, picker);
    } else {
      drugProportionDate.value = dayjs();
      // .subtract(1, picker);
    }
    getDrugProportionData();
  };
  const handleEnergyDateChange = () => {
    getDrugCostData();
  };

  // const state = reactive({
  //   sevenDaysData: JSON.parse(JSON.stringify(mockChartData)),
  // });

  // const getSevenDaysData = async () => {};

  const { indexList } = useIndexList();
  const { titleList } = useTitleList();

  function disabledDate(current) {
    // 禁止选择今天以后的日期
    return current && current > dayjs().subtract(0, 'day');
  }
  // const yearPickerOptions = {
  //   disabledDate(date) {
  //     return date && date.getFullYear() > new Date().getFullYear();
  //   },
  // };

  // 用药比例
  const drugProportionDate = ref(dayjs().subtract(1, 'day'));
  const drugProportionData = ref([]);
  const drugPropoLoad = ref<Boolean>(false);
  const drugCostDataLoad = ref<Boolean>(false);
  // 用药成本
  // const drugCostDate = ref(dayjs().format('YYYY'));
  const drugCostData = ref<any>({});

  const title1 = computed(() => {
    return titleList.value.title_RT;
  });
  // const title2 = computed(() => {
  //   return titleList.value.title_RB1;
  // });

  async function getDrugProportionData() {
    // drugPropoLoad.value = true;
    const picker =
      pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    const startDate = dayjs(drugProportionDate.value).startOf(picker).format('YYYY-MM-DD 00:00:00');
    const endDataTime = dayjs(drugProportionDate.value).endOf(picker).format('YYYY-MM-DD 23:59:59');
    const tempParams = { resourceInterfaceId: '1914928351570112514', groupCode: sludgeTime2.value };
    const params = {
      startDateTime: startDate,
      endDateTime: endDataTime,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      ...tempParams,
      jsConvert: true,
      paramsData: JSON.stringify(params),
    };
    const { data } = await callResourceFunction(paramData);
    if (!data || !data.length) {
      drugPropoLoad.value = true;
      drugProportionData.value = [];
      return null;
    } else {
      drugPropoLoad.value = false;
    }

    const sum = data.map((item) => item.value || 0).reduce((prev, next) => prev + next);
    const colorList = [
      'rgba(45, 130, 254, 1)',
      'rgba(31, 195, 164, 1)',
      'rgba(254, 197, 45, 1)',
      'rgba(115, 45, 254, 1)',
      'rgba(254, 45, 226, 1)',
      'rgba(62, 254, 45, 1)',
    ];
    const seriesData = data.map((item, index) => ({
      rawValue: roundAndConvertCheckNullAndUnDef(item.value, 0),
      value: Number(toDecimalFloor((item.value || 0) / sum)) * 100,
      unitName: item.unitName,
      indexName: item.indexName,
      name: item.indexName,
      incr: item.incr ? item.incr + '%' : '',
      itemStyle: {
        color: colorList[index % 6],
      },
    }));
    drugProportionData.value = seriesData;
  }

  async function getDrugCostData() {
    // drugCostDataLoad.value = true;
    const tempParams = indexList.value.index_LT2[0];
    const collDate = Number(dateType.value) - 1;
    const endDataTime = dayjs().subtract(1, 'd').format('YYYY-MM-DD 23:59:59');
    const params = {
      startDateTime: dayjs(endDataTime).subtract(collDate, 'd').format('YYYY-MM-DD 00:00:00'),
      endDateTime: endDataTime,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
      type: 3,
    };
    const paramData = {
      ...tempParams,
      paramsData: JSON.stringify(params),
    };
    const { data } = await callResourceFunction(paramData);
    if (data && data.length) {
      const newData = JSON.parse(JSON.stringify(mockChartData));
      newData.title = titleList.value.title_LT2;
      if (data[0].data.length) {
        newData.chartOptions.xAxis.data = data[0].data.map((item) =>
          dayjs(item.collectDateTime).format('MM-DD'),
        );
        drugCostDataLoad.value = false;
      } else {
        drugCostDataLoad.value = true;
      }

      newData.chartOptions.series = data.map((item, index) => {
        const oldItem = mockChartData.chartOptions.series[index];
        let t_data = mockChartData.chartOptions.series[index]?.data;
        t_data = flatMap(item.data, 'value').map((item) =>
          roundAndConvertCheckNullAndUnDef(item, 2),
        );

        return {
          ...oldItem,
          name: item.indexName,
          data: t_data,
          unitName: item.unitName,
        };
      });
      drugCostData.value = newData;
    } else {
      drugCostData.value = mockChartData;
    }
    // const tempParams = indexList.value.index_RB[0];
    // const params = {
    //   startDateTime: dayjs().subtract(6, 'M').format('YYYY-MM'),
    //   endDateTime: dayjs().subtract(1, 'M').format('YYYY-MM'),
    //   indexCodes: '@￥Resource',
    //   tenantId: '@￥TenantId',
    //   type: '4',
    // };
    // const paramData = {
    //   ...tempParams,
    //   paramsData: JSON.stringify(params),
    // };
    // const { data } = await callResourceFunction(paramData);
    // console.log('近半年', data);

    // if (data && data.length) {
    //   const newData = JSON.parse(JSON.stringify(mockDrugCostData));
    //   newData.title = titleList.value.title_RB1;

    //   if (data[0].data.length) {
    //     newData.chartOptions.xAxis.data = data[0].data.map((item) => {
    //       let row;
    //       row = dayjs(item.collectDateTime).format('MM-DD');
    //       return row;
    //     });
    //   }

    //   newData.chartOptions.series = data.map((item, index) => {
    //     const oldItem = mockDrugCostData.chartOptions.series[index] || {};
    //     let t_data = mockDrugCostData.chartOptions.series[index]?.data;
    //     t_data = item.data.map((i) => {
    //       return [
    //         `${dayjs(i.collectDateTime).format('MM-DD')}`,
    //         roundAndConvertCheckNullAndUnDef(i.value, 0),
    //       ];
    //     });
    //     return {
    //       ...oldItem,
    //       name: item.indexName,
    //       data: t_data,
    //       unitName: item.unitName,
    //     };
    //   });
    //   drugCostData.value = newData;
    // } else {
    //   drugCostData.value = mockDrugCostData;
    // }
  }

  watch(
    () => indexList.value,
    () => {
      getDrugCostData();
    },
  );
</script>

<style lang="less" scoped>
  .drug-right {
    width: 100%;
    height: 100%;

    .select-box {
      flex: 1;
      display: flex;
      justify-content: end;
      gap: 0 12px;
    }

    :deep(.ant-select-selection-item) {
      color: #fff;
    }

    .content-pie {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      // padding: 0rem 0.5rem 0.5rem 1rem;
      @media screen and (max-height: 900px) {
        // padding: 0rem 0.5rem 0 1rem;
      }
    }

    :deep(.ant-picker) {
      .ant-picker-input > input {
        color: #fff;
      }
    }
  }
</style>
