<template>
  <Card :bordered="false" dis-hover>
    <template #title>
      <div>节能统计</div>
    </template>
    <div class="container-list" v-loading="loading" v-if="indexList.length">
      <div class="list-item" v-for="(item, index) in indexList" :key="index">
        <div class="item">
          <div class="conten-box">
            <div class="name">
              <div class="text">{{ item.indexName }}</div>
            </div>
            <div class="value value-before">
              <div class="number-value" v-if="item.value === null" style="font-weight: 400">-</div>
              <Tooltip @mouseenter="showTooltip" v-else>
                <template #title>
                  {{ Number(item.value) ? Number(item.value) : '-' }}{{ item.unitName }}
                </template>
                <div class="number-value">
                  {{ Number(item.value) ? Number(item.value) : '-' }}
                </div>
              </Tooltip>
              <span class="unit" v-if="item.value !== null">{{ item.unitName }}</span>
            </div>
          </div>
        </div>
        <div class="item">
          <div class="conten-box">
            <div class="name">
              <!-- <Icon icon="icon-park-outline:lightning" :size="13" color="var(--theme-color)" /> -->
              <div class="text">{{ item.indexPreName }}</div>
            </div>
            <div class="value">
              <div class="number-value" v-if="item.preVal === null" style="font-weight: 400">-</div>
              <Tooltip @mouseenter="showTooltip" v-else>
                <template #title
                  >{{ Number(item.preVal) ? Number(item.preVal) : '-' }}{{ item.unitName }}
                </template>
                <div class="number-value">
                  {{ Number(item.preVal) ? Number(item.preVal) : '-' }}
                </div>
              </Tooltip>
              <span class="unit" v-if="item.preVal !== null">{{ item.unitName }}</span>
            </div>
          </div>
        </div>
        <div class="item">
          <div class="conten-box">
            <div class="name">
              <!-- <Icon icon="icon-park-outline:cycle-one" :size="13" color="var(--theme-color)" /> -->
              <div class="text">{{ item.ratioName }}</div>
            </div>
            <div class="value">
              <div class="number-value" v-if="item.ratioVal === null" style="font-weight: 400"
                >-</div
              >
              <Tooltip @mouseenter="showTooltip" v-else>
                <template #title
                  >{{ Number(item.ratioVal) ? Number(item.ratioVal) : '-' }}%</template
                >
                <div class="number-value">
                  {{ Number(item.ratioVal) ? Number(item.ratioVal) : '-' }}
                </div>
              </Tooltip>
              <span class="unit" v-if="item.ratioVal !== null">%</span>
              <div class="ups-icon" v-if="item.ratioVal">
                <img v-if="item.ratioVal > 0" :src="up" />
                <img v-else-if="item.ratioVal < 0" :src="down" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="empty">
      <HEmpty />
    </div>
  </Card>
</template>
<script setup lang="ts">
  import { ref, onUnmounted, onMounted, onActivated, onDeactivated } from 'vue';
  import { Card, Tooltip } from 'ant-design-vue';
  import { mockEnergyAllStatisticsData } from '../data';
  import { zydtjData } from '/@zhcz/api/cost-management';
  import up from '/@zhcz/views/cost-management/assets/images/up.svg';
  import down from '/@zhcz/views/cost-management/assets/images/down.svg';
  interface IndexListType {
    value: null | number;
    preVal: null | number;
    indexName: string;
    indexPreName: string;
    ratioName: string;
    ratioVal: null | number;
    indexCode: null | number | string;
    unitName: string;
  }
  function showTooltip(e) {
    // console.log(e.target.clientWidth, e.target.scrollWidth);
    if (e.target.clientWidth >= e.target.scrollWidth) {
      e.target.style.pointerEvents = 'none';
    }
  }
  const loading = ref(false);
  const indexList = ref<IndexListType[]>(mockEnergyAllStatisticsData);

  async function getData() {
    try {
      const res = await zydtjData({ factoryId: 1 });
      console.log('res', res);
      indexList.value = res;
    } catch (err) {
      console.log(err);
    }
  }
  getData();

  onMounted(async () => {
    await getData();
  });

  onActivated(async () => {});

  onUnmounted(async () => {});

  onDeactivated(async () => {});
</script>
<style lang="less" scoped>
  .ant-card {
    height: 100%;

    :deep(.ant-card-head) {
      padding: 0 16px;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      min-height: 48px;
      border-bottom: 1px solid #e9e9e9;
    }

    :deep(.ant-card-body) {
      padding: 16px;
      height: calc(100% - 48px);
      overflow: auto;

      .container-list {
        min-width: 360px;
        min-height: 178px;
      }
    }
  }

  .empty {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .container-list {
    display: flex;
    flex-direction: column;
    gap: 16px 0;
    height: 100%;

    .list-item {
      padding: 0 12px 0 0;
      background: rgba(11, 98, 203, 0.08);
      border-radius: 4px 4px 4px 4px;
      // flex: 1;
      height: 25%;
      display: flex;
      align-items: center;
      justify-content: center;

      .item {
        width: 100%;
        overflow: hidden;
        flex: 1;
        display: flex;
        align-items: center;

        &:first-child::after {
          content: '';
          display: inline-block;
          width: 1px;
          height: 34px;
          background-color: #bebebe;
        }

        .conten-box {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: self-start;
          overflow: hidden;
          width: 100%;
          // padding-left: 12px;
        }

        .name {
          width: 100%;
          display: flex;
          padding-left: 24px;
          justify-content: flex-start;
          align-items: center;

          .icon {
            width: 24px;
            height: 24px;

            img {
              width: 100%;
            }
          }

          .text {
            // padding-left: 4px;
            font-size: 14px;
            color: #333;
            line-height: 24px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .value {
          font-size: 1rem;
          font-weight: 600;
          color: #333333;
          display: flex;
          width: 100%;
          justify-content: start;
          padding-left: 24px;
          overflow: hidden;
          align-items: end;

          .number-value {
            font-family: 'D-DIN-PRO, D-DIN-PRO';
            display: inline-block;
            max-width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .unitLevel {
            display: inline-block;
            margin-left: 4px;
            font-size: 14px;
            font-weight: 400;
            color: #666;
            margin-bottom: 3px;
          }

          .unit {
            display: inline-block;
            margin-left: 4px;
            font-size: 14px;
            font-weight: 400;
            color: #666;
            // margin-bottom: 3px;
          }

          .ups-icon {
            display: inline-block;
            margin-left: 6px;
          }
        }

        .value-before {
          margin: 0 0 0 0;
        }

        &:nth-child(1) {
          .conten-box {
            padding-left: 24px;
            padding-right: 24px;
          }

          .name {
            padding-left: 0;
          }

          .value {
            padding-left: 0;
          }
        }

        &:nth-child(2),
        &:nth-child(3) {
          .name {
            .text {
              // padding-left: 4px;
            }
          }

          .value {
            padding-left: 24px;
            // padding-left: 41px;
          }
        }
      }
    }
  }
</style>
