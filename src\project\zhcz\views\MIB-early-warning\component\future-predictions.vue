<!-- 未来预测 -->
<template>
  <div class="future-predictions">
    <HLCard
      className="custom-card"
      :styleData="{ borderRadius: '16px' }"
      themeColor="Light"
      :line="true"
    >
      <template #headerLeftAfter>
        <div class="text__header" @click="openDetail('future')">未来预测</div>
        <img
          class="text__next"
          src="../assets/images/detail-goto.png"
          @click="openDetail('future')"
          alt=""
          srcset=""
        />
      </template>

      <template #defaultBody>
        <div class="future-predictions__list">
          <div class="future-predictions__pre" @click="handlePre">
            <img src="../assets/images/center-img/future-pre.png" alt="" srcset="" />
          </div>
          <div
            v-for="(item, index) in dataList"
            :key="index"
            class="future-predictions__item"
            :class="{ current: originDate === dayjs(item.date).format('MM-DD') }"
            :style="{
              backgroundImage:
                originDate === dayjs(item.date).format('MM-DD')
                  ? `url(${colorShades[item.level] ? colorShades[item.level].img : ''})`
                  : 'none',
            }"
          >
            <div class="future-predictions__date">{{
              originDate === dayjs(item.date).format('MM-DD')
                ? '今日'
                : dayjs(item.date).format('MM-DD') || '--'
            }}</div>
            <div
              class="future-predictions__value"
              :style="{
                color: colorShades[item.level]
                  ? colorShades[item.level].color
                  : colorShades[0].color,
              }"
              >{{ item.value || '--' }}</div
            >
            <div v-if="index === 0" class="current-indicator">
              <div class="indicator-bg"></div>
            </div>
          </div>
          <div class="future-predictions__next" @click="handleNext">
            <img src="../assets/images/center-img/future-next.png" alt="" srcset="" />
          </div>
        </div>
      </template>
    </HLCard>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch, onMounted } from 'vue';
  import { brominePredictionsList } from '/@zhcz/api/mib-early-warning';
  import dayjs from 'dayjs';
  import { HLCard } from '/@/project/zhcz/components/HLCard';
  import futureGreen from '../assets/images/center-img/future-green.png';
  import futureYellow from '../assets/images/center-img/future-yellow.png';
  import futureRed from '../assets/images/center-img/future-red.png';
  import futureOrange from '../assets/images/center-img/future-orange.png';
  const colorShades = [
    { color: '#0CAA46', img: futureGreen },
    { color: '#E41B1B', img: futureRed },
    { color: '#DE7F0B', img: futureOrange },
    { color: '#D9BC02', img: futureYellow },
  ];
  const now = ref(new Date());
  const originDate = ref(dayjs().format('MM-DD'));
  const dataList = ref([
    { date: '08-06', value: '67.20', level: 2 },
    { date: '08-07', value: '28.12', level: 3 },
    { date: '08-08', value: '32.14', level: 2 },
    { date: '08-09', value: '24.45', level: 1 },
    { date: '08-10', value: '56.34', level: 2 },
    { date: '08-11', value: '22.12', level: 0 },
  ]);
  const handleNext = () => {
    now.value = new Date(now.value.setDate(now.value.getDate() + 1));
    getAllData();
  };
  const handlePre = () => {
    now.value = new Date(now.value.setDate(now.value.getDate() - 1));
    getAllData();
  };
  const openDetail = (type: string) => {
    console.log(type);
  };
  const getAllData = async () => {
    console.log(
      dayjs(now.value).format('YYYY-MM-DD'),
      dayjs(now.value).add(5, 'day').format('YYYY-MM-DD'),
      '002',
    );
    const res = await brominePredictionsList({
      startDate: dayjs(now.value).format('YYYY-MM-DD'),
      endDate: dayjs(now.value).add(5, 'day').format('YYYY-MM-DD'),
      deviceId: '002',
    });
    console.log('未来预测数据', res);
    if (res && res.length) {
      dataList.value = res;
      //   messageAlarmData.value = res.map((item: any) => ({
      //     warnTime: dayjs(item.warnTime).format('HH:mm:ss'),
      //     warnDesc: item.warnDesc,
      //     warnType: item.warnType || 0,
      //   }));
    }
  };
  onMounted(() => {
    getAllData();
  });
  const props = defineProps({
    updata: {
      type: Number,
      default: 1,
    },
  });
  watch(
    () => props.updata,
    (newVal, oldVal) => {
      console.log(props.updata, newVal, oldVal);
      if (newVal !== oldVal) {
        getAllData();
      }
    },
  );
</script>
<style lang="less" scoped>
  .future-predictions {
    width: 100%;

    .custom-card {
      border-radius: 16px;
      border: 2px solid #ffffff;
      background: rgba(247, 250, 254, 1);

      :deep(.base-header) {
        padding: 16px 24px;
      }

      :deep(.base-header_line) {
        border-bottom: 1px solid #bcc5ce;
      }

      :deep(.card-body) {
        padding: 20px 24px;
      }

      .text__header {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 24px;
        line-height: 24px;
        color: #000000;
        position: relative;
        cursor: pointer;
      }

      .text__next {
        height: 32px;
        cursor: pointer;
      }
    }

    .future-predictions__list {
      width: 100%;
      display: flex;
      gap: 12px;
      justify-content: space-between;
      overflow-x: auto;
      padding: 20px 0;

      .future-predictions__next {
        width: 44px;
        height: 120px;
        line-height: 120px;
        background: #ffffff;
        border-radius: 0px 8px 8px 0px;
        border: 2px solid rgba(11, 98, 203, 0.4);
        cursor: pointer;
      }

      .future-predictions__pre {
        width: 44px;
        height: 120px;
        line-height: 120px;
        background: #ffffff;
        border-radius: 8px 0px 0px 8px;
        border: 2px solid rgba(11, 98, 203, 0.4);
        cursor: pointer;
      }
    }

    .future-predictions__item {
      flex: 1;
      min-width: 124px;
      height: 120px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      font-family: PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #000000;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;

      &.current {
        box-shadow: 0px 4px 12px 0px #c9dceb;
        background-size: 100% 100%;

        .future-predictions__value {
          color: #000000 !important;
        }
      }

      .future-predictions__date {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #000000;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .future-predictions__value {
        margin-top: 12px;
      }
    }
  }
</style>
