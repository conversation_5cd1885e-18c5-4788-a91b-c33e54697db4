<template>
  <div ref="chartRef" style="width: 100%; height: 100%"></div>
</template>
<script lang="ts" setup>
  import { onMounted, ref, Ref, watch } from 'vue';
  import dayjs from 'dayjs';
  import { useECharts } from '/@/hooks/web/useECharts';

  const props = defineProps({
    data: {
      type: Object as any,
      default: () => {},
    },
  });

  const chartRef = ref<HTMLDivElement | null>(null);
  const echartData = ref<{ collectDateTime: string; value: number }[]>([]);
  const unit = ref('');
  const indexName = ref('');

  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);

  const renderEchart = () => {
    const xAxis = echartData.value.map(
      (item) => `${Number(dayjs(item.collectDateTime).format('MM'))}月`,
    );

    const dataList = echartData.value.map((i) => i.value);
    let maxdata = Math.max(...dataList);
    if (isNaN(maxdata)) maxdata = 0;
    const max = Math.ceil(maxdata / 8) * 8;
    const option = {
      legend: {
        show: false,
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          return `${params[0].name}<br/> ${params[0].marker}${
            params[0].seriesName
          }&nbsp;&nbsp;&nbsp;&nbsp;${
            params[0].value === null || params[0].value === undefined ? '-' : params[0].value
          }${
            params[0].value === null || params[0].value === undefined || !unit.value
              ? ''
              : unit.value
          }`;
        },
      },
      grid: {
        top: 5,
        left: '0',
        right: max ? 0 : 15,
        bottom: '0',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          data: xAxis,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#999999',
              fontSize: 14,
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          // name: unit.value ? `单位（${unit.value}）` : '',
          name: '',
          nameTextStyle: {
            color: '#999',
            fontSize: 14,
            padding: [0, 0, 0, 35],
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          max: max ? max : 8,
          splitNumber: 4,
          interval: max ? max / 4 : 2,
          min: 0,
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#999999',
              fontSize: 14,
            },
          },
        },
      ],
      series: [
        {
          name: indexName.value,
          type: 'bar',
          barWidth: 20,
          color: 'rgba(255, 140, 46, 0.80)',
          label: {
            show: true,
            position: 'top',
            textStyle: {
              fontSize: 12,
              fontWeight: 500,
              color: '#333333',
            },
            formatter: (val) => {
              return val.value ? val.value : '';
            },
          },
          data: dataList,
        },
      ],
    };
    setOptions(option as any);
  };

  onMounted(() => {
    unit.value = props.data?.unit;
    echartData.value = props.data?.itemDataList || [];
    if (echartData.value.length) {
      renderEchart();
    }
  });

  watch(
    () => props.data,
    () => {
      unit.value = props.data?.unit;
      indexName.value = props.data?.indexName;
      echartData.value = props.data?.itemDataList || [];
      renderEchart();
    },
    {
      deep: true,
    },
  );
</script>
