import { defZhczHttp } from '/@/utils/http/axios';

enum Api {
  GetAdapterList = '/event-center/adapter/getList',
  GetWarnEventLevelList = '/event-center/enumType/getWarnEventLevel',
  GetWarnEventTypeList = '/event-center/warnEventType/getWarnEventType',
  GetDefaultRuleActionList = '/event-center/warnEvent/getDefaultRuleActionList',
  GetRuleTypeList = '/event-center/enumType/getRuleType',
  GetEventSourceList = '/event-center/enumType/getEventSource',
  GetEventStatusAllDateStatistics = '/event-center/statistics/getEventStatusAllDateStatistics',
  GetEventStatusStatistics = '/event-center/statistics/getEventStatusStatistics',
  GetWarnEventLevel = '/event-center/statistics/getEventLevelStatistics',
  SearchEvents = '/event-center/warnEventEs/search',

  // 报警记录
  GetListByPage = '/event-center/event/getPage',
  GetAllEventList = '/event-center/event/getAllEventList',
  GetDetail = '/event-center/event/getDetail',
  ConfirmEvent = '/event-center/event/confirmEvent',
  BatchConfirmEvent = '/event-center/event/batchConfirmEvent',

  // 规则配置
  GetWarnEventByPage = '/event-center/warnEvent/getPage',
  ChangeWarnEvent = '/event-center/warnEvent/changeWarnEvent',
  RemoveWarnEvent = '/event-center/warnEvent/removeWarnEvent',
  ChangeRepeatWarnStatus = '/event-center/warnEvent/changeRepeatWarnStatus',
  ChangeAutoConfirmStatus = '/event-center/warnEvent/changeAutoConfirmStatus',

  // 事件类型
  GetWarnEventTypePage = '/event-center/warnEventType/getWarnEventType',
  GetWarnEventTypeDetail = '/event-center/warnEventType/getDetail',
  CreateWarnEventType = '/event-center/warnEventType/changeWarnEventType',
  DeleteWarnEventType = '/event-center/warnEvent/removeWarnEventType',

  GetPageByContentAndType = '/event-center/event/getPageByContentAndType',
  ListByContentAndType = '/event-center/event/listByContentAndType',
  GetEventConfigById = '/event-center/warnEvent/getEventConfigById/',
  GetWarnEventFlow = '/event-center/warnEvent/getWarnEventFlow',
  ListWarnGroup = '/event-center/warnEvent/listWarnGroup/',

  // BI大屏事件中心
  GetBiEventNum = '/event-center/event/getBiEventNum',
  GetBiEvent = '/event-center/event/getBiEvent',
}

// 获取处置流程详情
export const getWarnEventFlowApi = (warnEventId = '') =>
  defZhczHttp.get<any>({
    url: Api.GetWarnEventFlow,
    params: {
      warnEventId,
    },
  });

// 获取事件规则详情
export const getEventConfigByIdApi = (id) =>
  defZhczHttp.get<any>({
    url: Api.GetEventConfigById + id,
  });

// 获取同源事件
export const searchEvents = (params) =>
  defZhczHttp.post<any>({
    url: Api.GetPageByContentAndType,
    params,
  });

// 获取同源事件
export const getListByContentAndType = (params) =>
  defZhczHttp.post<any>({
    url: Api.ListByContentAndType,
    params,
  });

// 获取报警事件等级
export const getWarnEventLevel = (params) =>
  defZhczHttp.post({
    url: Api.GetWarnEventLevel,
    params,
  });

// 报警记录工单状态 全年汇总
export const getEventStatusAllDateStatistics = () =>
  defZhczHttp.get<any>({
    url: Api.GetEventStatusAllDateStatistics,
  });

// 报警记录工单状态 搜索过滤
export const getEventStatusStatistics = (params) =>
  defZhczHttp.post<any>({
    url: Api.GetEventStatusStatistics,
    params,
  });

// 批量确认报警记录
export const batchConfirmEvent = (params) =>
  defZhczHttp.post<any>({
    url: Api.BatchConfirmEvent,
    params,
  });

// 确认报警记录
export const confirmEvent = (params) =>
  defZhczHttp.post<any>({
    url: Api.ConfirmEvent,
    params,
  });

// 获取报警记录详情
export const getDetail = (id: string) =>
  defZhczHttp.get<any>({
    url: `${Api.GetDetail}/${id}`,
  });

// 获取报警记录列表 分页
export const getListByPage = (params, headers = {}) =>
  defZhczHttp.post<any>({
    url: Api.GetListByPage,
    params,
    headers,
  });
// 根据状态类型获取报警记录列表
export const getAllEventList = (status) =>
  defZhczHttp.post<any>({
    url: Api.GetAllEventList + '/' + status,
  });

// 获取适配器列表
export const getAdapterList = () =>
  defZhczHttp.post<any>({
    url: Api.GetAdapterList,
  });

// 获取告警事件级别列表
export const getWarnEventLevelList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetWarnEventLevelList,
    params,
  });

// 获取告警事件类型列表
export const getWarnEventTypeList = () =>
  defZhczHttp.get<any>({
    url: Api.GetWarnEventTypeList,
  });

// 获取默认规则动作列表
export const getDefaultRuleActionList = () =>
  defZhczHttp.get<any>({
    url: Api.GetDefaultRuleActionList,
  });

// 获取规则类型列表
export const getRuleTypeList = () =>
  defZhczHttp.get<any>({
    url: Api.GetRuleTypeList,
  });

// 获取事件来源列表
export const getEventSourceList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetEventSourceList,
    params,
  });

// 获取规则配置列表 分页
export const getWarnEventByPage = (params) =>
  defZhczHttp.post<any>({
    url: Api.GetWarnEventByPage,
    params,
  });

// 新增编辑规则配置
export const changeWarnEvent = (params) =>
  defZhczHttp.post<any>({
    url: Api.ChangeWarnEvent,
    params,
  });

// 删除规则配置
export const removeWarnEvent = (id: string) =>
  defZhczHttp.post<any>({
    url: `${Api.RemoveWarnEvent}?id=${id}`,
  });

// 修改持续警报状态
export const changeRepeatWarnStatus = (id) =>
  defZhczHttp.post<any>({
    url: `${Api.ChangeRepeatWarnStatus}?id=${id}`,
  });

// 修改自动确认状态
export const changeAutoConfirmStatus = (id) =>
  defZhczHttp.post<any>({
    url: `${Api.ChangeAutoConfirmStatus}?id=${id}`,
  });

// 事件类型
export const getWarnEventTypePage = () =>
  defZhczHttp.get<any>({
    url: Api.GetWarnEventTypePage,
  });
export const getWarnEventTypeDetail = (id: string) =>
  defZhczHttp.get<any>({
    url: `${Api.GetWarnEventTypeDetail}/${id}`,
  });

export const createWarnEventType = (data) =>
  defZhczHttp.post<any>({
    url: Api.CreateWarnEventType,
    data,
  });

export const deleteWarnEventType = (id) =>
  defZhczHttp.post<any>({
    url: Api.DeleteWarnEventType,
    data: { id },
  });

export const getListWarnGroupApi = (id, name = '') =>
  defZhczHttp.get<any>({
    url: Api.ListWarnGroup + id,
    params: { name },
  });

// 查询大屏报警事件列表
export const getBiEvent = (params: { factoryId: string; startTime: string; endTime: string }) =>
  defZhczHttp.get<any>({
    url: `${Api.GetBiEvent}?factoryId=${params.factoryId}&&startTime=${params.startTime}&&endTime=${params.endTime}`,
  });
// 查询大屏报警事件统计
export const getBiEventNum = (params: { factoryId: string; startTime: string; endTime: string }) =>
  defZhczHttp.get<any>({
    url: `${Api.GetBiEventNum}?factoryId=${params.factoryId}&&startTime=${params.startTime}&&endTime=${params.endTime}`,
  });
