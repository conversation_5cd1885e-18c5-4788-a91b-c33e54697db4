<template>
  <BasicModal
    width="40%"
    :canFullscreen="false"
    v-bind="$attrs"
    @register="registerModal"
    @cancel="handleCancel"
  >
    <div class="container">
      <BasicForm @register="registerForm">
        <template #e>
          <div class="info-content"></div>
        </template>
      </BasicForm>
    </div>
    <template #footer>
      <a-button class="default-btn" @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="okLoading" @click="handleSubmit">保存</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    createCameraInfoApi,
    updateCameraInfoApi,
  } from '/@zhcz/api/security-management/camera-management';
  // import { GetEgpSupplierListApi } from '/@zhcz/api/equipment-ledger/table';
  // import { getManufacturerList } from '/@zhcz/api/manufacturer';

  import { schemas } from './data';

  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();

  const detailId = ref(null); // 编辑id
  const [registerModal, { closeModal, setModalProps }] = useModalInner(async (data) => {
    if (data.id) {
      setModalProps({ title: '编辑摄像头' });
      detailId.value = data.id;
    } else {
      setModalProps({ title: '新增摄像头' });
      detailId.value = null;
    }

    setFieldsValue(data);
  });
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 116,
    schemas: schemas,
    showActionButtonGroup: false,
  });

  const okLoading = ref(false);
  async function handleSubmit() {
    const formValues = await validate();
    // console.log('formValues', formValues);
    // const options = await GetEgpSupplierListApi();
    // const options = await getManufacturerList({ enterpriseType: 2 });
    // const name = options.find((item) => item.id === formValues.manufacturerId)?.name;
    // if (name) {
    //   formValues.manufacturerName = name;
    // }

    try {
      okLoading.value = true;
      if (detailId.value) {
        //编辑保存
        await updateCameraInfoApi({
          ...formValues,
          id: detailId.value,
        });
        createMessage.success('编辑成功');
      } else {
        //新增保存
        await createCameraInfoApi({ ...formValues });
        createMessage.success('新增成功');
      }
      handleCancel();
      emit('success');
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    resetFields();
    closeModal();
  }
</script>

<style lang="less" scoped>
  .container {
    .info-content {
      width: 320px;
      height: 200px;

      background: #ccc;
    }
  }
</style>
