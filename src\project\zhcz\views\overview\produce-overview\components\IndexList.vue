<template>
  <div class="index-list">
    <div :class="getTitleBindClass">{{ title }}</div>
    <div :class="getContentBindClass">
      <template v-if="!loading">
        <template v-if="data.length">
          <div :class="getItemBindClass" v-for="(item, index) in data" :key="index">
            <div class="item-left" :title="item.indexName">
              <span class="label">{{ item.indexName }}</span>
            </div>
            <div class="item-right">
              <span class="value">{{
                roundAndConvertCheckNullAndUnDef(item.data[0]?.value, 2)
              }}</span>
              <span class="unit">{{ item.unitName }}</span>
            </div>
          </div>
        </template>
        <span v-else>暂无数据</span>
      </template>
      <template v-else>
        <div style="background: unset"></div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { roundAndConvertCheckNullAndUnDef } from '/@zhcz/utils/number';

  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    data: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });

  const getTitleBindClass = computed(() => {
    return {
      title: true,
      'title-raw': props.type === 'blue',
      'title-factory': props.type === 'green',
    };
  });

  const getContentBindClass = computed(() => {
    return {
      content: true,
      content_empty: props.data.length === 0,
      content_loading: props.loading,
      'content-raw': props.type === 'blue',
      'content-factory': props.type === 'green',
    };
  });

  const getItemBindClass = computed(() => {
    return {
      item: true,
      'item-raw': props.type === 'blue',
      'item-factory': props.type === 'green',
    };
  });

  // const getIconColor = computed(() => {
  //   return props.type === 'blue' ? 'rgba(45, 130, 254, .7)' : 'rgba(31, 195, 164, .7)';
  // });
</script>

<style lang="less" scoped>
  .index-list {
    // width: 10.375rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .title {
      width: 100%;
      height: 2rem;
      font-size: 0.875rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      text-align: center;
      line-height: 2rem;

      &-raw {
        background: url('../../assets/images/index-bg-blue.png') 0 0 no-repeat;
        background-size: 100% 100%;
      }

      &-factory {
        background: url('../../assets/images/index-bg-green.png') 0 0 no-repeat;
        background-size: 100% 100%;
      }
    }

    .content {
      position: relative;
      margin-top: 4px;
      width: 100%;
      // height: 11.125rem;
      height: ~'calc(100% - 2rem)';
      border-radius: 2px;
      border: 1px solid;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 0px;
        height: 0px;
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: #808695;
        border-radius: 0.25rem;
      }

      &-raw {
        background: rgba(45, 130, 254, 0.1);
        border-image: linear-gradient(2deg, rgba(45, 130, 254, 0.1), rgba(45, 130, 254, 0)) 1 1;
      }

      &-factory {
        background: rgba(31, 195, 164, 0.1);

        border-image: linear-gradient(0deg, rgba(31, 195, 164, 0.1), rgba(31, 195, 164, 0)) 1 1;
      }

      &_empty {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 0.875rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.6);
      }

      // &_loading {
      //   @keyframes ani-demo-spin {
      //     from {
      //       transform: rotate(0deg);
      //     }

      //     50% {
      //       transform: rotate(180deg);
      //     }

      //     to {
      //       transform: rotate(360deg);
      //     }
      //   }
      //   display: flex;
      //   justify-content: center;
      //   align-items: center;

      //   .demo-spin-icon-load {
      //     animation: ani-demo-spin 1s linear infinite;
      //   }
      // }

      .item {
        margin-top: 0.5rem;
        padding: 0 1.375rem 0 1rem;
        height: 2.25rem;
        display: flex;
        align-items: center;
        cursor: pointer;

        &-raw {
          border-bottom: 1px solid rgba(45, 130, 254, 0.2);

          &:hover {
            background: linear-gradient(
              180deg,
              rgba(45, 130, 254, 0) 0%,
              rgba(45, 130, 254, 0.3) 99%
            );
          }
        }

        &-factory {
          border-bottom: 1px solid rgba(31, 195, 164, 0.2);

          &:hover {
            background: linear-gradient(
              180deg,
              rgba(31, 195, 164, 0) 0%,
              rgba(31, 195, 164, 0.3) 98%
            );
          }
        }

        &:last-child {
          border-bottom: none;
        }

        &-left {
          width: 3rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .label {
            font-size: 0.875rem;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.9);
          }
        }

        &-right {
          display: flex;
          flex: 1;

          .value {
            flex: 1;
            font-size: 0.875rem;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            text-align: right;
            color: rgba(255, 255, 255, 0.9);
          }

          .unit {
            margin-left: 0.5rem;
            width: 2.25rem;
            font-size: 0.875rem;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.65);
          }
        }
      }
    }
  }
</style>
