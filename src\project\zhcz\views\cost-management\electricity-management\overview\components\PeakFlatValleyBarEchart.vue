<template>
  <div style="height: 100%">
    <div ref="chartRef" style="height: 100%"></div>
  </div>
</template>
<script setup lang="ts">
  import { useECharts } from '/@/hooks/web/useECharts';
  import { ref, watch } from 'vue';
  // import { fpgColorList } from '../data';
  import { roundAndConvert } from '/@zhcz/utils/number';

  const props = defineProps({
    data: {
      type: Array as any,
      default: () => [],
    },
    priceList: {
      type: Array as any,
      default: () => [],
    },
  });

  const chartRef = ref<any>(null);
  const { setOptions } = useECharts(chartRef);
  const fpgColorList = [
    'rgba(46, 196, 255, 1)',
    'rgba(67, 92, 255, 0.80)',
    'rgba(34, 205, 128, 0.80)',
    'rgba(255, 140, 46, 0.80)',
    'rgba(237, 210, 38, 0.80)',
    'rgba(255, 82, 43, 0.80)',
  ];
  function handleSetVisitChart() {
    type dataList = {
      name: string;
      type: string;
      label: Object;
      barWidth: number;
      barGap: string;
      itemStyle: Object;
      data: any[];
      yAxisIndex: number;
      step?: string;
      // data.smooth = true;
      symbol?: string;
    };
    const option = {
      color: fpgColorList,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: '#fff',
        textStyle: {
          color: '#333',
          fontSize: 14,
          // lineHeight: 28,
          // height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
        formatter: (params) => {
          const item = params.filter((item) => item.value !== undefined);
          console.log('item', item, props.data);
          if (item.length) {
            let htmlStr = ``;
            item.forEach((val, index) => {
              const unitName =
                props.data.find((i) => val.seriesName === i.indexName)?.unitName || '';
              htmlStr += `<div>${index === 0 ? val.name : ''}</div>
              ${val.marker} <span style="display: inline-block; width: 30px;">${
                val.seriesName
              }</span><span  style="display: inline-block; width: 150px; font-weight: bold; text-align: right;">${
                val.value
              }</span><span  style="padding-left: 4px">${unitName}</span>`;
            });
            return htmlStr;
          }
          return;
        },
        // formatter: (params) => {
        //   const item = params.filter((item) => item.value !== undefined);
        //   if (item.length) {
        //     let htmlStr = ``;
        //     item.forEach((val, index) => {
        //       htmlStr += `<div style="color: #999;">${index === 0 ? val.name : ''}</div>
        //       ${val.marker} <span style="display: inline-block; width: 30px;">${
        //         val.seriesName
        //       }</span><span  style="display: inline-block; width: 150px; font-weight: bold; text-align: right;">${
        //         val.value
        //       }${val.seriesName === '电价' ? '元' : 'kW·h'}</span>`;
        //     });
        //     return htmlStr;
        //   }
        //   return;
        // },
      },
      legend: {
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 24,
        color: fpgColorList,
        data: props.data.map((item) => item.indexName),
        textStyle: {
          fontSize: 14,
          color: '#333',
        },
      },
      grid: {
        top: '25%',
        left: '0',
        right: '0',
        bottom: '1%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          data: props.data[0].data.map((i) => i.collectDateTime),
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#666666',
              fontSize: 14,
            },
          },
          axisPointer: {
            type: 'shadow',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: `电量（${props.data[0]?.unitName}）`,
          nameTextStyle: {
            color: '#333',
            fontSize: 14,
            align: 'left',
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#666666',
              fontSize: 14,
            },
          },
        },
        {
          type: 'value',
          name: `电价(元)`,
          nameTextStyle: {
            color: '#333',
            fontSize: 14,
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#666666',
              fontSize: 14,
            },
          },
        },
      ],

      series: props.data.map((item, index) => {
        const data: dataList = {
          name: item.indexName,
          type: 'bar',
          label: {
            show: false,
          },
          barWidth: 20,
          barGap: '-100%',
          itemStyle: {
            color: fpgColorList[index],
            barBorderRadius: [2, 2, 0, 0],
          },
          data: item.data.map((i) =>
            i.value == null ? null : roundAndConvert(Number(i.value), 2),
          ),
          yAxisIndex: 0,
        };
        if (item.indexName == '电价') {
          data.step = 'start';
          // data.smooth = true;
          data.symbol = 'none';
          data.type = 'line';
          data.yAxisIndex = 1;
          data.barWidth = 0;
          data.barGap = '%';
          // data.areaStyle = {
          //   color: {
          //     type: 'linear',
          //     x: 0,
          //     y: 0,
          //     x2: 0,
          //     y2: 1,
          //     colorStops: [
          //       {
          //         offset: 0,
          //         color: 'rgba(255, 82, 43,0.16)', // 0% 处的颜色
          //       },
          //       {
          //         offset: 1,
          //         color: 'rgba(255, 82, 43,0.04)', // 100% 处的颜色
          //       },
          //     ],
          //     global: false, // 缺省为 false
          //   },
          // };
        }
        return data;
      }),
      animation: false,
    };
    setOptions(option as any);
  }

  watch(
    () => props.data,
    () => {
      handleSetVisitChart();
    },
    { immediate: true },
  );
</script>
<style lang="less" scoped></style>
