<template>
  <div class="drug-proportion">
    <div class="chart-pie" id="pieChart" ref="chartRef"></div>
    <div class="dash">
      <div class="dash-content">
        <div class="dash-box">
          <div class="dash-box-inner"></div>
        </div>
      </div>
    </div>
    <div class="chart-legend">
      <ul>
        <li class="legend-li" v-for="(item, index) in dataList" :key="index">
          <span class="color" :style="{ background: item.itemStyle.color }"></span>
          <Tooltip @mouseenter="showTooltip">
            <template #title>{{ item.name }} {{ item.incr }}</template>
            <span> {{ item.name }} &nbsp; {{ item.incr }} </span>
          </Tooltip>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
  import { defineComponent, ref, watch, onMounted, nextTick } from 'vue';
  import 'echarts-gl';
  import { useECharts } from '/@zhcz/hooks/useECharts';
  import { Tooltip } from 'ant-design-vue';
  export default defineComponent({
    components: { Tooltip },
    props: {
      dataList: {
        type: Array,
        default: () => [
          {
            name: '高锰酸钾',
            indexName: '高锰酸钾',
            unitName: '%',
            value: 20,
            itemStyle: {
              color: 'rgba(45, 130, 254, 1)',
            },
          },
          {
            name: '次氯酸钠',
            indexName: '次氯酸钠',
            unitName: '%',
            value: 20,
            itemStyle: {
              color: 'rgba(31, 195, 164, 1)',
            },
          },
          {
            name: '氢氧化钠',
            indexName: '氢氧化钠',
            unitName: '%',
            value: 20,
            itemStyle: {
              color: 'rgba(254, 197, 45, 1)',
            },
          },
          {
            name: '石灰',
            indexName: '石灰',
            unitName: '%',
            value: 20,
            itemStyle: {
              color: 'rgba(115, 45, 254, 1)',
            },
          },
        ],
      },
    },
    setup(props) {
      // 饼状图
      const chartRef = ref(null);
      const { setOptions } = useECharts(chartRef);

      function getPie3D(pieData) {
        const series = [];
        let sumValue = 0;
        let startValue = 0;
        let endValue = 0;
        const legendData = [];
        const k = 1.5 / 20;

        // 3个饼图参数
        for (let i = 0; i < pieData.length; i++) {
          // console.log('pieData.length', pieData);
          sumValue += pieData[i].value;
          const seriesItem = {
            name: typeof pieData[i].indexName === 'undefined' ? `series${i}` : pieData[i].indexName,
            type: 'surface',
            parametric: true,
            wireframe: {
              show: false,
            },
            label: {
              normal: {
                position: 'inner',
                show: false,
              },
            },
            labelLine: {
              normal: {
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.3)',
                },
                smooth: 0.2,
                length: 50,
                length2: 100,
              },
            },
            pieData: pieData[i],
            pieStatus: {
              selected: false,
              hovered: false,
              k: k,
            },
          };
          if (typeof pieData[i].itemStyle !== 'undefined') {
            const itemStyle = {};
            if (typeof pieData[i].itemStyle.color !== 'undefined') {
              itemStyle.color = pieData[i].itemStyle.color;
            } else {
              itemStyle.color = null;
            }
            if (typeof pieData[i].itemStyle.opacity !== 'undefined') {
              itemStyle.opacity = pieData[i].itemStyle.opacity;
            } else {
              itemStyle.opacity = null;
            }
            seriesItem.itemStyle = itemStyle;
          }
          series.push(seriesItem);
        }

        // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
        // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
        const arr = series.map((i) => i.pieData.value * 1);
        const max = Math.max.apply(null, arr);
        for (let i = 0; i < series.length; i++) {
          console.log(1111, series[i]);
          endValue = startValue + series[i].pieData.value;

          series[i].pieData.startRatio = startValue / sumValue;
          series[i].pieData.endRatio = endValue / sumValue;
          series[i].parametricEquation = getParametricEquation(
            series[i].pieData.startRatio,
            series[i].pieData.endRatio,
            false,
            true,
            k,
            series[i].pieData.value * 1 === max ? 380 : 200,
          );
          startValue = endValue;
          legendData.push(series[i].name);
        }
        // 准备待返回的配置项，把准备好的 legendData、series 传入。
        const option = {
          tooltip: {
            formatter: (params) => {
              if (params.seriesName !== 'mouseoutSeries') {
                return `${params.seriesName} ${option.series[params.seriesIndex].pieData.rawValue}${
                  option.series[params.seriesIndex].pieData.unitName
                }`;
                /** <br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:5px;height:5px;background-color:${
                  params.color
                };"></span>${option.series[params.seriesIndex].pieData.value} */
              }
            },
            backgroundColor: 'rgba(50, 50, 50, 0.7)',
            textStyle: {
              color: '#fff',
              fontSize: 14,
              lineHeight: 28,
              height: 28,
            },
            borderColor: 'transparent',
          },

          // tooltip: {
          //   trigger: 'axis',
          //   backgroundColor: '#132b4e',
          //   borderColor: '#132b4e',
          //   textStyle: {
          //     color: '#fff',
          //   },
          // },
          legend: {
            // icon: 'circle',
            show: false,
            data: legendData,
            itemWidth: 8, // 色标图宽
            itemHeight: 8, // 色标图高
            orient: 'vertical', // 垂直显示
            top: 90,
            right: 100,
            itemGap: 24, // 色标间隔
            textStyle: {
              color: '#fff',
              fontSize: 14,
              padding: 5,
            },
          },
          xAxis3D: {
            min: -1,
            max: 1,
          },
          yAxis3D: {
            min: -1,
            max: 1,
          },
          zAxis3D: {
            min: -1,
            max: 1,
          },
          grid3D: {
            show: false,
            boxHeight: 0.1,
            top: '-30',
            left: '0',
            bottom: '80%',
            environment: 'auto',
            viewControl: {
              distance: 180, // 远近
              alpha: 22, // 饼图X轴旋转
              beta: -60, // 饼图Y轴旋转
              rotateSensitivity: 0, // 设置为0无法旋转
              zoomSensitivity: 0, // 设置为0无法缩放
              panSensitivity: 0, // 设置为0无法平移
            },
          },
          series: series,
        };
        return option;
      }
      function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, height) {
        // 计算
        const midRatio = (startRatio + endRatio) / 2;
        const startRadian = startRatio * Math.PI * 2;
        const endRadian = endRatio * Math.PI * 2;
        const midRadian = midRatio * Math.PI * 2;
        if (startRatio === 0 && endRatio === 1) {
          isSelected = false;
        }
        // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
        k = typeof k !== 'undefined' ? k : 1 / 3;

        // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
        const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
        const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

        // 计算高亮效果的放大比例（未高亮，则比例为 1）
        const hoverRate = isHovered ? 1.2 : 1;

        // 返回曲面参数方程
        return {
          u: {
            min: -Math.PI,
            max: Math.PI * 3,
            step: Math.PI / 32,
          },

          v: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20,
          },

          x: function (u, v) {
            if (u < startRadian) {
              return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            if (u > endRadian) {
              return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
          },

          y: function (u, v) {
            if (u < startRadian) {
              return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            if (u > endRadian) {
              return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
          },

          z: function (u, v) {
            if (u < -Math.PI * 0.5) {
              return Math.sin(u);
            }
            if (u > Math.PI * 2.5) {
              return Math.sin(u);
            }
            return Math.sin(v) > 0 ? 1 * height : -1;
          },
        };
      }

      function handlePieChart() {
        const data = props.dataList.map((i) => ({ ...i, value: i.value }));
        const option = getPie3D(data);
        setOptions(option);
      }
      function showTooltip(e) {
        if (e.target.clientWidth >= e.target.scrollWidth) {
          e.target.style.pointerEvents = 'none';
        }
      }
      onMounted(() => {
        // on(window, 'resize', handleWindowResize)
        nextTick(() => {
          handlePieChart();
        });
      });
      watch(
        () => props.dataList,
        () => {
          handlePieChart();
        },
        {
          immediate: true,
          deep: true,
        },
      );
      return {
        chartRef,
        showTooltip,
      };
    },
  });
</script>

<style lang="less" scoped>
  .drug-proportion {
    width: 100%;
    height: 100%;
    position: relative;
    margin: 0 auto;

    @media screen and (max-width: 1920px) {
      width: 100%;
      height: 100%;

      .chart-pie {
        width: 100%;
        height: 313px;
      }
    }

    @media screen and (max-width: 1600px) {
      // width: 343.4px;
      width: 100%;
      height: 100%;
      scale: 0.8;

      .chart-pie {
        // width: 343.4px;
        // height: 266.05px;
        width: 100%;
        height: 100%;
      }

      .chart-legend {
        bottom: -20px;

        ul {
          padding-left: 1.875rem;
        }
      }

      .dash {
        height: 5.3125rem;
        top: 4.5625rem;

        .dash-content {
          width: 14.875rem;
          height: 6.4813rem;

          .dash-box {
            width: 13.9187rem;
            height: 5.95rem;

            .dash-box-inner {
              width: 13.5469rem;
              height: 5.5781rem;
              border-radius: 50%;
              opacity: 1;
              border-bottom: 2px solid rgba(255, 255, 255, 0.30000001192092896);
              margin: 0 auto;
            }
          }
        }
      }
    }

    @media screen and (max-width: 1440px) {
      // width: 323.2px;
      width: 100%;
      height: 100%;

      .chart-pie {
        width: 100%;
        height: 100%;
        // width: 323.2px;
        // height: 250.4px;
      }

      .chart-legend {
        ul {
          padding-left: 0;
        }
      }

      .dash {
        height: 5rem;
        top: 4.0625rem;

        .dash-content {
          width: 224px;
          height: 97.6px;

          .dash-box {
            width: 209.6px;
            height: 89.6px;

            .dash-box-inner {
              width: 204px;
              height: 84px;
              border-radius: 50%;
              opacity: 1;
              border-bottom: 2px solid rgba(255, 255, 255, 0.30000001192092896);
              margin: 0 auto;
            }
          }
        }
      }
    }

    .chart-pie {
      width: 100%;
      height: 19.5625rem;
      position: relative;
      top: -1.25rem;
      display: flex;
      justify-content: center;
      z-index: 1;
    }

    .chart-legend {
      width: 100%;
      position: absolute;
      bottom: 0;
      z-index: 100;
      overflow: hidden;

      ul {
        width: 100%;
        overflow: hidden;
        display: grid;
        // flex-wrap: wrap;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
        justify-content: space-between;
        // padding-left: 1.875rem;

        .legend-li {
          // width: 32%;
          display: flex;
          align-items: center;
          font-size: 0.875rem;
          font-weight: 400;
          line-height: 1.375rem;
          color: #ffffff;
          margin-top: 1.25rem;
          font-family: PingFang SC-Regular, PingFang SC;

          & > span:last-child {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .color {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 4px;
            margin-right: 0.5rem;
            display: inline-block;
          }
        }
      }
    }

    .dash {
      width: 100%;
      height: 100px;
      position: absolute;
      left: 0;
      top: 92px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 0;

      .dash-content {
        width: 280px;
        height: 122px;
        background: linear-gradient(180deg, rgba(45, 130, 254, 0) 0%, rgba(45, 130, 254, 0.2) 100%);
        opacity: 1;
        border-radius: 50%;

        .dash-box {
          width: 262px;
          height: 112px;
          background: linear-gradient(
            179deg,
            rgba(45, 130, 254, 0) 0%,
            rgba(45, 130, 254, 0.3) 98%
          );
          box-shadow: inset 0px -4px 10px 0px rgba(45, 130, 254, 0.2);
          opacity: 1;
          border-radius: 50%;
          margin: 0 auto;

          .dash-box-inner {
            width: 255px;
            height: 105px;
            border-radius: 50%;
            opacity: 1;
            border-bottom: 2px solid rgba(255, 255, 255, 0.30000001192092896);
            margin: 0 auto;
          }
        }
      }
    }
  }
</style>
