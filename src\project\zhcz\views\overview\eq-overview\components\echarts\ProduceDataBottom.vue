<template>
  <div class="chart-container">
    <div ref="chartRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
  import { defineComponent, ref, onMounted, watch } from 'vue';
  // import CardIcon from '@/components/card-icon/index.vue';
  import { useECharts } from '/@zhcz/hooks/useECharts';
  import { getEchartFontSize } from '../../../utils';

  export default defineComponent({
    // components: {
    //   CardIcon,
    // },
    props: {
      data: {
        type: Object,
        default: () => ({
          chartOptions: {
            xAxis: {
              data: ['2023-01', '2023-02'],
            },
            series: [
              {
                name: '数量',
                color: ['#2DC4FE', '#2D82FE'],
                data: [100, 120],
              },
            ],
          },
        }),
      },
    },
    setup(props) {
      const chartRef = ref(null);
      const { setOptions } = useECharts(chartRef);
      function renderChart() {
        const { chartOptions } = props.data;
        if (!chartOptions) {
          return;
        }
        const showZoom = chartOptions.xAxis.data.length > 7;
        const option = {
          grid: {
            left: 0,
            right: 5,
            top: 40,
            bottom: showZoom ? 30 : 0,
            containLabel: true,
          },
          // tooltip: {
          // trigger: 'axis',
          // axisPointer: {
          //   type: 'cross',
          //   label: {
          //     backgroundColor: '#6a7985'
          //   }
          // }
          // },
          tooltip: {
            trigger: 'axis',
            backgroundColor: '#132b4e',
            borderColor: '#132b4e',
            textStyle: {
              color: '#fff',
            },
            formatter: (params) => {
              const item = params.filter((item) => item.value !== undefined);
              // console.log('item', item);
              if (item.length) {
                let htmlStr = ``;
                item.forEach((val, index) => {
                  const unitName =
                    chartOptions.series.find((i) => val.seriesName === i.name)?.unitName || '';
                  htmlStr += `<div>${index === 0 ? val.name : ''}</div>
                  ${val.marker} <span style="display: inline-block; width: 30px;">${
                    val.seriesName
                  }</span><span  style="display: inline-block; width: 60px; font-weight: bold; text-align: right;">${
                    val.value
                  }</span><span  style="padding-left: 4px">${unitName}</span>`;
                });
                return htmlStr;
              }
              return;
            },
          },
          xAxis: {
            data: chartOptions.xAxis.data,
            triggerEvent: true,
            axisTick: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.5)',
                type: 'solid',
              },
            },
            axisLabel: {
              show: true,
              rotate: 0,
              interval: 0,
              textStyle: {
                fontSize: getEchartFontSize(14),
                color: 'rgba(255,255,255,1)',
              },
            },
          },
          legend: {
            show: true,
            icon: 'circle',
            textStyle: {
              color: '#fff',
            },
            right: 0,
          },
          yAxis: {
            // name: `${chartOptions.series[0].unitName ? chartOptions.series[0].unitName : ''}`,
            min: 0,
            // max: 100,
            nameTextStyle: {
              color: '#fff',
              align: 'left',
              fontSize: 14,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255,255,255,0.2)',
                type: 'dashed',
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#fff',
                fontSize: getEchartFontSize(14),
              },
            },
          },
          dataZoom: showZoom
            ? [
                {
                  type: 'slider',
                  height: 15,
                  bottom: 10,
                  start: 0,
                  end: chartOptions.xAxis.data.length === 12 ? 50 : 20,
                  maxSpan: chartOptions.xAxis.data.length === 12 ? 100 : 100,
                },
                // {
                //   start: 0,
                //   end: 20,
                // },
              ]
            : [],
          series: chartOptions.series.map((item) => {
            return {
              name: item.name,
              // type: 'pictorialBar',
              // type: 'bar',
              barMaxWidth: 30,
              color: item.color,
              type: 'bar',
              symbol: 'none',
              // barCategoryGap: '40%',
              // barGap: index > 0 ? '20%' : null,
              // symbol: 'path://M0,10 L10,10 C8.5,10 5.5,5 5,0 C4.5,5 1.5,10 0,10 z',
              itemStyle: {
                // // 渐变色
                // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //   {
                //     offset: 0,
                //     color: item.color[0],
                //   },
                //   {
                //     offset: 1,
                //     color: item.color[1],
                //   },
                // ]),
              },
              data: item.data,
              z: 10,
            };
          }),
        };
        setOptions(option);
      }

      onMounted(() => {
        renderChart();
      });
      watch(
        () => props.data,
        () => {
          renderChart();
        },
        { deep: true },
      );
      return {
        chartRef,
      };
    },
  });
</script>

<style lang="less" scoped>
  .chart-container {
    flex: 1;
    height: 100%;
  }
</style>
