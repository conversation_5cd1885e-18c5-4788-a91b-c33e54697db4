import { FormSchema } from '/@/components/Table';
import { getDictTypeListApi } from '/@/api/admin/dict';

export const editModalSchemas: FormSchema[] = [
  {
    field: 'equipmentTypeName',
    component: 'Input',
    label: '类型名称',
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    field: 'equipmentTypeCode',
    component: 'Input',
    label: '编码',
    componentProps: {
      disabled: true,
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'equipmentTypeStatusKey',
    label: '类型状态',
    required: true,
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: 'equipment_type_status' },
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'equipmentTypeSort',
    component: 'InputNumber',
    label: '排序',
    required: true,
    componentProps: {
      precision: 0,
      min: 0,
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: `备注`,
    colProps: {
      span: 24,
    },
  },
  {
    field: 'equipmentModelList',
    label: '设备型号',
    component: 'Input',
    slot: 'equipmentModelList',
    colProps: {
      span: 24,
    },
    rules: [
      {
        trigger: ['change', 'blur'],
        validator: async (_rule, arr) => {
          const isFilled = arr?.every?.((item) => Object.values(item)?.every((v) => v || v === 0));
          if (!isFilled && arr?.length) {
            return Promise.reject('请输入设备型号');
          }
          return Promise.resolve();
        },
      },
    ],
  },
  // {
  //   component: 'SubTable',
  //   componentProps: {
  //     config: {
  //       modelId: '604',
  //       operationType: 'batch',
  //       tableColumn: [
  //         {
  //           label: 'test',
  //           field: 'test',
  //           ifShow: 1,
  //           width: '200px',
  //           component: 'Input',
  //         },
  //       ],
  //       tableBtn: [
  //         {
  //           label: '新增',
  //           eventType: 'add',
  //           IconPicker: '',
  //           type: 'primary',
  //           location: 'item',
  //           permission: '1',
  //           isDanger: false,
  //           icon: 'icon-park-outline:plus',
  //         },
  //         {
  //           label: '删除',
  //           eventType: 'del',
  //           IconPicker: '',
  //           type: 'default',
  //           permission: '1',
  //           location: 'action',
  //           icon: 'icon-park-outline:notes',
  //         },
  //       ],
  //       column: [
  //         {
  //           label: 'test',
  //           field: 'test',
  //           ifShow: 1,
  //           width: '200px',
  //           component: 'Input',
  //         },
  //       ],
  //     },
  //   },
  //   label: '设备型号',
  //   field: 'model',
  // },
];
