// === 服务启动动态替换禁止删除注释 start===
const projectPages: Indexable[] = [
  { module: 'zhcz', pages: import.meta.glob('../../../../project/zhcz/views/**/*.{vue,tsx}') },
  {
    module: 'zoology-wzb',
    pages: import.meta.glob('../../../../project/zoology-wzb/views/**/*.{vue,tsx}'),
  },
];
// === 服务启动动态替换禁止删除注释 end ===

export function getModules(VITE_GLOB_PROJECT: string[] = []) {
  const mainPage = import.meta.glob('../../../../views/**/*.{vue,tsx}');

  let subPagesMap = {};
  if (VITE_GLOB_PROJECT.length) {
    const arr = projectPages.filter((item) => VITE_GLOB_PROJECT.includes(item.module));
    arr.forEach((el) => {
      subPagesMap = Object.assign(subPagesMap, el.pages);
    });
  } else {
  }

  return Object.assign(mainPage, subPagesMap);
}

// === 服务启动动态替换用于业务表单组件禁止删除注释 start===
const projectBussinessComponents: Indexable[] = [
  {
    module: 'zhcz',
    pages: import.meta.glob('../../../../project/zhcz/business-components/Form/**/*.{vue,tsx}'),
  },
  {
    module: 'zoology-wzb',
    pages: import.meta.glob(
      '../../../../project/zoology-wzb/business-components/Form/**/*.{vue,tsx}',
    ),
  },
];
// === 服务启动动态替换用于业务表单组件禁止删除注释 end ===

function capitalizeFirstLetter(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export function getBussinessComponents() {
  const subPagesMap = {};
  projectBussinessComponents.forEach((el) => {
    const camelCasePages = Object.keys(el.pages).reduce((acc, key) => {
      const parts = key.split('/');
      const fileName = parts[parts.length - 1].split('.')[0];
      const camelCasedName = capitalizeFirstLetter(el.module) + capitalizeFirstLetter(fileName);

      acc[camelCasedName] = el.pages[key];
      return acc;
    }, {} as Record<string, any>);

    Object.assign(subPagesMap, camelCasePages);
  });

  return subPagesMap;
}

// === 服务启动动态替换用于业务列表组件禁止删除注释 start===
const projectBussinessTableComponents: Indexable[] = [
  {
    module: 'zhcz',
    pages: import.meta.glob('../../../../project/zhcz/business-components/Table/**/*.{vue,tsx}'),
  },
  {
    module: 'zoology-wzb',
    pages: import.meta.glob(
      '../../../../project/zoology-wzb/business-components/Table/**/*.{vue,tsx}',
    ),
  },
];
// === 服务启动动态替换用于业务列表组件禁止删除注释 end ===

export function getBussinessTableComponents() {
  const subPagesMap = {};
  projectBussinessTableComponents.forEach((el) => {
    const camelCasePages = Object.keys(el.pages).reduce((acc, key) => {
      const parts = key.split('/');
      const fileName = parts[parts.length - 1].split('.')[0];
      const camelCasedName = capitalizeFirstLetter(el.module) + capitalizeFirstLetter(fileName);

      acc[camelCasedName] = el.pages[key];
      return acc;
    }, {} as Record<string, any>);

    Object.assign(subPagesMap, camelCasePages);
  });

  return subPagesMap;
}
