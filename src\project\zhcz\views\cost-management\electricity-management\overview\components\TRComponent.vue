<template>
  <div class="trc">
    <HlxbLinePlusCard
      :title="'电耗统计'"
      v-bind="{ empty, bottomList: indexList, topList: totalList, loading }"
    >
      <template #headerRight>
        <div class="header-right">
          <div>
            <Select
              v-model:value="indicator"
              style="width: 100px"
              placeholder="请选择"
              @change="handleChangeIndicator"
              v-if="indicatorList.length"
            >
              <SelectOption v-for="item in indicatorList" :key="item.value" :value="item.value">
                {{ item.label }}
              </SelectOption>
            </Select>
          </div>
          <div>
            <Select v-model:value="dateType" style="width: 80px" v-if="dataList.length">
              <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
                {{ item.label }}
              </SelectOption>
            </Select>
          </div>
          <DatePicker
            v-if="dataList.find((item) => item.value === dateType)?.label === '月'"
            style="width: 200px"
            v-model:value="monthDate"
            picker="month"
            valueFormat="YYYY-MM"
            :disabledDate="disabledDateMonth"
          />
          <DatePicker
            v-if="dataList.find((item) => item.value === dateType)?.label === '日'"
            valueFormat="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model:value="dayDate"
            placeholder="请选择"
            style="width: 200px"
            :allowClear="false"
            :disabledDate="disabledDate"
          />
        </div>
      </template>
    </HlxbLinePlusCard>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  // import { LinePlusCard } from '/@zhcz/components/HLCardComponent';
  import { HlxbLinePlusCard } from 'hlxb-ui';
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import {
    getSenceGroupTreeWithGroupCode,
    listSenceGroupByParent,
  } from '/@zhcz/api/cost-management';
  import icon1 from '/@zhcz/views/cost-management/assets/images/energy-ratio_1.png';
  import icon2 from '/@zhcz/views/cost-management/assets/images/energy-ratio_2.png';
  type OptionItem = { label: String; value: String };
  const indicatorList = ref<OptionItem[]>([]);
  const indicator = ref('');
  const dateType = ref<any>(null);
  const dataList = ref<OptionItem[]>([
    // {
    //   label: '日',
    //   value: 'dhtj2_r',
    // },
    // {
    //   label: '月',
    //   value: 'dhtj2_y',
    // },
  ]);
  const handleChangeIndicator = async () => {
    await getData();
  };
  const loading = ref(false);
  const empty = ref(false);
  const monthDate = ref(dayjs().subtract(0, 'month').format('YYYY-MM'));
  const dayDate = ref(dayjs().format('YYYY-MM-DD'));
  type DataList = {
    indexName: string;
    indexCode: string;
    unitName: string;
    data: any[];
    XAxis: string[];
  };
  const indexList = ref<DataList[]>([]);
  const totalList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      icon?: string;
      value: number | null;
    }[]
  >([]);

  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }
  function disabledDateMonth(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('month');
  }
  async function getIndicatorList() {
    try {
      loading.value = true;
      empty.value = false;
      const res = await getSenceGroupTreeWithGroupCode({
        groupCode: dateType.value,
        factoryId: 1,
      });
      if (res.length) {
        loading.value = false;
        empty.value = false;
        indicator.value = res.find((item) => item.groupCode)?.groupCode || '';
        indicatorList.value = res.map((item) => ({
          value: item.groupCode,
          label: item.groupName,
        }));
        getData();
      } else {
        empty.value = true;
        loading.value = false;
      }
    } catch (err) {
      empty.value = true;
      loading.value = false;
    }
  }
  async function getTimeList() {
    try {
      empty.value = false;
      const res = await listSenceGroupByParent({
        // dhzl2_dlph
        groupCode: 'dhzl2_dhtj',
        factoryId: 1,
        platformld: 1,
      });
      console.log('res.data', res);
      if (Object.keys(res).length) {
        dateType.value = Object.keys(res)[0];
        dataList.value = Object.keys(res).map((item) => ({
          value: item,
          label: res[item],
        }));
        getIndicatorList();
      } else {
        empty.value = true;
      }
    } catch (err) {
      empty.value = true;
    }
  }
  // const emit = defineEmits(['setTRHData']);
  const getData = async () => {
    try {
      const startDateTime =
        dataList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? dayjs(dayDate.value).format('YYYY-MM-DD 00:00:00')
          : dayjs(monthDate.value).startOf('month').format('YYYY-MM-DD 00:00:00');
      const endDateTime =
        dataList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? dayjs(dayDate.value).format('YYYY-MM-DD 23:59:59')
          : dayjs(monthDate.value).endOf('month').format('YYYY-MM-DD 23:59:59');
      const params = {
        startDateTime: startDateTime,
        endDateTime: endDateTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        resourceInterfaceId: '5',
        jsConvert: true,
        groupCode: indicator.value,
        paramsData: JSON.stringify(params),
      };
      loading.value = true;
      empty.value = false;
      const { data } = await callResourceFunction(paramData);
      loading.value = false;
      console.log('节能统计', data);
      if (data && data.length) {
        empty.value = false;
        interface arrItem {
          collectDateTime?: string;
          value?: number;
        }
        let XAxisData: string[] = [];
        if (dataList.value.find((item) => item.value === dateType.value)?.label === '日') {
          let x = 0;
          while (x < 24) {
            if (x < 10) {
              XAxisData.push(`0${x}:00`);
            } else {
              XAxisData.push(`${x}:00`);
            }
            x++;
          }
        } else if (dataList.value.find((item) => item.value === dateType.value)?.label === '月') {
          let x = 1;
          let maxD;
          if (
            dayjs(monthDate.value).endOf('month').format('DD') >
            dayjs(monthDate.value).subtract(1, 'month').endOf('month').format('DD')
          ) {
            maxD = dayjs(monthDate.value).endOf('month').format('DD');
          } else {
            maxD = dayjs(monthDate.value).subtract(1, 'month').endOf('month').format('DD');
          }
          maxD = Number(maxD) + 1;
          while (x < maxD) {
            if (x < 10) {
              XAxisData.push(`0${x}`);
            } else {
              XAxisData.push(`${x}`);
            }
            x++;
          }
        }
        const newData: any = [];
        const XAxis = XAxisData.map((i) => {
          return dataList.value.find((item) => item.value === dateType.value)?.label === '日'
            ? i
            : `${i}日`;
        });
        if (data.length) {
          data.forEach((item) => {
            newData.push({
              indexName: item.indexName,
              indexCode: item.indexCode,
              unitName: item.unitName,
              data: addXais(XAxisData, item.data),
              XAxis: XAxis,
            });
          });
        }
        function addXais(data: string[], data2: []) {
          return data.map((item) => {
            const copyArr: arrItem[] = data2.filter((i: arrItem) => {
              if (dataList.value.find((item) => item.value === dateType.value)?.label === '日') {
                return item === dayjs(i.collectDateTime).format('HH:mm');
              } else if (
                dataList.value.find((item) => item.value === dateType.value)?.label === '月'
              ) {
                return item === dayjs(i.collectDateTime).format('DD');
              }
            });
            return copyArr.length > 0 ? copyArr[0]?.value : '';
          });
        }
        totalList.value = data.map((item, index) => {
          return {
            indexName: `${item.indexName}`,
            indexCode: item.indexCode,
            unitName: item.unitName,
            icon: index % 2 === 0 ? icon1 : icon2,
            value: item.total
              ? item.total
              : // item.data.length
                //   ? Number(
                //       item.data
                //         .map((i) => i.value)
                //         .reduce((prev, cur) => {
                //           return prev + cur;
                //         }, 0),

                //     ).toFixed(2)
                null,
          };
        });
        indexList.value = newData;
      } else {
        empty.value = true;
      }
    } catch (err) {
      empty.value = true;
      loading.value = false;
    }
  };

  watch(
    () => dayDate.value,
    async (newVal) => {
      console.log('newValArr=>日', newVal);
      // pause();
      await getIndicatorList();
      // resume();
    },
  );

  watch(
    () => monthDate.value,
    async (newVal) => {
      console.log('newVal=>月', newVal);
      await getIndicatorList();
    },
  );

  watch(
    () => dateType.value,
    async (newVal) => {
      console.log('newVal=>日期类型', newVal);
      monthDate.value = dayjs().subtract(0, 'month').format('YYYY-MM');
      dayDate.value = dayjs().format('YYYY-MM-DD');
      await getIndicatorList();
    },
  );
  onMounted(async () => {
    // 初始化
    getTimeList();
  });
</script>
<style lang="less" scoped>
  .trc {
    background-color: #fcfcfc;
    height: 100%;
    overflow: hidden;
    border-radius: 4px;

    .header-right {
      display: flex;
      gap: 0 16px;
    }
  }
</style>
