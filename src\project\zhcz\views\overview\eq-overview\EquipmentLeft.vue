<template>
  <div class="equipment-left">
    <BigBoxContainer>
      <BoxContainer style="height: calc(52% - 4px)">
        <template #header>
          <!-- 关键KPI变成，设备完好率。 -->
          <BoxHeader title="设备完好率">
            <template #right>
              <DatePicker
                v-model:value="date"
                picker="month"
                class="date-picker big-screen-date-picker"
                popupClassName="big-screen-date-picker-dropdown"
                placeholder="选择日期"
                style="width: 120px !important"
                :allowClear="false"
                :showToday="false"
                :disabledDate="disabledDate"
                @change="handleDateChange"
              />
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div class="bottom-container">
            <ProduceDataTop :data-list="rateList" style="margin-bottom: 1rem" />
            <div class="chart-container-c">
              <!-- <template v-if="rateLoad">
                <div style="background: unset"></div>
              </template> -->
              <template v-if="rateLoad">
                <ProduceDataChart :data="kpiData" />
              </template>
              <DataEmpty v-else />
            </div>
          </div>
        </template>
      </BoxContainer>
      <BoxContainer style="height: calc(48% - 4px)">
        <template #header>
          <!-- 重点设备健康指数，变更为设备运行情况统计，样式需要UI设计美化  加上日月年 变成柱状图-->
          <BoxHeader title="设备运行情况统计">
            <template #right>
              <!-- 设备运行情况统计，支持日、月、年过滤 -->
              <div class="select-box">
                <Select
                  v-model:value="sludgeTime2"
                  style="width: 5rem"
                  @change="handleSludgeChange2"
                  class="big-screen-select"
                  popupClassName="big-screen-select-dropdown"
                >
                  <SelectOption v-for="item in dataList2" :value="item.value" :key="item.value">
                    {{ item.label }}
                  </SelectOption>
                </Select>
                <DatePicker
                  v-model:value="otherDate"
                  :picker="getPicker2"
                  class="date-picker big-screen-date-picker"
                  popupClassName="big-screen-date-picker-dropdown"
                  style="width: 126px !important"
                  placeholder="选择日期"
                  :allowClear="false"
                  :showToday="false"
                  :disabledDate="disabledDate2"
                  @change="handleOtherDateChange"
                />
              </div>
              <!-- <Popover overlayClassName="device-health-popover-content">
                <template #content>
                  <div class="tip-list">
                    <div class="item" v-for="(item, index) in tipList" :key="index">
                      <span class="label">{{ item.label }}</span>
                      <span class="value">{{ item.value }}</span>
                    </div>
                  </div>
                </template>
                <InfoCircleOutlined style="color: white" />
              </Popover> -->
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <!-- 变成柱状图 -->
          <div class="bottom-container">
            <!-- <template v-if="equiLoad">
              <div style="background: unset"></div>
            </template> -->
            <template v-if="equiLoad">
              <ProduceDataBottom style="padding-top: 1rem" :data="equiData" />
            </template>
            <template v-else>
              <DataEmpty />
            </template>
          </div>
        </template>
      </BoxContainer>
    </BigBoxContainer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onUnmounted, computed } from 'vue';
  import dayjs from 'dayjs';
  import { DatePicker, SelectOption, Select } from 'ant-design-vue';
  // import { InfoCircleOutlined } from '@ant-design/icons-vue';
  import DataEmpty from '../components/data-empty/index.vue';
  import BigBoxContainer from '../components/box-container/BigBoxContainer.vue';
  import BoxContainer from '../components/box-container/index.vue';
  import BoxHeader from '../components/box-container/BoxHeader.vue';
  import ProduceDataTop from '../components/produce-data/index.vue';
  import ProduceDataBottom from './components/echarts/ProduceDataBottom.vue';
  import ProduceDataChart from '../components/echarts/ProduceDataChart.vue';
  // import EquipmentHealth from './components/echarts/EquipmentHealth.vue';
  // import { getKpi } from '/@zhcz/api/overview';
  // import { tipList } from './data';
  import { useEmitt } from '/@/hooks/web/useEmitt';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';
  import { mockChartData } from './data';
  // import { flatMap } from 'lodash-es';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { roundAndConvertCheckNullAndUnDef } from '/@zhcz/utils/number';
  // import { getEqpIntactApi } from '/@zhcz/api/device-management';
  import { getBiOperatingStatistic } from '/@zhcz/api/overview';
  import { getEqpIntactApi } from '/@zhcz/api/device-management';

  const props = defineProps({
    isScene: {
      type: Boolean,
      default: true,
    },
  });

  const otherDate = ref(dayjs().subtract(0, 'day'));
  const sludgeTime2 = ref<null | string>('0');
  const dataList2 = ref<{ label: string; value: string }[]>([
    { label: '日', value: '0' },
    { label: '月', value: '1' },
    { label: '年', value: '2' },
  ]);
  const rateLoad = ref<Boolean>(true);
  const equiLoad = ref<Boolean>(true);
  const equiData = ref<any>({});

  const timeTypeMap = {
    date: {
      type: 0,
      format: 'YYYY-MM-DD',
    },
    month: {
      type: 1,
      format: 'YYYY-MM',
    },
    year: {
      type: 2,
      format: 'YYYY',
    },
  };
  async function getTimeList2() {
    if (!props.isScene) {
      getConsumptionData();
      return;
    }
    const res = await listSenceGroupByParent({
      groupCode: 'sbzldpcj_sbyxqk',
      factoryId: 1,
      platformld: 1,
    });
    if (Object.keys(res).length) {
      sludgeTime2.value = Object.keys(res)[0];
      dataList2.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      handleOtherDateChange();
    }
  }
  getTimeList2();
  enum pickerType {
    '日' = 'date',
    '月' = 'month',
    '年' = 'year',
  }

  const getPicker2 = computed(() => {
    if (sludgeTime2.value) {
      return pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    } else {
      return pickerType['日'];
    }
  });
  const handleSludgeChange2 = () => {
    const picker =
      pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    if (picker === 'date') {
      otherDate.value = dayjs().subtract(0, 'day');
    } else {
      otherDate.value = dayjs().subtract(0, picker);
    }
    // otherDate.value = dayjs().subtract(0, picker);
    // otherDate.value = dayjs();
    handleOtherDateChange();
  };
  function disabledDate2(current) {
    // 禁止选择今天以后的日期
    // const picker =
    //   pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    return current && current > dayjs().subtract(0, 'day');
    // return current && current > dayjs();
  }
  const handleOtherDateChange = () => {
    getConsumptionData();
  };

  function getApiParams() {
    const { type, format } =
      timeTypeMap[
        pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日']
      ];
    return {
      type,
      time: dayjs(otherDate.value).format(format),
    };
  }
  // 设备运行情况统计
  async function getConsumptionData() {
    try {
      // 三组数据 正常 维修 维保
      const picker =
        pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
      const startDate = dayjs(otherDate.value).startOf(picker).format('YYYY-MM-DD 00:00:00');
      const endDataTime = dayjs(otherDate.value).endOf(picker).format('YYYY-MM-DD 23:59:59');
      const tempParams = { resourceInterfaceId: '3', groupCode: sludgeTime2.value };
      const params = {
        startDateTime: startDate,
        endDateTime: endDataTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };
      const paramApiData = getApiParams();
      const res = props.isScene
        ? await callResourceFunction(paramData)
        : await getBiOperatingStatistic(paramApiData);

      // const { data } = await callResourceFunction(paramData);
      const data = props.isScene ? res.data : res;

      let XAxisData: string[] = [];
      if (picker === 'date') {
        let x = 0;
        while (x < 24) {
          if (x < 10) {
            XAxisData.push(`0${x}:00`);
          } else {
            XAxisData.push(`${x}:00`);
          }
          x++;
        }
      } else if (picker === 'month') {
        let x = 1;
        let maxD;
        if (
          dayjs(otherDate.value).endOf('month').format('DD') >
          dayjs(otherDate.value).subtract(0, 'month').endOf('month').format('DD')
        ) {
          maxD = dayjs(otherDate.value).endOf('month').format('DD');
        } else {
          maxD = dayjs(otherDate.value).subtract(0, 'month').endOf('month').format('DD');
        }
        maxD = Number(maxD) + 1;
        while (x < maxD) {
          if (x < 10) {
            XAxisData.push(`0${x}日`);
          } else {
            XAxisData.push(`${x}日`);
          }
          x++;
        }
      } else if (picker === 'year') {
        let x = 1;
        const maxD = 13;
        while (x < maxD) {
          if (x < 10) {
            XAxisData.push(`0${x}月`);
          } else {
            XAxisData.push(`${x}月`);
          }
          x++;
        }
      }
      const color = ['#22CD80', '#2E8CFF', '#FF8C2E'];
      const seriesBox = [
        {
          name: '正常',
          color: color[0],
          data: [1, 2, 3, 2, 3, 4, 5, 15],
          unitName: '个',
        },
        {
          name: '维修',
          color: color[1],
          data: [2, 3, 2, 3, 4, 5, 15],
          unitName: '个',
        },
        {
          name: '维保',
          color: color[2],
          data: [3, 2, 3, 4, 5, 15],
          unitName: '个',
        },
      ];
      if (data && data.length) {
        equiLoad.value = true;
        const xAxis = data[0]?.data.map((item) => {
          let row;
          if (picker === 'date') {
            row = dayjs(item.collectDateTime).format('HH:mm');
          } else if (picker === 'month') {
            row = dayjs(item.collectDateTime).format('DD') + '日';
          } else if (picker === 'year') {
            row = dayjs(item.collectDateTime).format('MM') + '月';
          }
          return row;
        });
        equiData.value.chartOptions = {
          xAxis: {
            data: xAxis.length ? xAxis : XAxisData,
          },
          series: data.map((item, index) => {
            return {
              name: item.indexName ? item.indexName : seriesBox[index].name,
              color: seriesBox[index].color,
              data: item.data.map((i) => i.value),
              unitName: item.unitName ? item.unitName : seriesBox[index].unitName,
            };
          }),
        };
      } else {
        equiLoad.value = false;
        equiData.value.chartOptions = {
          xAxis: {
            data: XAxisData,
          },
          series: [
            {
              name: '正常',
              color: color[0],
              data: [1, 2, 3, 2, 3, 4, 5, 15],
              unitName: '个',
            },
            {
              name: '维修',
              color: color[1],
              data: [2, 3, 2, 3, 4, 5, 15],
              unitName: '个',
            },
            {
              name: '维保',
              color: color[2],
              data: [3, 2, 3, 4, 5, 15],
              unitName: '个',
            },
          ],
        };
      }
    } catch (_) {
      equiLoad.value = false;
    }
  }
  const date = ref(dayjs().subtract(0, 'month'));

  function disabledDate(current) {
    // 禁止选择今天以后的日期
    return current && current > dayjs().subtract(0, 'day');
  }
  const rateCoList = [
    {
      indexName: '设备完好率',
      value: 0,
      unitName: '',
    },
    {
      indexName: '维修及时率',
      value: 0,
      unitName: '',
    },
    {
      indexName: '设备故障率',
      value: 0,
      unitName: '',
    },
  ];
  const rateList = ref([
    {
      indexName: '设备完好率',
      value: 0,
      unitName: '',
    },
    {
      indexName: '维修及时率',
      value: 0,
      unitName: '',
    },
    {
      indexName: '设备故障率',
      value: 0,
      unitName: '',
    },
  ]);

  // const chartOptions = {
  //   xAxis: {
  //     data: [dayjs(date.value).format('YYYY-MM-DD')],
  //   },
  //   series: [
  //     {
  //       name: '数量',
  //       color: '#2D82FE',
  //       data: [],
  //     },
  //   ],
  // };

  const kpiData = ref<any>({
    // chartOptions: JSON.parse(JSON.stringify(mockChartData)),
  });

  // const colorList = [
  //   ['#2DC4FE', '#2D82FE'],
  //   ['#50D6BD', '#1FC3A4'],
  //   ['#FEE22D', '#FEC52D'],
  // ];
  // 设备完好率
  const getProduceTopData = async () => {
    try {
      const tempParams = { resourceInterfaceId: '19', groupCode: 'sbzldpcj_sbwhl' };
      const params = {
        startDateTime: dayjs(date.value).startOf('month').format('YYYY-MM-DD 00:00:00'),
        endDateTime: dayjs(date.value).endOf('month').format('YYYY-MM-DD 23:59:59'),
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };

      // const { data } = await callResourceFunction(paramData);
      const paramApiData = {
        type: 1,
        time: dayjs(date.value).format('YYYY-MM'),
      };
      const res = props.isScene
        ? await callResourceFunction(paramData)
        : await getEqpIntactApi(paramApiData);

      const data = props.isScene ? res.data : res;
      if (data && data.length) {
        if (props.isScene) {
          rateList.value = data.map((i, index) => {
            const value = i.indicatorsByTimeIntervalResp.length
              ? i.indicatorsByTimeIntervalResp[0]?.data.reduce((prev, cur) => {
                  return prev + cur.value;
                }, 0)
              : 0;
            return {
              indexName: i.indicatorsByTimeIntervalResp.length
                ? i.indicatorsByTimeIntervalResp[0]?.indexName
                : rateCoList[index].indexName,
              value: roundAndConvertCheckNullAndUnDef(value, 0) + '%',
              unitName: '',
              src: i.imgByTimeIntervalResps[0]?.sourceUniqueKey,
            };
          });
        } else {
          rateList.value = data.map((i, index) => {
            const value = i.data.at(-1) ? i.data.at(-1).value : 0;
            return {
              indexName: i.indexName ?? rateCoList[index].indexName,
              value: roundAndConvertCheckNullAndUnDef(value, 0) + i.unitName,
              unitName: '',
            };
          });
        }
      } else {
        rateList.value = rateCoList;
      }
    } catch (_) {}
  };

  // 设备完好率曲线
  const getRateData = async () => {
    try {
      // rateLoad.value = true;
      // const params = {
      //   endTime: dayjs(date.value).format('YYYY-MM-DD 23:59:59'),
      // };
      // const data = await getKpi(params);
      const tempParams = { resourceInterfaceId: '3', groupCode: 'sbzldpcj_sbwhlqx' };
      const params = {
        startDateTime: dayjs(date.value).startOf('month').format('YYYY-MM-DD 00:00:00'),
        endDateTime: dayjs(date.value).endOf('month').format('YYYY-MM-DD 23:59:59'),
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };
      // const { data } = await callResourceFunction(paramData);
      const paramApiData = {
        type: 1,
        time: dayjs(date.value).format('YYYY-MM'),
      };
      const res = props.isScene
        ? await callResourceFunction(paramData)
        : await getEqpIntactApi(paramApiData);

      const data = props.isScene ? res.data : res;

      if (data && data.length) {
        rateLoad.value = true;
        const newData = JSON.parse(JSON.stringify(mockChartData));
        if (data.length) {
          newData.chartOptions.xAxis.data = data[0]?.data.map((item) => {
            let row;
            row = dayjs(item.collectDateTime).format('DD') + '日';
            return row;
          });
        }
        newData.chartOptions.series = data.map((val, index) => {
          const oldItem = mockChartData.chartOptions.series[index];
          let t_data = mockChartData.chartOptions.series[index].data;
          t_data = val.data.map((i) => i.value);

          return {
            ...oldItem,
            name: val.indexName ? val.indexName : oldItem.name,
            data: t_data,
            unitName: val.unitName ? val.unitName : oldItem.unitName,
          };
        });
        // if (!newData.chartOptions.series[0].data.length) {
        //   rateLoad.value = false;
        // }
        kpiData.value = newData;
      } else {
        rateLoad.value = false;
      }
    } catch (_) {
      rateLoad.value = false;
    }
  };
  getRateData();
  getProduceTopData();

  const handleDateChange = () => {
    getRateData();
    getProduceTopData();
  };

  const { emitter } = useEmitt();
  emitter.on('bi:change-factory', () => {
    getRateData();
  });

  onUnmounted(() => {
    emitter.off('bi:change-factory');
  });
</script>

<style lang="less" scoped>
  .equipment-left {
    width: 100%;
    height: 100%;

    .select-box {
      flex: 1;
      display: flex;
      justify-content: end;
      gap: 0 12px;
    }

    :deep(.ant-select-selection-item) {
      color: #fff;
    }
    // .ivu-tooltip {
    //   ::v-deep .ivu-tooltip-dark {
    //     .ivu-tooltip-inner {
    //       background-color: #132b4e;
    //     }

    //     .ivu-tooltip-arrow {
    //       border-left-color: #132b4e;
    //     }
    .date-picker {
      width: 160px !important;
    }

    :deep(.tip-list) {
      font-size: 14px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 400;
      color: #fff;
    }
    // }
    // }

    :deep(.ant-picker) {
      color: #fff;

      .ant-picker-input > input {
        color: #fff;
      }
    }

    .bottom-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      position: relative;

      .chart-container-c {
        position: relative;
        flex: 1;
      }
    }
  }
</style>

<style lang="less">
  .device-health-popover-content {
    max-width: 260px;

    .ant-popover-inner {
      background-color: #132b4e;
    }

    .ant-popover-inner-content {
      color: white;
      font-weight: 400;
    }
  }
</style>
