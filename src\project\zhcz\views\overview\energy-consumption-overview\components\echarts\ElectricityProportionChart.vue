<template>
  <div style="width: 100%; height: 100%">
    <div ref="chartRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
  import { defineComponent, ref, onMounted, watch } from 'vue';
  import { useECharts } from '/@zhcz/hooks/useECharts';
  import {
    hasMax,
    roundFun,
    toDecimalFloor,
    getEchartFontSize,
    getScaleValByClientWidth,
  } from '../../../utils';
  import { circleGraphic } from '../../data';
  import { onUnmounted } from 'vue';

  export default defineComponent({
    props: {
      data: {
        type: Object,
        default: () => ({
          series: [
            {
              data: [
                { name: '送水泵房', value: 1048 },
                { name: '炭滤提升', value: 735 },
                { name: '臭氧间月用电', value: 580 },
                { name: '其他用电', value: 484 },
                { name: '回收水泵', value: 300 },
                { name: '综合楼', value: 360 },
                { name: '砂滤池月用电', value: 528 },
                { name: '炭滤反冲', value: 622 },
                { name: '调节池月用电', value: 345 },
                { name: '加药间月用电', value: 469 },
              ],
            },
          ],
        }),
      },
    },
    setup(props) {
      const chartRef = ref(null);

      // const sum = computed(() => {
      //   return props.data.series[0].data.reduce((prev, next) => prev + next.value, 0);
      // });

      const { setOptions } = useECharts(chartRef);

      function getColor(val) {
        const colors = [
          '#2D82FE',
          '#1FC3A4',
          '#FEC52D',
          '#732DFE',
          '#1FC369',
          '#FE8B2D',
          '#FEC52D',
          '#FEF42D',
          '#2DCAFE',
          '#2DE2FE',
          '#5470c6',
          '#91cc75',
          '#fac858',
          '#ee6666',
          '#73c0de',
          '#3ba272',
          '#fc8452',
          '#9a60b4',
          '#ea7ccc',
        ];

        const index = val > colors.length - 1 ? val % colors.length : val;
        return colors[index];
      }

      function renderChart() {
        const { series } = props.data;
        console.log('series', series);
        if (!series || !series[0] || !series[0].data[0]?.totalvalue) {
          return;
        }
        const isW = hasMax([{ data: [series[0].data[0]?.totalvalue || 0] }]);
        const titleText = formatterNum(series[0].data[0]?.totalvalue || 0);
        function formatterNum(value) {
          const val = isW ? `${roundFun(value / 10000, 1)}万` : value;
          return val;
        }
        const innerWidth = window.innerWidth;
        const option = {
          title:
            // [
            //   {
            //     text: `1232`,
            //     left: commonLeft - 0.35 + '%',
            //     top: '32%',
            //     textAlign: 'center',
            //     textStyle: {
            //       fontSize: 24,
            //       fontWeight: 600,
            //       height: 50,
            //       color: '#FFFFFF',
            //     },
            //   },
            //   {
            //     text: `总用电`,
            //     left: commonLeft + '%',
            //     top: '42%',
            //     textAlign: 'center',
            //     textStyle: {
            //       fontSize: 14,
            //       fontWeight: 400,
            //       color: 'rgba(255,255,255,0.6)',
            //     },
            //   },
            // ],
            {
              text: `${titleText}${series[0].data[0]?.unitName || ''}`,
              subtext: '总用电',
              left: 'center',
              top: getScaleValByClientWidth(87),
              itemGap: 6,
              textStyle: {
                fontSize: 14,
                fontWeight: 700,
                color: '#fff',
              },
              subtextStyle: {
                fontSize: 14,
                fontWeight: 400,
                color: 'rgba(255,255,255,0.6)',
              },
            },

          tooltip: {
            show: true,
            // trigger: 'axis',
            backgroundColor: '#132b4e',
            borderColor: '#132b4e',
            textStyle: {
              color: '#fff',
            },
            formatter: (params) => {
              console.log('params', params);
              let item;
              if (Array.isArray(params)) {
                // 适用折线柱状堆叠的多条数据
                item = params.filter((item) => item.value !== undefined);
              } else {
                // 饼图
                item = [params];
              }
              // console.log('item', item, 'series', series);
              if (item.length) {
                let htmlStr = ``;
                item.forEach((val, index) => {
                  const isW = hasMax(Number(val.value) ? Number(val.value) : 0);
                  const value = isW ? `${roundFun(val.value / 10000, 2)}万` : val.value;
                  if (Array.isArray(params)) {
                    // 适用折线柱状堆叠的多条数据
                    const unitName = series.find((i) => val.seriesName === i.name)?.unitName || '';
                    htmlStr += `<div>${index === 0 ? val.name : ''}</div>
                    ${val.marker} <span style="display: inline-block; width: 30px;">${
                      val.seriesName
                    }</span><span  style="display: inline-block; width: ${
                      props.tooltipWidth ? props.tooltipWidth + 'px' : '100px'
                    }; font-weight: bold; text-align: right;">${value}</span><span  style="padding-left: 4px">${unitName}</span>`;
                  } else {
                    // 饼图
                    const unitName =
                      series[0].data.find((i) => val.name === i.name)?.unitName || '';
                    htmlStr += `
                    ${val.marker} <span style="display: inline-block; width: 30px;">${
                      val.name
                    }</span><span  style="display: inline-block; width: ${
                      props.tooltipWidth ? props.tooltipWidth + 'px' : '100px'
                    }; font-weight: bold; text-align: right;">${value}</span><span  style="padding-left: 4px">${unitName}</span>`;
                  }
                });
                return htmlStr;
              }
              return;
            },
          },
          graphic: {
            elements: [
              {
                type: 'image',
                z: 2,
                style: {
                  image: circleGraphic,
                  width: getScaleValByClientWidth(174),
                  height: getScaleValByClientWidth(174),
                },
                left: 'center',
                top: getScaleValByClientWidth(24),
              },
            ],
          },
          legend: {
            type: innerWidth < 1920 ? 'scroll' : 'plain',
            show: true,
            icon: 'circle',
            bottom: -5,
            left: 0,
            itemGap: 16,
            itemWidth: 8,
            itemHeight: 8,
            // align: 'right',
            data: series[0].data
              .filter((item) => item.value !== 0)
              .map((item) => {
                return { ...item, value: toDecimalFloor(item.value) };
              }),
            formatter: function (name) {
              const b = series[0].data.find((i) => i.name === name)?.value || '';
              const c = series[0].data.find((i) => i.name === name)?.unitName || '';
              const isW = hasMax([{ data: [b] }]);
              const textB = formatterNum(b);
              function formatterNum(value) {
                const val = isW ? `${roundFun(value / 10000, 1)}万` : value;
                return val;
              }
              // console.log(isW, name, b, textB, 'c', c);
              // return `<div><br />${name}<div/>`;
              return `{a|${name}}{b|${textB}}{c|${c}}`;
            },
            textStyle: {
              // width: '50%',
              fontSize: getEchartFontSize(14),
              color: 'rgba(255, 255, 255, 1)',
              overflow: 'truncate',
              ellipsis: '...',
              align: 'right',
              rich: {
                a: {
                  width: 56,
                  fontSize: 14,
                  align: 'left',
                  lineHeight: 22,
                },
                b: {
                  width: 80,
                  // width: '50%',
                  fontSize: 16,
                  fontWeight: 600,
                  color: '#FFFFFF',
                  padding: [0, 4, 0, 12],
                  // backgroundColor: {
                  //   image: 'xxx/xxx.jpg',
                  // },
                  align: 'right',
                  lineHeight: 22,
                  // align: 'left',
                },
                c: {
                  // width: '20%',
                  width: 20,
                  color: 'rgba(255,255,255,0.8)',
                  fontSize: 14,
                  padding: [0, 0, 0, 0],
                  lineHeight: 22,
                  align: 'left',
                },
              },
            },
            pageTextStyle: {
              color: '#fff',
            },
            pageIconColor: '#fff',
            pageIconInactiveColor: 'rgba(255,255,255, 0.56)',
          },
          grid: {
            left: 0,
            right: 0,
            top: 0,
            // bottom: fontSize(0.5),
            containLabel: true,
          },
          series: [
            {
              type: 'pie',
              // top: '-30%',
              center: ['50%', getScaleValByClientWidth(110)],
              // center: ['50%', '40%'],
              clockWise: false,
              radius: [getScaleValByClientWidth(64), getScaleValByClientWidth(79)],
              hoverAnimation: false,
              // label: {
              //   show: true,
              //   position: 'outside',
              //   color: '#fff',
              //   fontSize: getEchartFontSize(14),
              //   fontWeight: 400,
              //   formatter(params) {
              //     if (params.name !== '') {
              //       return `${((params.value / sum.value) * 100).toFixed(2)}%`;
              //     } else {
              //       return '';
              //     }
              //   },
              // },
              label: {
                alignTo: 'edge',
                formatter(params) {
                  if (params.name !== '' && params.value) {
                    return Number(titleText)
                      ? `${((params.value / Number(titleText)) * 100).toFixed(2)}%`
                      : '';
                  } else {
                    return '';
                  }
                },
                minMargin: 5,
                edgeDistance: 10,
                lineHeight: 15,
                textStyle: {
                  color: '#FFFFFF',
                  fontWeight: 600,
                  fontSize: 16,
                },
                // rich: {
                //   time: {
                //     fontSize: 10,
                //     color: '#999',
                //   },
                // },
              },
              labelLine: {
                show: true,
                length: 15,
                // length2: 15,
                maxSurfaceAngle: 80,
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.6)',
                },
              },
              itemStyle: {
                color(params) {
                  const color = getColor(params.dataIndex);
                  return color;
                },
              },
              data: series[0].data
                .filter((item) => item.value !== 0)
                .map((item) => {
                  return { ...item, value: toDecimalFloor(item.value) };
                }),
            },
          ],
        };
        setOptions(option);
      }

      watch(
        () => props.data,
        () => {
          renderChart();
        },
      );

      onMounted(() => {
        renderChart();
        window.onresize = function () {
          // console.log('onresize', window.innerWidth);
          renderChart();
        };
      });
      onUnmounted(() => {
        window.onresize = null;
      });
      return {
        chartRef,
      };
    },
  });
</script>

<style lang="less" scoped></style>
