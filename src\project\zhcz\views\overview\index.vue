<template>
  <div class="overview-page">
    <div class="container">
      <BaseMap />
      <div :class="['content', { content_personnel: activeItem.text === '人员总览' }]">
        <HomeLeft>
          <KeepAlive>
            <component :is="HomeLeftComponent" />
          </KeepAlive>
        </HomeLeft>
        <HomeCenter v-show="activeItem.text !== '人员总览'">
          <KeepAlive>
            <component :is="HomeCenterComponent" />
          </KeepAlive>
        </HomeCenter>
        <HomeRight>
          <KeepAlive>
            <component :is="HomeRightComponent" />
          </KeepAlive>
        </HomeRight>
      </div>
      <NavigationFooter />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, watch } from 'vue';
  import {
    BaseMap,
    HomeLeft,
    HomeCenter,
    HomeRight,
    NavigationFooter,
    leftComponents,
    centerComponents,
    rightComponents,
  } from './components';
  import { useNavigation, useIndexList, useTitleList } from './hooks';
  import { navigationData } from './data';
  import { getSenceGroupTree } from '/@zhcz/api/scenes-group';

  const { activeItem, setActiveItem } = useNavigation();
  const { setIndexList } = useIndexList();
  const { setTitleList } = useTitleList();

  const HomeLeftComponent = computed(() => {
    return leftComponents.get(activeItem.value.component);
  });

  const HomeCenterComponent = computed(() => {
    return centerComponents.get(activeItem.value.component);
  });

  const HomeRightComponent = computed(() => {
    return rightComponents.get(activeItem.value.component);
  });

  // 递归查找children中groupCode为指定值的name
  const findName = (indexList, groupCode) => {
    if (!indexList || !indexList.length) return '';
    const item = indexList.find((item) => item.groupCode === groupCode);
    if (item) {
      return item?.name;
    } else {
      findName(item?.children, groupCode);
    }
  };

  async function getResourceData() {
    const data = await getSenceGroupTree();
    const { text, senceCode } = activeItem.value;
    const indexList = data.filter((item) => item.senceCode === senceCode)[0]?.children;
    // 设置 ResourcesKeys
    if (text === '生产总览') {
      setIndexList({
        index_LT1: [{ resourceInterfaceId: '19', groupCode: 'sczl_scsj' }],
        index_LT2: [{ resourceInterfaceId: '1816732630201143299', groupCode: 'jqtsjqx' }],
        index_LB1: [{ resourceInterfaceId: '12', groupCode: 'js' }],
        index_LB2: [{ resourceInterfaceId: '12', groupCode: 'cs' }],
        index_RT: [{ resourceInterfaceId: '1816732630201143298', groupCode: 'sczl_nwsj' }],
        index_RB: [{ resourceInterfaceId: '1816732630201143299', groupCode: 'lssh' }],
        index_CT: [{ resourceInterfaceId: '19', groupCode: 'scdpzxsj' }],
        index_CB: [{ resourceInterfaceId: '19', groupCode: 'zxtpz' }],
      });
      setTitleList({
        title_LT1: findName(indexList, 'sczl_scsj') || '生产数据',
        title_LT2: findName(indexList, 'jqtsjqx') || '近七天数据曲线',
        title_LB: findName(indexList, 'ssszzb') || '实时水质指标',
        title_LB_1: findName(indexList, 'js') || '进水',
        title_LB_2: findName(indexList, 'cs') || '出水',
        title_RT: findName(indexList, 'sczl_nwsj') || '药剂数据',
        title_RB1: findName(indexList, 'lssh') || '历史数据',
        // title_RB2: '',
      });
    } else if (text === '能耗总览') {
      setIndexList({
        index_LT1: [{ resourceInterfaceId: '20', groupCode: 'zr_zh_nh', jsConvert: true }],
        index_LT2: [{ resourceInterfaceId: '1816732630201143299', groupCode: 'NH_QT_SJQX' }],
        index_LB1: [{ resourceInterfaceId: '1816732630201143298', groupCode: 'RSC_NH' }],
        index_LB2: [],
        index_RT: [{ resourceInterfaceId: '3', groupCode: 'R_NH_YD' }],
        index_RB: [{ resourceInterfaceId: '1816732630201143299', groupCode: 'NH_SJ' }],
        index_CT: [{ resourceInterfaceId: '19', groupCode: 'NH_DP_ZXSJ' }],
        index_CB: [{ resourceInterfaceId: '19', groupCode: 'NHDP_ZXTPZ' }],
      });
      setTitleList({
        title_LT1: findName(indexList, 'zr_zh_nh') || '电耗统计',
        title_LT2: findName(indexList, 'NH_QT_SJQX') || '生产数据数据曲线',
        title_LB: findName(indexList, 'RSC_NH') || '生产能耗',
        title_LB_1: '',
        title_LB_2: '',
        title_RT: findName(indexList, 'R_NH_YD') || '分类用电',
        title_RB1: findName(indexList, 'NH_SJ') || '能耗数据',
        title_RB2: '',
      });
    } else if (text === '药耗总览') {
      setIndexList({
        index_LT1: [{ resourceInterfaceId: '19', groupCode: 'DrugConsumptionProductData' }],
        index_LT2: [
          {
            resourceInterfaceId: '1816732630201143299',
            groupCode: 'DrugConsumptionProductDataByDays',
          },
        ],
        index_LB1: [
          { resourceInterfaceId: '1816732630201143299', groupCode: 'DrugUnitConsumption' },
        ],
        index_LB2: [],
        index_RT: [{ resourceInterfaceId: '3', groupCode: 'DrugConsumptionProportion' }],
        index_RB: [
          { resourceInterfaceId: '1819296216953917441', groupCode: 'MedicationStatistics' },
        ],
        index_CT: [{ resourceInterfaceId: '19', groupCode: 'DrugConsumptionCenterData' }],
        index_CB: [{ resourceInterfaceId: '19', groupCode: 'DrugConsumptionCenterPic' }],
      });
      setTitleList({
        title_LT1: findName(indexList, 'DrugConsumptionProductData') || '昨日用药单耗',
        title_LT2: findName(indexList, 'DrugConsumptionProductDataByDays') || '单耗近七天数据曲线',
        title_LB: findName(indexList, 'DrugUnitConsumption') || '日用药单耗排行',
        title_LB_1: '',
        title_LB_2: '',
        title_RT: findName(indexList, 'DrugConsumptionProportion') || '日药耗比例',
        title_RB1: findName(indexList, 'MedicationStatistics') || '药耗统计(近半年)',
        title_RB2: '',
      });
    } else if (text === '设备总览') {
      // setIndexList({
      //   index_LT1: [],
      //   index_LT2: [],
      //   index_LB1: [],
      //   index_LB2: [],
      //   index_RT: [],
      //   index_RB: [],
      //   index_CT: indexList[1],
      //   index_CB: getCenterModelKeys(arrData[0].groupLinkLists),
      // });
      // setTitleList({
      //   title_LT1: '',
      //   title_LB: '',
      //   title_LB_1: '',
      //   title_LB_2: '',
      //   title_RT: arrData[2].groupName,
      //   title_RB1: arrData[3].groupName,
      // });
    }
  }

  watch(
    () => activeItem.value,
    () => {
      const commonModules = ['生产总览', '能耗总览', '药耗总览', '设备总览'];
      if (commonModules.includes(activeItem.value.text)) {
        getResourceData();
      }
    },
    {
      immediate: true,
    },
  );

  onMounted(() => {
    setActiveItem(navigationData[0]);
  });
</script>

<style lang="less">
  @import './styles/big-screen.less';
</style>

<style lang="less" scoped>
  @import '/@zhcz/views/overview/assets/css/font.less';

  .page {
    padding-bottom: 80px;
  }

  .overview-page {
    width: 100%;
    // 100%可视高度 - 顶部菜单 - margin-top - 底部导航栏
    // height: ~'calc(100vh - 64px - 16px - 80px)';
    height: calc(100vh - 80px);
    overflow: auto;

    @media screen and (max-height: 716px) {
      height: ~'calc(100vh - 64px)';
    }

    &::-webkit-scrollbar {
      width: 0px;
      height: 0px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #808695;
      border-radius: 0.25rem;
    }

    .container {
      position: relative;
      width: 100%;
      height: 100%;
      max-width: unset;

      // @media screen and (max-height: 900px) {
      //   height: 800px;
      // }
      .main-title {
        background-size: 100% 100%;
        position: relative;
        height: 80px;
        width: 100%;
        text-align: center;
        font-style: normal;
        background-image: url('./assets/images/main_title.png');
        background-repeat: no-repeat;

        span {
          position: relative;
          font-family: YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 44px;
          line-height: 80px;
          text-transform: none;
          background: linear-gradient(180deg, #ffffff 0%, #95c0ff 100%);
          -webkit-text-fill-color: transparent;
          background-clip: text;
          cursor: pointer;
          // z-index: 1;
          @media screen and (max-width: 1440px) {
            font-size: 38px;
          }
        }

        .main-title-right {
          position: absolute;
          right: 30px;
          top: 0;
          color: #fff;
          display: flex;
          align-items: center;
          height: 100%;
          justify-content: flex-end;
          line-height: 1;

          .date-box {
            margin-right: 22px;

            @media screen and (max-width: 1440px) {
              margin-right: 12px;
            }

            .time {
              font-family: D-DIN-PRO;
              font-weight: 700;
              font-size: 16px;
              margin-bottom: 6px;
              text-align: left;
            }

            .date {
              font-family: D-DIN-PRO;
              font-weight: 400;
              font-size: 13px;
            }

            .day {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 11px;
              margin-left: 6px;
            }
          }

          .weather-box {
            display: flex;
            border-left: 1px solid #ffffff;
            padding-left: 22px;
            @media screen and (max-width: 1440px) {
              padding-left: 12px;
            }

            .left {
              display: flex;

              img {
                width: 32px;
                height: 32px;
              }

              & > div {
                display: flex;
                flex-direction: column;
                margin-left: 10px;
              }
            }

            .right {
              display: flex;
              flex-wrap: wrap;
              margin-left: 22px;
              width: 270px;
              justify-content: flex-start;
              align-items: flex-start;
              @media screen and (max-width: 1440px) {
                margin-left: 12px;
                width: 245px;
              }

              img {
                width: 16px;
                height: 16px;
              }

              & > div {
                min-width: 100px;
                display: flex;
                margin-right: 22px;
                @media screen and (max-width: 1440px) {
                  margin-right: 12px;
                }

                .con {
                  width: 48px;
                  margin-left: 5px;
                  text-align: left;
                }
              }
            }
          }
        }
      }

      .content {
        position: absolute;
        // top: 96px;
        top: 24px;
        left: 0;
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: calc(100% - 106px);
        overflow: hidden;

        &_personnel {
          overflow-x: hidden;
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 0px;
          }

          &::-webkit-scrollbar-track {
            background-color: transparent;
          }

          &::-webkit-scrollbar-thumb {
            background: #808695;
            border-radius: 0.25rem;
          }
        }
      }
    }
  }
</style>
