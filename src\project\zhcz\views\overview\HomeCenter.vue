<template>
  <div class="home-center">
    <slot></slot>
  </div>
</template>

<script lang="ts" setup></script>

<style lang="less" scoped>
  .home-center {
    flex: 1;
    overflow: hidden;
    // position: absolute;
    // left: 50%;
    // top: 2px;
    // z-index: 1;
    // height: 100%;
    // transform: translate(-50%, 0);

    // @media screen and (max-width: 1440px) {
    //   width: calc(100% - 50rem + 22px);
    // }
    // @media screen and (max-width: 1600px) {
    //   width: calc(100% - 54rem + 76px);
    // }
    // @media screen and (max-width: 1742px) {
    //   width: calc(100% - 55.25rem + 76px);
    // }
    // @media screen and (max-width: 1920px) {
    //   width: calc(100% - 59.25rem + 76px);
    // }
    // @media screen and (min-width: 1921px) {
    //   width: calc(100% - 81.25rem + 76px);
    // }
  }
</style>
