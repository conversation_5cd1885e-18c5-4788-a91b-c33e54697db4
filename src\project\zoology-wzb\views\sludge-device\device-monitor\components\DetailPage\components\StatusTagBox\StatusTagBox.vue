<template>
  <div class="card-box">
    <div class="label-box">
      <div class="label-item">
        <div class="label">
          <div :title="item.label" class="txt">{{ item.label }}</div>
          <img v-if="item.afterLabelImage" :src="item.afterLabelImage" class="after-label-image" />
        </div>
        <div v-if="item.tag" :class="['tag', item.tag?.type]" :style="item.tag?.style">{{
          item.tag?.txt
        }}</div>
        <div v-if="item.value" class="value-box">
          <div class="value">{{ item.value }}</div>
          <div v-if="item.unit" class="unit">{{ item.unit }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { StatusTagBoxItem } from './hooks';
  import { PropType } from 'vue';
  defineProps({
    item: {
      type: Object as PropType<StatusTagBoxItem>,
      default: () => ({}),
    },
  });
</script>
<style lang="less" scoped>
  .card-box {
    display: flex;
    align-items: center;
    padding: 12px 8px;
    width: 165px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;

    .label-box {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      overflow: hidden;

      .label-item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        font-size: 14px;
        overflow: hidden;

        .label {
          display: flex;
          align-items: center;
          color: #333;
          font-weight: 600;
          margin-bottom: 12px;

          .txt {
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.1;
            overflow: hidden;
          }

          .after-label-image {
            margin-left: 4px;
            width: 16px;
            height: 14px;
          }
        }

        .tag {
          padding: 4px 8px;
          width: max-content;
          font-size: 12px;
          border-radius: 4px;
          line-height: 1;

          &.success {
            color: #4db803;
            background-color: rgba(77, 184, 3, 0.12);
          }

          &.error {
            color: #ff522b;
            background-color: rgba(255, 82, 43, 0.12);
          }

          &.info {
            color: #333;
            background-color: #eeeff1;
          }

          &.normal {
            color: var(--theme-color);
            background-color: rgba(11, 98, 203, 0.12);
          }

          &.blue {
            color: #fff;
            background-color: var(--theme-color);
            cursor: pointer;
          }

          &.red {
            color: #fff;
            background-color: #ff522b;
            cursor: pointer;
          }
        }

        .value-box {
          display: flex;
          line-height: 1;
          align-items: end;
          white-space: nowrap;

          .value {
            color: #333;
            font-size: 16px;
            font-weight: 600;
          }

          .unit {
            margin-left: 4px;
            color: #999999;
          }
        }
      }
    }
  }
</style>
