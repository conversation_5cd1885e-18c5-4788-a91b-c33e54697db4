<template>
  <div class="time-column">
    <div class="list">
      <div
        :class="{ item: true, item_hmtime: dataTimeType > 2 }"
        v-for="(item, index) in data"
        :key="index"
      >
        <div class="item-content">
          <img :src="item.icon" alt="" class="icon" />
          <div class="time" :class="{ timeName: item.timeName }">
            {{ item.timeName ? `${item.timeName} - ${item.time}` : item.time }}
          </div>
        </div>
        <div class="box-remark">
          <!-- <div
            :style="{ cursor: item.isShowDuty === 1 && dataTimeType === 2 ? '' : 'not-allowed' }"
            @click.stop="changeRemark(item)"
          > -->
          <div
            class="btn"
            v-if="item.timeName"
            :class="{ isBefore: item.isBefore }"
            @click.stop="changeRemark(item)"
          >
            <!-- 过了值班时间可以填日志 -->
            <Tooltip placement="topLeft" :align="{ offset: [-6, 0] }">
              <template #title v-if="item.isBefore">填写日志</template>
              <Icon icon="icon-park-outline:edit-two" />
            </Tooltip>
          </div>
          <!-- 过了值班时间可以插补数据 -->
          <div
            class="copy-data btn"
            :class="{
              isBefore: item.isBefore,
              isEmpty: item.isBefore && (item.isEmpty || !getIsHaveEmptyValue(index)),
              index0: index == 0,
            }"
            @click.stop="copyPreviousLineData(item, index)"
          >
            <Tooltip placement="topLeft" :align="{ offset: [-6, 0] }">
              <template #title v-if="item.isBefore && !item.isEmpty && index !== 0">
                插补数据
              </template>
              <Icon icon="icon-park-outline:copy" />
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="last-row">合计</div> -->
  </div>
</template>

<script setup lang="ts">
  import { PropType, watch, getCurrentInstance, nextTick, inject } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { SAVE_REPORT_DATA } from '/@zhcz/views/data-management/constant';
  import { BasicQueryParamsSymbol } from '/@zhcz/views/data-management/injectionSymbols';
  import { useEmitt } from '/@/hooks/web/useEmitt';

  const instance = getCurrentInstance();
  let props = defineProps({
    data: {
      type: Array as PropType<Recordable[]>,
      default: () => [],
    },
    showHistory: {
      type: Boolean,
      default: false,
    },
    dataTimeType: {
      type: Number,
      default: Infinity,
    },
    allData: {
      type: Array,
      default: () => [],
    },
  });

  const emit = defineEmits(['log', 'copy']);
  const basicQueryParams = inject(BasicQueryParamsSymbol) as Ref<Recordable>;

  watch(() => props.allData, getRows);
  let rowArr = [];
  function getRows() {
    if (props.allData.length < 2) return;
    let allData = props.allData.slice(1);
    rowArr = new Array(allData[0].data.length).fill('').map(() => []);

    allData.forEach((cols) => {
      cols.data.forEach((item, colIndex) => {
        rowArr[colIndex].push(item);
      });
    });
  }

  function getIsHaveEmptyValue(currentIndex) {
    if (!currentIndex || !rowArr.length) return false;
    let preRowData = rowArr[currentIndex - 1];
    let currentRowData = rowArr[currentIndex];
    // 当前单元格没有数据 且 前一行对应单元格有数据
    let isHaveEmptyValue = currentRowData.some((i, index) => !i.value && preRowData[index].value);
    return isHaveEmptyValue;
  }

  function changeRemark(item) {
    if (item.isShowDuty !== 1) return;
    emit('log', item);
  }

  function copyPreviousLineData(item, index) {
    if (item.isShowDuty !== 1 || index === 0) return;
    emit('copy', { item, index });
  }

  const { emitter } = useEmitt();

  emitter.on(SAVE_REPORT_DATA + basicQueryParams.value.timeType, (data) => {
    if (!data.value) {
      updateRowData(data);
    }
    nextTick(() => {
      instance.proxy.$forceUpdate();
    });
  });
  function updateRowData(data) {
    for (let i = 0; i < rowArr.length; i++) {
      for (let j = 0; j < rowArr[i].length; j++) {
        if (
          rowArr[i][j].provideTime === data.provideTime &&
          rowArr[i][j].indicatorCode === data.indicatorCode
        ) {
          rowArr[i][j] = { ...data };
          return;
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .time-column {
    // padding: 16px 16px 0 24px;

    .list {
      // position: relative;
      // display: flex;
      // flex-direction: column;
      // gap: 32px;

      // &::before {
      //   position: absolute;
      //   top: 0;
      //   bottom: 0;
      //   left: 8px;
      //   display: block;
      //   content: '';
      //   width: 1px;
      //   background-color: rgba(0, 0, 0, 0.1);
      //   z-index: 1;
      // }
      .item_hmtime {
        justify-content: left !important;

        .box-remark {
          padding-left: 16px;
        }
      }

      .item {
        padding: 0 20px 0 24px;
        height: 49px;
        width: 100%;

        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;

        &:not(&:last-of-type)::after {
          content: '';
          position: absolute;
          top: calc(50% + 8.5px);
          height: 32px;
          left: 31px;
          width: 1px;
          background-color: rgba(0, 0, 0, 0.1);
        }

        .item-content {
          position: relative;
          display: flex;
          align-items: center;
          gap: 8px;
          // height: 32px;
          z-index: 2;
          background-color: #ffffff;

          .icon {
            width: 16px;
            height: 16px;
          }

          .time {
            width: 64px;
            font-size: 14px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            line-height: 24px;
            height: 24px;
            color: #333;
            border-radius: 12px;
            background-color: #f0f0f0;
            text-align: center;
            user-select: none;

            &.timeName {
              width: 108px;
            }
          }
        }

        .box-remark {
          font-size: 14px;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 400;
          display: flex;
          gap: 16px;
          // width: 48px;
          align-items: center;
          margin-left: 6px;

          .btn {
            cursor: not-allowed;
            color: #999999;

            &.isBefore {
              color: @theme-color;
              cursor: pointer;
            }

            &.isEmpty {
              color: @theme-color;
              cursor: not-allowed;
              opacity: 0.56;
            }

            &.index0.isBefore {
              color: @theme-color;
              cursor: not-allowed;
              opacity: 0.56;
            }
          }
        }
      }
    }

    .last-row {
      margin-top: 16px;
      padding: 0 16px 0 24px;
      height: 60px;
      line-height: 60px;
      font-size: 14px;
      color: #666666;
    }
  }
</style>
