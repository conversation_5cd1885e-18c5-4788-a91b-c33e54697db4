<template>
  <div class="center-index">
    <span class="name" v-show="data.indexName">
      <Tooltip @mouseenter="showTooltip">
        <template #title>{{ data.indexName }}</template>
        {{ data.indexName }}
      </Tooltip>
    </span>
    <span class="value">
      {{ roundAndConvertCheckNullAndUnDef(data.value, getDecimalPlaces(data.indexName || '')) }}
    </span>
    <span class="unit">{{ data.unitName }}</span>
  </div>
</template>

<script lang="ts" setup>
  import { Tooltip } from 'ant-design-vue';
  import { roundAndConvertCheckNullAndUnDef } from '/@zhcz/utils/number';

  defineProps({
    data: {
      type: Object,
      default: () => ({
        indexName: '沉前余氯',
        value: '2.96',
        unitName: 'mg/L',
      }),
    },
  });

  function showTooltip(e) {
    if (e.target.clientWidth >= e.target.scrollWidth) {
      e.target.style.pointerEvents = 'none';
    }
  }
  function getDecimalPlaces(name: string) {
    if (name.includes('工单')) {
      return 0;
    } else if (name.includes('故障')) {
      return 0;
    }

    return 2;
  }
</script>

<style lang="less" scoped>
  .center-index {
    // width: 16.5rem;
    // max-width: 16.5rem;
    width: 100%;
    // height: 2.5rem;
    display: flex;
    align-items: center;

    @media screen and (max-width: 1855px) {
      // 0.9
      .name {
        width: 7.11rem;
      }

      .unit {
        max-width: 3.15rem;
      }
    }

    @media screen and (max-width: 1770px) {
      // 0.85

      .name {
        // 0.9
        width: 7.11rem;
      }

      .unit {
        max-width: 2.975rem;
      }
    }

    @media screen and (max-width: 1440px) {
      // 0.7

      .name {
        // 0.9
        width: 5.53rem;
      }

      .unit {
        // 0.85
        max-width: 2.975rem;
      }
    }

    .name {
      width: 7.9rem;
      font-size: 0.875rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .value {
      // padding-right: 0.5rem;
      flex: 1;
      font-size: 1rem;
      font-family: SF Pro-Compressed Regular, SF Pro;
      font-weight: 410;
      text-align: right;
      color: #1fc3a4;
    }

    .unit {
      padding-left: 0.5rem;
      max-width: 3.5rem;
      font-size: 0.875rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      text-align: right;
    }
  }
</style>
