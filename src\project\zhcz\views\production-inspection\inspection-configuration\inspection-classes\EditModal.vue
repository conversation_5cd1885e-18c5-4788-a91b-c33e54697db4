<template>
  <BasicModal v-bind="$attrs" @cancel="handleCancel" @register="register" width="30%">
    <BasicForm @register="registerForm">
      <template #nameId="{ model, field }">
        <Select
          v-model:value="model[field]"
          allow-clear
          :options="classList"
          placeholder="请选择"
        />
      </template>
      <template #fkInspectGroupId="{ model, field }">
        <Select
          v-model:value="model[field]"
          allow-clear
          :options="inspectGroupList"
          @change="handleChangeInspectGroup"
          placeholder="请选择"
        />
      </template>
      <template #userIds="{ model, field }">
        <Select
          v-model:value="model[field]"
          allow-clear
          showArrow
          mode="multiple"
          max-tag-count="responsive"
          :options="userOptions"
          placeholder="请选择"
        />
      </template>
      <template #beginTime="{ model, field }">
        <TimePicker
          v-model:value="model[field]"
          format="HH:mm"
          :showNow="false"
          :disabledHours="disabledBeginHours"
          :disabledMinutes="disabledBeginMinutes"
          placeholder="请选择"
          @change="handleChangeBeginTimeRangePicker"
          @openChange="handleOpenBeginTimeRangePicker"
          @click="handleChangeBeginTimeRangePicker"
        />
      </template>
      <template #endTime="{ model, field }">
        <span style="margin-left: -16px; margin-right: 5px">~</span>
        <TimePicker
          v-model:value="model[field]"
          format="HH:mm"
          :showNow="false"
          :disabledHours="disabledEndHours"
          :disabledMinutes="disabledEndMinutes"
          placeholder="请选择"
          @change="handleChangeEndTimeRangePicker"
          @openChange="handleOpenEndTimeRangePicker"
        />
      </template>
    </BasicForm>
    <template #footer>
      <a-button class="default-btn" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">保存</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { Select } from 'ant-design-vue';
  import TimePicker from 'ant-design-vue/es/time-picker';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formSchema } from './data';
  import dayjs from 'dayjs';
  import {
    addInspectClasses,
    updateInspectClasses,
  } from '/@zhcz/api/production-inspection/inspection-team';

  const props = defineProps({
    inspectGroupList: {
      type: Array as any,
      default: () => [],
    },
    classList: {
      type: Array as any,
      default: () => [],
    },
    allUserList: {
      type: Array as any,
      default: () => [],
    },
  });

  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();

  const userOptions = ref([]);
  const detailInfo = ref<any>({});
  const [register, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ title: data.form ? '编辑班次' : '新增班次' });
    resetFields();
    clearValidate();
    if (data.form) {
      const currentUserIdsStr = props.inspectGroupList.find(
        (item) => item.value === data.form.fkInspectGroupId,
      )?.userIds;
      if (currentUserIdsStr) {
        const userList = currentUserIdsStr?.split(',');
        userOptions.value = userList.map((item) => {
          return {
            label: props.allUserList.find((i) => i.userId === item)?.name,
            value: item,
          };
        });
      }
      setFieldsValue({ ...data.form });
    }
    detailInfo.value = data?.form;
  });

  const [registerForm, { resetFields, setFieldsValue, clearValidate, validate, getFieldsValue }] =
    useForm({
      schemas: formSchema,
      labelWidth: 107,
      showActionButtonGroup: false,
    });
  const okLoading = ref(false);
  async function handleSubmit() {
    const values = await validate();
    const { userIds } = values;
    okLoading.value = true;
    try {
      let params: any = {};
      if (detailInfo.value?.id) {
        params = {
          id: detailInfo.value.id,
          ...values,
          userIds: userIds.join(','),
          beginTime: dayjs(values.beginTime).format('HH:mm:ss'),
          endTime: dayjs(values.endTime).format('HH:mm:ss'),
        };
      } else {
        params = {
          ...values,
          userIds: userIds.join(','),
          beginTime: dayjs(values.beginTime).format('HH:mm:ss'),
          endTime: dayjs(values.endTime).format('HH:mm:ss'),
        };
      }
      delete params.date;
      detailInfo.value?.id ? await updateInspectClasses(params) : await addInspectClasses(params);
    } finally {
      okLoading.value = false;
    }
    createMessage.success('操作成功');
    emit('success');
    handleCancel();
  }

  const handleChangeInspectGroup = (_, option) => {
    userOptions.value = [];
    setFieldsValue({ userIds: [] });
    const userList = option?.userIds?.split(',');
    userOptions.value = userList.map((item) => {
      return {
        label: props.allUserList.find((i) => i.userId === item)?.name,
        value: item,
      };
    });
  };

  const beginTimeHour = ref<number>();
  const endTimeHour = ref<number>();
  const beginTimeMinute = ref<number>();
  const endTimeMinute = ref<number>();

  const handleChangeBeginTimeRangePicker = (time) => {
    console.log('time', time);
    const timeFormat = dayjs(time).format('HH:mm');
    beginTimeHour.value = Number(timeFormat.split(':')[0]);
    beginTimeMinute.value = Number(timeFormat.split(':')[1]);
  };

  const handleOpenBeginTimeRangePicker = (open) => {
    if (open) {
      const values = getFieldsValue();
      const { beginTime, endTime } = values;
      if (beginTime && beginTimeHour.value === undefined && beginTimeMinute.value === undefined) {
        const time = dayjs(beginTime).format('HH:mm');
        beginTimeHour.value = Number(time.split(':')[0]);
        beginTimeMinute.value = Number(time.split(':')[1]);
      }
      if (endTime && endTimeHour.value === undefined && endTimeMinute.value === undefined) {
        const time = dayjs(endTime).format('HH:mm');
        endTimeHour.value = Number(time.split(':')[0]);
        endTimeMinute.value = Number(time.split(':')[1]);
      }
    }
  };
  const handleChangeEndTimeRangePicker = (time) => {
    const timeFormat = dayjs(time).format('HH:mm');
    endTimeHour.value = Number(timeFormat.split(':')[0]);
    endTimeMinute.value = Number(timeFormat.split(':')[1]);
  };

  const handleOpenEndTimeRangePicker = (open) => {
    if (open) {
      const values = getFieldsValue();
      const { beginTime, endTime } = values;
      if (beginTime && beginTimeHour.value === undefined && beginTimeMinute.value === undefined) {
        const time = dayjs(beginTime).format('HH:mm');
        beginTimeHour.value = Number(time.split(':')[0]);
        beginTimeMinute.value = Number(time.split(':')[1]);
      }
      if (endTime && endTimeHour.value === undefined && endTimeMinute.value === undefined) {
        const time = dayjs(endTime).format('HH:mm');
        endTimeHour.value = Number(time.split(':')[0]);
        endTimeMinute.value = Number(time.split(':')[1]);
      }
    }
  };

  const disabledBeginHours = () => {
    let hours: number[] = [];
    if (endTimeHour.value !== null && endTimeHour.value !== undefined) {
      for (let i = endTimeHour.value + 1; i <= 24; i++) {
        hours.push(i);
      }
    }
    return hours;
  };

  const disabledBeginMinutes = (selectedHour) => {
    let minutes: number[] = [];
    if (endTimeHour.value !== undefined && endTimeMinute.value !== undefined) {
      if (selectedHour === endTimeHour.value) {
        for (let i = endTimeMinute.value + 1; i <= 59; i++) {
          minutes.push(i);
        }
      } else if (selectedHour < endTimeHour.value) {
        minutes = [];
      }
    }
    return minutes;
  };

  const disabledEndHours = () => {
    let hours: number[] = [];
    if (beginTimeHour.value !== null && beginTimeHour.value !== undefined) {
      for (let i = 0; i < beginTimeHour.value; i++) {
        hours.push(i);
      }
    }
    return hours;
  };

  const disabledEndMinutes = (selectedHour) => {
    let minutes: number[] = [];
    if (beginTimeHour.value !== undefined && beginTimeMinute.value !== undefined) {
      if (selectedHour === beginTimeHour.value) {
        for (let i = 0; i < beginTimeMinute.value; i++) {
          minutes.push(i);
        }
      } else if (selectedHour > beginTimeHour.value) {
        minutes = [];
      }
    }
    return minutes;
  };

  function handleCancel() {
    beginTimeHour.value = undefined;
    endTimeHour.value = undefined;
    beginTimeMinute.value = undefined;
    endTimeMinute.value = undefined;
    userOptions.value = [];

    closeModal();
  }
</script>

<style lang="less" scoped></style>
