<template>
  <div class="personnel-right">
    <BoxHeader title="工作人员" />
    <div style="position: relative; height: calc(100% - 48px)">
      <template v-if="dataLoading">
        <div style="background: unset"></div>
      </template>
      <template v-else>
        <div class="content">
          <WorkForce v-for="(item, index) in list" :key="index" :parentIndex="index" :data="item" />
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onUnmounted } from 'vue';
  import { orderBy } from 'lodash-es';
  import { getAllUserList, getDeptList } from '/@zhcz/api/warehouse';
  import { mockWorkerData } from './data';
  import { useEmitt } from '/@/hooks/web/useEmitt';

  import BoxHeader from '../components/box-container/BoxHeader.vue';
  import WorkForce from './components/work-force/index.vue';

  const list = ref<any[]>([]);
  const deptList = ref<any[]>([]);
  const dataLoading = ref<Boolean>(true);
  function getDeptName(id: string) {
    const dept = deptList.value.find((item) => item.deptId === id);
    return dept?.name ?? '';
  }

  const getData = async () => {
    dataLoading.value = true;
    const depts = await getDeptList();
    const users = await getAllUserList();
    dataLoading.value = false;
    deptList.value = depts.filter((item) => item.parentId != '0' && item.deptId != null);
    if (!deptList.value.length) {
      list.value = mockWorkerData;
      return;
    }
    deptList.value = orderBy(deptList.value, ['sortOrder']);

    deptList.value.map((item) => {
      item.identity = getDeptName(item.deptId);
      item.persons = [];
      item.persons = users
        .filter((user) => user.deptId == item.deptId)
        .map((user) => ({ ...user, identity: getDeptName(user.deptId) }));
    });

    if (depts && users) {
      list.value = deptList.value;
    }
    console.log('list.value', list.value);
  };

  getData();

  const { emitter } = useEmitt();
  emitter.on('bi:change-factory', () => {
    getData();
  });

  onUnmounted(() => {
    emitter.off('bi:change-factory');
  });
</script>

<style lang="less" scoped>
  .personnel-right {
    position: relative;
    height: 100%;
    background: linear-gradient(160deg, rgba(0, 63, 137, 0.04) 1%, rgba(31, 132, 250, 0.24) 97%);
    border: 1px solid;
    border-image: linear-gradient(
        180deg,
        rgba(151.1946925520897, 208.2876169681549, 255, 0),
        rgba(151.00000619888306, 208.0000028014183, 255, 0.4000000059604645)
      )
      1 1;
    border-radius: 8px 8px 8px 8px;

    .content {
      height: 100%;
      overflow-y: auto;
      padding: 1.5rem 0 0 0rem;

      &::-webkit-scrollbar {
        width: 0px;
      }
    }
  }
</style>
