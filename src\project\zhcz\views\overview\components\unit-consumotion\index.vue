<template>
  <div class="unit-consumption">
    <div class="unit-consumption-ul">
      <div class="unit-consumption-li" v-for="(item, index) in dataList" :key="index">
        <div class="li-top">
          <span>
            {{ item.indexName }}
          </span>
          <span>
            {{
              item.unitName == '次' || item.unitName == '个'
                ? roundAndConvertCheckNullAndUnDef(item.value, 0)
                : roundAndConvertCheckNullAndUnDef(item.value, 2)
            }}{{ item.unitName }}
          </span>
        </div>
        <div class="li-bottom">
          <span class="progress-bar-container">
            <i
              class="progress-bar"
              :style="{ width: item.percentage }"
              :class="`progress-bar${index % 4}`"
            ></i>
            <div class="mid-line" v-if="item.value * 1"></div>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { roundAndConvertCheckNullAndUnDef } from '/@zhcz/utils/number';

  defineProps({
    dataList: {
      type: Array as any,
      default: () => [],
    },
  });
</script>

<style lang="less" scoped>
  @import '../../styles/publicFunction.less';

  .unit-consumption {
    width: 100%;
    height: 100%;
    padding: 1rem 0rem 0 0rem;

    .unit-consumption-ul {
      height: 100%;
      overflow-y: auto;
      // --scrollbar: red;
      // .defaultScrollbarStyle(8px, rgba(250, 250, 250, 0.56), rgba(250, 250, 250, 0.16), 3px);
      &::-webkit-scrollbar {
        width: 0px !important;
      }

      &::-webkit-scrollbar-track {
        background-color: rgba(45, 64, 102, 1) !important;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(163, 169, 185, 1) !important;
        // background: rgba(63, 194, 37, 0.56) ;
        // border-radius: @radius;
        &:hover {
          background: rgba(213, 216, 223, 1) !important; /* 设置滚动条的悬停颜色 */
        }
      }

      // &::-webkit-scrollbar {
      //   display: none; /* Chrome, Safari, Opera*/
      // }
      /* stylelint-disable-next-line no-duplicate-selectors */
      // & {
      // scrollbar-width: 8px; /* 设置滚动条的宽度 */
      // scrollbar-color: rgba(250, 250, 250, 0.56) rgba(250, 250, 250, 0.16); /* 设置滚动条的颜色 */
      // }
      // &:hover {
      //   scrollbar-color: rgba(250, 250, 250, 0.8) rgba(250, 250, 250, 0.16); /* 设置滚动条的悬停颜色 */
      // }
      // &::-webkit-scrollbar {
      //   width: 6px;
      //   height: 6px;
      // }

      // &::-webkit-scrollbar-thumb {
      //   background-color: red;
      //   border-radius: 3px;
      //   border: none;
      // }

      // &::-webkit-scrollbar-thumb:hover {
      //   background-color: green;
      // }

      // &::-webkit-scrollbar-track {
      //   border-radius: 0px;
      //   background-color: transparent;
      // }

      .unit-consumption-li {
        &:not(:last-child) {
          margin-bottom: 1rem;
        }

        .li-top {
          display: flex;
          justify-content: space-between;
          font-size: 0.875rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 1.375rem;
        }

        .li-bottom {
          margin-top: 0.475rem;
          height: 1.375rem;
          background: rgba(255, 255, 255, 0.12);
          border-radius: 4px;
          opacity: 1;
          border: 1px solid rgba(255, 255, 255, 0.101);
          box-sizing: border-box;
          padding: 0 0.4375rem;

          .progress-bar-container {
            @keyframes bar {
              0% {
                width: 0;
              }
            }
            background: rgba(255, 255, 255, 0.24);
            border-radius: 2px;
            // border: 1px solid rgba(255, 255, 255, 0.101);
            opacity: 1;
            // 进度条容器
            float: left;
            width: 100%;
            height: 0.5rem;
            margin-top: 0.375rem;
            display: flex;

            .progress-bar {
              // 进度条
              display: block;
              height: 100%;
              //border-radius: 5px;
              width: 0%;
              transition: all 2s;
              animation-name: bar;
              animation-duration: 2s;
              animation-timing-function: ease-in;
              background: linear-gradient(
                270deg,
                rgba(254, 145, 45, 1) 0%,
                rgba(254, 145, 45, 0.2) 100%
              );
            }

            .progress-bar0 {
              background: rgba(45, 130, 254, 1);
            }

            .progress-bar1 {
              background: rgba(254, 197, 45, 1);
            }

            .progress-bar2 {
              background: rgba(31, 195, 164, 1);
            }

            .progress-bar3 {
              background: rgba(253, 165, 78, 1);
            }

            .mid-line {
              position: relative;
              bottom: 3px;
              height: 0.875rem;
              width: 2px;
              background: rgba(255, 255, 255, 0.8);
              border-radius: 2px;
            }
          }
        }
      }
    }
  }
</style>
