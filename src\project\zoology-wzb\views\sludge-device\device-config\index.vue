<template>
  <PageWrapper dense contentFullHeight fixedHeight contentClass="flex">
    <div :class="prefixCls">
      <div class="page-content">
        <!-- 左侧设备菜单 -->
        <div class="type-box" v-loading="loading">
          <div class="title">
            <span class="text"> 设备类型 </span>
          </div>
          <div class="search">
            <Input allowClear placeholder="请输入" v-model:value="nameKeywords" @change="onSearch">
              <template #prefix>
                <Icon icon="icon-park-outline:search" color="#8f959e" size="14" />
              </template>
            </Input>
          </div>
          <div class="list-box">
            <div class="empty-box" v-if="!loading && !typeList.length">
              <img :src="emptyImg" />
              <div class="text">暂无数据</div>
            </div>
            <VScroll
              :itemHeight="44"
              :items="typeList"
              height="100%"
              width="100%"
              :bench="30"
              v-if="typeList.length"
            >
              <template #default="{ item }">
                <div
                  class="item"
                  @click="setTypeItem(item)"
                  :class="{ active: item.id == actTypeItem?.id, hover: item.showDropdown }"
                >
                  <span class="text overflow-hidden whitespace-nowrap text-ellipsis">
                    {{ item.equipmentTypeName }}
                  </span>
                </div>
              </template>
            </VScroll>
          </div>
        </div>
        <!-- 右侧内容 -->
        <div class="type-entry-box">
          <BasicTable @register="registerTable" class="type-table">
            <template #tableTitle>
              <a-button
                v-if="actTypeItem?.systemFlag !== '1'"
                type="primary"
                @click="openEditModal(null)"
                class="mt-2 flex items-center"
                :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
              >
                新增
              </a-button>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      label: '编辑',
                      onClick: openEditModal.bind(null, record?.id),
                    },
                    {
                      label: '删除',
                      popConfirm: {
                        title: '是否确认删除',
                        placement: 'left',
                        confirm: handleDeleteItem.bind(null, record),
                      },
                    },
                  ]"
                />
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
    </div>
    <!-- 新增、编辑配置弹窗 -->
    <EditModal @register="registerModal" @success="reload" />
  </PageWrapper>
</template>

<script lang="ts" setup>
  import { debounce } from 'lodash-es';
  import Icon from '/@/components/Icon';
  import EditModal from './components/EditModal/EditModal.vue';
  import { onBeforeMount, ref, h } from 'vue';
  import { useModal } from '/@/components/Modal';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { VScroll } from '/@/components/VirtualScroll';
  import { Input } from 'ant-design-vue';
  import emptyImg from '/@/assets/images/table-empty.png';
  import { columns, searchFormSchema } from './data';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import {
    searchEquipmentConfigListApi,
    deleteEquipmentConfigApi,
    searchEquipmentTypeOptionsApi,
  } from '/@zoology-wzb/api/device';
  const { prefixCls } = useDesign('sludge-device-config');

  const loading = ref(false);
  const nameKeywords = ref('');
  const typeList = ref([]); //左侧设备菜单列表
  const actTypeItem = ref(); //左侧设备菜单选中项

  const [registerModal, { openModal }] = useModal();
  const { createMessage } = useMessage();

  const [registerTable, { reload, setPagination }] = useTable({
    api: searchEquipmentConfigListApi,
    immediate: false,
    columns: columns,
    bordered: true,
    resizeHeightOffset: 80,
    showIndexColumn: false,
    pagination: { pageSize: 20 },
    formConfig: {
      schemas: searchFormSchema,
      labelCol: {
        span: 6,
      },
    },
    fetchSetting: {
      pageField: 'current',
      sizeField: 'size',
      listField: 'records',
      totalField: 'total',
    },
    beforeFetch: (params) => {
      return { ...params, equipmentTypeId: actTypeItem.value?.id };
    },
    useSearchForm: true,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
    indexColumnProps: {
      width: 85,
      align: 'left',
    },
  });

  /**
   * @description: 获取设备类型列表
   */
  const getTypeList = async () => {
    try {
      loading.value = true;
      typeList.value = await searchEquipmentTypeOptionsApi({
        equipmentTypeName: nameKeywords.value,
      });
      return typeList.value;
    } finally {
      loading.value = false;
    }
  };

  const onSearch = debounce(getTypeList, 500);

  /**
   * @description: 设置左侧设备菜单选中项
   */
  const setTypeItem = (item) => {
    if (!item) return;
    actTypeItem.value = item || {};
    const { id } = item;
    if (id) {
      setPagination({ current: 1 });
      reload();
    }
  };

  function openEditModal(itemId) {
    openModal(true, {
      id: itemId,
      equipmentTypeId: actTypeItem.value?.id,
    });
  }

  async function handleDeleteItem(record: Recordable) {
    await deleteEquipmentConfigApi([record.id]);
    createMessage.success('操作成功');
    reload();
  }

  onBeforeMount(async () => {
    await getTypeList();
    setTypeItem(typeList.value?.[0]);
  });
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-sludge-device-config';

  .@{prefix-cls} {
    padding: 0 16px 16px;
    width: 100%;
    height: 100%;

    .page-content {
      background-color: @white;
      border-radius: 4px;
      height: 100%;
      display: flex;
      width: 100%;
      overflow: hidden;

      .type-box {
        width: 256px;
        height: 100%;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;

        .title {
          padding: 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .text {
            font-size: 16px;
            line-height: 1;
            font-weight: bold;
          }

          .btn {
            display: flex;
            align-items: center;
            color: @theme-color;
            line-height: 1;
            font-size: 14px;
            cursor: pointer;
          }
        }

        .search {
          margin: 0 16px 12px;
        }

        .list-box {
          flex: 1;
          overflow: hidden;

          .empty-box {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            width: 100%;
            height: 100%;
            color: rgba(255, 255, 255, 0.6);

            img {
              width: 100px;
              height: 100px;
            }

            .text {
              font-size: 14px;
              color: #999999;
            }
          }

          .item {
            margin-left: 16px;
            width: 224px;
            height: 40px;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
            cursor: default;

            .text {
              font-size: 14px;
              color: #333;
              flex: 1;
            }

            .btn {
              width: 16px;
              height: 16px;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.2s;
              display: none;
              opacity: 0;

              &.hover,
              &:hover {
                display: flex;
                opacity: 1;

                .dot {
                  background-color: @theme-color;
                }
              }

              .dot {
                width: 3px;
                height: 3px;
                border-radius: 50%;
                background-color: #333333;
                margin-right: 3px;

                &:last-of-type {
                  margin-right: 0;
                }
              }
            }

            &.active {
              background-color: @theme-color-12p;

              .text {
                color: @theme-color;
              }
            }

            &:hover,
            &.hover {
              .btn {
                display: flex;
                opacity: 1;
              }

              &:not(.active) {
                background-color: #f0f0f0;
              }
            }
          }
        }
      }

      .type-entry-box {
        flex: 1;
        overflow: hidden;
        border-left: #e9e9e9 1px solid;

        :deep(.type-table) {
          padding: 0;

          .ant-form {
            margin-bottom: 0;
            border-bottom: #e9e9e9 1px solid;
            border-radius: 0;
          }

          .ant-table-wrapper {
            padding-top: 8px;
          }
        }
      }
    }
  }
</style>
