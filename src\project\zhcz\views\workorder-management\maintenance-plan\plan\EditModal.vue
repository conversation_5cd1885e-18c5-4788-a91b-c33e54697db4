<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    width="55%"
    ref="modalRef"
  >
    <div class="container">
      <BasicForm @register="registerForm">
        <template #planFactoryId="{ model, field, schema }">
          <Select
            v-model:value="model[field]"
            disabled
            :placeholder="schema.placeholder ?? '请选择'"
            :options="factoryInfoList"
            :field-names="{ label: 'factoryName', value: 'factoryId' }"
          />
        </template>
        <template #planName="{ model, field, schema }">
          <Input
            v-model:value="model[field]"
            :disabled="isEdit"
            allowClear
            :placeholder="schema.placeholder ?? '请输入'"
          />
        </template>
        <template #name="{ model, field, schema }">
          <Select
            v-model:value="model[field]"
            @change="handleChangeMaintenanceName"
            allowClear
            :placeholder="schema.placeholder ?? '请选择'"
          >
            <SelectOption
              v-for="item in maintenanceNameOptions"
              :value="item.value"
              :key="item.value"
              >{{ item.label }}</SelectOption
            >
          </Select>
        </template>
        <template #type="{ model, field }">
          <Select v-model:value="model[field]" disabled>
            <SelectOption
              v-for="item in overhaulTypeOptions"
              :value="item.value"
              :key="item.value"
              >{{ item.label }}</SelectOption
            >
          </Select>
        </template>
        <template #eqType="{ model, field, schema }">
          <Select
            v-model:value="model[field]"
            allowClear
            :placeholder="schema.placeholder ?? '请选择'"
          >
            <SelectOption v-for="item in eqCategoryOptions" :value="item.value" :key="item.value">{{
              item.label
            }}</SelectOption>
          </Select>
        </template>
        <template #project-table>
          <div class="project-table">
            <BasicTable @register="registerProjectTable">
              <template #tableTitle>
                <div class="text-right w-full">
                  <AButton class="add-btn-n" type="ghost" @click="handleAddDevice">新增 </AButton>
                  <DeviceSelectModal @register="registerDeviceModal" @ok="handleChangeDevice" />
                </div>
              </template>
            </BasicTable>
          </div>
        </template>
        <template #planStartTime="{ model, field }">
          <DatePicker
            v-model:value="model[field]"
            :disabled-date="disabledDate"
            format="YYYY-MM-DD"
            valueFormat="YYYY-MM-DD 00:00:00"
            @change="handleChangePlanStartTime"
          />
        </template>
        <template #planEndTime="{ model, field }">
          <DatePicker
            v-model:value="model[field]"
            :disabled-date="disabledDate"
            format="YYYY-MM-DD"
            valueFormat="YYYY-MM-DD 23:59:59"
            @change="handleChangePlanEndTime"
          />
        </template>
        <template #cronExpression="{ model, field }">
          <div class="cronExpression">
            <div class="cronExpression-ipt">
              <Popover
                trigger="click"
                placement="topLeft"
                :getPopupContainer="(target) => target.parentNode"
              >
                <template #content>
                  <CronInput
                    v-model:value="model[field]"
                    style="width: 100%"
                    :week-by-num="cronForm.weekByNum"
                    :sundayIndex="cronForm.sundayIndex"
                    :item="cronForm.item"
                    :year-start="cronForm.yearStart"
                    :lang="cronForm.lang"
                    @change="handleChangeCronExpressionModal"
                  />
                </template>
                <Input
                  v-model:value="model[field]"
                  placeholder="0 0 8 * * ?"
                  @change="handleChangeCronExpression"
                />
              </Popover>
              <a-button
                :loading="checkLoading"
                @click="createExecutionDescription"
                :icon="h(Icon, { icon: 'icon-park-outline:doc-detail' })"
                ghost
                type="primary"
                >生成计划描述</a-button
              >
            </div>
            <Space class="mt-2">
              <Alert
                class="tip-alert"
                v-if="formulaVerifyInfo.code !== null && model[field]"
                :message="formulaVerifyInfo.message"
                :type="formulaVerifyInfo.type"
                show-icon
              />
            </Space>
          </div>
        </template>
      </BasicForm>
    </div>
    <template #footer>
      <a-button class="default-btn" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">保存</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, reactive, nextTick, h, computed } from 'vue';
  import dayjs, { Dayjs } from 'dayjs';
  import {
    Button,
    Input,
    Select,
    SelectOption,
    DatePicker,
    Popover,
    Space,
    Alert,
  } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { Icon } from '/@/components/Icon';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  import { createLocalStorage } from '/@/utils/cache';
  import { FACTORY_KEY } from '/@/enums/cacheEnum';
  import {
    createMaintenancePlan,
    updateMaintenancePlan,
    getMaintenancePlanDetail,
    getMaintenanceTypesList,
    getMaintenancePlanExecutionDescription,
  } from '/@zhcz/api/workorder-management';
  import { schemas, projectColumns } from './data';
  import CronInput from '/@zhcz/components/CronInput/index.vue';
  import DeviceSelectModal from '/@zhcz/business-components/components/Form/DeviceSelectModal.vue';
  import { getDevicePageListApi } from '/@zhcz/api/device-management';
  import { uniqBy } from 'lodash-es';

  const AButton = Button;
  const userStore = useUserStore();
  const ls = createLocalStorage();
  const userInfo = computed(() => userStore.getUserInfo);
  const factoryInfoList = computed(() => userInfo.value.factoryInfoList || []);
  const factoryId = ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId;

  const disabledDate = (current: Dayjs) => {
    return current && current < dayjs().startOf('day');
  };

  const emit = defineEmits(['success', 'register']);
  defineProps({
    overhaulTypeOptions: {
      type: Array as any,
      default: () => [],
    },
    eqCategoryOptions: {
      type: Array as any,
      default: () => [],
    },
  });

  const cronForm = reactive({
    lang: 'cn',
    // item: ['second', 'minute', 'hour', 'day', 'month', 'week', 'year'],
    item: ['hour', 'day', 'month', 'week', 'year'],
    weekByNum: false,
    sundayIndex: 0,
    yearStart: new Date().getFullYear(),
  });

  const maintenanceNameOptions = ref<
    { value: string; label: string; eqpList: Indexable[]; type: string }[]
  >([]);
  const title = ref('');
  const isEdit = ref(false);
  let editData = reactive({
    id: null,
  });

  const { createMessage } = useMessage();

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    getMaintenanceName();
    isEdit.value = data.isEdit;

    if (data.isEdit) {
      title.value = '编辑维保计划';
      await getDetail(data.data.id);
      await setFieldsValue(data);
      editData.id = data.data.id;
    } else {
      title.value = '新增维保计划';
      setFieldsValue({
        cronExpression: '0 0 8 * * ?',
        planFactoryId: String(factoryId),
      });
    }
  });
  const [registerDeviceModal, { openModal: openDeviceModal }] = useModal();

  const checkLoading = ref(false);
  const formulaVerifyInfo = reactive<{
    code: number | null;
    type: 'error' | 'warning' | 'success' | 'info' | undefined;
    message: string;
  }>({
    code: null,
    type: undefined,
    message: '',
  });

  const [registerForm, { resetFields, setFieldsValue, getFieldsValue, validate, clearValidate }] =
    useForm({
      labelWidth: 120,
      schemas: schemas,
      showActionButtonGroup: false,
    });

  const projectTableData = ref<Indexable[]>([]);
  const [registerProjectTable, { reload, redoHeight, setTableData, getDataSource }] = useTable({
    dataSource: projectTableData.value,
    columns: projectColumns,
    maxHeight: 80,
    pagination: false,
    useSearchForm: false,
    showIndexColumn: true,
    indexColumnProps: {
      width: 100,
    },
    showTableSetting: false,
  });

  const modalRef = ref(null);
  const scrollBottom = () => {
    modalRef.value?.modalWrapperRef.scrollBottom();
  };

  async function handleSubmit() {
    try {
      const values = await validate();
      await createExecutionDescription();
      setModalProps({ confirmLoading: true });
      delete values.blank1;
      delete values.blank2;
      delete values.blank4;
      if (formulaVerifyInfo.code != 1) {
        return;
      }

      const executorUserIds = values.executorUserIds.map((item) => item.userId).join(',');
      const dispatchUserIds = values.dispatchUserIds.map((item) => item.userId).join(',');
      const responsibilityUserIds = values.responsibilityUserIds
        .map((item) => item.userId)
        .join(',');
      const acceptanceUserIds = values.acceptanceUserIds.map((item) => item.userId).join(',');
      const dataList = getDataSource();
      if (!dataList || !dataList.length) {
        return createMessage.warning('请添加保养设备');
      }
      values.eqpList = dataList.map((item) => item.eqpId);

      const params = isEdit.value
        ? {
            ...values,
            executorUserIds,
            dispatchUserIds,
            responsibilityUserIds,
            acceptanceUserIds,
            id: editData.id,
            eqType: values.eqType ?? '',
          }
        : {
            ...values,
            executorUserIds,
            dispatchUserIds,
            responsibilityUserIds,
            acceptanceUserIds,
          };
      isEdit.value ? await updateMaintenancePlan(params) : await createMaintenancePlan(params);
      await handleCancel();
      createMessage.success('操作成功');
      emit('success');
    } catch (error: any) {
      const errorFields = error.errorFields;
      if (
        errorFields.every(
          (item) =>
            item.name[0] === 'executionDescription' ||
            item.name[0] === 'planStartTime' ||
            item.name[0] === 'cronExpression',
        )
      ) {
        scrollBottom();
      }
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  function handleCancel() {
    resetFields();
    projectTableData.value = [];
    formulaVerifyInfo.code = null;
    isEdit.value = false;
    editData.id = null;
    setFieldsValue({
      executorUserIds: [],
      dispatchUserIds: [],
      responsibilityUserIds: [],
      acceptanceUserIds: [],
    });
    setTableData([]);
    closeModal();
    clearValidate();
  }

  const getDetail = async (id) => {
    const data = await getMaintenancePlanDetail({ id });
    const findItem = maintenanceNameOptions.value.find(
      (item) => item.value === data.maintenanceTypeId,
    );
    const executorUserIds =
      data.executorUserList && data.executorUserList.length
        ? data.executorUserList.map((item) => {
            return {
              userId: item.userId,
              name: item.username,
            };
          })
        : [];
    const dispatchUserIds =
      data.dispatchUserList && data.dispatchUserList.length
        ? data.dispatchUserList.map((item) => {
            return {
              userId: item.userId,
              name: item.username,
            };
          })
        : [];
    const responsibilityUserIds =
      data.responsibilityUserList && data.responsibilityUserList.length
        ? data.responsibilityUserList.map((item) => {
            return {
              userId: item.userId,
              name: item.username,
            };
          })
        : [];
    const acceptanceUserIds =
      data.acceptanceUserList && data.acceptanceUserList.length
        ? data.acceptanceUserList.map((item) => {
            return {
              userId: item.userId,
              name: item.username,
            };
          })
        : [];
    setFieldsValue({
      ...data,
      eqType: String(data.eqType),
      type: findItem?.type || '',
      planFactoryId: String(data.factoryId),
      executorUserIds,
      dispatchUserIds,
      responsibilityUserIds,
      acceptanceUserIds,
    });

    projectTableData.value = isEdit.value ? data.eqpList : findItem?.eqpList || [];
    await nextTick(() => {
      setTableData(projectTableData.value);
      reload();
      redoHeight();
    });
  };

  // 保养类别
  const getMaintenanceName = async () => {
    const data = await getMaintenanceTypesList();
    maintenanceNameOptions.value = data.map((item) => {
      return {
        value: item.id,
        label: item.name,
        eqpList: item.eqpList || [],
        type: String(item.type),
      };
    });
  };

  const handleChangeMaintenanceName = async (value) => {
    const findItem = maintenanceNameOptions.value.find((item) => item.value === value);

    const projectData = findItem?.eqpList || [];
    projectTableData.value = projectData;
    setFieldsValue({
      type: findItem ? findItem.type : undefined,
      planName: dayjs().format('YYYYMMDD') + findItem?.label + '保养计划',
    });

    await nextTick(() => {
      setTableData(projectData);
      reload();
      redoHeight();
    });
  };

  const createExecutionDescription = async () => {
    const values = getFieldsValue();
    console.log('values', values);
    if (!values.planStartTime) {
      createMessage.warning('开始时间不能为空');
      return;
    }
    if (!values.cronExpression) {
      createMessage.warning('cron表达式不能为空');
      return;
    }
    checkLoading.value = true;
    const params = {
      cron: values.cronExpression,
      startDate: dayjs(values.planStartTime).format('YYYY-MM-DD 00:00:00'),
      endDate: dayjs(values.planEndTime).format('YYYY-MM-DD 23:59:59'),
    };
    try {
      const data = await getMaintenancePlanExecutionDescription(params);
      formulaVerifyInfo.code = 1;
      formulaVerifyInfo.type = 'success';
      formulaVerifyInfo.message = `校验通过`;
      setFieldsValue({
        executionDescription: data,
      });
    } catch (_) {
      formulaVerifyInfo.code = 0;
      formulaVerifyInfo.type = 'error';
      formulaVerifyInfo.message = `校验不通过`;
      setFieldsValue({
        executionDescription: '',
      });
    } finally {
      checkLoading.value = false;
    }
  };
  const handleChangeCronExpressionModal = (value) => {
    formulaVerifyInfo.code = null;
    setFieldsValue({
      executionDescription: '',
      cronExpression: value,
    });
    clearValidate();
  };

  const handleChangeCronExpression = () => {
    formulaVerifyInfo.code = null;
    setFieldsValue({
      executionDescription: '',
    });
    clearValidate();
  };

  const handleChangePlanStartTime = () => {
    formulaVerifyInfo.code = null;
    setFieldsValue({
      executionDescription: '',
    });
    clearValidate();
  };
  const handleChangePlanEndTime = () => {
    formulaVerifyInfo.code = null;
    setFieldsValue({
      executionDescription: '',
    });
    clearValidate();
  };

  function handleAddDevice() {
    openDeviceModal(true, {});
  }
  async function handleChangeDevice(selects = []) {
    const ret = await getDevicePageListApi({
      ids: selects.reverse(),
    });
    if (ret) {
      const res = ret.map((item) => {
        return {
          ...item,
          eqpId: item.id,
          name: item.equipmentName,
        };
      });
      const list = getDataSource() || [];
      const data = res.concat(list);

      setTableData(uniqBy(data, 'eqpId'));
    }
  }
</script>
<style lang="less" scoped>
  .project-table {
    :deep(.ant-table-wrapper) {
      margin: 0;
      padding: 0;
    }
  }

  .add-btn-n {
    border-color: @theme-color;
    color: @theme-color;

    &:hover {
      background: @theme-color-8p;
    }
  }

  .cronExpression {
    .cronExpression-ipt {
      position: relative;
      display: flex;
      align-items: center;
      gap: 0 12px;

      :deep(.ant-popover) {
        .ant-popover-inner {
          padding: 16px;
        }
      }
    }
  }

  .tip-alert {
    height: 32px;
  }
</style>
