<template>
  <div class="p-4 w-full h-full flex flex-col">
    <!-- <div class="px-30">
      <ChartLegend
        :data="legends"
        theme="light"
        :colors="colors"
        @legend-click="handleLegendClick"
      />
    </div> -->
    <div class="flex-1 echart">
      <Echarts :options="options" :legend="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, nextTick, PropType, watch, onMounted } from 'vue';
  import { groupBy, cloneDeep, uniq, merge } from 'lodash-es';
  import Echarts from './components/echart/index.vue';
  import { radomNumber, isEmpty, getYAxisInterval } from './utils';
  import { colorList } from './data';
  import {
    xAxisLight as xAxis,
    yAxisLight as yAxis,
    lineSeries,
    getYAxisDataInteger,
  } from './echarts';
  import { Legend } from './types';

  const props = defineProps({
    data: {
      type: Array as PropType<Recordable[]>,
      required: true,
    },
    first: <PERSON>olean,
    yAxisName: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
  });

  const emits = defineEmits(['update:first']);

  const color = [
    ['rgba(43, 99, 161, 0)', 'rgba(43, 99, 161, 0.8)', 'rgba(43, 99, 161, 1)'],
    ['rgba(252, 124, 34, 0)', 'rgba(252, 124, 34, 0.8)', 'rgba(252, 124, 34, 1)'],
    ['rgba(31, 195, 164, 0)', 'rgba(31, 195, 164, 0.8)', 'rgba(31, 195, 164, 1)'],
  ];

  const colors = ref(color.concat(colorList));

  const chartOptions = ref({
    xAxis: {
      data: ['11-01', '11-02', '11-03', '11-04', '11-05', '11-06', '11-07'],
    },
    yAxis: [cloneDeep(yAxis)],
    series: [
      {
        name: '碳滤后水流量',
        color: colors.value[0][2],
        data: Array.from({ length: 7 }, () => radomNumber(10, 50)),
      },
    ],
  });
  const legends = ref<Legend[]>([]);
  const options = ref({});
  const empty = ref(false);

  watch(
    () => props.data,
    () => {
      init(props.first);
    },
  );

  onMounted(() => {
    init(props.first);
  });

  async function init(first: boolean) {
    await nextTick();
    empty.value = isEmpty(props.data, 'data');
    if (!empty.value) {
      const data = props.data as Recordable[];
      const supplementaryData = supplementaryUnit(data);
      if (first) setLegendsData(supplementaryData);
      const newData = getFilterData(supplementaryData, legends.value);
      setXAxisData(newData[0].data, 'datetime');
      setYAxisData(newData, 'unit');
      setSeriesData(newData, 'indexName', 'data', 'value');
      setOptions();
    }
  }

  function getFilterData(data: Recordable[], legends: Legend[]) {
    const names = legends.filter((item) => item.show).map((i) => i.name);
    return data.filter((item) => names.includes(item.indexName));
  }

  function setOptions() {
    const yLength = chartOptions.value.yAxis.length;
    const yAxis = getYAxisDataInteger(cloneDeep(chartOptions.value.yAxis), props.yAxisName);
    const _xAxis_ = merge(cloneDeep(xAxis), {
      data: chartOptions.value.xAxis.data,
    });

    const option = {
      tooltip: {
        trigger: 'axis',
        // appendToBody: true,
        formatter: (params) => {
          function getVal(value, seriesName) {
            return value === undefined || value === ''
              ? '-'
              : (seriesName.includes('次') && !seriesName.includes('次氯酸钠')) ||
                seriesName.includes('数量')
              ? Number(value).toFixed(0)
              : Number(value).toFixed(2);
          }

          function getUnit(seriesName: string, value) {
            if (value === undefined || value === '') return '';
            const item: any = option.series.find((i: any) => i.indexName === seriesName);

            const str = item?.unitName || '';
            // 达标率(%) 括号内位单位，没有括号为空
            return str.includes('(') ? str.split('(')[1].split(')')[0] : str;
          }

          function getItem(index: number) {
            const item = params[index];
            return `
            <div style="display: flex; align-items: center;">
              ${item.marker}
              <span>${item.seriesName}</span>
              <span style="padding-left: 16px; display: flex; flex: 1;justify-content: flex-end;">
                <span style="font-weight: 600">${getVal(item.value, item.seriesName)}</span>
                <span>${getUnit(item.seriesName, item.value)}</span>
              </span>
            </div>`;
          }

          return `
            <div>${params[0].name}</div>
            ${params.map((_, index) => getItem(index)).join('')}
          `;
        },
      },
      legend: {
        show: true,
        top: 0,
        icon: 'circle',
        itemGap: 8,
        itemWidth: 12,
        itemHeight: 12,
        // padding: [0, 30, 0, 150],
        textStyle: {
          fontSize: 12,
          lineHeight: 13,
          // color: 'rgba(255, 255, 255, 0.8)',
        },
      },
      grid: {
        left: 20,
        // right: (yLength - 1) * 40,
        right: 20 * yLength,
        top: 40,
        bottom: 0,
        containLabel: true,
      },
      xAxis: _xAxis_,
      yAxis: yAxis.map((item) => ({
        ...item,
        axisLabel: {
          ...item.axisLabel,
          formatter: (value) => {
            const unit = item?.name || '';
            if ((unit.includes('次') && !unit.includes('次氯酸钠')) || unit.includes('个')) {
              return value.toFixed(0);
            }
            return value.toFixed(2);
          },
        },
      })),
      series: chartOptions.value.series,
    };

    options.value = option;
  }

  function setXAxisData(data: Recordable[], key: string) {
    chartOptions.value.xAxis.data = data.map((item) => item[key]);
  }

  function setYAxisData(data: Recordable[], key: string) {
    function getOffset(index) {
      const category = Math.ceil((index - 1) / 2);
      return category > 0 ? category * 80 : 0;
    }
    const names = uniq(data.map((item) => item[key]));
    const group = groupBy(data, (item) => item.unit);
    const numberArr = Object.keys(group).map((key) => {
      const result = group[key].map((item) => {
        const number = item.data.map((i) => i.value);
        return number;
      });
      return result;
    });

    const result: any[] = names.map((item, index) => {
      let min = getYAxisInterval(numberArr[index])?.min;
      let max = getYAxisInterval(numberArr[index])?.max;
      min = item && (item.includes('个') || (item.includes('次') && min < 0)) ? 0 : min;

      if (min === max) {
        max = (min + 0.1) * 1.2;
      }

      const data = {
        ...cloneDeep(yAxis),
        name: !item ? '' : `${item}`,
        position: index > 0 ? 'right' : 'left',
        nameTextStyle: {
          padding: index > 0 ? [0, -40, 0, 0] : [0, 20, 0, 0],
        },
        offset: getOffset(index),
        min: min,
        max: max,
        interval: (max - min) / 5,
      };
      return data;
    });
    chartOptions.value.yAxis = result;
  }

  function setSeriesData(data: Recordable[], nameKey: string, dataKey: string, valueKey: string) {
    const yNames = chartOptions.value.yAxis.map((i) => i.name);
    const result: any = data.map((item: Recordable, index) => {
      const name = item[nameKey];
      const findItem = legends.value.find((legend) => legend.name === name) as Legend;
      const color = findItem.color;
      const yAxisIndex = yNames.findIndex((i) => i === item.unit);
      const series: Recordable = {
        ...cloneDeep(lineSeries),
        name,
        color,
        data: item[dataKey].map((i) => i[valueKey]),
        indexName: item.indexName,
        unitName: item.unit,
        areaStyle: {
          opacity: 0.1,
        },
        zlevel: -1,
        z: -1,
        markPoint: {},
        markLine: {},
      };

      const hasData = item.data.some((i) => i.value);
      if (hasData) {
        series.markPoint = {
          symbol: 'diamond',
          symbolSize: [16, 12],
          symbolOffset: -8,
          animation: false,
          label: {
            fontSize: 14,
            position: 'top',
            distance: -8,
            height: 24,
            backgroundColor: colors.value[index % 17][2],
            lineHeight: 22,
            width: 50,
            borderRadius: 4,
          },
          data: [
            {
              type: 'max',
              name: 'Max',
              label: { color: '#fff' },
            },
            {
              type: 'min',
              name: 'Min',
              label: { color: '#fff' },
            },
          ],
          emphasis: { disabled: false },
        };

        series.markLine = {
          symbol: 'arrow',
          symbolSize: 10,
          data: [
            {
              type: 'average',
              name: 'Avg',
            },
          ],
          label: {
            position: index % 2 === 0 ? 'insideEndTop' : 'insideEndBottom',
            fontSize: 14,
            distance: 12,
            color: '#ffffff',
            textBorderColor: colors.value[index % 17][2],
            textBorderWidth: 2,
          },
        };
      }

      if (yAxisIndex !== -1) {
        series.yAxisIndex = yAxisIndex;
      } else {
        const yEmptyAxisIndex = yNames.findIndex((i) => i === '');
        series.yAxisIndex = yEmptyAxisIndex !== -1 ? yEmptyAxisIndex : 0;
      }

      return series;
    });

    chartOptions.value.series = result;
  }

  function supplementaryUnit(data: Recordable[]) {
    return data.map((item) => {
      const newItem = { ...item };
      // if (!item.unit && item.indexName.includes(searchKey)) {
      //   newItem.unit = searchKey;
      // }

      return newItem;
    });
  }

  function setLegendsData(data: Recordable[]) {
    const result = data.map((item, index) => {
      return {
        name: item.indexName,
        // show: index + 1 > divide ? false : true,
        show: true,
        color: colors.value[index % 17][2],
      };
    });
    legends.value = result;
    emits('update:first', false);
  }

  // function handleLegendClick(data: Legend[]) {
  //   legends.value = data;
  //   init(false);
  // }
</script>

<style lang="less" scoped>
  .chartLegend {
    :deep(.chart-legend) {
      display: flex !important;
      flex-wrap: wrap;
    }
  }

  .echart {
    :deep(.w-full) {
      & > div {
        overflow: auto !important;
      }
    }
  }
</style>
