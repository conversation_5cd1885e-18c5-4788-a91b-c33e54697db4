<template>
  <div class="frequency-monitor">
    <HLCard title="频率监测" :styleData="{ borderRadius: '4px' }">
      <template #defaultBody>
        <TableSimple
          :dataSource="list"
          :columns="tableColumns"
          :showIndexColumn="false"
          :pagination="false"
        />
      </template>
    </HLCard>
  </div>
</template>
<script lang="ts" setup>
  import { HLCard } from '/@/project/zhcz/components/HLCard';
  import { TableSimple } from '/@zhcz/components/HLCardTable';
  import { ref, h } from 'vue';

  const emits = defineEmits(['onDetail']);
  defineProps({
    list: {
      type: Array,
      default: () => [],
    },
  });

  const tableColumns = ref([
    {
      title: '名称',
      dataIndex: 'indexName',
      // fixed: 'left',
      width: 274,
    },
    {
      title: '给定值',
      dataIndex: 'value',
      customRender: ({ record }) => {
        let style = record.alertStyle;
        if (
          record.maxVal &&
          record.minVal &&
          record.value <= record.maxVal &&
          record.value >= record.minVal
        ) {
          style = record.normalStyle;
        }
        if (typeof style != 'object') style = {};
        style.cursor = 'pointer';
        return h(
          'span',
          {
            style,
            onClick() {
              emits('onDetail', record);
            },
          },
          record.value ?? '-',
        );
      },
      width: 130,
    },
    {
      width: 130,
      title: '反馈值',
      dataIndex: 'value2',
      customRender: ({ record }) => {
        let style = record.alertStyle2;
        if (
          record.maxVal &&
          record.minVal &&
          record.value <= record.maxVal &&
          record.value >= record.minVal
        ) {
          style = record.normalStyle2;
        }
        if (typeof style != 'object') style = {};
        style.cursor = 'pointer';
        return h(
          'span',
          {
            style,
            onClick() {
              emits('onDetail', { indexCode: record.indexCode2, indexName: record.indexName });
            },
          },
          record.value2 ?? '-',
        );
      },
    },
  ]);
</script>
<style lang="less" scoped>
  .frequency-monitor {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 560px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
  }
</style>
