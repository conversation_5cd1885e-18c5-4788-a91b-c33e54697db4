<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    width="55%"
    ref="modalRef"
  >
    <div class="container">
      <BasicForm @register="registerForm">
        <template #planFactoryId="{ model, field }">
          <Select v-model:value="model[field]" disabled :options="factoryList" />
        </template>
        <template #recordType="{ model, field }">
          <div class="cronExpression">
            <div class="cronExpression-ipt">
              <Select
                v-model:value="model[field]"
                placeholder="请选择"
                show-search
                @change="changeDeptId"
              >
                <SelectOption
                  v-for="item in deptIdobj.deptIdList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.label }}</SelectOption
                >
              </Select>
              <Popover
                v-model:visible="visible"
                :getPopupContainer="(target) => target.parentNode"
                title="分类项:"
                trigger="click"
              >
                <template #content>
                  <div class="container">
                    <Input v-model:value="addType" placeholder="请输入" :maxlength="20" />
                  </div>
                  <div class="add-popover-foot">
                    <Button class="default-btn" @click="handleClose">取消</Button>
                    <Button type="primary" @click="handleSave">确认</Button>
                  </div>
                </template>
                <Button
                  ghost
                  style="width: 72px"
                  :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
                  type="primary"
                  @click="handleCreate"
                  >新增</Button
                >
              </Popover>

              <!-- :style="{
                opacity: !model[field] ? 0.8 : 1,
                cursor: !model[field] ? 'no-drop' : 'pointer',
              }" -->
              <Button
                style="width: 72px"
                ghost
                danger
                type="primary"
                :icon="h(Icon, { icon: 'icon-park-outline:delete-one' })"
                :disabled="!model[field]"
                @click="model[field] ? delType() : ''"
                >删除</Button
              >
            </div>
          </div>
        </template>
        <template #planName="{ model, field }">
          <Input v-model:value="model[field]" :disabled="isEdit" allowClear placeholder="请输入" />
        </template>
        <template #recordContent="{ model, field }">
          <Textarea v-model:value="model[field]" :maxlength="200" placeholder="请输入" />
        </template>
        <template #type="{ model, field }">
          <Select v-model:value="model[field]" disabled>
            <SelectOption
              v-for="item in overhaulTypeOptions"
              :value="item.value"
              :key="item.value"
              >{{ item.label }}</SelectOption
            >
          </Select>
        </template>
        <!-- <template #recordTime="{ model, field }">
          <a-date-picker
        v-model:value="value1"
        format="YYYY-MM-DD HH:mm:ss"
        :disabled-date="disabledDate"
        :disabled-time="disabledDateTime"
        :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }"
      />
        </template> -->
        <template #eqType="{ model, field }">
          <Select v-model:value="model[field]" allowClear placeholder="请选择">
            <SelectOption v-for="item in eqCategoryOptions" :value="item.value" :key="item.value">{{
              item.label
            }}</SelectOption>
          </Select>
        </template>
      </BasicForm>
    </div>
    <template #footer>
      <a-button class="default-btn" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">确认</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup name="SchedulingManagementChroniclesOfShift">
  import { ref, reactive, h, defineProps } from 'vue';
  import { Input, Select, SelectOption, Textarea, Popover } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  import { createLocalStorage } from '/@/utils/cache';
  import { FACTORY_KEY } from '/@/enums/cacheEnum';
  import { Button } from '/@/components/Button';
  import Icon from '/@/components/Icon/src/Icon.vue';
  import dayjs from 'dayjs';
  import {
    addOrUpdateScheduleRecord,
    getAllRecordType,
    getScheduleRecordById,
    getScheduleRecordByTime,
  } from '/@/project/zhcz/api/scheduling/index';
  import { schemas } from './data';
  interface labelData {
    label: string;
    value: string;
    origin: boolean;
  }
  let deptIdobj = reactive<{
    deptIdList: labelData[];
  }>({
    deptIdList: [],
  });

  const visible = ref<boolean>(false);
  const addType = ref<string>('');
  function handleSave(): void {
    if (addType.value) {
      handleSuccess(addType.value);
      createMessage.success('操作成功');
    } else {
      createMessage.warning('请先输入分类项');
    }
  }
  function handleClose(): void {
    addType.value = '';
    visible.value = false;
  }
  const handleCreate = () => {
    visible.value = true;
  };
  const handleSuccess = async (values) => {
    const data = [values];
    let copyIdList: string[] = [];
    if (deptIdobj.deptIdList.length > 0) {
      copyIdList = deptIdobj.deptIdList.map((item: labelData) => item.label);
    }
    data.forEach((item: string) => {
      !copyIdList.includes(item)
        ? deptIdobj.deptIdList.push({ label: item, value: item, origin: false })
        : '';
    });
    handleClose();
  };

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const ls = createLocalStorage();

  const factoryInfoList = userInfo.factoryInfoList;
  const factoryId = ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId;
  const factoryList =
    factoryInfoList &&
    factoryInfoList.map((item) => ({ value: item.factoryId, label: item.factoryName }));

  const emit = defineEmits(['success', 'register']);
  const props = defineProps({
    overhaulTypeOptions: {
      type: Array as any,
      default: () => [],
    },
    eqCategoryOptions: {
      type: Array as any,
      default: () => [],
    },
    dataType: {
      type: String,
      default: 'day',
    },
  });
  const title = ref('');
  const isEdit = ref(false);
  let editData = reactive({
    id: null,
  });

  const { createMessage, createConfirm } = useMessage();

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    // getMaintenanceName();
    await handleSearch();
    // console.log('data', data, data.data);
    // schemas[0].componentProps.disabled = false;
    if (data.isEdit) {
      title.value = '编辑当班记事';
      if (props.dataType === 'hours') {
        data.recordTime = data.date;
        // schemas[0].componentProps.disabled = true;
        await getDetail({
          factoryId: '1',
          time: dayjs(data.date).format('YYYY-MM-DD HH:mm:ss'),
        });
      } else {
        await getDetail(data.id);
      }
      await setFieldsValue(data);
      editData.id = data.id;
    } else {
      title.value = '新增当班记事';
      setFieldsValue({
        // deptIdList: [],
        // dutyId: [],
      });
    }
    isEdit.value = data.isEdit;
  });

  const checkLoading = ref(false);
  const formulaVerifyInfo = reactive<{
    code: number | null;
    type: 'error' | 'warning' | 'success' | 'info' | undefined;
    message: string;
  }>({
    code: null,
    type: undefined,
    message: '',
  });

  const [registerForm, { resetFields, setFieldsValue, getFieldsValue, validate, clearValidate }] =
    useForm({
      labelWidth: 120,
      schemas: schemas,
      showActionButtonGroup: false,
    });

  const projectTableData = ref<{ name: string }[]>([]);

  const modalRef = ref(null);
  // const scrollBottom = () => {
  //   modalRef.value?.modalWrapperRef.scrollBottom();
  // };

  async function handleSubmit() {
    try {
      const values = await validate();
      await createExecutionDescription();
      if (props.dataType === 'hours') {
        values.recordTime = dayjs(values.recordTime).format('YYYY-MM-DD HH:00:00');
      }
      setModalProps({ confirmLoading: true });
      const dutyId = values.dutyId.map((item) => item.userId).join(',');
      const params = isEdit.value
        ? {
            ...values,
            dutyId,
            id: editData.id,
          }
        : {
            ...values,
            dutyId,
          };
      await addOrUpdateScheduleRecord(params);
      await handleCancel();
      createMessage.success('操作成功');
      emit('success');
    } catch (error: any) {
      console.log(error);
      // const errorFields = error.errorFields;
      // if (
      //   errorFields.every(
      //     (item) =>
      //       item.name[0] === 'executionDescription' ||
      //       item.name[0] === 'recordTime' ||
      //       item.name[0] === 'cronExpression',
      //   )
      // ) {
      //   scrollBottom();
      // }
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  function handleCancel() {
    resetFields();
    projectTableData.value = [];
    formulaVerifyInfo.code = null;
    isEdit.value = false;
    editData.id = null;
    setFieldsValue({
      // deptIdList: [],
      dutyId: [],
    });
    closeModal();
    clearValidate();
  }

  const getDetail = async (id: any) => {
    const api = props.dataType === 'hours' ? getScheduleRecordByTime : getScheduleRecordById;
    const data = await api(id);
    let dutyId: { userId: any; name: string }[] = [];
    dutyId =
      data.dutyPersonList && data.dutyPersonList.length
        ? data.dutyPersonList.map((item) => {
            return {
              userId: item.userId,
              name: item.name,
            };
          })
        : [];
    if (data.id) {
      setFieldsValue({
        ...data,
        eqType: String(data.eqType),
        dutyId,
      });
    }
  };

  const changeDeptId = (value: string): void => {
    console.log('value', value);
  };
  const handleSearch = async () => {
    const params = factoryId;
    const data = await getAllRecordType(params);
    let copyIdList: string[] = [];
    deptIdobj.deptIdList = [];
    if (deptIdobj.deptIdList.length > 0) {
      copyIdList = deptIdobj.deptIdList.map((item: labelData) => item.label);
    }
    data.forEach((item: string) => {
      if (item) {
        !copyIdList.includes(item)
          ? deptIdobj.deptIdList.push({ label: item, value: item, origin: true })
          : '';
      }
    });
  };
  const okLoading = ref(false);
  const delType = async () => {
    createConfirm({
      iconType: 'info',
      title: '提示',
      content: '是否确认删除？',
      onOk: async () => {
        okLoading.value = true;
        const values = await getFieldsValue();
        if (values.recordType) {
          let flag = false;
          deptIdobj.deptIdList.forEach((item) => {
            if (item.value === values.recordType) {
              if (!item.origin) {
                flag = true;
              } else {
                createMessage.warning('只能删除当前新增的类型');
              }
            }
          });
          if (flag) {
            deptIdobj.deptIdList = deptIdobj.deptIdList.filter(
              (item) => item.value !== values.recordType,
            );
            createMessage.success('操作成功');
            setFieldsValue({
              recordType: '',
            });
            okLoading.value = false;
          }
        } else {
          createMessage.warning('类型不能为空');
          okLoading.value = false;
          return;
        }
      },
    });
  };

  const createExecutionDescription = async () => {
    const values = getFieldsValue();
    if (!values.recordTime) {
      createMessage.warning('日期不能为空');
      return;
    }
    if (!values.planType) {
      createMessage.warning('班次不能为空');
      return;
    }
    if (!values.recordContent) {
      createMessage.warning('内容不能为空');
      return;
    }
    if (values.dutyId.length < 1) {
      createMessage.warning('值班人不能为空');
      return;
    }
    checkLoading.value = true;
  };
</script>
<style lang="less" scoped>
  .project-table {
    :deep(.ant-table-wrapper) {
      margin: 0;
      padding: 0;
    }
  }

  .cronExpression {
    .cronExpression-ipt {
      position: relative;
      display: flex;
      align-items: center;
      // gap: 0 12px;
      :deep(.ant-btn) {
        margin-left: 12px;
      }

      :deep(.ant-popover) {
        .ant-popover-inner {
          padding: 16px;
          width: 300px;

          .ant-popover-title {
            margin-bottom: 12px;
          }

          .container {
            .ant-input {
              margin-bottom: 16px;
            }
          }

          .add-popover-foot {
            display: flex;
            justify-content: end;

            .ant-btn {
              padding: 9px 22px;
              border-radius: 4px;

              &:last-child {
                margin-left: 16px;
              }
            }
          }
        }
      }
    }
  }

  .tip-alert {
    height: 32px;
  }
</style>
