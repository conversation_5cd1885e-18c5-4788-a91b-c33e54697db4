<template>
  <div style="height: 100%">
    <PieSimpleCard
      v-bind="{
        bottomList: indexList,
        empty,
        loading,
        pieType: 'vertical',
        title: '设备类型占比',
        series,
      }"
    />
  </div>
</template>
<script lang="ts" setup>
  import { PieSimpleCard } from '/@zhcz/components/HLCardComponent';
  import { ref } from 'vue';
  import { searchEqptApi } from '/@zoology-wzb/api/device';
  // 设置饼图配置
  const series = {
    roseType: 'radius',
    radius: ['22%', '100%'],
  };
  const loading = ref(false);
  const empty = ref(false);
  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);
  const getData = async () => {
    try {
      empty.value = false;
      loading.value = true;

      const res = await searchEqptApi(1);
      loading.value = false;
      if (res?.length) {
        empty.value = false;
        indexList.value = res.map((item) => {
          return {
            indexName: item.indexName,
            indexCode: item.indexCode,
            unitName: item.unitName,
            value: item.value,
            ratio: item.rate,
          };
        });
      } else {
        indexList.value = [];
        empty.value = true;
      }
    } catch (err) {
      empty.value = true;
      loading.value = false;
    }
  };
  getData();
</script>
