<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    :loading="okLoading"
    width="750px"
    wrapClassName="checklist-template-modal"
  >
    <BasicForm @register="registerForm">
      <template #template-slot="{ model, field }">
        <Select
          v-model:value="model[field]"
          show-search
          :default-active-first-option="false"
          show-arrow
          :filter-option="false"
          :options="assayTemplateList"
          :disabled="isEdit"
          style="width: calc(100% - 24px)"
          @search="handleSearchAssayTemplate"
          @change="handleChangeTemplate"
          placeholder="请选择"
        />
      </template>
      <template #table-slot>
        <div class="table-container">
          <div class="table-inner">
            <div class="col-container">
              <div v-for="(colItem, colIndex) in colsData" :key="colIndex" class="col-item">
                <template v-if="colItem.unit">
                  {{ colItem.value + '(' + colItem.unit + ')' }}
                </template>
                <template v-else>
                  {{ colItem.value }}
                </template>
              </div>
            </div>
            <div class="row-container">
              <div class="row-container">
                <div v-for="(rowItem, rowIndex) in tableData" :key="rowIndex" class="row-item">
                  <div
                    v-for="(colItem, colIndex) in rowItem.cells"
                    :key="colItem.sort"
                    class="value-item"
                  >
                    <template v-if="colIndex === 0">
                      <span v-if="colItem.unit">
                        {{ colItem.value + '(' + colItem.unit + ')' }}
                      </span>
                      <span v-else>
                        {{ colItem.value }}
                      </span>
                    </template>
                    <template v-else>
                      <!-- v-if="colItem.cellStyleType === 0" -->
                      <InputNumber v-model:value="colItem.value" :step="0.01" :min="0" />
                      <!-- <InputSelect v-else v-model="colItem.value" :options="colItem.enumOptions" /> -->
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </BasicForm>
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">确定</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup name="TemplateModal">
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicForm, useForm } from '/@/components/Form';
  import { templateSchemas } from '../data';
  import {
    getAllAssaysProjects,
    getAllAssayTemplatesByPage,
    getAssayTemplateDetail,
    createAssayDocument,
    updateAssayDocument,
  } from '/@zhcz/api/chemical';
  import { Select, InputNumber } from 'ant-design-vue';
  import { useDebounceFn } from '@vueuse/core';
  // import InputSelect from './InputSelect.vue';
  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    baseColProps: { span: 12 },
    schemas: templateSchemas,
    showActionButtonGroup: false,
    labelAlign: 'left',
  });
  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const colsData: any = ref([]);
  const tableData: any = ref([]);
  const isEdit = ref(false);
  const okLoading = ref(false);
  const assayTemplateList = ref([]);
  const rowDenormalized = ref(false);
  const title = computed(() => (isEdit.value ? '编辑化验单' : '新增'));
  const isTest = ref(false);

  const [registerModal, { closeModal }] = useModalInner((data) => {
    isEdit.value = !!data?.isEdit;
    isTest.value = !!data?.isTest;
    updateSchema([
      {
        field: 'conclusion',
        show: isEdit.value || isTest.value,
      },
      {
        field: 'table',
        show: isEdit.value || isTest.value,
      },
    ]);
    if (isEdit.value || data.isTest) {
      setFieldsValue({
        id: data.record.id,
        templateId: data.record.templateId,
        inspectionTitle: data.record.title,
        conclusion: data.record.conclusion,
      });
      if (data.record.config) {
        const config = JSON.parse(data.record.config);
        colsData.value = config.colsData;
        tableData.value = config.tableData;
        rowDenormalized.value = config.rowDenormalized;
      } else {
        handleChangeTemplate(data.record.templateId);
      }
    }
  });

  const handleChangeTemplate = async (id) => {
    okLoading.value = true;

    try {
      const data = await getAssayTemplateDetail(id);
      const { config, inspectionTitle } = data;

      // 过滤单位
      const cols = config.find((i) => !i.projectId).cells.filter((j) => j.value !== '单位');
      const rows = config.filter((i) => i.projectId);

      // 模板中的枚举code
      const enumCodes = rows
        .filter((i) => i.cells[0].cellStyleType === 1)
        .map((i) => i.cells[0].code);

      rowDenormalized.value = data.rowDenormalized;

      if (data.rowDenormalized) {
        processRowDenormalized(cols, rows);
      } else {
        processRowNormalized(cols, rows);
      }

      setFieldsValue({ inspectionTitle });

      const res = await getAllAssaysProjects({ codes: enumCodes });

      populateEnumOptions(res, data.rowDenormalized);

      updateSchema([
        {
          field: 'conclusion',
          show: true,
        },
        {
          field: 'table',
          show: true,
        },
      ]);
      okLoading.value = false;
    } catch (error) {
      console.error(error);
      okLoading.value = false;
    }
  };

  const processRowDenormalized = (cols, rows) => {
    colsData.value = rows.map((row) => ({
      value: row.cells[0].value,
      unit: row.cells[1].value,
      cellStyleType: row.cells[0].cellStyleType,
      code: row.cells[0].code,
    }));

    colsData.value.unshift({ value: '采集点' });

    const newRows = cols.filter((col) => col.nodeId);
    newRows.forEach((row) => {
      row.cells = [{ value: row.value }];
      for (let i = 1; i < colsData.value.length; i++) {
        row.cells.push({ cellStyleType: colsData.value[i].cellStyleType, value: '' });
      }
    });

    tableData.value = newRows;
  };

  const processRowNormalized = (cols, rows) => {
    colsData.value = cols;

    rows.forEach((row) => {
      row.cells[0].unit = row.cells[1].value;
      row.cells.splice(1, 1);
    });

    tableData.value = rows;
  };

  const populateEnumOptions = (res, rowDenormalized) => {
    tableData.value.forEach((row) => {
      row.cells.forEach((cell, cellIndex) => {
        if (cellIndex > 0) {
          const enumOptions = res
            .find(
              (i) =>
                i.code === (rowDenormalized ? colsData.value[cellIndex].code : row.cells[0].code),
            )
            ?.list.map((j) => ({ label: j.enumKey, value: j.enumName }));

          cell.enumOptions = enumOptions;
        }
      });
    });
  };

  const queryAssayTemplate = (value = '') => {
    const params = {
      pageIndex: 1,
      pageCount: 20,
      title: value,
    };
    getAllAssayTemplatesByPage(params).then((data) => {
      assayTemplateList.value = data.records.map((i) => {
        return {
          label: i.title,
          value: i.id,
        };
      });
    });
  };
  queryAssayTemplate();

  const handleSearchAssayTemplate = useDebounceFn(queryAssayTemplate, 280);

  async function handleSubmit() {
    if (isTest.value) {
      createMessage.success(isEdit.value ? '编辑成功' : '创建成功');
      handleCancel();
      return;
    }
    try {
      okLoading.value = true;
      const values = await validate();
      const data = {
        id: values.id,
        templateId: values.templateId,
        title: values.inspectionTitle,
        config: JSON.stringify({
          colsData: colsData.value,
          tableData: tableData.value,
          rowDenormalized: rowDenormalized.value,
        }),
        conclusion: values.conclusion,
      };
      const postUrl = isEdit.value ? updateAssayDocument : createAssayDocument;
      await postUrl(data);
      createMessage.success(isEdit.value ? '编辑成功' : '创建成功');
      handleCancel();
      emit('success');
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    closeModal();
    colsData.value = [];
    tableData.value = [];
    resetFields();
  }
</script>

<style lang="less" scoped>
  .table-container {
    position: relative;
    margin-top: 16px;
    background-color: #fff;
    border-radius: 4px;
    overflow: auto;
    max-height: 504px;

    .table-inner {
      width: 100%;
      display: table;
      border-collapse: collapse;
    }

    .col-container {
      display: flex;
      background: #f1f4f7;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      width: fit-content;
      min-width: 100%;
      position: sticky;
      top: 0;
      z-index: 2;

      .col-item {
        position: relative;
        flex: 1;
        min-width: 150px;
        padding: 12px 8px;
        color: #666666;
        font-weight: 500;
        border-bottom: 1px solid #f0f0f0;

        &:nth-child(1) {
          position: sticky;
          left: -8px;
          z-index: 1;
          background: #f1f4f7;
        }
      }
    }

    .row-item {
      display: flex;
      border-bottom: 1px solid #f5f6f7;
      transition: background 0.3s;
      width: fit-content;
      min-width: 100%;

      &:hover {
        background: #f8f8f8;
      }

      .value-item {
        flex: 1;
        min-width: 150px;
        padding: 12px 8px;
        color: #666666;

        .ant-input {
          &:hover {
            border-color: #d9d9d9;
          }

          &:focus {
            border-color: #d9d9d9;
            box-shadow: none;
          }
        }
      }

      .value-item:nth-child(1) {
        position: sticky;
        left: -8px;
        z-index: 1;
        background: white;
      }
    }
  }
</style>

<style lang="less">
  .checklist-template-modal .ant-select-selection-overflow-item {
    .ant-select-selection-item {
      background: rgba(43, 99, 161, 0.05);
      color: @primary-color;
      border-radius: 2px;

      .ant-select-selection-item-remove {
        color: @primary-color;
      }
    }
  }

  .checklist-template-modal {
    .vben-basic-form .basic-form-row .ant-col-12:nth-child(2) .ant-form-item,
    .vben-basic-form .basic-form-row .ant-col-12:nth-child(3) .ant-form-item {
      margin-bottom: 0 !important;
    }
  }
</style>
