<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    title="绑定资源"
    width="1200px"
    :minHeight="448"
    wrapClassName="data-configuration-modal"
    :loading="okLoading"
  >
    <div class="content-container">
      <div>
        <Input v-model:value="inputVal" placeholder="请输入">
          <template #prefix>
            <img :src="searchIcon" />
          </template>
        </Input>
        <div class="select-container">
          <div>资源名称</div>
          <div>资源类型</div>
          <div>预览</div>
          <div class="checkAll-box">
            <span>全选</span>
            <Checkbox
              @click="handleChangeCheckAll"
              :checked="checkedAllState == 'checked'"
              :indeterminate="checkedAllState == 'indeterminate'"
            />
          </div>
        </div>
        <div class="indicator-container">
          <VScroll :itemHeight="42" :items="filterTagetList" :height="330">
            <template #default="{ item }">
              <div class="scroll-item">
                <div>{{ item.displayName }}</div>
                <div>
                  {{ item.mediumName }}
                  <!-- <Tooltip>
                    <template #title>{{ item.sourceUniqueKey }}</template>
                    {{ item.sourceUniqueKey }}
                  </Tooltip> -->
                </div>
                <div>
                  <div class="preview-img">
                    <img
                      :src="
                        item.mediumType === '2'
                          ? videoDemo
                          : getEevReturnDomain(item.sourceUniqueKey)
                      "
                      style="cursor: pointer"
                      :alt="item.displayName"
                      @click="openDetail(item)"
                    />
                  </div>
                </div>
                <Checkbox v-model:checked="item.checked" @click="handleCheckIndicator(item)" />
              </div>
            </template>
          </VScroll>
        </div>
      </div>
      <div>
        <div class="checked-header-container">
          <div>序号</div>
          <div>资源名称</div>
          <div>资源类型</div>
          <div>预览</div>
          <div>操作</div>
        </div>
        <Draggable
          group="form-draggable"
          class="checked-container"
          tag="div"
          :component-data="{
            tag: 'div',
            type: 'transition-group',
            name: 'list',
          }"
          ghostClass="moving"
          :animation="180"
          v-model="draggableList"
          handle=".th-drag"
          item-key="key"
          ref="draggableListRef"
          @scroll="draggableListScroll"
        >
          <template #item="{ element: item, index }">
            <div class="checked-item">
              <div class="th-drag"> <Icon icon="carbon:drag-vertical" /> {{ index + 1 }}</div>
              <div class="flex items-center">
                <Input
                  v-show="item.isEditName"
                  :ref="
                    (el) => {
                      setInputRef(el, 'name', index);
                    }
                  "
                  v-model:value="item.displayName"
                  @blur="item.isEditName = false"
                />
                <div
                  v-show="!item.isEditName"
                  class="edit-item"
                  @click="handleShowEdit(index, 'name')"
                >
                  <!-- @mouseenter="showTooltip" @mouseleave="defaultTooltip" -->
                  <Tooltip>
                    <template #title> {{ item.displayName }}</template>
                    {{ item.displayName || '-' }}
                  </Tooltip>
                </div>
              </div>
              <div>
                {{ item.mediumName }}
                <!-- <Tooltip>
                  <template #title> {{ item.sourceUniqueKey }}</template>
                  {{ item.sourceUniqueKey }}
                </Tooltip> -->
              </div>
              <div
                class="preview-img"
                style="cursor: pointer"
                v-if="resourceIndexInfo?.intValue === RESOURCE_TYPE.DATABASE"
                ><img
                  :src="
                    item.mediumType === '2' ? videoDemo : getEevReturnDomain(item.sourceUniqueKey)
                  "
                  :alt="item.displayName"
                  @click="openDetail(item)"
              /></div>

              <div class="delete-btn" @click="handleDeleteItem(item)">删除</div>
            </div>
          </template>
        </Draggable>
        <div class="bottom-container">
          <div class="tips">
            <span>已选择</span>
            <span style="color: var(--theme-color)">{{ checkedList.length }}</span>
            <span>个资源</span>
            <span style="color: var(--theme-color)">(展示名称和展示单位可编辑)</span>
          </div>
          <span class="clear-btn" @click="clearList">清空</span>
        </div>
      </div>
    </div>
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">确认</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup name="BindModal">
  import Draggable from 'vuedraggable';
  import { Icon } from '/@/components/Icon';
  import { getEevReturnDomain } from '/@/utils/url';
  import { computed, ref, nextTick } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Input, Checkbox, Tooltip } from 'ant-design-vue';
  import { VScroll } from '/@/components/VirtualScroll/index';
  import videoDemo from '../../img-resource-manager/videoDemo.png';
  import { createImgPreview } from '/@zhcz/components/Preview/index';
  import {
    addOrUpdateDisplayResourceInfos,
    getResourceInfos,
    deleteDisplayResourceInfos,
    getDsImgPage,
  } from '/@zhcz/api/config-center/scenes-group';
  import searchIcon from '/@/assets/images/search-icon.png';
  // import checkEmpty from '/@/assets/images/check-empty.png';
  // import checked from '/@/assets/images/checked.png';
  // import indeterminate from '/@/assets/images/indeterminate.png';
  import { ResourceInfo } from '../typing';
  import { RESOURCE_TYPE } from '/@zhcz/enums/sceneResource';

  const okLoading = ref(false);
  const inputVal = ref('');
  const indicatorList: any = ref([]);
  const checkedList = ref<ResourceInfo[]>([]);
  const nameInputRef = ref<HTMLElement[]>([]);
  const unitInputRef = ref<HTMLElement[]>([]);
  const tagInputRef = ref<HTMLElement[]>([]);
  const maxValInputRef = ref<HTMLElement[]>([]);
  const minValInputRef = ref<HTMLElement[]>([]);
  const digitValInputRef = ref<HTMLElement[]>([]);
  const draggableList = ref<ResourceInfo[]>([]);
  const draggableListRef = ref<HTMLElement>();

  // function showTooltip(e) {
  //   // console.log(1, e.target.clientWidth, e.target.scrollWidth);
  //   if (e.target.clientWidth >= e.target.scrollWidth) {
  //     e.target.style.pointerEvents = 'none';
  //   }
  // }

  // function defaultTooltip(e) {
  //   console.log(2, e.target.clientWidth, e.target.scrollWidth);
  //   e.target.style.pointerEvents = 'auto';
  // }
  const openDetail = (row) => {
    try {
      const { sourceUniqueKey, mediumType } = row;
      const fileType = sourceUniqueKey.split('.').pop();
      if (mediumType === '2') {
        if (['mp4', 'flv', 'webm'].includes(fileType)) {
          createImgPreview({
            imageList: [getEevReturnDomain(sourceUniqueKey)],
            defaultWidth: 700,
            rememberState: true,
          });
          return;
        }
      } else if (mediumType === '1') {
        if (['png', 'jpeg', 'jpg'].includes(fileType)) {
          createImgPreview({
            imageList: [getEevReturnDomain(sourceUniqueKey)],
            defaultWidth: 700,
            rememberState: true,
          });
          return;
        }
      }
      // previewFile(record.fileDTOS[0]);
      // openPreviewModal(true);
    } catch (error) {
      createMessage.error('预览失败，请检查文件是否存在');
    }
  };
  const setInputRef = (el: any, type: string, index: number) => {
    if (!el) return;
    const inputRef = {
      name: nameInputRef,
      unit: unitInputRef,
      tag: tagInputRef,
      maxVal: maxValInputRef,
      minVal: minValInputRef,
      digitVal: digitValInputRef,
    };
    inputRef[type].value[index] = el;
  };
  const selectNode: any = ref({});
  const resourceIndexInfo: any = ref({});
  // 初始已选资源
  const initCheckedList = ref<ResourceInfo[]>([]);

  const checkedAllState = computed(() => {
    if (!checkedList.value.length || filterTagetList.value.every((item: any) => !item.checked)) {
      return 'checkEmpty';
    }

    if (
      filterTagetList.value.every((item: any) => item.checked) &&
      checkedList.value.length >= filterTagetList.value.length
    ) {
      return 'checked';
    }

    return 'indeterminate';
  });

  const filterTagetList = computed(() => {
    let result = indicatorList.value;
    if (inputVal.value.trim()) {
      const inputValArr = inputVal.value.split(' ').filter((item) => item);
      result = result.filter((item: any) => {
        return inputValArr.every((i) => {
          return (
            item.displayName.toLocaleLowerCase().includes(i.toLocaleLowerCase()) ||
            item.sourceUniqueKey.toLocaleLowerCase().includes(i.toLocaleLowerCase())
          );
        });
      });
    }
    // console.log('result', result);
    return result;
  });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    selectNode.value = data.selectNode;
    resourceIndexInfo.value = data.resourceIndexInfo;
    await initForm();
  });

  const handleShowEdit = (index: number, type: string) => {
    const propertyMap = {
      name: 'isEditName',
      unit: 'isEditUnit',
      tag: 'isEditTag',
      maxVal: 'isEditMaxVal',
      minVal: 'isEditMinVal',
      digitVal: 'isEditDigitVal',
    };

    const property = propertyMap[type];
    if (property) {
      draggableList.value[index][property] = true;

      nextTick(() => {
        const inputRef = {
          name: nameInputRef,
          unit: unitInputRef,
          tag: tagInputRef,
          maxVal: maxValInputRef,
          minVal: minValInputRef,
          digitVal: digitValInputRef,
        }[type];

        if (inputRef) {
          inputRef.value[index].focus();
        }
      });
    }
  };

  const index = ref(0);
  // 切换全选
  const handleChangeCheckAll = () => {
    index.value = 0;
    const checked = ['checkEmpty', 'indeterminate'].includes(checkedAllState.value);
    console.log('checked', checked);
    filterTagetList.value.forEach((item: any) => {
      item.checked = checked;
    });
    checkedList.value = indicatorList.value.filter((item: any) => item.checked);

    draggableList.value = checkedList.value.slice(index.value, 100);
  };

  const draggableListScroll = (event) => {
    const scrollHeight = event.target.scrollHeight;
    const scrollTop = event.target.scrollTop;
    if (scrollHeight - scrollTop <= 400) {
      index.value += 100;
      draggableList.value.push(...checkedList.value.slice(index.value, index.value + 100));
    }
  };

  const handleDeleteItem = (item: any) => {
    const findItem = indicatorList.value.find(
      (i: any) => i.sourceUniqueKey === item.sourceUniqueKey,
    );
    if (findItem) {
      indicatorList.value.find((i: any) => i.sourceUniqueKey === item.sourceUniqueKey).checked =
        false;
    }
    checkedList.value = checkedList.value.filter(
      (i: any) => i.sourceUniqueKey !== item.sourceUniqueKey,
    );

    console.log(
      'handleDeleteItem',
      item,
      draggableList.value.findIndex((i) => i.sourceUniqueKey == item.sourceUniqueKey),
    );
    const result = draggableList.value.filter((i) => i.sourceUniqueKey !== item.sourceUniqueKey);
    draggableList.value = result;
  };

  const handleCheckIndicator = (item: any) => {
    item.checked = !item.checked;
    if (item.checked) {
      checkedList.value.push(JSON.parse(JSON.stringify(item)));
      draggableList.value.push(JSON.parse(JSON.stringify(item)));
    } else {
      checkedList.value = checkedList.value.filter(
        (i: any) => i.sourceUniqueKey !== item.sourceUniqueKey,
      );
      const result = draggableList.value.filter((i) => i.sourceUniqueKey !== item.sourceUniqueKey);
      draggableList.value = result;
    }
  };

  const clearList = () => {
    indicatorList.value.forEach((item: any) => {
      item.checked = false;
    });
    checkedList.value = [];
    draggableList.value = [];
  };

  // 初始化表单数据
  const initForm = async () => {
    okLoading.value = true;
    try {
      // 全部资源
      await getDsImgPage({
        page: 1,
        size: 9999,
      }).then((res) => {
        console.log('res', res);
        indicatorList.value = res.records.map((item: any) => {
          return {
            sourceUniqueKey: item.url,
            displayName: item.name,
            mediumName: item.mediumName,
            mediumType: item.mediumType,
          };
        });
      });
      // 已选资源
      const checkedData = await getResourceInfos({
        resourceId: resourceIndexInfo.value.id,
        groupId: selectNode.value.id,
        sort: 1,
      });
      indicatorList.value.forEach((item: any) => {
        const findItem = checkedData.find((i: any) => i.sourceUniqueKey === item.sourceUniqueKey);
        item.checked = !!findItem;
        item.id = findItem?.id;
      });
      initCheckedList.value = checkedData;
      checkedList.value = checkedData;
      draggableList.value = checkedList.value.slice(0, 100);
      console.log('draggableList.value', draggableList.value);
      okLoading.value = false;
    } catch (error) {
      okLoading.value = false;
    }
  };

  async function handleSubmit() {
    try {
      okLoading.value = true;
      const deleteList = initCheckedList.value.filter(
        (item) => !checkedList.value.some((i) => i.sourceUniqueKey === item.sourceUniqueKey),
      );
      if (deleteList.length) {
        await deleteDisplayResourceInfos({ ids: deleteList.map((item) => item.id) });
      }
      const data = {
        resourceId: resourceIndexInfo.value.id,
        resourceType: resourceIndexInfo.value.intValue,
        groupId: selectNode.value.id,
        displayResources: draggableList.value.map((item: any, index) => {
          return {
            id: item.id || null,
            sourceUniqueKey: item.sourceUniqueKey,
            displayName: item.displayName,
            mediumName: item.mediumName,
            mediumType: item.mediumType,
            unitName: '',
            tag: '',
            maxVal: '',
            minVal: '',
            openLimit: null,
            sourceUniqueKeyType: 2,
            sort: index,
          };
        }),
      };
      await addOrUpdateDisplayResourceInfos(data);
      createMessage.success('操作成功');
      handleCancel();
      emit('success');
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    closeModal();
    inputVal.value = '';
    indicatorList.value = [];
    checkedList.value = [];
    initCheckedList.value = [];
    nameInputRef.value = [];
    unitInputRef.value = [];
  }
</script>

<style lang="less" scoped>
  .content-container {
    display: flex;
    border: 1px solid #e9e9e9;
    height: 448px;
    color: #666;

    ::-webkit-scrollbar {
      height: 0 !important;
      width: 0 !important;
      background: transparent;
    }

    & > div {
      width: 50%;
      padding: 12px;

      &:first-child {
        // min-width: 676px;
      }

      &:last-child {
        border-left: 1px solid #e9e9e9;
        position: relative;
        // width: 58%;
      }
    }

    .select-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12px;
      font-weight: 500;
      color: #333;

      & > div {
        flex-shrink: 0;

        &:nth-child(1) {
          width: 40%;
        }

        &:nth-child(2) {
          width: 25%;
        }

        &:nth-child(3) {
          width: 80px;
        }
      }

      .checkAll-box {
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          margin-right: 8px;
        }

        img {
          cursor: pointer;
        }
      }
    }

    .indicator-container {
      margin-top: 17px;

      .scroll-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        & > * {
          flex-shrink: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        & > div {
          &:nth-child(1) {
            width: 40%;
          }

          &:nth-child(2) {
            width: 25%;
          }

          &:nth-child(3) {
            width: 80px;
          }
        }

        .ant-checkbox-wrapper {
          width: 52px;
          justify-content: flex-end;
        }
      }
    }

    .checked-header-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 500;
      line-height: 1;
      margin-bottom: 16px;
      color: #333;

      & > div {
        &:nth-child(1) {
          width: 50px;
        }

        &:nth-child(2) {
          width: 40%;
        }

        &:nth-child(3) {
          width: 25%;
        }

        &:nth-child(4) {
          width: 80px;
        }
      }
    }

    .checked-container {
      width: 100%;
      height: 360px;
      overflow-y: auto;

      .checked-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        margin-top: 12px;

        width: 100%;

        & > * {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        & > div {
          &:nth-child(1) {
            width: 50px;
          }

          &:nth-child(2) {
            width: 40%;
          }

          &:nth-child(3) {
            width: 25%;
          }

          &:nth-child(4) {
            width: 80px;
          }
        }

        :deep(.ant-input-number) {
          min-width: 100%;
        }

        .edit-item {
          cursor: text;
          position: relative;
          width: 100%;
          display: flex;
          align-items: center;

          .name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            display: inline-block;
            line-height: 1;
            flex: 1;
          }

          :deep(span) {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            display: inline-block;
            line-height: 1;
            flex: 1;
          }

          .icon {
            display: none;
          }

          &:hover {
            color: @theme-color;

            .icon {
              display: block;
            }
          }
        }

        .delete-btn {
          cursor: pointer;
          color: @theme-color;
        }
      }
    }

    .bottom-container {
      position: absolute;
      bottom: 0;
      height: 35px;
      border-top: 1px solid #e9e9e9;
      left: 0;
      width: 100%;
      line-height: 35px;
      padding: 0 12px;
      display: flex;
      justify-content: space-between;

      .tips {
        color: #999999;
      }

      .clear-btn {
        color: @theme-color;
        cursor: pointer;
      }
    }

    .preview-img {
      height: 30px;

      img {
        height: 100%;
      }
    }
  }
</style>

<style lang="less">
  .data-configuration-modal {
    // .ant-modal-body > .scrollbar {
    //   padding: 24px;
    // }

    .ant-input-affix-wrapper {
      border-radius: 2px;

      .ant-input-prefix img {
        width: 14px;
      }
    }

    .ant-select-selector {
      border-radius: 2px !important;
    }

    .ant-checkbox-inner {
      border-radius: 2px;
    }

    .ant-select-disabled {
      .ant-select-selector {
        background: rgba(0, 0, 0, 5%) !important;
        color: #333 !important;
        border: none;
      }

      .ant-select-arrow {
        display: none;
      }
    }
  }
</style>
