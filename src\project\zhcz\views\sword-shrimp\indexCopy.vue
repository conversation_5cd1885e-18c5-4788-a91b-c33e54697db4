<template>
  <div class="sword-shrimp">
    <Row :gutter="12" style="height: calc(50% - 6px)">
      <Col :span="24" style="height: 100%">
        <HLCard title="实时数据">
          <template #headerRight>
            <div class="card-header">
              <RadioGroup
                buttonStyle="solid"
                v-model:value="modelType"
                @change="handleModelTypeChange"
              >
                <RadioButton v-for="item in modelList" :key="item.value" :value="item.value">
                  {{ item.label }}
                </RadioButton>
              </RadioGroup>
            </div>
          </template>
          <template #defaultBody>
            <div class="card-body">
              <Row :gutter="12" style="height: 100%">
                <Col :span="12" style="height: 100%">
                  <TableSimple
                    :dataSource="tableDataBefore"
                    :columns="tableColumns"
                    :pagination="{}"
                  >
                    <template #tableAction="{ column, record }">
                      <div
                        v-if="column.key === 'realTimeDensity' || column.key === 'totalNumber'"
                        :style="{
                          color: record[column.key] > 0 ? 'red' : 'none',
                        }"
                      >
                        {{ record[column.key] }}
                      </div>
                      <div v-else>
                        {{ record[column.key] }}
                      </div>
                    </template>
                  </TableSimple>
                </Col>
                <Col :span="12" style="height: 100%">
                  <TableSimple
                    :dataSource="tableDataAfter"
                    :columns="tableColumns"
                    :pagination="{}"
                  >
                    <template #tableAction="{ column, record }">
                      <div
                        v-if="column.key === 'realTimeDensity' || column.key === 'totalNumber'"
                        :style="{
                          color: record[column.key] > 0 ? 'red' : 'none',
                        }"
                      >
                        {{ record[column.key] }}
                      </div>
                      <div v-else>
                        {{ record[column.key] }}
                      </div>
                    </template>
                  </TableSimple>
                </Col>
              </Row>
            </div>
          </template>
        </HLCard>
      </Col>
    </Row>
    <Row :gutter="12" style="height: calc(50% - 6px); margin-top: 12px">
      <Col :span="24" style="height: 100%">
        <LineSimpleCard title="曲线数据" v-bind="{ bottomList: indexList }">
          <template #headerRight>
            <div class="header-right">
              <Form layout="inline">
                <!-- :model="formState"
                @finish="handleFinish"
                @finishFailed="handleFinishFailed" -->
                <FormItem label="选择滤池" name="filterPool">
                  <!-- :maxTagCount="1"
                  :show-checked-strategy="SHOW_PARENT"
                  :tree-checkable="false"
                  v-model:value="filterPool"
                  :tree-data="filterPoolTreeData"
                  allow-clear
                  style="width: 200px; height: 32px"
                  :dropdownMatchSelectWidth="false" -->
                  <TreeSelect
                    v-model:value="filterPool"
                    show-search
                    style="width: 150px; height: 32px"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :allow-clear="false"
                    tree-default-expand-all
                    :tree-data="filterPoolTreeData"
                    tree-node-filter-prop="name"
                    :fieldNames="{
                      children: 'children',
                      label: 'name',
                      value: 'id',
                    }"
                    @change="handleTreeSelectChange"
                    placeholder="请选择"
                  />
                </FormItem>
                <FormItem label="类型" name="typeName">
                  <Select
                    v-model:value="typeName"
                    style="width: 100px"
                    @change="getRealTimeDataChartData"
                    placeholder="请选择"
                  >
                    <SelectOption v-for="item in TypeList" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </SelectOption>
                  </Select>
                </FormItem>
                <FormItem label="选择时间" name="dateValue">
                  <RangePicker
                    v-model:value="dateValue"
                    @change="getRealTimeDataChartData"
                    style="width: 200px"
                    :placeholder="['开始日期', '结束日期']"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :allow-clear="false"
                  />
                </FormItem>
                <FormItem name="export">
                  <Button type="primary" @click="handleExport">导出数据</Button>
                </FormItem>
              </Form>
            </div>
          </template>
        </LineSimpleCard>
      </Col>
    </Row>
  </div>
</template>

<script lang="ts" setup name="swordShrimp">
  import { ref, unref, onMounted, onUnmounted, onActivated, onDeactivated, computed } from 'vue';
  import {
    Row,
    Col,
    RadioGroup,
    RadioButton,
    Select,
    SelectOption,
    Form,
    FormItem,
    RangePicker,
    TreeSelect,
    Button,
  } from 'ant-design-vue';
  // import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { useLoading } from '/@/components/Loading';
  import { HLCard } from '/@zhcz/components/HLCard';
  import { TableSimple } from '/@zhcz/components/HLCardTable';
  import { LineSimpleCard } from '/@zhcz/components/HLCardComponent';
  import { echartsListType } from '/@zhcz/components/HLCardComponent/src/dataType';
  import {
    getLastOne,
    realTimeDataChartData,
    realTimeDataGetFilterTree,
    realTimeDataExport,
  } from '/@zhcz/api/sword-shrimp';
  import { downloadByData } from '/@/utils/file/download';
  import { useIntervalFn } from '@vueuse/core';
  import dayjs from 'dayjs';
  const empty = ref(false);
  // const SHOW_PARENT = TreeSelect.SHOW_PARENT;
  // const modelConfigStore = modelConfigStatusWithOut();
  const filterPoolTreeData = ref([]);
  const dateValue = ref<any[]>([dayjs(), dayjs()]);
  const filterPool = ref<string>();
  const typeName = ref('3');
  const TypeList = ref([
    {
      label: '实时密度',
      value: '3',
    },
    {
      label: '总水量',
      value: '1',
    },
    {
      label: '总个数',
      value: '2',
    },
  ]);
  const modelType = ref('1');
  const modelList = ref([
    {
      label: '一期滤池',
      value: '1',
    },
    {
      label: '二期滤池',
      value: '2',
    },
  ]);
  const indexList = ref<echartsListType[]>([]);
  const tableDataBefore = ref([]);
  const tableDataAfter = ref([]);
  const tableData = ref<any>({});
  const tableColumns = ref([
    {
      title: '滤池编号',
      dataIndex: 'filterName',
    },
    {
      title: '总水量(L)',
      dataIndex: 'totalWaterVolume',
    },
    {
      title: '总个数(个)',
      dataIndex: 'totalNumber',
    },
    {
      title: '实时密度(个/100L)',
      dataIndex: 'realTimeDensity',
    },
  ]);
  const [openFullLoading, closeFullLoading] = useLoading({});
  openFullLoading();
  // const { createMessage } = useMessage();
  const { currentRoute } = useRouter();
  const route = unref(currentRoute);
  const fId = computed(() => {
    if (route && route.query && route.query.fId) {
      return route.query.fId;
    } else {
      return '-1';
    }
  });
  // const { modelId, classify, isUpdate = 'true' } = route.query as Indexable;

  // const update = isUpdate === 'true';

  // const isOpen = ref(false);
  // const modelConfig = ref<Indexable>({ modelId, isUpdate: update });
  const getableList = async () => {
    const res = await getLastOne({ fId: fId.value });
    // console.log('res', res);
    tableData.value = res;
    if (modelType.value === '1') {
      tableDataBefore.value = tableData.value.data1.filter((item, index) => index < 6);
      tableDataAfter.value = tableData.value.data1.filter((item, index) => index > 5);
    } else if (modelType.value === '2') {
      tableDataBefore.value = tableData.value.data2.filter((item, index) => index < 6);
      tableDataAfter.value = tableData.value.data2.filter((item, index) => index > 5);
    }
  };
  const getTreeData = async () => {
    const res = await realTimeDataGetFilterTree({ fId: fId.value });
    // console.log('res', res);
    if (res.length) {
      filterPoolTreeData.value = res;
      filterPool.value = '1-1';
      filterPoolTreeData.value.forEach((item: any) => {
        if (item.parentId === '-1') {
          item.disabled = true;
        }
      });
      handleTreeSelectChange(filterPool.value);
    }
  };
  const handleExport = async () => {
    // dataType: typeName.value,
    const currentTime = dayjs().format('HH:mm:ss');
    const today = dayjs().format('YYYY-MM-DD');
    const selectIDsC = selectIDs.value.filter((item) => item !== '99' && item !== '100');
    const blob = await realTimeDataExport({
      filterKeys: selectIDsC,
      dataType: typeName.value,
      startTime: dayjs(dateValue.value[0]).format('YYYY-MM-DD 00:00:00'),
      endTime:
        dayjs(dateValue.value[1]).format('YYYY-MM-DD') === today
          ? dayjs(dateValue.value[1]).format(`YYYY-MM-DD ${currentTime}`)
          : dayjs(dateValue.value[1]).format('YYYY-MM-DD 23:59:59'),
      fId: fId.value,
    });
    const startDateTime = dayjs(dateValue.value[0]).format('YYYY-MM-DD');
    const endDateTime = dayjs(dateValue.value[1]).format('YYYY-MM-DD');
    downloadByData(blob, `${startDateTime}-${endDateTime}滤池数据.xlsx`);
    // console.log('res', blob);
  };
  const selectIDs = ref<string[]>([]);
  // TreeSelect 选择变化时的处理函数
  const handleTreeSelectChange = (value: string) => {
    // console.log('value', value);
    let allIds: string[] = [];
    // value.forEach((selectedId) => {
    let selectedNode = filterPoolTreeData.value.find((node: any) => node.id === value);
    if (selectedNode) {
      allIds = allIds.concat(getAllChildIds(selectedNode));
    } else {
      filterPoolTreeData.value.forEach((item: any) => {
        if (item.children) {
          selectedNode = item.children.find((node) => node.id === value);
        }
      });
      if (selectedNode) {
        allIds = allIds.concat(getAllChildIds(selectedNode));
      } else {
        allIds.push(value);
      }
    }
    // });
    // 过滤重复的 ID
    selectIDs.value = [...new Set(allIds)];
    // console.log('selectIDs.value', selectIDs.value);
    getRealTimeDataChartData();
  };
  // 递归函数，用于获取节点及其子节点的所有 ID
  const getAllChildIds = (node) => {
    let ids = [node.id];
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        ids = ids.concat(getAllChildIds(child));
      });
    }
    return ids;
  };
  const getRealTimeDataChartData = async () => {
    // filterKeys=1-1&dataType=1&startTime=2025-06-27 14:35:01&endTime=2025-06-27 14:35:01
    try {
      const currentTime = dayjs().format('HH:mm:ss');
      const selectIDsC = selectIDs.value.filter((item) => item !== '99' && item !== '100');
      const today = dayjs().format('YYYY-MM-DD');
      const res = await realTimeDataChartData({
        filterKeys: selectIDsC,
        dataType: typeName.value,
        startTime: dayjs(dateValue.value[0]).format('YYYY-MM-DD 00:00:00'),
        endTime:
          dayjs(dateValue.value[1]).format('YYYY-MM-DD') === today
            ? dayjs(dateValue.value[1]).format(`YYYY-MM-DD ${currentTime}`)
            : dayjs(dateValue.value[1]).format('YYYY-MM-DD 23:59:59'),
        fId: fId.value,
      });
      // console.log('res', res);
      if (res.length) {
        empty.value = false;
        indexList.value = res.map((item: any) => {
          return {
            indexName: item.name,
            indexCode: item.indexCode || '',
            unitName: item.unit,
            type: 'line',
            symbolSize: 8,
            showSymbol: true,
            data: item.data.map((i: any) => ({
              value: i.value,
            })),
            XAxis: item.data.map((i: any) => i.datetime),
            seriesLabel: item.seriesLabel,
          };
        });
        console.log('indexList.value', indexList.value);
      } else {
        empty.value = true;
      }
    } catch (err) {
      console.log('err', err);
      empty.value = true;
    }
  };
  const handleModelTypeChange = () => {
    if (modelType.value === '1') {
      tableDataBefore.value = tableData.value.data1.filter((item, index) => index < 6);
      tableDataAfter.value = tableData.value.data1.filter((item, index) => index > 5);
    } else if (modelType.value === '2') {
      tableDataBefore.value = tableData.value.data2.filter((item, index) => index < 6);
      tableDataAfter.value = tableData.value.data2.filter((item, index) => index > 5);
    }
  };

  async function init() {
    getableList();
    getTreeData();
    // if (!classify || (update && !modelId)) return createMessage.error('缺失模型类型或模型ID参数');
    // switch (Number(classify)) {
    //   case MODEL_TYPE.FORM:
    //     modelConfig.value = { modelId, isUpdate: update };
    //     isOpen.value = true;
    //     break;
    //   case MODEL_TYPE.LIST:
    //     // openDrawer(true, {
    //     //   record: { modelId },
    //     //   isUpdate: update,
    //     // });
    //     break;

    //   case MODEL_TYPE.COMPLEX:
    //     // openDrawer(true, {
    //     //   record: { modelId },
    //     //   isUpdate: update,
    //     // });
    //     break;
    //   default:
    // }
    closeFullLoading();
  }
  const { pause, resume } = useIntervalFn(() => {
    getableList();
  }, 1000 * 60 * 1);
  onMounted(async () => {
    resume();
    init();
  });
  onActivated(() => {
    resume();
  });

  onUnmounted(() => {
    pause();
  });

  onDeactivated(() => {
    pause();
  });
</script>
<style lang="less" scoped>
  .sword-shrimp {
    background-color: rgb(240, 242, 245);
    border: 1px solid rgb(196 1 88 188);
    height: 100%;
    width: 100%;
    flex: 1;
    padding: 16px;

    ::v-deep(.vben-basic-table .ant-table-wrapper) {
      padding: 0;
      margin: 0;
      height: 100%;
    }

    .header-right {
      ::v-deep(.ant-form .ant-form-item) {
        &:last-child {
          margin-right: 0;
        }
      }
    }

    .card-body {
      height: 100%;
      width: 100%;
      flex: 1;
    }
  }
</style>
