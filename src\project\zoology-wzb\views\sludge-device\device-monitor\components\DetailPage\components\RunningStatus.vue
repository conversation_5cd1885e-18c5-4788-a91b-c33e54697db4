<template>
  <div class="running-status">
    <HLCard title="运行状态" :styleData="{ borderRadius: '4px' }">
      <template #defaultBody>
        <div class="content-container">
          <div
            @mouseenter="itemHoverHandele"
            :class="['status-box-wrapper', { error: item.label == '电池电量' }]"
            v-for="(item, index) in list"
            :key="index"
          >
            <Tooltip
              :overlayClassName="`sludge-device-monitor-status-tag-tooltip ${
                rightItemHover ? 'right' : ''
              }`"
            >
              <StatusTagBox :item="item" />
              <template #title v-if="false">
                <div>
                  <div>报警类型：{{ item.label }}</div>
                  <div>报警持续：{{ '41分钟' }}</div>
                </div>
              </template>
            </Tooltip>
          </div>
        </div>
      </template>
    </HLCard>
  </div>
</template>
<script lang="ts" setup>
  import { HLCard } from '/@/project/zhcz/components/HLCard';
  import { Tooltip } from 'ant-design-vue';
  import StatusTagBox from './StatusTagBox/StatusTagBox.vue';
  import { ref } from 'vue';

  defineProps({
    list: {
      type: Array,
      default: () => [],
    },
  });

  const rightItemHover = ref(false); // 鼠标移入最右侧卡片
  const itemHoverHandele = () => {
    // const rect = v.target.getBoundingClientRect();
    // const x = rect.left + window.scrollX;
    // if (window.innerWidth - x < 205) rightItemHover.value = true;
    // else rightItemHover.value = false;
    console.log(rightItemHover.value);
  };
</script>
<style lang="less" scoped>
  .running-status {
    display: flex;
    flex-direction: column;
    // height: 288px;
    background-color: #fff;
    border-radius: 4px;

    .status-box-wrapper {
      margin: 0 0 8px 15px;
      border-radius: 4px;

      &.error {
        box-shadow: inset 0px 0px 8px 0px rgba(255, 43, 43, 0.56);
        border: 1px solid #ff522b;
      }
    }
  }

  .content-container {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;

    :deep(.card-box) {
      padding: 8px 8px;

      .label-box .label-item .label {
        margin-bottom: 8px;
      }

      .tag {
        &.success {
          color: #fff;
          background-color: #4db803;
        }

        &.info {
          color: #333;
          background-color: #e9e9e9;
        }
      }
    }
  }

  :deep(.card_in) {
    .card-body {
      padding: 8px 0 !important;
    }
  }
</style>

<style>
  .sludge-device-monitor-status-tag-tooltip {
    &.right {
      .ant-tooltip-content {
        transform: translate(-60px, 12px);

        .ant-tooltip-arrow {
          left: unset !important;
          right: 0 !important;
        }
      }
    }

    .ant-tooltip-content {
      transform: translate(60px, 12px);
    }

    .ant-tooltip-inner {
      padding: 10px 14px !important;
      background: linear-gradient(180deg, #e54242 4%, #6d0a0a 100%) !important;
    }

    .ant-tooltip-arrow {
      left: 12% !important;

      &::before {
        background-color: #6d0a0a !important;
      }
    }
  }
</style>
