<template>
  <div class="drug-left">
    <BigBoxContainer>
      <BoxContainer style="height: calc(50% - 4px)">
        <template #header>
          <BoxHeader :title="title1">
            <template #right>
              <DatePicker
                v-model:value="prodDate"
                picker="month"
                class="date-picker big-screen-date-picker"
                popupClassName="big-screen-date-picker-dropdown"
                style="width: 120px !important"
                placeholder="选择日期"
                :allowClear="false"
                :showToday="false"
                :disabledDate="disabledDate"
                @change="getProduceData"
              />
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div v-if="produceDataFlag" class="container consumption">
            <ConsumptionItem
              v-for="(item, index) in produceData"
              :className="`item${getClass(index)}`"
              :key="index"
              :data="item"
            />
          </div>
          <div v-else class="container consumption">
            <DataEmpty />
            <!-- <ConsumptionItem
              v-for="(item, index) in produceData"
              :className="`item${getClass(index)}`"
              :key="index"
              :data="item"
              :empty="true"
            /> -->
          </div>
        </template>
      </BoxContainer>
      <BoxContainer
        style="height: calc(50% - 4px)"
        :data-resource-code="indexList?.index_LB1[0]?.groupCode"
      >
        <template #header>
          <BoxHeader :title="title2">
            <template #right>
              <!-- 日用药单耗，变更为用药排行，增加原料矾、氢氧化钠、活性炭的排行，支持日、月、年过滤。 -->
              <!-- <DatePicker
                v-model:value="drugConsumptionDate"
                class="date-picker big-screen-date-picker"
                popupClassName="big-screen-date-picker-dropdown"
                style="width: 120px !important"
                placeholder="选择日期"
                :allowClear="false"
                :showToday="false"
                :disabledDate="disabledDate"
                @change="getDrugConsumptionData"
              /> -->
              <div class="select-box">
                <Select
                  v-model:value="sludgeTime2"
                  style="width: 5rem"
                  @change="handleSludgeChange2"
                  class="big-screen-select"
                  popupClassName="big-screen-select-dropdown"
                >
                  <SelectOption v-for="item in dataList2" :value="item.value" :key="item.value">
                    {{ item.label }}
                  </SelectOption>
                </Select>
                <DatePicker
                  v-model:value="drugConsumptionDate"
                  :picker="getPicker2"
                  class="date-picker big-screen-date-picker"
                  popupClassName="big-screen-date-picker-dropdown"
                  style="width: 126px !important"
                  placeholder="选择日期"
                  :allowClear="false"
                  :showToday="false"
                  :disabledDate="disabledDate"
                  @change="getDrugConsumptionData"
                />
              </div>
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div style="width: 100%; height: 100%; position: relative">
            <!-- <template v-if="drugConsLoad">
              <div :style="{ background: 'unset' }"></div>
            </template> -->
            <template v-if="!drugConsLoad && drugConsumptionData.length">
              <UnitConsumption :data-list="drugConsumptionData" />
            </template>
            <DataEmpty v-else />
          </div>
        </template>
      </BoxContainer>
    </BigBoxContainer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import dayjs from 'dayjs';
  import { useIndexList, useTitleList } from '../hooks';
  import { otherIconData, mockDrugDataList, mockConsumptionData } from './data.js';
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import { toDecimalFloor } from '../utils';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { roundAndConvertCheckNullAndUnDef } from '/@zhcz/utils/number';
  import ConsumptionItem from '../components/consumption-item/index.vue';
  import BigBoxContainer from '../components/box-container/BigBoxContainer.vue';
  import BoxContainer from '../components/box-container/index.vue';
  import BoxHeader from '../components/box-container/BoxHeader.vue';
  // import ProduceDataTop from '../components/produce-data/index.vue';
  // import ProduceDataChart from '../components/echarts/ProduceDataChart.vue';
  import DataEmpty from '../components/data-empty/index.vue';
  import UnitConsumption from '../components/unit-consumotion/index.vue';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';
  import { getEevReturnDomain } from '/@zhcz/utils/file/url';

  const prodDate = ref(dayjs().subtract(1, 'month'));
  const sludgeTime2 = ref<null | string>(null);
  const dataList2 = ref<{ label: string; value: string }[]>([]);
  async function getTimeList2() {
    const res = await listSenceGroupByParent({
      groupCode: 'DrugUnitConsumption',
      factoryId: 1,
      platformld: 1,
    });
    if (Object.keys(res).length) {
      sludgeTime2.value = Object.keys(res)[0];
      dataList2.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      const picker =
        pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
      if (picker === 'date') {
        drugConsumptionDate.value = dayjs().subtract(1, 'day');
      } else if (picker === 'month') {
        drugConsumptionDate.value = dayjs().subtract(1, picker);
      } else {
        drugConsumptionDate.value = dayjs().subtract(0, picker);
      }
      getDrugConsumptionData();
    }
  }
  getTimeList2();
  enum pickerType {
    '日' = 'date',
    '月' = 'month',
    '年' = 'year',
  }
  const getPicker2 = computed(() => {
    if (sludgeTime2.value) {
      return pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    } else {
      return pickerType['日'];
    }
  });
  const handleSludgeChange2 = () => {
    const picker =
      pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    if (picker === 'date') {
      drugConsumptionDate.value = dayjs().subtract(1, 'day');
    } else if (picker === 'month') {
      drugConsumptionDate.value = dayjs().subtract(1, picker);
    } else {
      drugConsumptionDate.value = dayjs().subtract(0, picker);
    }
    getDrugConsumptionData();
  };
  function getClass(index) {
    let item = '';
    let coIndex = index + 1;
    switch (coIndex % 4) {
      case 1:
        item = 'On';
        break;
      case 2:
        item = 'Tw';
        break;
      case 3:
        item = 'Th';
        break;
      case 0:
        item = 'Fo';
        break;
    }
    // UI特殊处理
    if (index === 4) {
      item = 'Fo';
    } else if (index === 5) {
      item = 'On';
    }
    return item;
  }
  function disabledDate(current) {
    // 禁止选择今天以后的日期
    return current && current > dayjs().subtract(0, 'day');
  }

  const { indexList } = useIndexList();
  const { titleList } = useTitleList();

  const title1 = computed(() => {
    return titleList.value.title_LT1;
  });
  const title2 = computed(() => {
    return titleList.value.title_LB;
  });

  const produceDataFlag = ref<Boolean>(true);
  // 药耗统计
  const produceData = ref<any>({});
  // const produceDate = ref(dayjs().subtract(1, 'day'));
  const getProduceData = async () => {
    try {
      const lastDayOfLastMonth = dayjs(prodDate.value).endOf('month').format('YYYY-MM-DD HH:mm:ss');
      const firstDayOfLastMonth = dayjs(prodDate.value)
        .startOf('month')
        .format('YYYY-MM-DD HH:mm:ss');

      const tempParams = indexList.value.index_LT1[0];
      const params = {
        startDateTime: lastDayOfLastMonth,
        endDateTime: firstDayOfLastMonth,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };

      const { data } = await callResourceFunction(paramData);
      if (data && data.length) {
        produceDataFlag.value = true;
        produceData.value = data.map((i, index) => {
          const value = i.indicatorsByTimeIntervalResp.length
            ? i.indicatorsByTimeIntervalResp[0]?.data.reduce((prev, cur) => {
                return prev + cur.value;
              }, 0)
            : 0;

          return {
            indexName: i.indicatorsByTimeIntervalResp.length
              ? i.indicatorsByTimeIntervalResp[0]?.indexName
              : mockConsumptionData[index].indexName,
            value: roundAndConvertCheckNullAndUnDef(value, 2),
            unitName: i.indicatorsByTimeIntervalResp.length
              ? i.indicatorsByTimeIntervalResp[0]?.unitName
              : mockConsumptionData[index].unitName,
            src:
              getEevReturnDomain(i.imgByTimeIntervalResps[0]?.sourceUniqueKey) ||
              otherIconData[index],
            // src: i.imgByTimeIntervalResps[0]?.sourceUniqueKey,
          };
        });
      } else {
        produceDataFlag.value = false;
        produceData.value = mockConsumptionData;
      }
    } catch (_) {
      produceDataFlag.value = false;
    }
  };

  // 用药排行
  const drugConsumptionData = ref(JSON.parse(JSON.stringify(mockDrugDataList)));
  const drugConsLoad = ref<Boolean>(true);
  const drugConsumptionDate = ref(dayjs().subtract(0, 'day'));
  const getDrugConsumptionData = async () => {
    try {
      // drugConsLoad.value = true;
      const picker =
        pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
      const startDate = dayjs(drugConsumptionDate.value)
        .startOf(picker)
        .format('YYYY-MM-DD 00:00:00');
      const endDataTime = dayjs(drugConsumptionDate.value)
        .endOf(picker)
        .format('YYYY-MM-DD 23:59:59');

      const tempParams = {
        resourceInterfaceId: '5',
        groupCode: sludgeTime2.value,
        jsConvert: true,
      };
      // const tempParams = indexList.value.index_LB1[0];
      const params = {
        startDateTime: startDate,
        endDateTime: endDataTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };
      const { data } = await callResourceFunction(paramData);
      if (data && data.length) {
        drugConsLoad.value = false;
        const max = Math.max(...data.map((item) => item.total));
        drugConsumptionData.value = data
          .map((item) => {
            return {
              indexName: item.indexName,
              unitName: item.unitName,
              value: item.total || 0,
              percentage: `${toDecimalFloor((item.total / max) * 100)}%`,
            };
          })
          .sort((a, b) => b.value - a.value);
      } else {
        drugConsLoad.value = true;
        drugConsumptionData.value = mockDrugDataList;
      }
    } catch (_) {
      drugConsLoad.value = false;
    }
  };

  watch(
    () => indexList.value,
    () => {
      getProduceData();
      // getSevenDaysData();
      getDrugConsumptionData();
    },
  );
</script>

<style lang="less" scoped>
  .drug-left {
    width: 100%;
    height: 100%;

    .container {
      height: 100%;
      padding: 1rem 0rem 0 0rem;
    }

    .select-box {
      flex: 1;
      display: flex;
      justify-content: end;
      gap: 0 12px;
    }

    :deep(.ant-select-selection-item) {
      color: #fff;
    }

    .consumption {
      // padding: 2.5rem 1rem 0 0.5rem;
      padding-top: 16px;
      display: grid;
      gap: 1rem;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(3, 1fr);
      overflow-y: auto;

      // @media screen and (min-width: 1921px) {
      //   .consumption-item {
      //     justify-content: center;
      //   }
      // }

      // @media screen and (max-width: 1440px) {
      //   padding: 2.8rem 0.5rem 0 0.5rem;
      // }

      &::-webkit-scrollbar {
        width: 0px;
        height: 0px;
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: #808695;
        border-radius: 0.25rem;
      }
    }

    :deep(.ant-picker) {
      .ant-picker-input > input {
        color: #fff;
      }
    }
  }
</style>
