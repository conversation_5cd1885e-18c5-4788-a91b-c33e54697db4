// import { ref } from 'vue';
import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
// import { uploadFile } from '/@zhcz/api/config-center/scenes-group';
// 筛选项字段
export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '资源名称',
    labelWidth: 68,
    component: 'Input',
    colProps: { span: 6 },
  },
];
// 表头标题字段
export const columns: BasicColumn[] = [
  {
    title: '资源名称',
    dataIndex: 'name',
  },
  {
    title: '资源类型',
    dataIndex: 'mediumName',
  },
  {
    title: '预览',
    dataIndex: 'image',
  },
  {
    title: '路径',
    dataIndex: 'url',
  },
  {
    title: '修改时间',
    dataIndex: 'updateTime',
  },
];

// const type = ref('1');

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '资源名称',
    required: true,
    component: 'Input',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'mediumType',
    label: '资源类型',
    required: true,
    component: 'Select',
    slot: 'mediumType',
    // componentProps: {
    //   // disabled: true,
    //   options: [
    //     {
    //       label: '图片',
    //       value: '1',
    //     },
    //     {
    //       label: '视频',
    //       value: '2',
    //     },
    //   ],
    //   onChange: (e) => {
    //     type.value = e;
    //     console.log('e', e, type.value);
    //   },
    // },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'url',
    component: 'HUpload',
    label: '资源地址',
    required: true,
    slot: 'url',
    // componentProps: {
    //   api: uploadFile,
    //   compType: 1,
    //   maxSize: 50,
    //   maxNumber: 1,
    //   // accept: '.jpg,.jpeg,.png,.mp4,.flv,.webm',
    //   accept: type.value === '1' ? '.jpg,.jpeg,.png,' : '.mp4,.flv,.webm',
    // },
    colProps: {
      span: 24,
    },
  },
];

export const mockData = [
  {
    id: 1,
    name: '粗栅格',
    src: '',
  },
  {
    id: 2,
    name: '鼓风机房',
    src: '',
  },
];
