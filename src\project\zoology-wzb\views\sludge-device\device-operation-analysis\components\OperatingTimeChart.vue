<template>
  <div class="operating-time-chart">
    <div class="chart-header">
      <div class="header-left">
        <h3 class="chart-title">设备运行时长</h3>
        <div class="device-info" v-if="deviceName">
          <span class="device-label">当前设备：</span>
          <span class="device-name">{{ deviceName }}</span>
        </div>
      </div>
      <div class="header-right">
        <a-select
          v-model:value="timeRange"
          style="width: 120px"
          @change="handleTimeRangeChange"
        >
          <a-select-option value="day">日</a-select-option>
          <a-select-option value="month">月</a-select-option>
        </a-select>
        <a-date-picker
          v-if="timeRange === 'day'"
          v-model:value="selectedDate"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 200px"
          @change="handleDateChange"
          :allowClear="false"
        />
        <a-date-picker
          v-if="timeRange === 'month'"
          v-model:value="selectedMonth"
          picker="month"
          format="YYYY-MM"
          value-format="YYYY-MM"
          style="width: 200px"
          @change="handleDateChange"
          :allowClear="false"
        />
      </div>
    </div>
    
    <div class="chart-content" ref="chartRef" v-loading="loading">
      <div v-if="isEmpty" class="empty-state">
        <Icon icon="ant-design:inbox-outlined" size="48" color="#ccc" />
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="OperatingTimeChart">
  import { ref, onMounted, watch, nextTick } from 'vue';
  import { Select, SelectOption, DatePicker } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { getEquipmentOperatingTimeApi } from '/@zoology-wzb/api/device';
  import type { equipmentOperatingTimType } from '/@zoology-wzb/api/device/type';
  import dayjs from 'dayjs';

  const ASelect = Select;
  const ASelectOption = SelectOption;
  const ADatePicker = DatePicker;

  // Props
  interface Props {
    deviceId?: string;
    deviceName?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    deviceId: '',
    deviceName: '',
  });

  // 响应式数据
  const chartRef = ref<HTMLElement>();
  const loading = ref(false);
  const isEmpty = ref(false);
  const timeRange = ref('day');
  const selectedDate = ref(dayjs().format('YYYY-MM-DD'));
  const selectedMonth = ref(dayjs().format('YYYY-MM'));
  const chartData = ref<equipmentOperatingTimType>([]);

  // ECharts实例
  const { setOptions, echarts } = useECharts(chartRef);

  // 获取设备运行时长数据
  const getOperatingTimeData = async () => {
    if (!props.deviceId) {
      isEmpty.value = true;
      return;
    }

    try {
      loading.value = true;
      isEmpty.value = false;

      const params = {
        deviceId: props.deviceId,
        timeType: timeRange.value,
        date: timeRange.value === 'day' ? selectedDate.value : selectedMonth.value,
      };

      const result = await getEquipmentOperatingTimeApi(params);
      chartData.value = result || [];

      if (!result || result.length === 0) {
        isEmpty.value = true;
      } else {
        isEmpty.value = false;
        await nextTick();
        renderChart();
      }
    } catch (error) {
      console.error('获取设备运行时长数据失败:', error);
      isEmpty.value = true;
      chartData.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 渲染图表
  const renderChart = () => {
    if (!chartData.value || chartData.value.length === 0) {
      isEmpty.value = true;
      return;
    }

    const colors = ['#2E7BFF', '#FF8C2E', '#52C41A', '#722ED1', '#FA541C'];
    
    const series = chartData.value.map((item, index) => ({
      name: item.name,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2,
        color: colors[index % colors.length],
      },
      itemStyle: {
        color: colors[index % colors.length],
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: colors[index % colors.length] + '20',
            },
            {
              offset: 1,
              color: colors[index % colors.length] + '05',
            },
          ],
        },
      },
      data: item.data,
    }));

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        backgroundColor: '#fff',
        borderColor: '#e9e9e9',
        borderWidth: 1,
        textStyle: {
          color: '#333',
        },
        formatter: (params: any) => {
          let result = `<div style="color: #999; margin-bottom: 8px;">${params[0].name}</div>`;
          params.forEach((param: any) => {
            result += `
              <div style="margin-bottom: 4px;">
                ${param.marker} 
                <span style="margin-right: 20px;">${param.seriesName}</span>
                <span style="font-weight: bold;">${param.value}h</span>
              </div>
            `;
          });
          return result;
        },
      },
      legend: {
        data: chartData.value.map(item => item.name),
        top: 20,
        right: 20,
        textStyle: {
          color: '#666',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.value[0]?.date || [],
        axisLine: {
          lineStyle: {
            color: '#e9e9e9',
          },
        },
        axisLabel: {
          color: '#666',
        },
      },
      yAxis: {
        type: 'value',
        name: '运行时长(h)',
        nameTextStyle: {
          color: '#666',
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#666',
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed',
          },
        },
      },
      series,
    };

    setOptions(option);
  };

  // 处理时间范围变化
  const handleTimeRangeChange = () => {
    getOperatingTimeData();
  };

  // 处理日期变化
  const handleDateChange = () => {
    getOperatingTimeData();
  };

  // 监听设备ID变化
  watch(
    () => props.deviceId,
    () => {
      getOperatingTimeData();
    },
    { immediate: true }
  );

  onMounted(() => {
    getOperatingTimeData();
  });
</script>

<style lang="less" scoped>
  .operating-time-chart {
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .chart-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }

        .device-info {
          .device-label {
            color: #666;
            font-size: 14px;
          }

          .device-name {
            color: #1890ff;
            font-weight: 500;
            font-size: 14px;
          }
        }
      }

      .header-right {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }

    .chart-content {
      flex: 1;
      min-height: 300px;
      position: relative;

      .empty-state {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #999;

        p {
          margin-top: 12px;
          font-size: 14px;
        }
      }
    }
  }
</style>
