<template>
  <div class="operating-time-chart">
    <HlxbLineSimpleCard
      :title="'设备运行时长'"
      v-bind="{ empty: isEmpty, bottomList: indexList, loading }"
    >
      <template #headerRight>
        <div class="header-right">
          <DatePicker
            @change="handleDateChange"
            style="width: 200px"
            v-model:value="selectedMonth"
            picker="month"
            valueFormat="YYYY-MM"
            format="YYYY-MM"
            :allowClear="false"
            :disabledDate="disabledMonthDate"
          />
        </div>
      </template>
    </HlxbLineSimpleCard>
  </div>
</template>

<script setup lang="ts" name="OperatingTimeChart">
  import { ref, onMounted, watch } from 'vue';
  import { DatePicker } from 'ant-design-vue';
  import { HlxbLineSimpleCard } from 'hlxb-ui';
  import { getEquipmentOperatingTimeApi } from '/@zoology-wzb/api/device';
  import type { equipmentOperatingTimType } from '/@zoology-wzb/api/device/type';
  import dayjs from 'dayjs';

  // Props
  interface Props {
    deviceId?: string;
    deviceName?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    deviceId: '',
    deviceName: '',
  });

  // 响应式数据
  const loading = ref(false);
  const isEmpty = ref(false);
  const selectedMonth = ref(dayjs().format('YYYY-MM'));
  const chartData = ref<equipmentOperatingTimType>([]);
  const indexList = ref<any[]>([]);

  // 获取设备运行时长数据
  const getOperatingTimeData = async () => {
    if (!props.deviceId) {
      isEmpty.value = true;
      indexList.value = [];
      return;
    }

    try {
      loading.value = true;
      isEmpty.value = false;

      const params = {
        deviceId: props.deviceId,
        timeType: 'month',
        date: selectedMonth.value,
      };

      const result = await getEquipmentOperatingTimeApi(params);
      chartData.value = result || [];

      if (!result || result.length === 0) {
        isEmpty.value = true;
        indexList.value = [];
      } else {
        isEmpty.value = false;
        processChartData(result);
      }
    } catch (error) {
      console.error('获取设备运行时长数据失败:', error);
      isEmpty.value = true;
      chartData.value = [];
      indexList.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 处理图表数据，转换为HlxbLineSimpleCard需要的格式
  const processChartData = (data: equipmentOperatingTimType) => {
    indexList.value = data.map((item) => ({
      indexName: item.name,
      data: item.data,
      XAxis: item.date,
      unitName: 'h', // 运行时长单位为小时
    }));
  };

  // 禁用未来月份
  const disabledMonthDate = (current: any) => {
    return current && current > dayjs().subtract(0, 'month').endOf('day');
  };

  // 处理日期变化
  const handleDateChange = () => {
    getOperatingTimeData();
  };

  // 监听设备ID变化
  watch(
    () => props.deviceId,
    () => {
      getOperatingTimeData();
    },
    { immediate: true },
  );

  onMounted(() => {
    getOperatingTimeData();
  });
</script>

<style lang="less" scoped>
  .operating-time-chart {
    height: 100%;
    background-color: #fcfcfc;
    border-radius: 4px;
    overflow: hidden;

    .header-right {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
</style>
