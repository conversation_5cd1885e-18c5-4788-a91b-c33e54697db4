<template>
  <div class="overview-page">
    <div class="container">
      <BaseMap />
      <div class="main-title">
        <span @click="back">智慧运营管控平台BI大屏</span>
        <div class="main-title-left">
          <factory-select className="big" @change="changeFactory" />
          <div class="date-box">
            <div class="date">{{ date }}</div>
            <div class="time">{{ time }}</div>
            <div class="day">{{ week }}</div>
          </div>
        </div>
        <div class="main-title-right">
          <div class="weather-box">
            <div class="left">
              <img src="./assets/images/tianqi1.png" />
              <div>
                <div class="temp">27°C</div>
                <div class="con">阵雨转晴</div>
              </div>
            </div>
            <div class="right">
              <div>
                <img src="./assets/images/tianqi2.png" />
                <div class="con">湿度</div>
                <div class="unit">75%</div>
              </div>
              <div>
                <img src="./assets/images/tianqi3.png" />
                <div class="con2">降雨量</div>
                <div class="unit2">0.00&nbsp;&nbsp;mm</div>
              </div>
              <div>
                <img src="./assets/images/tianqi4.png" />
                <div class="con">风力</div>
                <div class="unit" style="width: 30px">1级</div>
              </div>
              <div>
                <img src="./assets/images/tianqi5.png" />
                <div class="con2">气压</div>
                <div class="unit2">101&nbsp;&nbsp;kPa</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div :class="['content', { content_personnel: activeItem.text === '人员总览' }]">
        <HomeLeft>
          <component :is="HomeLeftComponent" :isScene="isSceneApi" />
        </HomeLeft>
        <HomeCenter v-show="activeItem.text !== '人员总览'">
          <component :is="HomeCenterComponent" :isScene="isSceneApi" />
        </HomeCenter>
        <HomeRight>
          <component :is="HomeRightComponent" :isScene="isSceneApi" />
        </HomeRight>
      </div>
      <NavigationFooter />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, watch, ref, onBeforeMount } from 'vue';
  import {
    BaseMap,
    HomeLeft,
    HomeCenter,
    HomeRight,
    NavigationFooter,
    leftComponents,
    centerComponents,
    rightComponents,
  } from './components';
  import { useRouter } from 'vue-router';
  import { useNavigation, useIndexList, useTitleList } from './hooks';
  import { navigationData, weekMap } from './data';
  import { getSenceGroupTree } from '/@zhcz/api/scenes-group';
  import { FactorySelect } from '/@zhcz/components/factory-select';
  import dayjs from 'dayjs';
  import { useIntervalFn1S } from '../../utils/useInterval';
  import { getParamKeyApi } from '/@/api/admin/param';
  import { DICT } from '/@zhcz/enums/sceneResource';

  const { activeItem, setActiveItem } = useNavigation();
  const { setIndexList } = useIndexList();
  const { setTitleList } = useTitleList();
  const router = useRouter();
  const date = dayjs().format('YYYY.MM.DD');
  const time = ref(dayjs().format('HH:mm:ss'));

  const setWeek = (week) => {
    return weekMap.get(week);
  };
  const getTime = () => {
    time.value = dayjs().format('HH:mm:ss');
  };
  const week = setWeek(dayjs().day());
  useIntervalFn1S(getTime);

  const isSceneApi = ref(true);

  const getIsSceneApi = async () => {
    const data = await getParamKeyApi(DICT.IS_SCENE_API);
    isSceneApi.value = data === '1';
  };

  onBeforeMount(() => {
    getIsSceneApi();
  });

  const HomeLeftComponent = computed(() => {
    return leftComponents.get(activeItem.value.component);
  });

  const HomeCenterComponent = computed(() => {
    return centerComponents.get(activeItem.value.component);
  });

  const HomeRightComponent = computed(() => {
    return rightComponents.get(activeItem.value.component);
  });

  const findName = (indexList, groupCode) => {
    if (!indexList || !indexList.length) return '';
    const item = indexList.find((item) => item.groupCode === groupCode);
    if (item) {
      return item?.name;
    } else {
      findName(item?.children, groupCode);
    }
  };

  async function getResourceData() {
    const data = await getSenceGroupTree();
    const { text, senceCode } = activeItem.value;
    const indexList = data.filter((item) => item.senceCode === senceCode)[0]?.children;
    // 设置 ResourcesKeys
    //TO DO 设置卡片标题
    if (text === '生产总览') {
      setIndexList({
        index_LT1: [{ resourceInterfaceId: '19', groupCode: 'sczl_scsj' }],
        // index_LT2: [{ resourceInterfaceId: '3', groupCode: 'jqtsjqx' }],
        index_LB1: [{ resourceInterfaceId: '12', groupCode: 'js' }],
        index_LB2: [{ resourceInterfaceId: '12', groupCode: 'cs' }],
        index_RT: [{ resourceInterfaceId: '1816732630201143298', groupCode: 'sczl_nwsj' }],
        index_RB: [{ resourceInterfaceId: '1816732630201143299', groupCode: 'lssh' }],
        index_CT: [{ resourceInterfaceId: '19', groupCode: 'scdpzxsj' }],
        index_CB: [{ resourceInterfaceId: '19', groupCode: 'zxtpz' }],
      });
      setTitleList({
        title_LT1: findName(indexList, 'sczl_scsj') || '生产数据',
        title_LT2: findName(indexList, 'jqtsjqx') || '生产数据曲线',
        title_LB: findName(indexList, 'ssszzb') || '实时水质指标',
        title_LB_1: findName(indexList, 'js') || '进水',
        title_LB_2: findName(indexList, 'cs') || '出水',
        title_RT: findName(indexList, 'sczl_nwsj') || '生产报警统计',
        title_RB1: findName(indexList, 'lssh') || '出水水质折线图',
        // title_RB2: '',
      });
    } else if (text === '能耗总览') {
      setIndexList({
        index_LT1: [{ resourceInterfaceId: '20', groupCode: 'zr_zh_nh', jsConvert: true }],
        index_LT2: [{ resourceInterfaceId: '1816732630201143299', groupCode: 'NH_QT_SJQX' }],
        index_LB1: [{ resourceInterfaceId: '1816732630201143298', groupCode: 'RSC_NH' }],
        index_LB2: [],
        index_RT: [{ resourceInterfaceId: '3', groupCode: 'R_NH_YD' }],
        index_RB: [{ resourceInterfaceId: '1816732630201143299', groupCode: 'NH_SJ' }],
        index_CT: [{ resourceInterfaceId: '19', groupCode: 'NH_DP_ZXSJ' }],
        index_CB: [{ resourceInterfaceId: '19', groupCode: 'NHDP_ZXTPZ' }],
      });
      setTitleList({
        title_LT1: findName(indexList, 'zr_zh_nh') || '昨日综合能耗',
        title_LT2: findName(indexList, 'NH_QT_SJQX') || '能耗近七天数据曲线',
        title_LB: findName(indexList, 'RSC_NH') || '日生产能耗',
        title_LB_1: '',
        title_LB_2: '',
        title_RT: findName(indexList, 'R_NH_YD') || '日能耗电用比例',
        title_RB1: findName(indexList, 'NH_SJ') || '能耗数据',
        title_RB2: '',
      });
    } else if (text === '药耗总览') {
      setIndexList({
        index_LT1: [{ resourceInterfaceId: '19', groupCode: 'DrugConsumptionProductData' }],
        index_LT2: [
          {
            resourceInterfaceId: '1816732630201143299',
            groupCode: 'DrugConsumptionProductDataByDays',
          },
        ],
        index_LB1: [
          { resourceInterfaceId: '1816732630201143299', groupCode: 'DrugUnitConsumption' },
        ],
        index_LB2: [],
        index_RT: [{ resourceInterfaceId: '3', groupCode: 'DrugConsumptionProportion' }],
        index_RB: [
          { resourceInterfaceId: '1819296216953917441', groupCode: 'MedicationStatistics' },
        ],
        index_CT: [{ resourceInterfaceId: '19', groupCode: 'DrugConsumptionCenterData' }],
        index_CB: [{ resourceInterfaceId: '19', groupCode: 'DrugConsumptionCenterPic' }],
      });
      setTitleList({
        title_LT1: findName(indexList, 'DrugConsumptionProductData') || '昨日用药单耗',
        title_LT2: findName(indexList, 'DrugConsumptionProductDataByDays') || '单耗近七天数据曲线',
        title_LB: findName(indexList, 'DrugUnitConsumption') || '日用药单耗排行',
        title_LB_1: '',
        title_LB_2: '',
        title_RT: findName(indexList, 'DrugConsumptionProportion') || '日药耗比例',
        title_RB1: findName(indexList, 'MedicationStatistics') || '药耗数据曲线',
        title_RB2: '',
      });
    } else if (text === '设备总览') {
      // setIndexList({
      //   index_LT1: [],
      //   index_LT2: [],
      //   index_LB1: [],
      //   index_LB2: [],
      //   index_RT: [],
      //   index_RB: [],
      //   index_CT: indexList[1],
      //   index_CB: getCenterModelKeys(arrData[0].groupLinkLists),
      // });
      // setTitleList({
      //   title_LT1: '',
      //   title_LB: '',
      //   title_LB_1: '',
      //   title_LB_2: '',
      //   title_RT: arrData[2].groupName,
      //   title_RB1: arrData[3].groupName,
      // });
    }
  }

  const back = () => {
    const path = router.currentRoute.value.meta.backRoutePath;
    if (path) {
      router.push(path);
    } else {
      router.go(-1);
    }
  };

  const changeFactory = () => {
    getResourceData();
  };

  watch(
    () => activeItem.value,
    () => {
      const commonModules = ['生产总览', '能耗总览', '药耗总览', '设备总览'];
      if (commonModules.includes(activeItem.value.text)) {
        getResourceData();
      }
    },
    {
      immediate: true,
    },
  );

  onMounted(() => {
    setActiveItem(navigationData[0]);
  });
</script>

<style lang="less">
  @import './styles/big-screen.less';
</style>

<style lang="less" scoped>
  @import '/@zhcz/views/overview/assets/css/font.less';

  .page {
    padding-bottom: 80px;
  }

  .overview-page {
    width: 100%;
    // 100%可视高度 - 顶部菜单 - margin-top - 底部导航栏
    // height: ~'calc(100vh - 64px - 16px - 80px)';
    height: calc(100vh - 94px);
    overflow: auto;

    @media screen and (max-height: 716px) {
      height: ~'calc(100vh - 64px)';
    }

    &::-webkit-scrollbar {
      width: 0px;
      height: 0px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #808695;
      border-radius: 0.25rem;
    }

    .container {
      position: relative;
      width: 100%;
      height: 100%;
      max-width: unset;

      // @media screen and (max-height: 900px) {
      //   height: 800px;
      // }
      .main-title {
        background-size: 100% 100%;
        position: relative;
        height: 80px;
        width: 100%;
        text-align: center;
        // font-style: normal;
        background-image: url('./assets/images/main_title.png');
        background-repeat: no-repeat;

        span {
          position: relative;
          // font-family: YouSheBiaoTiHei;
          // font-weight: 400;
          // background: linear-gradient(180deg, #ffffff 0%, #95c0ff 100%);
          -webkit-text-fill-color: transparent;
          cursor: pointer;

          font-weight: 700;
          font-size: 36px;
          line-height: 80px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(180deg, #ffffff 0%, #a4c9ff 100%);
          background-clip: text;

          // z-index: 1;
          // @media screen and (max-width: 1440px) {
          //   font-size: 38px;
          // }
        }

        .main-title-left {
          position: absolute;
          display: flex;
          align-items: center;
          height: 44px;
          margin-bottom: 4px;
          top: 32px;
          left: 0px;
          gap: 0 12px;

          .date-box {
            display: flex;
            gap: 0 12px;
            // margin-right: 22px;
            font-weight: 400;
            font-size: 18px;
            color: #ffffff;
            line-height: 20px;

            @media screen and (max-width: 1440px) {
              margin-right: 12px;
            }

            &:before {
              display: inline-block;
              content: '';
              width: 1px;
              height: 20px;
              border: 1px solid #ffffff;
              opacity: 0.5;
              margin-right: 20px;
            }

            .time {
              line-height: 20px;
              text-align: left;
            }

            .date {
              line-height: 20px;
              font-weight: 400;
            }

            .day {
              font-weight: 400;
              line-height: 20px;
              font-size: 16px;
            }
          }
        }

        .main-title-right {
          position: absolute;
          right: 1.25rem;
          color: #fff;
          display: flex;
          align-items: center;
          height: 50px;
          bottom: 0;
          justify-content: flex-end;
          line-height: 1;

          .weather-box {
            display: flex;
            // border-left: 1px solid #ffffff;
            // padding-left: 22px;
            // @media screen and (max-width: 1440px) {
            //   padding-left: 12px;
            // }

            .left {
              display: flex;

              img {
                width: 48px;
                height: 48px;
              }

              & > div {
                text-align: left;
                display: flex;
                justify-content: space-between;
                flex-direction: column;
                margin-left: 12px;

                .temp {
                  font-family: D-DIN-PRO;
                  font-weight: bold;
                  font-size: 24px;
                  color: #ffffff;
                  line-height: 24px;
                }

                .con {
                  font-size: 14px;
                  color: #ffffff;
                  line-height: 14px;
                  // width: 48px;
                  // margin-left: 5px;
                  text-align: left;
                }
              }
            }

            .right {
              display: flex;
              flex-wrap: wrap;
              margin-left: 26px;
              width: 266px;
              justify-content: flex-start;
              align-items: end;
              @media screen and (max-width: 1440px) {
                margin-left: 12px;
                width: 245px;
              }

              img {
                width: 16px;
                height: 16px;
              }

              & > div {
                min-width: 100px;
                display: flex;
                // margin-right: 25px;
                // @media screen and (max-width: 1440px) {
                //   margin-right: 12px;
                // }

                .temp {
                  font-family: D-DIN-PRO;
                  font-weight: bold;
                  font-size: 24px;
                  color: #ffffff;
                  line-height: 24px;
                }

                .con {
                  font-size: 14px;
                  color: #ffffff;
                  line-height: 14px;
                  width: 28px;
                  margin-left: 8px;
                  text-align: left;
                }

                .con2 {
                  font-size: 14px;
                  color: #ffffff;
                  line-height: 14px;
                  width: 42px;
                  margin-left: 8px;
                  margin-right: 16px;
                  text-align: left;
                  box-sizing: border-box;
                }

                .unit {
                  font-family: D-DIN-PRO;
                  font-weight: bold;
                  font-size: 16px;
                  color: #ffffff;
                  line-height: 16px;
                  text-align: left;
                  margin-left: 16px;
                  width: 30px;
                  margin-right: 25px;
                  font-style: normal;
                }

                .unit2 {
                  font-family: D-DIN-PRO;
                  font-weight: bold;
                  font-size: 16px;
                  color: #ffffff;
                  line-height: 16px;
                  text-align: left;
                  // margin-left: 16px;
                  width: 61px;
                  // margin-right: 25px;
                  font-style: normal;
                }
              }
            }
          }
        }
      }

      .content {
        position: absolute;
        top: 112px;
        left: 0;
        display: flex;
        bottom: 32px;
        justify-content: space-between;
        width: 100%;
        height: calc(100% - 122px);
        overflow: hidden;

        &_personnel {
          overflow-x: hidden;
          overflow-y: hidden;
          gap: 0 20px;

          &::-webkit-scrollbar {
            width: 0px;
          }

          &::-webkit-scrollbar-track {
            background-color: transparent;
          }

          &::-webkit-scrollbar-thumb {
            background: #808695;
            border-radius: 0.25rem;
          }
        }
      }
    }
  }
</style>
