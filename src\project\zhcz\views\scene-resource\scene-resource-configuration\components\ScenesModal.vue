<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    width="456px"
  >
    <BasicForm @register="registerForm" />
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">确认</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup name="ScenesModal">
  import { computed, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { addOrUpdateSence } from '/@zhcz/api/config-center/scenes-group';
  import { BasicForm, useForm } from '/@/components/Form';
  import { scenesSchemas } from '../data';

  const okLoading = ref(false);
  const isEdit = ref(false);

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 84,
    baseColProps: {
      span: 24,
    },
    schemas: scenesSchemas,
    showActionButtonGroup: false,
  });
  const title = computed(() => (isEdit.value ? '编辑场景' : '新增场景'));

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const [registerModal, { closeModal }] = useModalInner((data) => {
    isEdit.value = data.isEdit;
    if (isEdit.value) {
      setFieldsValue({
        ...data.selectNode,
        senceName: data.selectNode.name,
      });
    }
  });

  async function handleSubmit() {
    try {
      okLoading.value = true;
      const values = await validate();
      const data = {
        ...values,
      };
      await addOrUpdateSence(data);
      const msg = isEdit.value ? '编辑成功' : '新增成功';
      createMessage.success(msg);
      handleCancel();
      emit('success');
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    closeModal();
    resetFields();
  }
</script>
