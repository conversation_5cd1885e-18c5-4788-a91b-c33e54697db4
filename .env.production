# Whether to open mock
VITE_USE_MOCK = true

# public path
VITE_PUBLIC_PATH = ./

# Delete console
VITE_DROP_CONSOLE = false

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VITE_BUILD_COMPRESS = 'none'

# Whether to delete origin files when using compress, default false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# Basic interface address SPA
VITE_GLOB_API_URL=/basic-api


# 工艺流程编辑器服务
VITE_GLOB_EDITOR_API_URL = 'http://*************:14444'

# 厂站摄像头服务
VITE_GLOB_CAMERA_API_URL = 'http://************:15030'

# 天气服务
VITE_GLOB_WEATHER_API_URL = https://devapi.qweather.com

# 工艺流程上传文件地址
VITE_GLOB_UPLOAD_EQUIPMENT_FIRE_PATH = /data-sence/resourceIndex/uploadFile

# 工艺流程文件地址
VITE_GLOB_EQUIPMENT_FIRE_PATH = /industryflowapi/File/GetFile/

# 地理位置服务
VITE_GLOB_GEO_API_URL = https://geoapi.qweather.com/v2/city/lookup

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly
VITE_GLOB_UPLOAD_URL=/basic-api

# Interface prefix
VITE_GLOB_API_URL_PREFIX=

# Whether to enable image compression
VITE_USE_IMAGEMIN= true

# use pwa
VITE_USE_PWA = false

# Is it compatible with older browsers
VITE_LEGACY = false

# 项目启动与打包的项目
# VITE_GLOB_PROJECT = 'hlxb-mail,gis,jd,metering,aoa,equipment-maintenance,yjpt-yt,zhcz,hlxb-zhoukougas,water-balance,process-editor,zoology-wzb'
# VITE_GLOB_PROJECT = 'gis'
# VITE_GLOB_PROJECT = 'jd,metering,hlxb-mail'
# VITE_GLOB_PROJECT = 'aoa,zhcz'
# VITE_GLOB_PROJECT = 'water-balance'
# VITE_GLOB_PROJECT = 'equipment-maintenance'
# VITE_GLOB_PROJECT = 'jd,metering,hlxb-zhoukougas'
# VITE_GLOB_PROJECT = 'yjpt'
# 生态五指耙
VITE_GLOB_PROJECT = 'zhcz,zoology-wzb'
