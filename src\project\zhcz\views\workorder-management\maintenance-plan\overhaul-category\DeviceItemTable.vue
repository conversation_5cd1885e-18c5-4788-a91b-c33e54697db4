<template>
  <div class="table-wrapper" ref="tableWrapperRef">
    <BasicTable @register="registerList" :class="!data.length ? 'display-table' : ''">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup name="">
  import { watch, nextTick, ref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { deviceItemTableColumns } from './data';

  const props = defineProps({
    data: {
      type: Array as any,
      default: () => [],
    },
  });
  const emits = defineEmits(['delete']);

  const [registerList, { setTableData, setProps }] = useTable({
    dataSource: [],
    columns: deviceItemTableColumns,
    showIndexColumn: false,
    showTableSetting: false,
    bordered: false,
    useSearchForm: false,
    maxHeight: 200,
    pagination: false,
    canResize: false,
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  function handleDelete(record) {
    emits('delete', record.eqId);
  }

  const tableWrapperRef = ref<HTMLElement>();
  const scroll = () => {
    const scrollHeight =
      tableWrapperRef.value?.querySelector('.ant-table-body table')?.clientHeight || 0;
    const bodyDom = tableWrapperRef.value?.querySelector('.ant-table-body');
    if (props.data.length > 4 && bodyDom) {
      bodyDom.style.height = '200px';
    }
    const top = props.data.length > 4 ? scrollHeight - 200 : 0;

    bodyDom?.scrollTo({
      top: top,
      behavior: 'smooth',
    });
  };

  watch(
    () => props.data,
    (val) => {
      nextTick(async () => {
        if (val) {
          await setTableData(val);
          setProps({
            canResize: val.length > 4 ? true : false,
          });
          await scroll();
        }
      });
    },
    { deep: true },
  );
</script>
<style lang="less" scoped>
  .table-wrapper {
    :deep(.ant-table-wrapper) {
      margin: 0;
      padding: 0;
    }

    .display-table {
      height: 0;
      overflow: hidden;
    }
  }
</style>
