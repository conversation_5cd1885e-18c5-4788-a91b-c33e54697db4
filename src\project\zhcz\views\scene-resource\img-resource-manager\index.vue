<template>
  <div class="h-full">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button
          type="primary"
          :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
          @click="handleAdd"
        >
          新增
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '删除',
                onClick: ($event) => $event.stopPropagation(),
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
        <template v-if="column.key === 'image'">
          <img
            :src="record.mediumType === '2' ? videoDemo : getEevReturnDomain(record.url)"
            style="width: 40px; height: 40px; object-fit: contain; cursor: pointer"
            @click="openDetail(record)"
          />
        </template>
        <template v-if="column.key === 'url'">
          <div
            style="max-width: 180px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
          >
            <a-tooltip placement="topLeft">
              <template #title>{{ record.url }}</template>
              {{ record.url }}
            </a-tooltip>
          </div>
        </template>
      </template>
    </BasicTable>
    <EditModal @success="reload" @register="registerEditModal" />
  </div>
</template>

<script lang="ts" setup name="ResourceIndex">
  import { h } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import EditModal from './EditModal.vue';
  import { columns, searchFormSchema } from './data';
  import { getEevReturnDomain } from '/@/utils/url';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getDsImgPage, deleteDsImg } from '/@zhcz/api/config-center/scenes-group';
  import videoDemo from './videoDemo.png';
  import { createImgPreview } from '/@zhcz/components/Preview/index';

  defineOptions({
    name: 'SceneResourceImgResourceManager',
  });

  const [registerTable, { reload }] = useTable({
    columns,
    api: getDsImgPage,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'size',
      listField: 'records',
      totalField: 'total',
    },
    indexColumnProps: {
      title: '序号',
      width: 80,
    },
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: searchFormSchema,
    },
    handleSearchInfoFn: (data) => {
      return {
        ...data,
      };
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });
  const [registerEditModal, { openModal: openEditModal }] = useModal();

  const handleEdit = (record: Recordable) => {
    openEditModal(true, {
      isEdit: true,
      form: { ...record },
    });
  };

  function handleAdd() {
    openEditModal(true, {
      isEdit: false,
    });
  }
  const openDetail = (row) => {
    try {
      const { url, mediumType } = row;
      const fileType = url.split('.').pop();
      if (mediumType === '2') {
        if (['mp4', 'flv', 'webm'].includes(fileType)) {
          createImgPreview({
            imageList: [getEevReturnDomain(url)],
            defaultWidth: 700,
            rememberState: true,
          });
          return;
        }
      } else if (mediumType === '1') {
        if (['png', 'jpeg', 'jpg'].includes(fileType)) {
          createImgPreview({
            imageList: [getEevReturnDomain(url)],
            defaultWidth: 700,
            rememberState: true,
          });
          return;
        }
      }
      // previewFile(record.fileDTOS[0]);
      // openPreviewModal(true);
    } catch (error) {
      createMessage.error('预览失败，请检查文件是否存在');
    }
  };
  const { createMessage } = useMessage();
  async function handleDelete(record: Recordable) {
    const params = {
      id: [record.id],
    };
    await deleteDsImg(params);
    createMessage.success('操作成功');
    reload();
  }
</script>
