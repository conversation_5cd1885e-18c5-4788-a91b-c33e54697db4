// 设备管理
import { defZhczHttp } from '/@/utils/http/axios';
import { getParamKeyApi } from '/@/api/admin/param';
enum Api {
  // 设备
  GetDevicePageList = '/eq/eqpDetail/search/eqpDetaiList',
  GetDevicePageListNew = '/eq/eqpDetail/inner/search/eqpDetailListNew',
  GetDeviceList = '/eq/eqpDetail/list/eqpDetail',
  AddDevice = '/eq/eqpDetail/add',
  UpdateDevice = '/eq/eqpDetail/edit',
  DeleteDevice = '/eq/eqpDetail/delete',
  GetDeviceDetail = '/eq/eqpDetail/searchDetail',
  GetDeviceTree = '/eq/eqpDetail/search/eqpDetailTree',
  ExportDevice = '/eq/eqpDetail/export',
  ImportDevice = '/eq/eqpDetail/import',
  UpdateDeviceRunningState = '/eq/eqpDetail/updateRunningStateReq',
  searchEqpAssets = '/eq/eqpDetail/searchEqpAssets',
  getEqpIntact = '/eq/eqpDetail/zhexiantu', // 设备完好率

  // 设备分类
  GetDeviceCategoryList = '/eq/eqpCategory/search/eqpDetaiType',
  GetDeviceCategoryTree = '/eq/eqpCategory/search/eqpDetaiTypeTree',
  GetDeviceCategoryListTree = '/eq/eqpCategory/search/EqpCategoryListTree',
  CreateDeviceCategory = '/eq/eqpCategory/addOrUpdate',
  DeleteDeviceCategory = '/eq/eqpCategory/delete',
  ImportDeviceCategory = '/eq/eqpCategory/import',
  ExportDeviceCategory = '/eq/eqpCategory/export',

  // 设备管理分类
  GetDeviceManageCategoryList = '/eq/eqpManageCategory/search/eqpDetaiManageType',
  GetDeviceManageCategoryPageList = '/eq/eqpManageCategory/search/searchEqpManageCategoryByPage',
  CreateDeviceManageCategory = '/eq/eqpManageCategory/addOrUpdate',
  DeleteDeviceManageCategory = '/eq/eqpManageCategory/delete',

  // 设备位置
  GetDeviceLocationList = '/eq/eqpLocation/search/eqpDetaiLocation',
  GetDeviceLocationTree = '/eq/eqpLocation/search/eqpDetaiLocationTree',
  GetDeviceLocationTreeNew = '/eq/eqpLocation/search/eqpDetailLocationTree',
  CreateDeviceLocation = '/eq/eqpLocation/addOrUpdate',
  DeleteDeviceLocation = '/eq/eqpLocation/delete',

  // 检索条件树
  GetTechnologyUnitTree = '/eq/technologyUnit/search/tree',
  GetTechnologyUnitTreeNew = '/eq/technologyUnit/search/treeNew',

  // 水厂
  CreateStation = '/eq/StationInfo/addOrUpdate',
  DeleteStation = '/eq/StationInfo/delete',
  DetailStation = '/eq/StationInfo/detail',

  // 水线
  CreateWaterLine = '/eq/stationWaterLine/addOrUpdate',
  DeleteWaterLine = '/eq/stationWaterLine/delete',
  DetailWaterLine = '/eq/stationWaterLine/detail',

  // 工艺段
  CreateTechnology = '/eq/technologyUnit/addOrUpdate',
  DeleteTechnology = '/eq/technologyUnit/delete',
  DetailTechnology = '/eq/technologyUnit/detail',

  // 传感器
  GetSensorPageList = '/eq/eqpSensorRalation/search/sensorInfo',
  GetSensorList = '/eq/eqpSensorRalation/search/sensorInfoList',
  DeleteSensor = '/eq/eqpSensorRalation/delete',
  CreateSensor = '/eq/eqpSensorRalation/add',
  EditSensorDisplayName = '/eq/eqpSensorRalation/editSensorDisplayName',

  // 设备事件
  GetEventPageList = '/eq/eqpEvent/search/eqpEventList',
  CreateEvent = '/eq/eqpEvent/add',
  UpdateEvent = '/eq/eqpEvent/edit',
  DeleteEvent = '/eq/eqpEvent/delete',
  GetEventDetail = '/eq/eqpEvent/searchDetail',
  MockTestEvent = '/eq/eqpEvent/mockTest',
  GetEventFnInfo = '/eq/eqpEvent/searchTsInfo',
  GetEventLog = '/eq/eventLogInfo/search/eventLogInfo',
  AllEventLogInfo = '/eq/eventLogInfo/search/allEventLogInfo',

  // 设备分析
  GetAnalysisHealth = '/eq/eqpAnalysis/getHealth',
  GetAnalysisUsedYear = '/eq/eqpAnalysis/getUsedYear',
  GetAnalysisFixTime = '/eq/eqpAnalysis/getFixTime',
  GetAnalysisFixZhuZhuangTu = '/eq/eqpAnalysis/getEqFixZhuZhuangTu',
  GetEqEnvironmentData = '/eq/eqpAnalysis/getEqEnvironmentData',
  GetEqCoreIndicatorCode = '/eq/eqpAnalysis/getEqCoreIndicatorCode',
  GetEqCoreSituation = '/eq/eqpAnalysis/getEqCoreSituation',
  GetEqPowerIndicatorCode = '/eq/eqpAnalysis/getEqPowerIndicatorCode',
  GetEqPowerSituation = '/eq/eqpAnalysis/getEqPowerSituation',

  // 环境事件
  GetEnvironmentEventPageList = '/eq/eqpEnvironmentEvent/search/eqpEnvironmentEventList',
  CreateEnvironmentEvent = '/eq/eqpEnvironmentEvent/addOrUpdate',
  DeleteEnvironmentEvent = '/eq/eqpEnvironmentEvent/delete',
  GetEqEnvironmentEventIndicator = '/eq/eqpAnalysis/getEqEnvironmentIndicator',
  MockTestEnvironmentEvent = '/eq/eqpEnvironmentEvent/mockTest',
  // 设备事件库
  GetEqIndicatorList = '/eq/eqpAnalysis/searchEqIndicatorList',

  // 设备工单记录
  GetEqWordOrderPage = '/eq/order/workorder/page',

  // 设备资料
  GetEqDocPage = '/eq/eqpDoc/search/eqpDocPage',
  CreateEqDoc = '/eq/eqpDoc/save',
  DeleteEqDoc = '/eq/eqpDoc/delete',
}

/**
 * 设备
 */

// 获取设备分页列表
export const getDevicePageListApi = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetDevicePageList,
    data,
  });

export const getDevicePageListNew = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetDevicePageListNew,
    data,
  });

// 设备列表
export const getDeviceList = () =>
  defZhczHttp.get<any>({
    url: Api.GetDeviceList,
  });

// 新增设备
export const addDevice = (data) =>
  defZhczHttp.post<any>({
    url: Api.AddDevice,
    data,
  });

// 修改设备
export const updateDevice = (data) =>
  defZhczHttp.post<any>({
    url: Api.UpdateDevice,
    data,
  });

// 删除设备
export const deleteDevice = (data: { idList: number[] }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteDevice,
    data,
  });

// 获取设备详情
export const getDeviceDetail = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.GetDeviceDetail,
    data,
  });

// 获取设备树
export const getDeviceTree = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetDeviceTree,
    data,
  });
export const exportDevice = (data) =>
  defZhczHttp.post(
    {
      url: Api.ExportDevice,
      data,
      responseType: 'arraybuffer',
    },
    { isTransformResponse: false, isReturnNativeResponse: true },
  );

export const tempImportDevice = async () => {
  const filePath = await getParamKeyApi('eqp_detail_file_path');
  return defZhczHttp.get<any>(
    { url: filePath, responseType: 'arraybuffer' },
    { isTransformResponse: false },
  );
};

export const importDevice = (data) =>
  defZhczHttp.post<any>({
    url: Api.ImportDevice,
    data,
    timeout: 120 * 1000,
  });

// 更新设备运行状态
export const updateDeviceRunningState = (data) =>
  defZhczHttp.post<any>({
    url: Api.UpdateDeviceRunningState,
    data,
  });

// 获取设备资产
export const searchEqpAssetsApi = (data) =>
  defZhczHttp.post<any>({
    url: Api.searchEqpAssets,
    data,
  });

// 设备完好率
export const getEqpIntactApi = (data) =>
  defZhczHttp.post<any>({
    url: Api.getEqpIntact,
    data,
  });

/**
 * 设备分类
 */

// 设备分类列表
export const getDeviceCategoryList = (data: { useFlag: 0 | 1 }) =>
  defZhczHttp.post<any>({
    url: Api.GetDeviceCategoryList,
    data,
  });

// 设备分类树
export const getDeviceCategoryTree = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetDeviceCategoryTree,
    data,
  });

export const getDeviceCategoryListTree = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetDeviceCategoryListTree,
    data,
  });

// 新增编辑设备分类
export const createDeviceCategory = (data) =>
  defZhczHttp.post<any>({
    url: Api.CreateDeviceCategory,
    data,
  });

// 删除设备分类(树)
export const deleteDeviceCategory = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteDeviceCategory,
    data,
  });

// 设备分类导入
export const importDeviceCategory = (params: FormData) =>
  defZhczHttp.post<any>({
    url: Api.ImportDeviceCategory,
    params,
    timeout: 120 * 1000,
  });

// 设备分类导出
export const exportDeviceCategory = (data) =>
  defZhczHttp.post(
    {
      url: Api.ExportDeviceCategory,
      data,
      responseType: 'arraybuffer',
    },
    { isTransformResponse: false, isReturnNativeResponse: true },
  );

// 设备类别模板
export const tempDeviceCategory = async () => {
  const filePath = await getParamKeyApi('eqp_category_file_path');
  return defZhczHttp.get<any>(
    { url: filePath, responseType: 'arraybuffer' },
    { isTransformResponse: false },
  );
};

/**
 * 设备管理分类
 */

// 设备管理分类列表
export const getDeviceManageCategoryList = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetDeviceManageCategoryList,
    data,
  });
// 设备管理分类列表分页
export const getDeviceManageCategoryPageList = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetDeviceManageCategoryPageList,
    data,
  });

// 新增编辑设备管理分类
export const createDeviceManageCategory = (data) =>
  defZhczHttp.post<any>({
    url: Api.CreateDeviceManageCategory,
    data,
  });

// 删除设备管理分类
export const deleteDeviceManageCategory = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteDeviceManageCategory,
    data,
  });

/**
 * 设备位置
 */

// 设备位置列表
export const getDeviceLocationList = (data: { useFlag: 0 | 1 }) =>
  defZhczHttp.post<any>({
    url: Api.GetDeviceLocationList,
    data,
  });

// 设备位置树
export const getDeviceLocationTree = (data: { useFlag: 0 | 1 }) =>
  defZhczHttp.post<any>({
    url: Api.GetDeviceLocationTree,
    data,
  });
// 设备位置树新
export const getDeviceLocationTreeNew = (data: { useFlag: 0 | 1 | null }) =>
  defZhczHttp.post<any>({
    url: Api.GetDeviceLocationTreeNew,
    data,
  });

// 新增编辑设备位置
export const createDeviceLocation = (data) =>
  defZhczHttp.post<any>({
    url: Api.CreateDeviceLocation,
    data,
  });

// 删除设备位置
export const deleteDeviceLocation = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteDeviceLocation,
    data,
  });

/**
 * 检索条件树
 */
export const getTechnologyUnitTreeApi = (data: Indexable) =>
  defZhczHttp.post<any>({
    url: Api.GetTechnologyUnitTree,
    data,
  });

export const getTechnologyUnitTreeNew = (data: { showTuTypeFlag: boolean; hideEqpFlag: boolean }) =>
  defZhczHttp.post<any>({
    url: Api.GetTechnologyUnitTreeNew,
    data,
  });

/**
 * 水厂
 */

// 添加编辑水厂
export const createStation = (data: {
  stationName: string;
  stationId: string;
  sort: number;
  stationUnitType: number;
}) =>
  defZhczHttp.post<any>({
    url: Api.CreateStation,
    data,
  });

// 删除水厂
export const deleteStation = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteStation,
    data,
  });

// 水厂详情
export const detailStation = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.DetailStation,
    data,
  });

/**
 * 水线
 */

// 添加编辑水线
export const createWaterLine = (data) =>
  defZhczHttp.post<any>({
    url: Api.CreateWaterLine,
    data,
  });

// 删除水线
export const deleteWaterLine = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteWaterLine,
    data,
  });

// 水线详情
export const detailWaterLine = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.DetailWaterLine,
    data,
  });

/**
 * 工艺段
 */

// 添加编辑工艺段
export const createTechnology = (data) =>
  defZhczHttp.post<any>({
    url: Api.CreateTechnology,
    data,
  });

// 删除工艺段
export const deleteTechnology = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteTechnology,
    data,
  });

// 工艺段详情
export const detailTechnology = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.DetailTechnology,
    data,
  });

/**
 * 传感器
 */

// 获取传感器分页列表
export const getSensorPageList = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetSensorPageList,
    data,
  });

// 获取传感器列表
export const getSensorList = (data: { equipmentId: number }) =>
  defZhczHttp.post<any>({
    url: Api.GetSensorList,
    data,
  });

// 删除传感器
export const deleteSensor = (data: { idList: number[] }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteSensor,
    data,
  });

// 新增传感器
export const createSensor = (data: {
  type: number;
  associatedIndicatorList: { indicatorCode: string; displayName: string }[];
  equipmentId: number;
}) =>
  defZhczHttp.post<any>({
    url: Api.CreateSensor,
    data,
  });

// 编辑传感器显示名称
export const editSensorDisplayName = (data: { id: number; displayName: string }) =>
  defZhczHttp.post<any>({
    url: Api.EditSensorDisplayName,
    data,
  });

/**
 *
 * 设备事件
 */
export const getEventPageList = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetEventPageList,
    data,
  });

export const createEvent = (data) =>
  defZhczHttp.post<any>({
    url: Api.CreateEvent,
    data,
  });

export const updateEvent = (data) =>
  defZhczHttp.post<any>({
    url: Api.UpdateEvent,
    data,
  });

export const deleteEvent = (data: { idList: number[] }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteEvent,
    data,
  });

export const getEventDetail = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.GetEventDetail,
    data,
  });

export const mockTestEvent = (data: {
  equipmentId: number;
  content: string;
  associatedLabelList: string[];
}) =>
  defZhczHttp.post<any>({
    url: Api.MockTestEvent,
    data,
  });

export const getEventFnInfo = () =>
  defZhczHttp.post<any>({
    url: Api.GetEventFnInfo,
  });

// 事件日志
export const getEventLog = (data: { equipmentId: number; startTime: string; endTime: string }) =>
  defZhczHttp.post<any>({
    url: Api.GetEventLog,
    data,
  });

// 事件日志
export const getEventLogAll = (data?: Recordable) =>
  defZhczHttp.post<any>({
    url: Api.AllEventLogInfo,
    data,
  });

/**
 * 设备分析
 */

// 健康指数
export const getAnalysisHealth = (params: { eqId: number }) =>
  defZhczHttp.get<any>({
    url: Api.GetAnalysisHealth,
    params,
  });

// 使用年限
export const getAnalysisUsedYear = (params: { eqId: number }) =>
  defZhczHttp.get<any>({
    url: Api.GetAnalysisUsedYear,
    params,
  });

// 修复时长
export const getAnalysisFixTime = (params: { eqId: number }) =>
  defZhczHttp.get<any>({
    url: Api.GetAnalysisFixTime,
    params,
  });

// 维修情况
export const getAnalysisFixZhuZhuangTu = (data: {
  eqId: number;
  startTime: string;
  endTime: string;
}) =>
  defZhczHttp.post<any>({
    url: Api.GetAnalysisFixZhuZhuangTu,
    data,
  });

// 环境指标
export const getEqEnvironmentData = (params: { equipmentId: number }) =>
  defZhczHttp.get<any>({
    url: Api.GetEqEnvironmentData,
    params,
  });

// 设备情况类型
export const getEqCoreIndicatorCode = (params: { equipmentId: number }) =>
  defZhczHttp.get<any>({
    url: Api.GetEqCoreIndicatorCode,
    params,
  });

// 设备情况
export const getEqCoreSituation = (data: {
  equipmentId: number;
  indexCode: string;
  startDateTime: string;
  endDateTime: string;
  timeType: number;
}) =>
  defZhczHttp.post<any>({
    url: Api.GetEqCoreSituation,
    data,
  });

// 设备供电类型
export const getEqPowerIndicatorCode = (params: { equipmentId: number }) =>
  defZhczHttp.get<any>({
    url: Api.GetEqPowerIndicatorCode,
    params,
  });

// 设备供电
export const getEqPowerSituation = (data: {
  equipmentId: number;
  indexCode: string;
  startDateTime: string;
  endDateTime: string;
  timeType: number;
}) =>
  defZhczHttp.post<any>({
    url: Api.GetEqPowerSituation,
    data,
  });

/**
 * 环境事件
 */

// 环境事件列表
export const getEnvironmentEventPageList = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetEnvironmentEventPageList,
    data,
  });

// 新增环境事件
export const createEnvironmentEvent = (data) =>
  defZhczHttp.post<any>({
    url: Api.CreateEnvironmentEvent,
    data,
  });

export const deleteEnvironmentEvent = (data: { idList: number[] }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteEnvironmentEvent,
    data,
  });

export const getEqEnvironmentEventIndicator = (params: { equipmentId: number }) =>
  defZhczHttp.get<any>({
    url: Api.GetEqEnvironmentEventIndicator,
    params,
  });

export const mockTestEnvironmentEvent = (data: {
  equipmentId: number;
  content: string;
  associatedLabelList: string[];
}) =>
  defZhczHttp.post<any>({
    url: Api.MockTestEnvironmentEvent,
    data,
  });

// 设备事件库
// sensorTypeList  0：所有标签 1： 工艺标签  2：用电标签 3： 心跳标签 4： 核心标签
export const getEqIndicatorList = (data: { equipmentId: number; sensorTypeList: number[] }) =>
  defZhczHttp.post<any>({
    url: Api.GetEqIndicatorList,
    data,
  });

// 设备工单记录
export const getEqWordOrderPage = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetEqWordOrderPage,
    data,
  });

// 设备资料
export const getEqDocPage = (data) =>
  defZhczHttp.post<any>({
    url: Api.GetEqDocPage,
    data,
  });
export const createEqDoc = (data) =>
  defZhczHttp.post<any>({
    url: Api.CreateEqDoc,
    data,
  });
export const deleteEqDoc = (data: { id: number }) =>
  defZhczHttp.post<any>({
    url: Api.DeleteEqDoc,
    data,
  });
