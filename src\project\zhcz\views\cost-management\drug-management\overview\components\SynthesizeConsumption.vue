<template>
  <Card :bordered="false" dis-hover>
    <!-- :data-resource-code="dataType === 1 ? dayGroupCode : monthGroupCode" -->
    <template #title>
      <div data-index-name="ProportionDrugConsumption" class="flex-center"
        >药耗占比
        <div class="button_ai" v-if="indexList.length" @click="handleCreate()"
          ><img :src="aiImg" alt="" srcset="" /><span class="text">AI分析</span></div
        >
      </div>
    </template>
    <template #extra>
      <div class="flex extra-wrap">
        <DatePicker
          style="width: 200px"
          v-model:value="monthDate"
          picker="month"
          :allowClear="false"
          valueFormat="YYYY-MM"
          :disabledDate="disabledDateMonth"
        />
      </div>
    </template>
    <div
      style="
        padding-top: 1px;
        width: 100%;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;
      "
      v-loading="loading"
      v-if="indexList.length"
    >
      <div ref="chartRef" style="height: 100%; min-height: 140px"></div>
    </div>
    <div v-else class="empty-box">
      <HEmpty />
    </div>
    <HAiDrawer @register="registerDrawer" :aiQuestion="aiDataCopy[0].aiQuestion" />
  </Card>
</template>

<script setup lang="ts">
  import { Card, DatePicker } from 'ant-design-vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { ref, watch } from 'vue';
  import dayjs from 'dayjs';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  // import { EChartsOption } from 'echarts';
  // import { useIntervalFn } from '@vueuse/core';
  // import { useUserStore } from '/@/store/modules/user';
  import { roundAndConvert } from '/@zhcz/utils/number';
  import { useWindowSize } from '@vueuse/core';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import aiImg from '../AI.png';
  // import Icon from '/@/components/Icon/index';
  // import { aiData } from '../data';
  import { HAiDrawer } from '/@zhcz/components/HAiDrawer/index';
  import { useDrawer } from '/@/components/Drawer';
  // import { useMessage } from '/@/hooks/web/useMessage';
  const [registerDrawer, { openDrawer }] = useDrawer();
  function handleCreate() {
    openDrawer(true, {
      record: {},
      isUpdate: false,
    });
  }
  const aiDataCopy = ref([
    {
      aiQuestion:
        '自来水厂药耗占比中，2025年3月，碱铝_kg占比_%预估_元、次氯酸钠_kg占比_%预估_元、石灰_kg占比_%预估_元、高锰酸钾_kg占比_%预估_元、PAM_kg占比_%预估_元，综合以上内容分析当前情况。',
      deepAnalysis:
        '好的，用户问药耗占比的原因，我需要详细分析。首先，我得回忆一下浊度的定义，素全面分析.\n可能需要进一步的信息来确定具体原因，但先列出这些可能性。\n帮助用户排查。',
      explain:
        '药耗占比通常由多种因素引起，具体原因需结合环境、人为活动和水处理流程综合分析。以下是常见原因分类：',
      resultTitle: '总览分析',
      resultDes: '',
      summaryTitle: '总结',
      summary:
        '若药耗占比不合理，可能影响水质安全（如隐藏病原微生物），建议及时联系水务部门或环保机构介入调查。',
    },
  ]);
  // const props = defineProps({
  //   dayResourceInterfaceId: {
  //     type: String,
  //     default: '',
  //   },
  //   dayGroupCode: {
  //     type: String,
  //     default: '',
  //   },
  //   monthResourceInterfaceId: {
  //     type: String,
  //     default: '',
  //   },
  //   monthGroupCode: {
  //     type: String,
  //     default: '',
  //   },
  // });
  const colorList = [
    'rgba(46, 140, 255, 1)',
    'rgba(46, 196, 255, 1)',
    'rgba(129, 67, 255, 1)',
    'rgba(34, 205, 128, 1)',
    'rgba(118, 195, 31, 1)',
    'rgba(255, 140, 46, 1)',
  ];
  const loading = ref(false);
  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);
  const typeType = ref(1);
  const dataType = ref(2);
  const monthDate = ref(dayjs().subtract(1, 'month').format('YYYY-MM'));
  const dayDate = ref(dayjs().format('YYYY-MM-DD'));
  const chartRef = ref<any>(null);
  const { setOptions } = useECharts(chartRef);
  const { width } = useWindowSize();
  const priceList = ref<{ label: string; value: number; remarks: number }[]>([]);
  const getprice = async () => {
    try {
      const data = await getDictTypeListApi({
        type: 'yhdj',
      });
      if (data.length) {
        priceList.value = data.map((item) => {
          const { label, value, remarks } = item;
          if (Number(label) < 10) {
            return {
              value: Number(value),
              label,
              remarks: Number(remarks),
            };
          } else {
            return {
              value: Number(value),
              label,
              remarks: Number(remarks),
            };
          }
        });
      }
      console.log('priceList.value', priceList.value);
    } catch (err) {}
  };
  function handleSetVisitChart() {
    const sum = indexList.value
      .map((item) =>
        item.value == null || !item.value ? 0 : Number(roundAndConvert(item.value, 0)),
      )
      .reduce((prev, cur) => {
        return prev + cur;
      }, 0);

    let commonLeft = 15;
    const option = {
      title: [
        {
          text: `${sum}`,
          left: commonLeft - 0.25 + '%',

          top: '40%',
          textAlign: 'center',
          textStyle: {
            fontSize: 26,
            fontWeight: 600,
            height: 50,
            color: '#333333',
          },
        },
        {
          text: `总用药(${indexList.value[0]?.unitName ?? ''})`,
          left: commonLeft + '%',
          top: '52%',
          textAlign: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 400,
            color: '#999999',
          },
        },
      ],
      tooltip: {
        trigger: 'item',
        formatter: (data) => {
          const seriesIndex = data.seriesIndex;
          console.log('priceList.value[seriesIndex]', seriesIndex, priceList.value[seriesIndex]);
          const price = roundAndConvert(
            Number(priceList.value[seriesIndex].value * data.data.value),
            0,
          );
          // console.log('药耗占比tootipData', data);
          return `${data.marker} <span>${data.name}</span><br /> <span>${
            data.data.value === null ? '-' : roundAndConvert(Number(data.data.value), 2)
          }</span><span>${data.data.unit}</span><br /> <span>${
            data.percent
          }%</span><br /><span>${price}元</span>`;
        },
        backgroundColor: '#ffffff',
        textStyle: {
          color: '#333',
          fontSize: 14,
          lineHeight: 28,
          height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
      },
      legend: {
        icon: 'circle',
        left: '30%',
        top: 'middle',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 15,
        textStyle: {
          color: '#77899c',
          overflow: 'truncate',
          rich: {
            title: {
              width: width.value <= 1366 ? 65 : width.value <= 1440 ? 80 : 120,
              color: '#666666',
              fontSize: 14,
              fontWeight: 400,
              lineHeight: 66,
              padding: [25, 0, 25, 0],
            },
            num: {
              width: dataType.value === 1 ? 75 : 85,
              padding: [25, 8, 25, 0],
              color: '#333',
              fontSize: 16,
              lineHeight: 66,
              fontWeight: 600,
              align: 'left',
            },
            // unit: {
            //   padding: [3, 16, 0, 4],
            //   color: '#666',
            //   fontSize: 14,
            //   lineHeight: 14,
            // },
            tag: {
              color: '#333',
              fontSize: 16,
              width: width.value <= 1366 ? 100 : width.value <= 1920 ? 120 : 200,
              lineHeight: 66,
              fontWeight: 600,
              align: 'left',
              padding: [25, 0, 25, 0],
            },
          },
        },
        formatter: (name) => {
          // let percentage;
          let value = '';
          let unit = '';
          let tag = '()';
          const data = indexList.value.map((item) => ({
            ...item,
            value: Number(roundAndConvert(Number(item.value), 0)),
          }));

          data.forEach((item: any) => {
            if (name === item.indexName) {
              value = item.value === null || item.value === undefined ? '-' : `${item.value}`;
              unit = item.value === null || item.value === undefined ? '' : `${item.unitName}`;
              // const index = percentageResult.findIndex((i) => i.value == item.value);
              tag = `(${roundAndConvert(Number(item.price * item.value), 0)}元)`;
              // percentage = percentageResult[index].percentage;
            }
          });

          // return `{title|${name}}`;
          return `{title|${name}}{num|${value + unit}}{tag|${tag}}`;
          // {tag|${percentage}%}
        },
      },
      series: {
        type: 'pie',
        radius: ['50%', '80%'],
        center: [commonLeft + '%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emptyCircleStyle: {
          color: 'lightgray',
        },
        labelLine: {
          show: false,
        },
        itemStyle: {
          color: function (colors) {
            return colorList[colors.dataIndex % colorList.length];
          },
        },
        data: indexList.value.map((i) => ({
          value: i.value !== null ? (i.value ? roundAndConvert(i.value, 0) : '0.0000001') : '-',
          name: i.indexName,
          originValue: i.value,
          unit: i.unitName,
        })),
      },
      animation: false,
    };
    setOptions(option as any);
  }

  function disabledDateMonth(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('day');
  }

  const getData = async () => {
    const startDateTime =
      dataType.value === 1
        ? dayjs(dayDate.value).format('YYYY-MM-DD 00:00:00')
        : dayjs(monthDate.value).startOf('month').format('YYYY-MM-DD 00:00:00');
    const endDateTime =
      dataType.value === 1
        ? dayjs(dayDate.value).format('YYYY-MM-DD 23:59:59')
        : dayjs(monthDate.value).endOf('month').format('YYYY-MM-DD 23:59:59');
    const params = {
      startDateTime: startDateTime,
      endDateTime: endDateTime,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    // const indexCodeDataList = [
    //   {
    //     resourceInterfaceId: '3',
    //     groupCode: 'flydtj_r',
    //   },
    //   {
    //     resourceInterfaceId: '3',
    //     groupCode: 'flydtj_y',
    //   },
    // ];
    const paramData = {
      resourceInterfaceId: '3',
      groupCode: 'yhzl2_yhzb_y',
      paramsData: JSON.stringify(params),
    };

    const { data } = await callResourceFunction(paramData);
    console.log('药耗占比', data);
    if (data && data.length) {
      let newData = data.map((item, index) => {
        const price = priceList.value.find((i) => i.label === item.indexCode);
        return {
          indexName: item.indexName,
          indexCode: item.indexCode,
          unitName: item.unitName,
          price: price?.value,
          value: item.data
            .map((i) => Number(i.value))
            .reduce((prev, cur) => {
              return prev + cur;
            }, 0),
          color: colorList[index % 8],
          ratio: 0,
        };
      });
      const total = newData
        .map((i) => i.value)
        .reduce((prev, cur) => {
          return prev + cur;
        }, 0);
      if (total) {
        newData = newData.map((item) => {
          item.ratio = ((item.value / total) * 100).toFixed(2);
          return item;
        });
      }
      indexList.value = newData;
      const copyData = newData.map((item) => {
        const { indexName, price, unitName, value, ratio } = item;
        return (
          indexName + value + unitName + '占比' + ratio + '%预估' + price * value + '元' + '\n'
        );
      });
      aiDataCopy.value[0].aiQuestion = `自来水厂药耗占比中，${dayjs(monthDate.value).format(
        'YYYY年MM月',
      )}，${copyData.join(',')}综合以上内容分析当前情况。`;
    } else {
      indexList.value = [];
    }
    handleSetVisitChart();
  };

  // const { pause, resume } = useIntervalFn(getData, 60 * 1000);
  // const userStore = useUserStore();
  // const token = computed(() => userStore.getToken);
  // watch(
  //   () => token.value,
  //   (newVal) => {
  //     if (!newVal) {
  //       pause();
  //     }
  //   },
  // );

  // watch(
  //   () => dayDate.value,
  //   async () => {
  //     if (dataType.value === 1) {
  //       pause();
  //       await getData();
  //       resume();
  //     }
  //   },
  // );

  watch(
    () => monthDate.value,
    async (newVal) => {
      if (dataType.value === 2) {
        console.log('newVal=>月', newVal);
        // pause();
        await getData();
        // resume();
      }
    },
  );

  watch(
    () => dataType.value,
    async (newVal) => {
      console.log('newVal=>日期类型', newVal);
      monthDate.value = dayjs().subtract(1, 'month').format('YYYY-MM');
      dayDate.value = dayjs().format('YYYY-MM-DD');
      // pause();
      await getData();
      // resume();
    },
  );
  watch(
    () => typeType.value,
    async () => {
      monthDate.value = dayjs().subtract(1, 'month').format('YYYY-MM');
      dayDate.value = dayjs().format('YYYY-MM-DD');
      // pause();
      await getData();
      // resume();
    },
  );

  // onMounted(() => {
  getprice();
  getData();
  // resume();
  // });

  // onActivated(() => {
  //   resume();
  // });

  // onUnmounted(() => {
  //   pause();
  // });

  // onDeactivated(() => {
  //   pause();
  // });
</script>

<style lang="less" scoped>
  .ant-card {
    height: 100%;

    :deep(.ant-card-head) {
      padding: 0 16px;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      min-height: 48px;
      border-bottom: 1px solid #e9e9e9;
    }

    :deep(.ant-card-body) {
      padding: 16px;
      height: calc(100% - 50px);
      overflow: auto;
    }
  }

  .extra-wrap {
    :deep(.ant-select) {
      margin-right: 16px;

      // .ant-select-selector {
      //   width: 80px;
      // }
    }
  }

  .legend-container {
    position: absolute;
    width: 50%;
    height: 100%;
    right: 0;
    top: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 16px;

    .item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      cursor: pointer;

      .point {
        width: 12px;
        height: 12px;
        border-radius: 50%;
      }

      .name {
        padding-left: 8px;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 14px;
        width: 160px;
        white-space: nowrap; /* 让文本不换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 使用省略号代替超出部分 */
      }

      .value {
        margin-left: 8px;
        font-size: 20px;
        font-weight: 600;
        color: #333333;
        display: flex;
        align-items: flex-end;

        .number-value {
          display: inline-block;
          max-width: 120px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .unit {
          display: inline-block;
          margin-left: 4px;
          font-size: 14px;
          font-weight: 400;
          color: #666;
          margin-bottom: 4px;
        }
      }

      .tag {
        margin-left: 12px;
        width: 72px;
        height: 26px;
        background: rgba(32, 118, 212, 0.1);
        border-radius: 13px;
        text-align: center;
        font-weight: 500;
        font-size: 14px;
        color: #2076d4;
        line-height: 26px;
      }
    }
  }

  .flex-center {
    display: flex;
    align-items: center;
    justify-self: start;

    .button_ai {
      margin-left: 8px;
      padding: 5px 8px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      cursor: pointer;

      img {
        width: 16px;
      }

      .text {
        font-size: 14px;
        color: #0b62cb;
        line-height: 14px;
        padding-left: 4px;
      }

      &:hover {
        background: rgba(11, 98, 203, 0.12);
      }
    }
  }

  .empty-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #999;
  }
</style>
