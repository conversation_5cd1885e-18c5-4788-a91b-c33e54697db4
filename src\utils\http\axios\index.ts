// axios配置  可自行根据项目进行更改，只需更改该文件即可，其他文件可以不动
// The axios configuration can be changed according to the project, just change the file, other files can be left unchanged

import type { AxiosResponse } from 'axios';
import { clone } from 'lodash-es';
import type { RequestOptions, Result } from '/#/axios';
import type { AxiosTransform, CreateAxiosOptions } from './axiosTransform';
import { VAxios } from './Axios';
import { checkStatus } from './checkStatus';
import { useGlobSetting } from '/@/hooks/setting';
import { useMessage } from '/@/hooks/web/useMessage';
import { RequestEnum, ResultEnum, ContentTypeEnum } from '/@/enums/httpEnum';
import { isString, isUnDef, isNull, isEmpty, isFunction } from '/@/utils/is';
import { getToken } from '/@/utils/auth';
// getUserInfo
import { setObjToUrlParams, deepMerge } from '/@/utils';
import { useErrorLogStoreWithOut } from '/@/store/modules/errorLog';
import { useI18n } from '/@/hooks/web/useI18n';
import { joinTimestamp, formatRequestDate } from './helper';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { AxiosRetry } from '/@/utils/http/axios/axiosRetry';
import { createLocalStorage } from '/@/utils/cache';
import axios from 'axios';
import { h } from 'vue';
import { AxiosResponseTranslate } from './axiosResponseTranslate';
import { getAppEnvConfig } from '/@/utils/env';
const {
  VITE_GLOB_EDITOR_API_URL,
  VITE_GLOB_CAMERA_API_URL,
  VITE_GLOB_WEATHER_API_URL,
  VITE_GLOB_GEO_API_URL,
} = getAppEnvConfig();

import { TENANTID_KEY, FACTORY_KEY, PROJECTCODE_KEY } from '/@/enums/cacheEnum';
// import { useEmitt } from '/@/hooks/web/useEmitt';

const globSetting = useGlobSetting();
const urlPrefix = globSetting.urlPrefix;
const { createMessage, createErrorModal, createSuccessModal } = useMessage();

/**
 * @description: 数据处理，方便区分多种处理方式
 */
const transform: AxiosTransform = {
  /**
   * @description: 处理响应数据。如果数据不是预期格式，可直接抛出错误
   */
  transformResponseHook: async (res: AxiosResponse<Result>, options: RequestOptions) => {
    const { t } = useI18n();

    const { isTransformResponse, isReturnNativeResponse, fileName } = options;
    // 是否返回原生响应头 比如：需要获取响应头时使用该属性
    if (isReturnNativeResponse) {
      return res;
    }
    // 不进行任何处理，直接返回
    // 用于页面代码可能需要直接获取code，data，message这些信息时开启
    if (!isTransformResponse) {
      // 自动解析文件名称
      if (fileName) {
        const filename = decodeURI(res.headers['content-disposition'].split(';')[1].split('=')[1]);
        return {
          data: res.data,
          fileName: filename,
        };
      }
      return res.data;
    }
    // 错误的时候返回

    const { data } = res;
    if (!data) {
      // return '[HTTP] Request has no return value';
      throw new Error(t('sys.api.apiRequestFailed'));
    }
    //  这里 code，result，message为 后台统一的字段，需要在 types.ts内修改为项目自己的接口返回格式
    const { code, data: result, msg } = data;

    // 这里逻辑可以根据项目进行修改
    const hasSuccess = data && Reflect.has(data, 'code') && code === ResultEnum.SUCCESS;
    if (hasSuccess) {
      let successMsg = msg;

      if (
        !options.successCustomMsg &&
        (isNull(successMsg) || isUnDef(successMsg) || isEmpty(successMsg))
      ) {
        successMsg = t(`sys.api.operationSuccess`);
      }

      if (options.successMessageMode === 'modal') {
        createSuccessModal({ title: t('sys.api.successTip'), content: successMsg });
      } else if (successMsg && options.successMessageMode === 'msg') {
        createMessage.success(successMsg);
      }

      return result;
    }

    // 在此处根据自己项目的实际情况对不同的code执行不同的操作
    // 如果不希望中断当前请求，请return数据，否则直接抛出异常即可
    let timeoutMsg = '';
    switch (code) {
      case ResultEnum.TIMEOUT:
      case ResultEnum.TIMEOUT1:
        const userStore = useUserStoreWithOut();
        userStore.setToken(undefined);
        await userStore.logout(true);
        timeoutMsg = msg || t('sys.api.timeoutMessage');
        break;
      default:
        if (msg) {
          timeoutMsg = msg;
        }
    }

    // errorMessageMode='modal'的时候会显示modal错误弹窗，而不是消息提示，用于一些比较重要的错误
    // errorMessageMode='none' 一般是调用时明确表示不希望自动弹出错误提示
    if (options.errorMessageMode === 'modal') {
      createErrorModal({
        title: t('sys.api.errorTip'),
        content: renderTipsText(options.richErrorMessage, timeoutMsg),
        onOk() {
          isFunction(options.errorOnOKcallback) && options.errorOnOKcallback(timeoutMsg);
        },
        ...(options?.modalConfig ?? {}),
      });
    } else if (options.errorMessageMode === 'msg') {
      createMessage.error(timeoutMsg);
    }

    throw new Error(timeoutMsg || t('sys.api.apiRequestFailed'));
  },

  // 请求之前处理config
  beforeRequestHook: (config, options) => {
    const { apiUrl, joinPrefix, joinParamsToUrl, formatDate, joinTime = true, urlPrefix } = options;

    if (joinPrefix) {
      config.url = `${urlPrefix}${config.url}`;
    }

    if (apiUrl && isString(apiUrl)) {
      config.url = `${apiUrl}${config.url}`;
    }
    const params = config.params || {};
    const data = config.data || false;
    formatDate && data && !isString(data) && formatRequestDate(data);
    if (config.method?.toUpperCase() === RequestEnum.GET) {
      if (!isString(params)) {
        // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
        config.params = Object.assign(params || {}, joinTimestamp(joinTime, false));
      } else {
        // 兼容restful风格
        config.url = config.url + params + `${joinTimestamp(joinTime, true)}`;
        config.params = undefined;
      }
    } else {
      if (!isString(params)) {
        formatDate && formatRequestDate(params);
        if (
          Reflect.has(config, 'data') &&
          config.data &&
          (Object.keys(config.data).length > 0 || config.data instanceof FormData)
        ) {
          config.data = data;
          config.params = params;
        } else {
          // 非GET请求如果没有提供data，则将params视为data
          config.data = params;
          config.params = undefined;
        }
        if (joinParamsToUrl) {
          config.url = setObjToUrlParams(
            config.url as string,
            Object.assign({}, config.params, config.data),
          );
        }
      } else {
        // 兼容restful风格
        config.url = config.url + params;
        config.params = undefined;
      }
    }
    return config;
  },

  /**
   * @description: 请求拦截器处理
   */
  requestInterceptors: (config, options) => {
    // 请求之前处理config
    const token = getToken();

    if (token && (config as Recordable)?.requestOptions?.withToken !== false) {
      // jwt token
      (config as Recordable).headers.Authorization = options.authenticationScheme
        ? `${options.authenticationScheme} ${token}`
        : token;
    }

    // 区分pc端和移动端: 值(0,1)
    (config as Recordable).headers.client = 0;

    const ls = createLocalStorage();
    const tenantId = ls.get(TENANTID_KEY) || '';

    if (tenantId) {
      (config as Recordable).headers['TENANT-ID'] = tenantId;
    }
    // if (tenantId) {
    //   (config as Recordable).headers['System-Code'] = tenantId;
    // }

    return config;
  },

  /**
   * @description: 响应拦截器处理
   */
  responseInterceptors: (res: AxiosResponse<any>) => {
    return res;
  },

  /**
   * @description: 响应错误处理
   */
  responseInterceptorsCatch: (axiosInstance: AxiosResponse, error: any) => {
    const { t } = useI18n();
    const errorLogStore = useErrorLogStoreWithOut();
    errorLogStore.addAjaxErrorInfo(error, true);
    const { response, code, config } = error || {};
    const errorMessageMode = config?.requestOptions?.errorMessageMode || 'none';
    const richErrorMessage = config?.requestOptions?.richErrorMessage || false;
    const errorOnOKcallback = config?.requestOptions?.errorOnOKcallback;
    const modalConfig = config?.requestOptions?.modalConfig ?? {};

    const message: string = response?.data?.msg ?? '';
    const err: string = error?.toString?.() ?? '';
    let errMessage = '';

    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }

    try {
      if (code === 'ECONNABORTED' && message.indexOf('timeout') !== -1) {
        errMessage = t('sys.api.apiTimeoutMessage');
      }
      if (err?.includes('Network Error')) {
        errMessage = t('sys.api.networkExceptionMsg');
      }

      if (errMessage) {
        if (errorMessageMode === 'modal') {
          createErrorModal({
            title: t('sys.api.errorTip'),
            content: renderTipsText(richErrorMessage, errMessage),
            onOk() {
              isFunction(errorOnOKcallback) && errorOnOKcallback(errMessage);
            },
            ...modalConfig,
          });
        } else if (errorMessageMode === 'msg') {
          createMessage.error(errMessage);
        }
        return Promise.reject(error);
      }
    } catch (error) {
      throw new Error(error as unknown as string);
    }
    const { status = '', data } = response || {};
    checkStatus(status, data?.msg, errorMessageMode, true);

    // 添加自动重试机制 保险起见 只针对GET请求
    const retryRequest = new AxiosRetry();

    const { isOpenRetry } = config?.requestOptions?.retryRequest;
    config.method?.toUpperCase() === RequestEnum.GET &&
      isOpenRetry &&
      // @ts-ignore
      retryRequest.retry(axiosInstance, error);
    return Promise.reject(response ? response.data : error);
  },
};

function renderTipsText(richFlag, tipsText) {
  return richFlag ? h('div', { innerHTML: tipsText }) : tipsText;
}

export function createAxios(opt?: Partial<CreateAxiosOptions>) {
  return new VAxios(
    // 深度合并
    deepMerge(
      {
        // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Authentication#authentication_schemes
        // authentication schemes，e.g: Bearer
        // authenticationScheme: 'Bearer',
        authenticationScheme: '',
        timeout: 30 * 1000,
        // 基础接口地址
        // baseURL: globSetting.apiUrl,
        headers: { 'Content-Type': ContentTypeEnum.JSON },
        // 如果是form-data格式
        // headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
        // 数据处理方式
        transform: clone(transform),
        // 配置项，下面的选项都可以在独立的接口请求中覆盖
        requestOptions: {
          // 默认将prefix 添加到url
          joinPrefix: true,
          // 是否返回原生响应头 比如：需要获取响应头时使用该属性
          isReturnNativeResponse: false,
          // 需要对返回数据进行处理
          isTransformResponse: true,
          // post请求的时候添加参数到url
          joinParamsToUrl: false,
          // 格式化提交参数时间
          formatDate: true,
          // 消息提示类型
          errorMessageMode: 'msg',
          // 请求失败消息提示类型为modal时 是否富文本渲染提示内容
          richErrorMessage: false,
          // 接口地址
          apiUrl: globSetting.apiUrl,
          // 接口拼接地址
          urlPrefix: urlPrefix,
          //  是否加入时间戳
          joinTime: true,
          // 忽略重复请求
          ignoreCancelToken: true,
          // 是否携带token
          withToken: true,
          retryRequest: {
            isOpenRetry: false,
            count: 5,
            waitTime: 100,
          },
          // 文件下载默认自动定义文件名
          fileName: false,
        },
      },
      opt || {},
    ),
  );
}

export const defHttp = createAxios();

// other api url
// export const otherHttp = createAxios({
//   requestOptions: {
//     apiUrl: 'xxx',
//     urlPrefix: 'xxx',
//   },
// });

// 工艺流程编辑器服务 api url
export const editorHttp = createAxios({
  requestOptions: {
    apiUrl: VITE_GLOB_EDITOR_API_URL,
  },
  transformResponse: (res) => {
    const data = new AxiosResponseTranslate(JSON.parse(res));

    return data.toLowerCase();
  },
});

// 厂站摄像头服务 api url
export const cameraHttp = createAxios({
  requestOptions: {
    apiUrl: VITE_GLOB_CAMERA_API_URL,
  },
});

// 天气服务
export const weatherHttp = createAxios({
  requestOptions: {
    apiUrl: VITE_GLOB_WEATHER_API_URL,
    withToken: false,
  },
  transformResponse: (res) => {
    const responseTranslate = new AxiosResponseTranslate(JSON.parse(res));
    const result = responseTranslate.weather();

    return result;
  },
});

// 地理位置服务
export const geoHttp = createAxios({
  requestOptions: {
    apiUrl: VITE_GLOB_GEO_API_URL,
  },
});

// 智慧厂站
export function getBaseZhczApiParams() {
  const ls = createLocalStorage();
  const userStore = useUserStoreWithOut();
  const tenantId = ls.get(TENANTID_KEY);
  const appId = ls.get(PROJECTCODE_KEY);

  let factoryId = ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId;
  if (!factoryId && factoryId !== 0) factoryId = -1;

  return {
    factoryId,
    orgId: appId == 'aoa' ? '1001' : '1000',
    appId: appId == 'aoa' ? appId : 'station',
    renterId: appId == 'aoa' ? tenantId : '2024',
  };
}

async function transformResponseHook(res: AxiosResponse<Result>, options: RequestOptions) {
  const { t } = useI18n();

  const { isTransformResponse, isReturnNativeResponse, fileName } = options;
  // 是否返回原生响应头 比如：需要获取响应头时使用该属性
  if (isReturnNativeResponse) {
    return res;
  }
  // 不进行任何处理，直接返回
  // 用于页面代码可能需要直接获取code，data，message这些信息时开启
  if (!isTransformResponse) {
    // 自动解析文件名称
    if (fileName) {
      const filename = decodeURI(res.headers['content-disposition'].split(';')[1].split('=')[1]);
      return {
        data: res.data,
        fileName: filename,
      };
    }
    return res.data;
  }
  // 错误的时候返回

  const { data } = res;
  if (!data) {
    // return '[HTTP] Request has no return value';
    throw new Error(t('sys.api.apiRequestFailed'));
  }
  //  这里 code，result，message为 后台统一的字段，需要在 types.ts内修改为项目自己的接口返回格式
  const { code, data: result, msg } = data;

  // 这里逻辑可以根据项目进行修改
  const hasSuccess = data && Reflect.has(data, 'code') && code === ResultEnum.SUCCESS;
  if (hasSuccess) {
    let successMsg = msg;

    if (
      !options.successCustomMsg &&
      (isNull(successMsg) || isUnDef(successMsg) || isEmpty(successMsg))
    ) {
      successMsg = t(`sys.api.operationSuccess`);
    }

    if (options.successMessageMode === 'modal') {
      createSuccessModal({ title: t('sys.api.successTip'), content: successMsg });
    } else if (successMsg && options.successMessageMode === 'msg') {
      createMessage.success(successMsg);
    }

    return result;
  }

  // 在此处根据自己项目的实际情况对不同的code执行不同的操作
  // 如果不希望中断当前请求，请return数据，否则直接抛出异常即可
  let timeoutMsg = '';
  switch (code) {
    case ResultEnum.TIMEOUT:
    case ResultEnum.TIMEOUT1:
      const userStore = useUserStoreWithOut();
      userStore.setToken(undefined);
      await userStore.logout(true);
      timeoutMsg = msg || t('sys.api.timeoutMessage');
      break;
    default:
      if (msg) {
        timeoutMsg = msg;
      }
  }

  // errorMessageMode='modal'的时候会显示modal错误弹窗，而不是消息提示，用于一些比较重要的错误
  // errorMessageMode='none' 一般是调用时明确表示不希望自动弹出错误提示
  // const noneMessageEnum = [400, 401, 403, 404, 405, 500, 501, 502, 503, 504, 505];
  if (options.errorMessageMode === 'modal') {
    createErrorModal({
      title: t('sys.api.errorTip'),
      content: renderTipsText(options.richErrorMessage, timeoutMsg),
      onOk() {
        isFunction(options.errorOnOKcallback) && options.errorOnOKcallback(timeoutMsg);
      },
      ...(options?.modalConfig ?? {}),
    });
  } else if (options.errorMessageMode === 'msg') {
    createMessage.error(timeoutMsg);
  }
  // else if (options.errorMessageMode === 'none' && timeoutMsg && code === 1) {
  //   createMessage.error(timeoutMsg);
  // }

  throw new Error(timeoutMsg || t('sys.api.apiRequestFailed'));
}

// const { emitter } = useEmitt();
export const defZhczHttp = createAxios({
  transform: {
    transformResponseHook: transformResponseHook,
    // 请求之前处理config
    beforeRequestHook: (config, options) => {
      const {
        apiUrl,
        joinPrefix,
        joinParamsToUrl,
        formatDate,
        joinTime = true,
        urlPrefix,
      } = options;
      const baseParamsByConfig = {
        factoryId: config.params?.factoryId,
        orgId: config.params?.orgId,
        appId: config.params?.appId,
        renterId: config.params?.renterId,
      };
      const defaultBaseParams = getBaseZhczApiParams();
      const baseParams = {
        factoryId: baseParamsByConfig.factoryId || defaultBaseParams.factoryId,
        orgId: baseParamsByConfig.orgId || defaultBaseParams.orgId,
        appId: baseParamsByConfig.appId || defaultBaseParams.appId,
        renterId: baseParamsByConfig.renterId || defaultBaseParams.renterId,
      };

      if (joinPrefix) {
        config.url = `${urlPrefix}${config.url}`;
      }
      if (apiUrl && isString(apiUrl)) {
        config.url = `${apiUrl}${config.url}`;
      }
      const params = config.params || {};
      const data = config.data || false;
      formatDate && data && !isString(data) && formatRequestDate(data);
      if (
        config.method?.toUpperCase() === RequestEnum.GET ||
        config.method?.toUpperCase() === RequestEnum.DELETE
      ) {
        if (!isString(params)) {
          // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
          config.params = Object.assign(params || {}, joinTimestamp(joinTime, false), baseParams);
        } else {
          // 兼容restful风格
          config.url =
            config.url +
            params +
            `${joinTimestamp(joinTime, true)}` +
            `factoryId=${baseParams.factoryId}` +
            `&orgId=${baseParams.orgId}` +
            `&appId=${baseParams.appId}` +
            `&renterId=${baseParams.renterId}`;
          config.params = undefined;
        }
      } else {
        if (!isString(params)) {
          formatDate && formatRequestDate(params);
          if (
            Reflect.has(config, 'data') &&
            config.data &&
            (Object.keys(config.data).length > 0 || config.data instanceof FormData)
          ) {
            config.data = Object.assign(data, baseParams);
            config.params = Object.assign(params, baseParams);
          } else {
            if (config.params instanceof FormData) {
              config.params.append('factoryId', baseParams.factoryId);
              config.params.append('orgId', baseParams.orgId);
              config.params.append('appId', baseParams.appId);
              config.params.append('renterId', baseParams.renterId);
              config.data = params;
              config.params = undefined;
            } else {
              // 非GET请求如果没有提供data，则将params视为data
              config.data = Object.assign(params, baseParams);
              config.params = undefined;
            }
          }
          if (joinParamsToUrl) {
            config.url = setObjToUrlParams(
              config.url as string,
              Object.assign({}, config.params, config.data, baseParams),
            );
          }
        } else {
          // 兼容restful风格
          config.url =
            config.url +
            params +
            `factoryId=${baseParams.factoryId}` +
            `&orgId=${baseParams.orgId}` +
            `&appId=${baseParams.appId}` +
            `&renterId=${baseParams.renterId}`;
          config.params = undefined;
        }
      }
      return config;
    },
    responseInterceptorsCatch: (axiosInstance: AxiosResponse, error: any) => {
      const { t } = useI18n();
      const errorLogStore = useErrorLogStoreWithOut();
      errorLogStore.addAjaxErrorInfo(error, true);
      const { response, code, config } = error || {};
      const errorMessageMode = config?.requestOptions?.errorMessageMode || 'none';
      const richErrorMessage = config?.requestOptions?.richErrorMessage || false;
      const errorOnOKcallback = config?.requestOptions?.errorOnOKcallback;
      const modalConfig = config?.requestOptions?.modalConfig ?? {};

      const message: string = response?.data?.msg ?? '';
      const err: string = error?.toString?.() ?? '';
      let errMessage = '';

      if (axios.isCancel(error)) {
        return Promise.reject(error);
      }

      try {
        if (code === 'ECONNABORTED' && message.indexOf('timeout') !== -1) {
          errMessage = t('sys.api.apiTimeoutMessage');
        }
        if (err?.includes('Network Error')) {
          errMessage = t('sys.api.networkExceptionMsg');
        }

        if (errMessage) {
          if (errorMessageMode === 'modal') {
            createErrorModal({
              title: t('sys.api.errorTip'),
              content: renderTipsText(richErrorMessage, errMessage),
              onOk() {
                isFunction(errorOnOKcallback) && errorOnOKcallback(errMessage);
              },
              ...modalConfig,
            });
          } else if (errorMessageMode === 'msg') {
            createMessage.error(errMessage);
          }
          return Promise.reject(error);
        }
      } catch (error) {
        throw new Error(error as unknown as string);
      }
      const { status = '', data } = response || {};
      checkStatus(status, data?.msg, errorMessageMode, true);

      // 添加自动重试机制 保险起见 只针对GET请求
      const retryRequest = new AxiosRetry();

      const { isOpenRetry } = config?.requestOptions?.retryRequest;
      config.method?.toUpperCase() === RequestEnum.GET &&
        isOpenRetry &&
        // @ts-ignore
        retryRequest.retry(axiosInstance, error);

      // emitter.emit('AjaxErrorInfo', error);
      return Promise.reject(response ? response.data : error);
    },
  },
  // requestOptions: {
  //   errorMessageMode: 'none',
  // },
});
