<template>
  <div :class="['table-simple', themeColor]">
    <!-- 渲染 HLCard 组件，将所有非 prop 属性传递给该组件 -->
    <HLCard v-bind="$attrs" :themeColor="themeColor">
      <template #[item] v-for="item in filtersSlots">
        <slot :name="item"></slot>
      </template>
      <!-- 渲染卡片默认主体内容 -->
      <template #defaultBody>
        <Loading v-if="loading" :themeColor="themeColor" />
        <TableSimple v-else-if="!empty" :themeColor="themeColor" v-bind="$attrs">
          <template #tableAction="{ column, record }">
            <slot name="tableAction" :column="column" :record="record"></slot>
          </template>
        </TableSimple>
        <Empty v-else :themeColor="themeColor" />
      </template>
    </HLCard>
  </div>
</template>

<script lang="ts" setup>
  import { defineProps, PropType } from 'vue';
  import { HLCard } from '../../../../HLCard';
  import { Empty, Loading } from '../../../../HLCardComponent';
  import TableSimple from '../../basicComponents/TableSimple.vue';
  import { useFilterSlots } from '../../hooks';
  // 设置组件选项，不继承父组件的非 prop 属性
  defineOptions({ inheritAttrs: false });

  /**
   * 定义组件的 props
   */
  defineProps({
    // 是否显示表格组件，类型为布尔值，默认值为 false
    empty: {
      type: Boolean,
      default: false,
    },
    // 是否显示加载状态，类型为布尔值，默认值为 false
    loading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    // 颜色模式
    themeColor: {
      type: String,
      default: 'light', // Dark, light, screenColor
    },
  });
  const { filtersSlots } = useFilterSlots();
</script>

<style lang="less" scoped>
  .table-simple {
    width: 100%;
    height: 100%;
  }

  .Dark {
    color: #ffffff;
    // padding: 16px 8px 16px 16px;
    // height: calc(100% - 50px);
    // overflow-y: auto;
    // box-sizing: border-box;
  }

  .light {
    color: #333333;
  }

  .screenColor {
    color: #ffffff;
    background: transparent;
  }
</style>
