<template>
  <PageWrapper contentFullHeight fixed-height>
    <div class="page">
      <div class="search-container">
        <a-button type="primary" :icon="h(Icon, { icon: 'icon-park-outline:voice' })">
          <!-- <img src="../../assets/images/mic.png" width="16" /> -->
          全体广播
        </a-button>
        <!-- <div class="redo-btn">
          <img :src="refresh" style="margin-top: 1px" />
          <span>刷新</span>
        </div> -->
      </div>
      <div class="content-container">
        <div v-for="(item, index) in dataList" :key="index" class="item-box">
          <div class="top-box">
            <img src="../../assets/images/people.png" width="36" />
            <span class="name">{{ item.name }}</span>
            <div class="operation-box">
              <img :src="item.status === 0 ? cameraDisabled : camera" width="40" />
              <img :src="item.status === 0 ? micDisabled : mic" width="40" />
            </div>
          </div>
          <div class="info-box">
            <div>
              <span class="label"> 安全帽编号： </span>
              <span class="con">
                {{ item.a }}
              </span>
            </div>
            <div>
              <span class="label"> 安全帽状态： </span>
              <span class="con">
                {{ item.status === 0 ? '离线' : '在线' }}
              </span>
            </div>
            <div>
              <span class="label">
                {{ item.status === 0 ? '离线时间：' : '在线时间：' }}
              </span>
              <span class="con">
                {{ item.time }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup>
  import { h } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { PageWrapper } from '/@/components/Page';
  // import refresh from '../../assets/images/refresh.png';
  import camera from '../../assets/images/camera.png';
  import cameraDisabled from '../../assets/images/camera-disabled.png';
  import mic from '../../assets/images/mic.png';
  import micDisabled from '../../assets/images/mic-disabled.png';

  defineOptions({
    name: 'SecurityManagementSafetyHelmetOnlineMonitoring',
  });

  const dataList = [
    {
      name: '张孟涵',
      status: 0,
      a: 'SH_001',
      time: '2023-09-12 12:00:00',
    },
    {
      name: '吴心真',
      status: 1,
      a: 'SH_002',
      time: '半小时',
    },
    {
      name: '洪振霞',
      status: 0,
      a: 'SH_003',
      time: '2023-09-12 12:00:00',
    },
    {
      name: '舒绿珮',
      status: 1,
      a: 'SH_004',
      time: '半小时',
    },
  ];
</script>

<style scoped lang="less">
  .page {
    background: #f2f3f5;
    height: 100%;

    .search-container {
      height: 64px;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;

      .ant-btn {
        display: flex;
        align-items: center;
        padding: 8px 12px;

        img {
          margin-right: 2px;
        }
      }

      .redo-btn {
        width: 70px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #e9e9e9;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        img {
          margin-right: 4px;
        }

        &:hover {
          background: #f8f8f8;
        }
      }
    }

    .content-container {
      display: flex;
      justify-content: space-between;
      margin-top: 12px;

      .item-box {
        padding: 24px;
        background: #fff;
        width: calc((100% - 16px * 3) / 4);
        border-radius: 8px;

        .top-box {
          display: flex;
          align-items: center;

          .name {
            margin: 0 auto 0 8px;
          }

          .operation-box {
            display: flex;
            align-items: center;

            img {
              // cursor: pointer;

              &:last-child {
                margin-left: 24px;
              }
            }
          }
        }

        .info-box {
          margin-top: 16px;
          padding: 16px 0 16px 16px;
          background: rgba(43, 99, 161, 0.03);
          border-radius: 4px;

          & > div {
            line-height: 1;
            margin-bottom: 24px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              color: #999;
            }
          }
        }
      }
    }
  }
</style>
