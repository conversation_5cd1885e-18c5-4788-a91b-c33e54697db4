import { defHttp } from '/@/utils/http/axios';
import { defZhczHttp } from '/@/utils/http/axios';
import type {
  faultDeviceTreeType,
  faultDeviceListType,
  addOrEditFaultDeviceType,
  FalutRatioType,
  faultDeviceRankType,
  faultDeviceRecordType,
  equipmentOperatingTimType,
  faultRepairDurationType,
} from './type';

enum Api {
  SearchEqrtd = '/hlxb/equipmentInfo/searchEqrtd',
  SearchEqpt = '/hlxb/equipmentInfo/searchEqpt',
  SearchEquipmentList = '/hlxb/equipmentInfo/searchEquipmentList',
  SaveEquipmentInfo = '/hlxb/equipmentInfo/saveEquipmentInfo',
  SearchEquipmentDeatil = '/hlxb/equipmentInfo/searchEquipmentDeatil',
  DeleteEquipment = '/hlxb/equipmentInfo/deleteEquipment',
  SearchEquipmentTags = '/hlxb/equipmentInfo/searchEquipmentTags',
  SaveEquipmentTags = '/hlxb/equipmentInfo/saveEquipmentTags',
  SearchEquipmentMonitor = '/hlxb/equipmentInfo/searchEquipmentMonitor',
  SearchEquipmentMonitorDetail = '/hlxb/equipmentInfo/searchEquipmentMonitorDetail',
  SearchEqar = '/hlxb/equipmentInfo/searchEqar',
  SearchEquipmentTypeList = '/hlxb/equipmentType/searchEquipmentTypeList',
  SearchEquipmentTypeDeatil = '/hlxb/equipmentType/searchEquipmentTypeDeatil',
  SaveEquipmentType = '/hlxb/equipmentType/saveEquipmentType',
  DeleteEquipmentType = '/hlxb/equipmentType/deleteEquipmentType',
  SearchEquipmentTypeOptions = '/hlxb/equipmentType/searchEquipmentTypeOptions',
  SearchEquipmentModelOptions = '/hlxb/equipmentType/searchEquipmentModelOptions',
  UpdateEquipmentTypeStatus = '/hlxb/equipmentType/updateEquipmentTypeStatus',
  SearchEquipmentConfigList = '/hlxb/equipmentConfig/searchEquipmentConfigList',
  SearchEquipmentConfigDeatil = '/hlxb/equipmentConfig/searchEquipmentConfigDeatil',
  SaveEquipmentConfig = '/hlxb/equipmentConfig/saveEquipmentConfig',
  DeleteEquipmentConfig = '/hlxb/equipmentConfig/deleteEquipmentConfig',
  SearchEquipmentConfigGroupInfo = '/hlxb/equipmentConfig/searchEquipmentConfigGroupInfo',
  SearchFactoryList = '/hlxb/factory/searchFactoryList',
  GetGroupImage = '/data-sence/groupInfo/getGroupImage',

  getFaultDeviceTree = '/data-sence/groupInfo/getFaultDeviceTree',
  getFaultDeviceList = '/data-sence/groupInfo/getFaultDeviceList',
  getDeviceModelList = '/data-sence/groupInfo/getDeviceModelList',
  addOrEditFaultDevice = '/data-sence/groupInfo/addOrEditFaultDevice',
  getFalutRatio = '/data-sence/groupInfo/getFalutRatio',
  getFaultDeviceRank = 'getFaultDeviceList',
  getFaultDeviceRecord = 'getFaultDeviceRecord',

  getEquipmentOperatingTime = 'getEquipmentOperatingTime',
  getFaultRepairDuration = 'getFaultRepairDuration',
}

/** 查询设备总览实时数据 */
export const searchEqrtdApi = () => {
  return defHttp.post({
    url: Api.SearchEqrtd,
    params: {},
  });
};
/** 1是查询设备类型占比 2是查询地区设备占比 */
export const searchEqptApi = (searchType) => {
  return defHttp.post({
    url: Api.SearchEqpt,
    params: { searchType },
  });
};
/** 查询设备列表 */
export const searchEquipmentListApi = (params) => {
  return defHttp.post({
    url: Api.SearchEquipmentList,
    params,
  });
};
/** 查询设备详情 */
export const searchEquipmentDeatilApi = (id) => {
  return defHttp.post({
    url: Api.SearchEquipmentDeatil,
    params: { id },
  });
};
/** 保存设备 */
export const saveEquipmentInfoApi = (params) => {
  return defHttp.post(
    {
      url: Api.SaveEquipmentInfo,
      params,
    },
    {
      richErrorMessage: true,
      errorMessageMode: 'modal',
      modalConfig: {
        okText: '确定',
      },
    },
  );
};
/** 批量删除设备 */
export const deleteEquipmentApi = (ids) => {
  return defHttp.post({
    url: Api.DeleteEquipment,
    params: { ids },
  });
};
/** 查询设备监控 */
export const SearchEquipmentMonitorApi = (params) => {
  return defHttp.post({
    url: Api.SearchEquipmentMonitor,
    params,
  });
};
/** 查询设备监控详情 */
export const searchEquipmentMonitorDetailApi = (id) => {
  return defHttp.post({
    url: Api.SearchEquipmentMonitorDetail,
    params: { id },
  });
};
/** 查询设备报警记录 */
export const searchEqarApi = (id) => {
  return defHttp.post({
    url: Api.SearchEqar,
    params: { id },
  });
};
/** 获取设备类型列表 */
export const searchEquipmentTypeListApi = (params) => {
  return defHttp.post({
    url: Api.SearchEquipmentTypeList,
    params,
  });
};
/** 查询设备分类下拉列表 */
export const searchEquipmentTypeOptionsApi = (params) => {
  return defHttp.post({
    url: Api.SearchEquipmentTypeOptions,
    params,
  });
};
/** 查询设备型号下拉列表 */
export const searchEquipmentModelOptionsApi = (params) => {
  return defHttp.post({
    url: Api.SearchEquipmentModelOptions,
    params,
  });
};
/** 更新设备分类状态 */
export const updateEquipmentTypeStatusApi = (params) => {
  return defHttp.post({
    url: Api.UpdateEquipmentTypeStatus,
    params,
  });
};

/** 查询设备类型详情 */
export const searchEquipmentTypeDeatilApi = (id) => {
  return defHttp.post({
    url: Api.SearchEquipmentTypeDeatil,
    params: { id },
  });
};
/** 保存设备类型详情 */
export const saveEquipmentTypeApi = (params) => {
  return defHttp.post(
    {
      url: Api.SaveEquipmentType,
      params,
    },
    {
      richErrorMessage: true,
      errorMessageMode: 'modal',
      modalConfig: {
        okText: '确定',
      },
    },
  );
};
/** 批量删除设备类型 */
export const deleteEquipmentTypeApi = (ids) => {
  return defHttp.post(
    {
      url: Api.DeleteEquipmentType,
      params: { ids },
    },
    {
      richErrorMessage: true,
      errorMessageMode: 'modal',
      modalConfig: {
        okText: '确定',
      },
    },
  );
};
/** 查询设备标签 */
export const searchEquipmentTagsApi = () => {
  return defHttp.post({
    url: Api.SearchEquipmentTags,
    params: {},
  });
};
/** 保存设备标签 */
export const saveEquipmentTagsApi = (tag) => {
  return defHttp.post({
    url: Api.SaveEquipmentTags,
    params: { tag },
  });
};
/** 查询设备配置列表 */
export const searchEquipmentConfigListApi = (params) => {
  return defHttp.post({
    url: Api.SearchEquipmentConfigList,
    params,
  });
};
/** 查询设备配置详情 */
export const searchEquipmentConfigDeatilApi = (id) => {
  return defHttp.post({
    url: Api.SearchEquipmentConfigDeatil,
    params: { id },
  });
};
/** 保存设备配置 */
export const saveEquipmentConfigApi = (params) => {
  return defHttp.post(
    {
      url: Api.SaveEquipmentConfig,
      params,
    },
    {
      richErrorMessage: true,
      errorMessageMode: 'modal',
      modalConfig: {
        okText: '确定',
      },
    },
  );
};
/** 批量删除设备配置 */
export const deleteEquipmentConfigApi = (ids) => {
  return defHttp.post({
    url: Api.DeleteEquipmentConfig,
    params: { ids },
  });
};
/** 查询设备配置分组信息 */
export const searchEquipmentConfigGroupInfoApi = (equipmentTypeId) => {
  return defHttp.post({
    url: Api.SearchEquipmentConfigGroupInfo,
    params: { equipmentTypeId },
  });
};

/* 通过分组编码获取其顶层父节点图片资源 */
export const getGroupImageApi = (params) => {
  return defZhczHttp.post({
    url: Api.GetGroupImage,
    params,
  });
};
/** 获取水厂列表 */
export const searchFactoryListApi = () => {
  return defHttp.post({
    url: Api.SearchFactoryList,
    params: {},
  });
};

/** 获取设备列表树 */
export const getFaultDeviceTreeApi = (params): Promise<faultDeviceTreeType> => {
  return defHttp.post({
    url: Api.getFaultDeviceTree,
    params: params,
  });
};

/** 获取故障设备列表 */
export const getFaultDeviceListApi = (params): Promise<faultDeviceListType> => {
  return new Promise((r) => {
    r({
      total: 15,
      page: 1,
      pageSize: 10,
      list: [
        {
          id: 1,
          name: '设备1',
          faultDate: '2021-01-01',
          finishedDate: '2021-01-01',
          faultPhenomenon: 'dfdss',
          faultReasonValue: 2,
        },
      ],
    });
  });
  defHttp.post({
    url: Api.getFaultDeviceList,
    params,
  });
};

export const getDeviceModelListApi = (params) => {
  return defHttp.post({
    url: Api.getDeviceModelList,
    params,
  });
};

// 新增或修改故障设备
export const addOrEditFaultDeviceApi = (params): Promise<addOrEditFaultDeviceType> => {
  return defHttp.post({
    url: Api.addOrEditFaultDevice,
    params,
  });
};

// 故障占比
export const getFalutRatioApi = (params): Promise<FalutRatioType> => {
  return defHttp.post({
    url: Api.getFalutRatio,
    params,
  });
};
// 故障设备排名
export const getFaultDeviceRankApi = (params): Promise<faultDeviceRankType> => {
  return defHttp.post({
    url: Api.getFaultDeviceRank,
    params,
  });
};
// 故障记录统计
export const getFaultDeviceRecordApi = (params): Promise<faultDeviceRecordType> => {
  return new Promise((r) => {
    r([
      {
        date: ['2021-01-01', '2021-01-02', '2021-01-03'],
        data: [1, 2, 3],
        name: '故障次数',
      },
      {
        date: ['2021-01-01', '2021-01-02', '2021-01-03'],
        data: [1, 2, 3],
        name: '故障时长',
      },
    ]);
  });
  return defHttp.post({
    url: Api.getFaultDeviceRecord,
    params,
  });
};
// 设备运行时长

export const getEquipmentOperatingTimeApi = (params): Promise<equipmentOperatingTimType> => {
  return new Promise((r) => {
    r([
      {
        date: ['2021-01-01', '2021-01-02', '2021-01-03'],
        data: [1, 2, 3],
        name: '运行时长',
      },
    ]);
  });
  return defHttp.post({
    url: Api.getEquipmentOperatingTime,
    params,
  });
};

// 故障修复时长曲线

export const getFaultRepairDurationApi = (params): Promise<faultRepairDurationType> => {
  return new Promise((r) => {
    r({
      date: ['2021-01-01', '2021-01-02', '2021-01-03'],
      data: [1, 2, 3],
    });
  });
  return defHttp.post({
    url: Api.getFaultRepairDuration,
  });
};
