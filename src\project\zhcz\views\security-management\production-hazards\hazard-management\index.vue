<template>
  <PageWrapper dense contentFullHeight>
    <div class="list-wrapper" v-show="!detailPageRef?.show">
      <div class="card-list">
        <div class="item" v-for="(item, index) in cardList" :key="index">
          <img class="left" :src="item.icon" alt="" />
          <div class="right">
            <div class="title">{{ item.name }}</div>
            <div>
              <span class="num">{{ item.num }}</span>
              <span class="unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
      <BasicTable @register="registerTable">
        <template #tableTitle>
          <a-button
            type="primary"
            :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
            @click="handleEscalation"
          >
            上报隐患
          </a-button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  label: '查看',
                  onClick: handleDetail.bind(null, record),
                },
                {
                  label: '排除',
                  popConfirm: {
                    title: '是否确认排除该隐患？',
                    placement: 'left',
                    confirm: handleDelete.bind(null, record),
                  },
                },
              ]"
            />
          </template>
          <template v-if="column.key === 'c'">
            <div>{{ getOptsItem(record.c, warnOptions).label }}</div>
          </template>
          <template v-if="column.key === 'e'">
            <div>{{ getOptsItem(record.e, partOptions).label }}</div>
          </template>
          <template v-if="column.key === 'h'">
            <div>{{ record.h?.[0]?.name || '-' }}</div>
          </template>
          <template v-if="column.key === 'k'">
            <CZStatus
              :text="getOptsItem(record.k, statusOptions).label"
              :color="getOptsItem(record.k, statusOptions).color"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <DetailPage ref="detailPageRef" @success="onSuccess" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref, h } from 'vue';
  import dayjs from 'dayjs/esm';
  import { Icon } from '/@/components/Icon';
  import { PageWrapper } from '/@/components/Page';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { CZStatus } from '/@zhcz/components/cz-status';
  import DetailPage from './DetailPage.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    columns,
    searchFormSchema,
    cardList,
    mockData,
    getOptsItem,
    warnOptions,
    partOptions,
    statusOptions,
  } from './data';

  defineOptions({
    name: 'SecurityManagementProductionHazardsHazardManagement',
  });

  const detailPageRef = ref();
  const [registerTable, { setTableData }] = useTable({
    columns,
    // api: getInspectTaskPageList,
    fetchSetting: {
      pageField: 'pageIndex',
      sizeField: 'pageSize',
    },
    dataSource: mockData,
    showIndexColumn: true,
    formConfig: {
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    actionColumn: {
      width: 128,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
    handleSearchInfoFn(params: Recordable) {
      searchInfo.value = params;
      setSearchList();
    },
  });

  const searchInfo = ref<any>({});

  const setSearchList = () => {
    const data = mockData.filter((item) => {
      const { b, c, e, k } = searchInfo.value;
      return (
        (b ? item.b.includes(b) : true) &&
        (c !== undefined ? item.c == c : true) &&
        (e !== undefined ? item.e == e : true) &&
        (k !== undefined ? item.k == k : true)
      );
    });
    setTableData(data);
  };

  function handleDetail(record) {
    detailPageRef.value.open(record);
  }

  const { createMessage } = useMessage();
  function handleDelete(record: Recordable) {
    const idx = mockData.findIndex((item) => item.a == record.a);
    if (idx > -1) {
      mockData.splice(idx, 1);
      setSearchList();
      createMessage.success('排除成功');
    }
  }

  function handleEscalation() {
    detailPageRef.value.open();
  }

  const onSuccess = (params: any) => {
    const idx = mockData.findIndex((item) => item.a == params.a);
    if (idx > -1) {
      mockData.splice(idx, 1, params);
    } else {
      params.a = mockData.length + 2;
      params.k = 0;
      params.i = dayjs().format('YYYY-MM-DD HH:mm:ss');
      mockData.unshift(params);
    }
    setSearchList();
  };
</script>

<style scoped lang="less">
  .vben-basic-table-form-container {
    :deep(.ant-form) {
      position: relative;
      margin-bottom: 0;
      border-radius: 4px 4px 0 0;

      &:after {
        display: block;
        content: '';
        position: absolute;
        left: 16px;
        right: 16px;
        bottom: 0;
        height: 1px;
        background-color: #f5f6f7;
      }
    }

    :deep(.ant-table-wrapper) {
      border-radius: 0 0 4px 4px;
    }
  }

  .list-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;

    .card-list {
      padding: 0 16px;
      height: 104px;
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .item {
        padding-left: 24px;
        height: 104px;
        flex: 1;
        display: flex;
        align-items: center;
        border-radius: 8px;
        background: #fff;

        .left {
          width: 56px;
          height: 56px;
        }

        .right {
          color: #999999;
          margin-left: 16px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .num {
            font-size: 24px;
            font-weight: 600;
            color: #333333;
            height: 24px;
            display: inline-block;
            margin-right: 4px;
          }
        }
      }
    }

    :deep(.vben-basic-table) {
      flex: 1;

      .ant-table {
        &-thead .ant-table-cell {
          &:has(.ant-table-cell-content .vben-basic-table-header-cell.action) {
            padding: 12px 24px 12px 10px;
          }
        }

        &-cell {
          &:has(.ant-table-cell-content .vben-basic-table-action) {
            padding: 12px 24px 12px 0;
          }

          &-content {
            .vben-basic-table-action {
              .ant-btn {
                padding: 0 10px;
                border: none;
                margin-right: 8px;

                &:last-of-type {
                  margin-right: 0;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
