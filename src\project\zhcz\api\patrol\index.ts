import { defZhczHttp, cameraHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';

enum Api {
  //巡检点
  CreateInspectLocation = '/api/v1/SIndustryInspect/InspectLocation/CreateInspectLocation',
  GetInspectLocationPageList = '/api/v1/SIndustryInspect/InspectLocation/GetInspectLocationPageList',
  GetInspectLocationList = '/api/v1/SIndustryInspect/InspectLocation/GetInspectLocationList',
  UpdateInspectLocation = '/api/v1/SIndustryInspect/InspectLocation/UpdateInspectLocation',
  DeleteInspectLocation = '/api/v1/SIndustryInspect/InspectLocation/DeleteInspectLocation',
  UpdateInspectLocationEnable = '/api/v1/SIndustryInspect/InspectLocation/UpdateInspectLocationEnable',
  CreateInspectLocationMappingInspectItem = '/api/v1/SIndustryInspect/InspectLocation/CreateInspectLocationMappingInspectItem',
  GetInspectLocationMappingInspectItemList = '/api/v1/SIndustryInspect/InspectLocation/GetInspectLocationMappingInspectItemList',
  CreateInspectLocationMappingCamera = '/api/v1/SIndustryInspect/InspectLocation/CreateInspectLocationMappingCamera',
  GetInspectLocationMappingCameraList = '/api/v1/SIndustryInspect/InspectLocation/GetInspectLocationMappingCameraList',
  //巡检项
  GetInspectItemPageList = '/api/v1/SIndustryInspect/InspectItem/GetInspectItemPageList',
  UpdateInspectItemEnable = '/api/v1/SIndustryInspect/InspectItem/UpdateInspectItemEnable',
  DeleteInspectItem = '/api/v1/SIndustryInspect/InspectItem/DeleteInspectItem',
  CreateInspectItem = '/api/v1/SIndustryInspect/InspectItem/CreateInspectItem',
  UpdateInspectItem = '/api/v1/SIndustryInspect/InspectItem/UpdateInspectItem',
  CreateInspectItemConfigRule = '/api/v1/SIndustryInspect/InspectItem/CreateInspectItemConfigRule',
  UpdateInspectItemConfigRule = '/api/v1/SIndustryInspect/InspectItem/UpdateInspectItemConfigRule',
  GetInspectItemConfigRule = '/api/v1/SIndustryInspect/InspectItem/GetInspectItemConfigRule',
  GetAllPdataNodeListByFactoryId = '/api/v1/SIndustryInspect/InspectItem/GetAllPdataNodeListByFactoryId',
  // 巡检路线
  GetInspectRoutePageList = '/api/v1/SIndustryInspect/InspectRoute/GetInspectRoutePageList',
  UpdateInspectRouteEnable = '/api/v1/SIndustryInspect/InspectRoute/UpdateInspectRouteEnable',
  DeleteInspectRoute = '/api/v1/SIndustryInspect/InspectRoute/DeleteInspectRoute',
  CreateInspectRoute = '/api/v1/SIndustryInspect/InspectRoute/CreateInspectRoute',
  UpdateInspectRoute = '/api/v1/SIndustryInspect/InspectRoute/UpdateInspectRoute',
  CreateInspectRouteMappingInspectLocation = '/api/v1/SIndustryInspect/InspectRoute/CreateInspectRouteMappingInspectLocation',
  GetInspectLocationByInspectRouteId = '/api/v1/SIndustryInspect/InspectRoute/GetInspectLocationByInspectRouteId',
  GetInspectTaskLocationRecord = '/api/v1/SIndustryInspect/InspectTask/GetInspectTaskLocationRecord',
  // 厂区图
  GetFactoryPicturePageList = '/api/v1/SIndustryInspect/FactoryPicture/GetFactoryPicturePageList',
  GetFactoryPictureList = '/api/v1/SIndustryInspect/FactoryPicture/GetFactoryPictureList',
  UpdateFactoryPictureEnable = '/api/v1/SIndustryInspect/FactoryPicture/UpdateFactoryPictureEnable',
  DeleteFactoryPicture = '/api/v1/SIndustryInspect/FactoryPicture/DeleteFactoryPicture',
  CreateFactoryPicture = '/api/v1/SIndustryInspect/FactoryPicture/CreateFactoryPicture',
  // 水厂
  GetAllFactory = '/factory/factory/getList',
  GetFactoryList = '/factory/factory/getUserList',
  UpdateFactoryUsedStatus = '/factory/factory/update/usedStatus',
  GetFactoryPage = '/factory/factory/getPage',
  CreateFactory = '/factory/factory/add',
  UpdateFactory = '/factory/factory/update',
  DeleteFactory = '/factory/factory/batchDelete',
  GetFactoryDetail = '/factory/factory/getDetail/',
  SearchLocationList = '/hlxb/factory/searchLocationList',
  GetCurrentUserFactoryList = '/factory/factory/getUserFactoryList',

  // 上传图片
  Upload = '/api/fs/upload',
  UploadImage = '/eq/file/uploadFile',
  //巡检路线
  GetInspectTaskPageList = '/api/v1/SIndustryInspect/InspectTask/GetInspectTaskPageList',
  //获取巡检任务进度过程
  GetInspectTaskRecordByInspectTaskId = '/api/v1/SIndustryInspect/InspectTask/GetInspectTaskRecordByInspectTaskId',
  CreateInspectTask = '/api/v1/SIndustryInspect/InspectTask/CreateInspectTask',
  SaveInspectTaskRecord = '/api/v1/SIndustryInspect/InspectTask/SaveInspectTaskRecord',
  GetCameraInfoList = '/api/v1/SIndustryCamera/CameraInfo/GetCameraInfoList',
  GetInspectTaskStatusList = '/api/v1/SIndustryInspect/InspectTask/GetInspectTaskStatusList',
  GetProductCraftIndexCodeLastValues = '/api/v1/SIndustryInspect/InspectItem/GetProductCraftIndexCodeLastValues',
}

export const uploadImg = async (params: UploadFileParams) => {
  const ret = await defZhczHttp.uploadFile<any>(
    {
      url: Api.Upload,
    },
    params,
  );
  return ret.data;
};

export const uploadImage = async (data): Promise<Response> => {
  return defZhczHttp
    .post({
      url: Api.UploadImage,
      data,
      headers: {
        'Content-Type': 'multipart/form-data;',
      },
    })
    .then((res) => {
      return res;
    });
};
// 获取指标

export const getProductCraftIndexCodeLastValues = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetProductCraftIndexCodeLastValues,
    params,
  });
// 获取巡检任务状态
export const getInspectTaskStatusList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectTaskStatusList,
    params,
  });
// 获取摄像头
export const getCameraInfoList = (params) =>
  cameraHttp.get<any>({
    url: Api.GetCameraInfoList,
    params,
  });
// 创建巡检任务
export const createInspectTask = (params) =>
  defZhczHttp.post<any>({
    url: Api.CreateInspectTask,
    params,
  });

//保存巡检任务
export const saveInspectTaskRecord = (params) =>
  defZhczHttp.post<any>({
    url: Api.SaveInspectTaskRecord,
    params,
  });
// 获取巡检任务进度过程
export const getInspectTaskRecordByInspectTaskId = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectTaskRecordByInspectTaskId,
    params,
  });

// 巡检任务分页
export const getInspectTaskPageList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectTaskPageList,
    params,
  });

// 创建巡检点
export const createInspectLocation = (params) =>
  defZhczHttp.post<any>({
    url: Api.CreateInspectLocation,
    params,
  });

// 编辑巡检点
export const updateInspectLocation = (params) =>
  defZhczHttp.post<any>({
    url: Api.UpdateInspectLocation,
    params,
  });

// 巡检点分页
export const getInspectLocationPageList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectLocationPageList,
    params,
  });
// 巡检点不分页
export const getInspectLocationList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectLocationList,
    params,
  });
// 删除巡检点
export const deleteInspectLocation = (params) =>
  defZhczHttp.post<any>({
    url: Api.DeleteInspectLocation,
    params,
  });
// 修改巡检点是否可用
export const updateInspectLocationEnable = (params) =>
  defZhczHttp.post<any>({
    url: Api.UpdateInspectLocationEnable,
    params,
  });
// 保存巡检项
export const createInspectLocationMappingInspectItem = (params) =>
  defZhczHttp.post<any>({
    url: Api.CreateInspectLocationMappingInspectItem,
    params,
  });
// 获取巡检项
export const getInspectLocationMappingInspectItemList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectLocationMappingInspectItemList,
    params,
  });
// 保存摄像头
export const createInspectLocationMappingCamera = (params) =>
  defZhczHttp.post<any>({
    url: Api.CreateInspectLocationMappingCamera,
    params,
  });
// 获取摄像头
export const getInspectLocationMappingCameraList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectLocationMappingCameraList,
    params,
  });

//巡检项分页
export const getInspectItemPageList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectItemPageList,
    params,
  });

//巡检项启用
export const updateInspectItemEnable = (params) =>
  defZhczHttp.post<any>({
    url: Api.UpdateInspectItemEnable,
    params,
  });
// 删除巡检点
export const deleteInspectItem = (params) =>
  defZhczHttp.post<any>({
    url: Api.DeleteInspectItem,
    params,
  });
// 创建巡检项
export const createInspectItem = (params) =>
  defZhczHttp.post<any>({
    url: Api.CreateInspectItem,
    params,
  });

// 编辑巡检项
export const updateInspectItem = (params) =>
  defZhczHttp.post<any>({
    url: Api.UpdateInspectItem,
    params,
  });
// 创建巡检项配置规则
export const createInspectItemConfigRule = (params) =>
  defZhczHttp.post<any>({
    url: Api.CreateInspectItemConfigRule,
    params,
  });

// 编辑巡检项配置规则
export const updateInspectItemConfigRule = (params) =>
  defZhczHttp.post<any>({
    url: Api.UpdateInspectItemConfigRule,
    params,
  });

// 获取巡检项配置规则
export const getInspectItemConfigRule = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectItemConfigRule,
    params,
  });
// 获取当前工厂的指标
export const getAllPdataNodeListByFactoryId = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetAllPdataNodeListByFactoryId,
    params,
  });

// 获取巡检路线分页
export const getInspectRoutePageList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectRoutePageList,
    params,
  });
//巡检路线启用
export const updateInspectRouteEnable = (params) =>
  defZhczHttp.post<any>({
    url: Api.UpdateInspectRouteEnable,
    params,
  });

// 删除巡检路线
export const deleteInspectRoute = (params) =>
  defZhczHttp.post<any>({
    url: Api.DeleteInspectRoute,
    params,
  });

//创建巡检路线
export const createInspectRoute = (params) =>
  defZhczHttp.post<any>({
    url: Api.CreateInspectRoute,
    params,
  });

//编辑巡检路线
export const updateInspectRoute = (params) =>
  defZhczHttp.post<any>({
    url: Api.UpdateInspectRoute,
    params,
  });

// 创建巡检路线的巡检点
export const createInspectRouteMappingInspectLocation = (params) =>
  defZhczHttp.post<any>({
    url: Api.CreateInspectRouteMappingInspectLocation,
    params,
  });

export const getInspectTaskLocationRecord = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectTaskLocationRecord,
    params,
  });
// 获取巡检路线的巡检点
export const getInspectLocationByInspectRouteId = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetInspectLocationByInspectRouteId,
    params,
  });

// 厂区分页
export const getFactoryPicturePageList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetFactoryPicturePageList,
    params,
  });
// 启用
export const updateFactoryPictureEnable = (params) =>
  defZhczHttp.post<any>({
    url: Api.UpdateFactoryPictureEnable,
    params,
  });
// 删除
export const deleteFactoryPicture = (params) =>
  defZhczHttp.post<any>({
    url: Api.DeleteFactoryPicture,
    params,
  });
//创建厂区图
export const createFactoryPicture = (params) =>
  defZhczHttp.post<any>({
    url: Api.CreateFactoryPicture,
    params,
  });
//获取所有厂区
export const getAllFactory = (data) =>
  defZhczHttp.post<any>(
    {
      url: Api.GetAllFactory,
      data,
    },
    // { isTransformResponse: false },
  );

export const getFactoryList = () =>
  defZhczHttp.get<any>({
    url: Api.GetFactoryList,
  });

// 更新用户当前使用的水厂
export const updateFactoryUsedStatus = (data: { factoryId: number }) =>
  defZhczHttp.post<any>({
    url: Api.UpdateFactoryUsedStatus,
    data,
  });

// 获取水厂列表
export const getFactoryPage = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetFactoryPage,
    params,
  });

// 添加水厂
export const createFactory = (data) =>
  defZhczHttp.post<any>({
    url: Api.CreateFactory,
    data,
  });

// 更新水厂
export const updateFactory = (data) =>
  defZhczHttp.post<any>({
    url: Api.UpdateFactory,
    data,
  });
// 更新水厂
export const deleteFactory = (data) =>
  defZhczHttp.post<any>({
    url: Api.DeleteFactory,
    data,
  });

// 获取水厂详情
export const getFactoryDetail = (id) =>
  defZhczHttp.get<any>({
    url: Api.GetFactoryDetail + id,
  });
// 获取水厂省市数据
export const searchLocationList = (params) =>
  defZhczHttp.post<any>({
    url: Api.SearchLocationList,
    params,
  });

// 获取所有厂区图
export const getFactoryPictureList = (params) =>
  defZhczHttp.get<any>({
    url: Api.GetFactoryPictureList,
    params,
  });

// 获取当前用户可用水厂
export const getCurrentUserFactoryList = () =>
  defZhczHttp.get<any>({
    url: Api.GetCurrentUserFactoryList,
  });
