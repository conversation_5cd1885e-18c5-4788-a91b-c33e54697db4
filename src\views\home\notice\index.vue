<template>
  <Container title="通知公告" height="350px" :class="prefixCls">
    <template #title-after>
      <a-button type="link" v-if="morePath" @click="go(morePath)" style="padding: 0">更多</a-button>
    </template>
    <div class="relative h-[100%] px-20px" v-loading="loading">
      <Empty desc="暂无通知公告" v-if="!noticeList.length && !loading" />
      <Table
        :columns="columns"
        :data-source="noticeList"
        :pagination="false"
        v-if="noticeList.length"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'title'">
            <div
              style="color: var(--theme-color); cursor: pointer"
              class="truncate"
              @click="handleCheck(record)"
            >
              {{ record.title }}
            </div>
          </template>
        </template>
      </Table>
    </div>
    <NoticeModal @register="registerModal" />
  </Container>
</template>

<script lang="ts" setup>
  import { useDesign } from '/@/hooks/web/useDesign';
  import Container from '../components/Container.vue';
  import { getNewsList } from '/@/api/admin/news';
  import { onActivated, ref } from 'vue';
  import { Table } from 'ant-design-vue';
  import Empty from '../components/Empty.vue';
  import NoticeModal from './NoticeModal.vue';
  import { useModal } from '/@/components/Modal';
  import { ParamsKeyEnum } from '../enum';
  import { getParamKeyApi } from '/@/api/admin/param';
  import { useGo } from '/@/hooks/web/usePage';

  defineOptions({
    name: 'Notice',
  });

  const { prefixCls } = useDesign('Notice-Box');
  const loading = ref(true);
  const noticeList = ref<any[]>([]);
  const [registerModal, { openModal }] = useModal();
  const morePath = ref();
  const go = useGo();

  function handleCheck(record: any) {
    openModal(true, record.id);
  }

  const columns = [
    {
      title: '文件名称',
      dataIndex: 'title',
      key: 'title',
      ellipsis: { showTitle: false },
    },
    {
      title: '发布时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 280,
      ellipsis: { showTitle: false },
    },
  ];

  const getNoticeList = async () => {
    try {
      const res = await getNewsList({
        current: 1,
        listType: 0,
        searchType: 0,
        size: 5,
        type: '2',
      });
      noticeList.value = res.records;
    } finally {
      loading.value = false;
    }
  };

  const getMorePath = async () => {
    morePath.value = await getParamKeyApi(ParamsKeyEnum.NOTICE_PATH);
  };

  getMorePath();
  getNoticeList();

  onActivated(() => {
    getNoticeList();
  });
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-Notice-Box';

  .@{prefix-cls} {
    :deep(.ant-table-wrapper) {
      height: 100%;

      .ant-table-cell {
        padding: 0 24px 0 11px;
        height: 46px;
      }

      .ant-table-thead {
        .ant-table-cell {
          height: 48px;
        }
      }

      .ant-table-row {
        .ant-table-cell {
          transition: all 0.3s;
          background-color: transparent !important;
        }

        &:hover .ant-table-cell {
          background-color: #f0f0f0 !important;
        }

        &:last-of-type {
          .ant-table-cell {
            border-bottom: none;
          }
        }
      }
    }
  }
</style>
