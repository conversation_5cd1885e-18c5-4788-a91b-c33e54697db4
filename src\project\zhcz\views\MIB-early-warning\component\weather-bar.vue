<!-- 顶部天气栏 -->
<template>
  <div class="weather-bar">
    <div class="weather-bar__left">
      <img class="weather-icon" :src="weatherIcon" />
      <span class="weather-bar__temp">{{ temperature }}℃</span>
      <span class="font18">{{ weather }}</span>
      <img class="weather-bar__icon" :src="rainwatherImg" />
      湿度
      <span class="font18">
        <span class="font-weight">{{ humidity }}</span
        >%</span
      >
      <img class="weather-bar__icon" :src="atmosphericPressureImg" />
      气压
      <span class="font18">
        <span class="font-weight">{{ pressure }}</span
        >kPa</span
      >
      <img class="weather-bar__icon" :src="windPowerImg" />
      风力
      <span class="font18"
        ><span class="font-weight">{{ windLevel }}</span
        >级</span
      >
    </div>
    <div class="weather-bar__right">
      <span class="weather-bar__time font-weight">{{ currentTime }}</span>
      <div class="weather-bar__alarm" @click="openAlarm('alarm')">
        <img class="alarm_box" :src="alarmImg" alt="" srcset="" />
        <span class="alarm_size">3</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  const currentTime = ref('');
  const weather = ref('阵雨转晴');
  const temperature = ref('25');
  const humidity = ref('75');
  const pressure = ref('101');
  const windLevel = ref('4');
  import weatherIcon from '../assets/images/weather.png';
  import rainwatherImg from '../assets/images/rainwather.png';
  import atmosphericPressureImg from '../assets/images/atmospheric-pressure.png';
  import windPowerImg from '../assets/images/wind-power.png';
  import alarmImg from '../assets/images/alarm.png';
  function updateTime() {
    const now = new Date();
    currentTime.value = now.toLocaleString('zh-CN', { hour12: false });
  }
  const openAlarm = (type: string) => {
    console.log('打开报警详情', type);
  };
  onMounted(() => {
    updateTime();
    setInterval(updateTime, 1000);
  });
</script>

<style lang="less" scoped>
  .weather-bar {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(241, 249, 251, 1);
    padding: 0 32px;
    height: 72px;
    font-size: 18px;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .weather-bar__left {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .weather-icon {
      width: 42px;
      height: 35px;
    }

    .font18 {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 18px;
      color: #000000;
    }

    .weather-bar__icon {
      width: 18px;
      height: 18px;
      margin-right: 4px;
      margin-left: 24px;
    }

    .weather-bar__temp {
      font-family: Alimama ShuHeiTi;
      font-weight: 700;
      font-size: 22px;
      color: #000000;
    }

    .font-weight {
      font-family: Alimama ShuHeiTi;
      font-weight: 700;
      font-size: 20px;
      color: #000000;
      line-height: 20px;
    }

    // .weather-bar__time {
    //   margin-right: 20px;
    // }

    .weather-bar__right {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .weather-bar__alarm {
      position: relative;
      cursor: pointer;

      .alarm_box {
        width: 48px;
        height: 48px;
      }

      .alarm_size {
        position: absolute;
        top: 0;
        right: 0;
        height: 24px;
        width: 24px;
        background: #f55400;
        border-radius: 12px;
        color: #fff;
        font-weight: 600;
        font-size: 16px;
        text-align: center;
      }
    }
  }
</style>
