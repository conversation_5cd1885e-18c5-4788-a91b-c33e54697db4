import { FormSchema } from '/@/components/Table';
import { ref } from 'vue';
import { getDictTypeListApi } from '/@/api/admin/dict';
import { DICT } from '../enums';
import { searchEquipmentTypeOptionsApi, searchFactoryListApi } from '/@zoology-wzb/api/device';

export const functionalCategoryOptiton = ref(); //设备分类下拉选项
export const equipmentStatusOptions = ref(); //设备状态下拉选项
export const factoryOptiton = ref(); //使用单位下拉选项
getDictTypeListApi({ type: 'equipment_status' }).then((res) => {
  equipmentStatusOptions.value = res;
});
searchFactoryListApi().then((res) => {
  factoryOptiton.value = res.map((v) => {
    return {
      label: v.name,
      value: v.id,
    };
  });
});
/* 获取下拉选项的label */
export const getOptionLabel = (option, str) => {
  return option?.find?.((i) => i.value == str)?.label;
};

export const searchFormSchema: FormSchema[] = [
  {
    field: 'equipmentExternalId',
    label: '设备标识',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'equipmentTypeId',
    label: '设备类型',
    component: 'ApiSelect',
    componentProps: {
      api: searchEquipmentTypeOptionsApi,
      labelField: 'equipmentTypeName',
      valueField: 'id',
      showSearch: true,
      params: { equipmentTypeName: '' },
      filterOption: (input, option) => {
        return option.label.indexOf(input) >= 0;
      },
    },
    colProps: { span: 6 },
    labelWidth: 80,
  },
  {
    field: 'functionalCategoryKey',
    label: '功能类别',
    component: 'ApiSelect',
    componentProps: {
      api: async () => {
        const res = await getDictTypeListApi({ type: DICT.FUNCTIONAL_CATEGORY });
        functionalCategoryOptiton.value = res;
        return res;
      },
    },
    colProps: { span: 6 },
    labelWidth: 80,
  },
];
export const infoCardSchemas = [
  {
    slot: 'equipmentName',
    field: 'equipmentName',
  },
  {
    label: '设备标识',
    field: 'equipmentExternalId',
  },
  {
    label: '设备类型',
    field: 'equipmentTypeName',
  },
  {
    label: '设备型号',
    field: 'equipmentModelName',
  },
  {
    label: '使用单位',
    field: 'usingOrganizationName',
  },
];
