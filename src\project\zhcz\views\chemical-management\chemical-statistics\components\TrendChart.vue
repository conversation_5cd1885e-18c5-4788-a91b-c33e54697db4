<template>
  <Card :bordered="false" dis-hover :title="title">
    <template #extra>
      <div class="extra-wrap">
        <DatePicker
          v-model:value="formData.dayDate"
          picker="month"
          valueFormat="YYYY-MM"
          :allowClear="false"
          class="!w-[auto]"
          placeholder="请选择"
          :disabledDate="disabledDate"
          @change="handleChangeDayDate"
        />
        <Select
          v-model:value="formData.argOne"
          @change="handleOneChange"
          placeholder="请选择"
          class="ml-16px"
          :dropdownMatchSelectWidth="false"
        >
          <SelectOption v-for="item in optionsOne" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
        <Select
          v-model:value="formData.argTwo"
          @change="handleTwoChange"
          placeholder="请选择"
          class="ml-16px"
          :dropdownMatchSelectWidth="false"
        >
          <SelectOption v-for="item in optionsTwo" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
      </div>
    </template>
    <div ref="chartRef" style="height: 100%; min-height: 120px" v-show="!isEmpty"></div>

    <div v-show="isEmpty" class="empty-box">
      <HEmpty />
    </div>
  </Card>
</template>

<script setup>
  import { DatePicker, Card, Select, SelectOption } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { ref, watch, reactive, onMounted } from 'vue';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { roundAndConvert } from '/@zhcz/utils/number';

  let formData = reactive({
    dayDate: dayjs().format('YYYY-MM'),
    argOne: null,
    argTwo: null,
  });

  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }
  let optionsOne = ref([]);
  let optionsTwo = ref([]);
  let cacheList = [];
  const chartRef = ref(null);

  const props = defineProps({
    title: { type: String, required: true },
    sourceData: { type: Array, default: () => [] },
  });

  // 更新选项一
  function updateOptionsOne(newValue) {
    if (newValue.length < 1) return;
    let res = newValue.find((i) => i.groupCode === 'hytj_szqst')?.children || [];
    if (res.length < 1) return;
    optionsOne.value = res.map(({ groupCode, name }) => ({
      value: groupCode,
      label: name,
    }));
    formData.argOne = res[0].groupCode;
    getData();
  }
  function updateOptionsTwo(list) {
    // 如果没找到，默认选中第一个
    !list.find((i) => i.indexCode === formData.argTwo) &&
      (formData.argTwo = list[0] ? list[0].indexCode : '');
    optionsTwo.value = list.map(({ indexCode, indexName }) => ({
      value: indexCode,
      label: indexName,
    }));
  }

  let getParams = () => {
    let paramsData = {
      startDateTime: dayjs(formData.dayDate).startOf('month').format('YYYY-MM-DD 00:00:00'),
      endDateTime: dayjs(formData.dayDate).endOf('month').format('YYYY-MM-DD 23:59:59'),
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    let params = {
      resourceInterfaceId: '3',
      groupCode: formData.argOne,
      paramsData: JSON.stringify(paramsData),
    };
    return params;
  };

  async function getData() {
    let params = getParams();
    try {
      let { data } = await callResourceFunction(params);
      cacheList = data = data || [];
      updateOptionsTwo(data);
      renderChart();
    } catch (error) {
      isEmpty.value = true;
    }
  }

  let isEmpty = ref(true);
  function renderChart() {
    let data = cacheList.find((i) => i.indexCode === formData.argTwo);
    if (!data?.data?.length) {
      isEmpty.value = true;
      return;
    } else {
      isEmpty.value = false;
    }

    let { unitName, data: list, indexName } = data;

    const option = {
      color: ['#2E7BFF'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(255, 255, 255, 1)',
        textStyle: {
          color: '#999',
          fontSize: 14,
          lineHeight: 28,
          height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
        formatter: (params) => {
          return `<div>${dayjs(params[0].name).format('YYYY-MM-DD')}</div>
            ${params[0].marker} <span style="color: #333;">${
            params[0].seriesName
          }</span><span style="padding-left: 15px; color: #333; font-weight: 600;">${
            params[0].value ?? '-'
          }</span><span>${unitName}</span>
     `;
        },
      },
      legend: {
        icon: 'circle',
        itemWidth: 8,
        itemHeight: 8,
        itemGap: 24,
        color: ['#2B63A1'],
        data: [indexName],
        textStyle: {
          fontSize: 14,
          color: '#333',
        },
      },
      grid: {
        top: '15%',
        left: '0%',
        right: '1%',
        bottom: '1%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          data: list.map((i) => i.collectDateTime),
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#666',
              fontSize: 14,
            },
            formatter(value) {
              return dayjs(value).format('DD日');
            },
          },
          axisPointer: {
            type: 'shadow',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: unitName ? `单位（${unitName}）` : '',
          nameTextStyle: {
            color: '#666',
            fontSize: 14,
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#666',
              fontSize: 14,
            },
          },
        },
      ],
      series: [
        {
          symbol: 'none',
          name: indexName,
          type: 'line',
          color: ['#2E7BFF'],
          data: list.map((i) => (i.value == null ? null : roundAndConvert(Number(i.value), 2))),
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(46, 123, 255, 0.24)',
              },
              {
                offset: 1,
                color: 'rgba(46, 123, 255, 0.08)',
              },
            ]),
          },
        },
      ],
      animation: false,
    };
    let instance = getInstance();
    instance?.clear();

    setTimeout(() => {
      setOptions(option);
    }, 10);
  }

  watch(() => props.sourceData, updateOptionsOne);

  function handleOneChange() {
    getData();
  }
  function handleTwoChange() {
    renderChart();
  }
  function handleChangeDayDate() {
    getData();
  }
  let setOptions;
  let echarts;
  let getInstance;
  onMounted(() => {
    ({ setOptions, echarts, getInstance } = useECharts(chartRef));
  });
</script>

<style lang="less" scoped></style>
