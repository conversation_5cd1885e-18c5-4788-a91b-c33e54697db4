import type { echartsListType, ArrayListType } from './dataType';

export const colorRgba: string[] = [
  'rgba(45,130,254,0.8)',
  'rgba(253,165,78,0.8)',
  'rgba(31,195,164,0.8)',
  'rgba(97,195,31,0.8)',
  'rgba(78,96,253,0.8)',
  'rgba(45,48,254,0.8)',
  'rgba(115,45,254,0.8)',
  'rgba(31,195,105,0.8)',
  'rgba(254,139,45,0.8)',
  'rgba(254,197,45,0.8)',
  'rgba(254,244,45,0.8)',
  'rgba(45,202,254,0.8)',
  'rgba(45,226,254,0.8)',
  'rgba(84,112,198,0.8)',
  'rgba(145,204,117,0.8)',
  'rgba(250,200,88,0.8)',
  'rgba(238,102,102,0.8)',
  'rgba(115,192,222,0.8)',
  'rgba(59,162,114,0.8)',
  'rgba(252,132,82,0.8)',
  'rgba(154,96,180,0.8)',
  'rgba(234,124,204,0.8)',
  'rgba(24,120,204,0.8)',
  'rgba(84,140,204,0.8)',
  'rgba(120,160,204,0.8)',
  'rgba(150,34,204,0.8)',
];
export const colorRgbaStar: string[] = [
  'rgba(45,130,254, 0.08)',
  'rgba(253,165,78,0.08)',
  'rgba(31,195,164,0.08)',
  'rgba(97,195,31,0.08)',
  'rgba(78,96,253,0.08)',
  'rgba(45,48,254,0.08)',
  'rgba(115,45,254,0.08)',
  'rgba(31,195,105,0.08)',
  'rgba(254,139,45,0.08)',
  'rgba(254,197,45,0.08)',
  'rgba(254,244,45,0.08)',
  'rgba(45,202,254,0.08)',
  'rgba(45,226,254,0.08)',
  'rgba(84,112,198,0.08)',
  'rgba(145,204,117,0.08)',
  'rgba(250,200,88,0.08)',
  'rgba(238,102,102,0.08)',
  'rgba(115,192,222,0.08)',
  'rgba(59,162,114,0.08)',
  'rgba(252,132,82,0.08)',
  'rgba(154,96,180,0.08)',
  'rgba(234,124,204,0.08)',
  'rgba(24,120,204,0.08)',
  'rgba(84,140,204,0.08)',
  'rgba(120,160,204,0.08)',
  'rgba(150,34,204,0.08)',
];
export const colorRgbaEnd: string[] = [
  'rgba(45,130,254, 0)',
  'rgba(253,165,78,0)',
  'rgba(31,195,164,0)',
  'rgba(97,195,31,0)',
  'rgba(78,96,253,0)',
  'rgba(45,48,254,0)',
  'rgba(115,45,254,0)',
  'rgba(31,195,105,0)',
  'rgba(254,139,45,0)',
  'rgba(254,197,45,0)',
  'rgba(254,244,45,0)',
  'rgba(45,202,254,0)',
  'rgba(45,226,254,0)',
  'rgba(84,112,198,0)',
  'rgba(145,204,117,0)',
  'rgba(250,200,88,0)',
  'rgba(238,102,102,0)',
  'rgba(115,192,222,0)',
  'rgba(59,162,114,0)',
  'rgba(252,132,82,0)',
  'rgba(154,96,180,0)',
  'rgba(234,124,204,0)',
  'rgba(24,120,204,0)',
  'rgba(84,140,204,0)',
  'rgba(120,160,204,0)',
  'rgba(150,34,204,0)',
];
export const colorNightRgba: string[] = [
  'rgba(45,130,254,1)',
  'rgba(253,165,78,1)',
  'rgba(31,195,164,1)',
  'rgba(97,195,31,1)',
  'rgba(78,96,253,1)',
  'rgba(45,48,254,1)',
  'rgba(115,45,254,1)',
  'rgba(31,195,105,1)',
  'rgba(254,139,45,1)',
  'rgba(254,197,45,1)',
  'rgba(254,244,45,1)',
  'rgba(45,202,254,1)',
  'rgba(45,226,254,1)',
  'rgba(84,112,198,1)',
  'rgba(145,204,117,1)',
  'rgba(250,200,88,1)',
  'rgba(238,102,102,1)',
  'rgba(115,192,222,1)',
  'rgba(59,162,114,1)',
  'rgba(252,132,82,1)',
  'rgba(154,96,180,1)',
  'rgba(234,124,204,1)',
];
export const colorRgbaNightStar: string[] = [
  'rgba(45,130,254, 0.3)',
  'rgba(253,165,78,0.3)',
  'rgba(31,195,164,0.3)',
  'rgba(97,195,31,0.3)',
  'rgba(78,96,253,0.3)',
  'rgba(45,48,254,0.3)',
  'rgba(115,45,254,0.3)',
  'rgba(31,195,105,0.3)',
  'rgba(254,139,45,0.3)',
  'rgba(254,197,45,0.3)',
  'rgba(254,244,45,0.3)',
  'rgba(45,202,254,0.3)',
  'rgba(45,226,254,0.3)',
  'rgba(84,112,198,0.3)',
  'rgba(145,204,117,0.3)',
  'rgba(250,200,88,0.3)',
  'rgba(238,102,102,0.3)',
  'rgba(115,192,222,0.3)',
  'rgba(59,162,114,0.3)',
  'rgba(252,132,82,0.3)',
  'rgba(154,96,180,0.3)',
  'rgba(234,124,204,0.3)',
];
export const colorRgbaNightEnd: string[] = [
  'rgba(45,130,254, 0)',
  'rgba(253,165,78,0)',
  'rgba(31,195,164,0)',
  'rgba(97,195,31,0)',
  'rgba(78,96,253,0)',
  'rgba(45,48,254,0)',
  'rgba(115,45,254,0)',
  'rgba(31,195,105,0)',
  'rgba(254,139,45,0)',
  'rgba(254,197,45,0)',
  'rgba(254,244,45,0)',
  'rgba(45,202,254,0)',
  'rgba(45,226,254,0)',
  'rgba(84,112,198,0)',
  'rgba(145,204,117,0)',
  'rgba(250,200,88,0)',
  'rgba(238,102,102,0)',
  'rgba(115,192,222,0)',
  'rgba(59,162,114,0)',
  'rgba(252,132,82,0)',
  'rgba(154,96,180,0)',
  'rgba(234,124,204,0)',
];
export const horizontaSmallSquareData: ArrayListType[] = [
  {
    indexName: '当前电价',
    indexCode: 'fyd',
    unitName: '元',
    value: 0.96,
  },
  {
    indexName: '储能状态',
    indexCode: 'fyd',
    unitName: '',
    color: 'rgba(77, 184, 3, 1)',
    value: '充电中',
  },
  {
    indexName: '当前温度',
    indexCode: 'fyd',
    unitName: ' ℃',
    value: 35,
  },
  {
    indexName: '当前天气',
    indexCode: 'fyd',
    unitName: '',
    value: '晴',
  },
  {
    indexName: '当前储能量',
    indexCode: 'fyd',
    unitName: 'kW',
    value: 2000,
  },
];

export const linechartsData: echartsListType[] = [
  {
    indexName: '电价',
    indexCode: 'E_PD_M_M_ZYDDH_JS_H_M',
    unitName: '元',
    type: 'line',
    data: [
      5459.36, 5474.6, 5596.57, 5496.15, 5580.98, 5474.6, 5622.18, 5611.82, 5622.93, 5597.89,
      5597.91, 5502.64, 5597.89, 5597.91, 5502.64, 5597.89, 5597.91, 5502.64, 5597.89, 5597.91,
      5502.64, 5597.89, 5597.91, 5502.64,
    ],
    XAxis: [
      '00:00',
      '01:00',
      '02:00',
      '03:00',
      '04:00',
      '05:00',
      '06:00',
      '07:00',
      '08:00',
      '09:00',
      '10:00',
      '11:00',
      '12:00',
      '13:00',
      '14:00',
      '15:00',
      '16:00',
      '17:00',
      '18:00',
      '19:00',
      '20:00',
      '21:00',
      '22:00',
      '23:00',
    ],
  },
  {
    indexName: '温度',
    indexCode: 'E_PD_M_M_SCGLYDDH_JS_H_M',
    unitName: '℃',
    type: 'line',
    data: [
      461.51, 499.25, 421.85, 414.17, 489.81, 414.17, 496.07, 407.94, 416.38, 402.58, 499.56,
      468.68, 402.58, 499.56, 468.68, 402.58, 499.56, 468.68, 402.58, 499.56, 468.68, 402.58,
      499.56, 468.68,
    ],
    XAxis: [
      '00:00',
      '01:00',
      '02:00',
      '03:00',
      '04:00',
      '05:00',
      '06:00',
      '07:00',
      '08:00',
      '09:00',
      '10:00',
      '11:00',
      '12:00',
      '13:00',
      '14:00',
      '15:00',
      '16:00',
      '17:00',
      '18:00',
      '19:00',
      '20:00',
      '21:00',
      '22:00',
      '23:00',
    ],
  },
  {
    indexName: '储能',
    indexCode: 'qtyd_jsxs',
    unitName: 'kW·h',
    type: 'line',
    data: [
      4997.85, 4975.35, 5174.72, 5081.98, 5091.17, 5174.72, 5174.72, 5203.88, 5174.72, 5195.31,
      5098.35, 5033.96, 5195.31, 5098.35, 5033.96, 5195.31, 5098.35, 5033.96, 5195.31, 5098.35,
      5033.96, 5195.31, 5098.35, 5033.96,
    ],
    XAxis: [
      '00:00',
      '01:00',
      '02:00',
      '03:00',
      '04:00',
      '05:00',
      '06:00',
      '07:00',
      '08:00',
      '09:00',
      '10:00',
      '11:00',
      '12:00',
      '13:00',
      '14:00',
      '15:00',
      '16:00',
      '17:00',
      '18:00',
      '19:00',
      '20:00',
      '21:00',
      '22:00',
      '23:00',
    ],
  },
];

export const barechartsData: echartsListType[] = [
  {
    indexName: '峰',
    indexCode: 'E_PD_M_M_1JLGDSGZ_JS_H_M',
    unitName: 'kW·h',
    type: 'bar',
    XAxis: [
      '00:00',
      '01:00',
      '02:00',
      '03:00',
      '04:00',
      '05:00',
      '06:00',
      '08:00',
      '09:00',
      '10:00',
      '11:00',
      '12:00',
      '13:00',
      '14:00',
      '15:00',
      '16:00',
      '17:00',
      '18:00',
      '19:00',
      '20:00',
      '21:00',
      '22:00',
      '23:00',
    ],
    data: [
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      '27.10',
      null,
      null,
      null,
      '25.05',
      '25.19',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
    ],
  },
  {
    indexName: '峰尖',
    indexCode: 'E_PD_M_M_1JLGDSGZ_JS_H_M',
    unitName: 'kW·h',
    type: 'bar',
    XAxis: [
      '00:00',
      '01:00',
      '02:00',
      '03:00',
      '04:00',
      '05:00',
      '06:00',
      '08:00',
      '09:00',
      '10:00',
      '11:00',
      '12:00',
      '13:00',
      '14:00',
      '15:00',
      '16:00',
      '17:00',
      '18:00',
      '19:00',
      '20:00',
      '21:00',
      '22:00',
      '23:00',
    ],
    data: [
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      '27.26',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
    ],
  },
  {
    indexName: '平',
    indexCode: 'E_PD_M_M_1JLGDSGZ_JS_H_M',
    unitName: 'kW·h',
    type: 'bar',
    XAxis: [
      '00:00',
      '01:00',
      '02:00',
      '03:00',
      '04:00',
      '05:00',
      '06:00',
      '08:00',
      '09:00',
      '10:00',
      '11:00',
      '12:00',
      '13:00',
      '14:00',
      '15:00',
      '16:00',
      '17:00',
      '18:00',
      '19:00',
      '20:00',
      '21:00',
      '22:00',
      '23:00',
    ],
    data: [
      '27.91',
      '25.12',
      '26.50',
      '27.29',
      '26.07',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
    ],
  },
  {
    indexName: '谷',
    indexCode: 'E_PD_M_M_1JLGDSGZ_JS_H_M',
    unitName: 'kW·h',
    type: 'bar',
    XAxis: [
      '00:00',
      '01:00',
      '02:00',
      '03:00',
      '04:00',
      '05:00',
      '06:00',
      '08:00',
      '09:00',
      '10:00',
      '11:00',
      '12:00',
      '13:00',
      '14:00',
      '15:00',
      '16:00',
      '17:00',
      '18:00',
      '19:00',
      '20:00',
      '21:00',
      '22:00',
      '23:00',
    ],
    data: [
      null,
      null,
      null,
      null,
      null,
      null,
      '27.92',
      null,
      null,
      '27.95',
      '27.68',
      null,
      null,
      '26.71',
      '26.44',
      '27.81',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
    ],
  },
  {
    indexName: '电价',
    indexCode: 'dj',
    unitName: '元',
    type: 'line',
    XAxis: [
      '00:00',
      '01:00',
      '02:00',
      '03:00',
      '04:00',
      '05:00',
      '06:00',
      '08:00',
      '09:00',
      '10:00',
      '11:00',
      '12:00',
      '13:00',
      '14:00',
      '15:00',
      '16:00',
      '17:00',
      '18:00',
      '19:00',
      '20:00',
      '21:00',
      '22:00',
      '23:00',
    ],
    data: [
      0.5, 0.5, 0.5, 0.6, 0.6, 0.6, 0.7, 0.5, 0.5, 0.5, 0.4, 0.4, 0.6, 0.5, 0.6, 0.6, 0.5, 0.5, 0.5,
      0.5, 0.6, 0.5, 0.6,
    ],
  },
];
