// 导出目录名数组
const fileNames = ['rightSelect'];
const componentImports = [import.meta.glob('./rightSelect/*.vue')];
// 导出组件对象
export const rightHeaderComponents: Record<string, any> = {};
// 导出加载组件的异步函数
export const loadHeaderComponents = async () => {
  for (const [index, component] of componentImports.entries()) {
    const currentDir = fileNames[index];
    for (const path in component) {
      const module = await component[path]();
      const regex = new RegExp(`^\\.\\/${currentDir}\\/(.*)\\.\\w+$`);
      const componentName = path.replace(regex, '$1');
      rightHeaderComponents[componentName] = (module as { default: any }).default;
    }
  }
  return rightHeaderComponents;
};
loadHeaderComponents().then((res) => console.log(res));
