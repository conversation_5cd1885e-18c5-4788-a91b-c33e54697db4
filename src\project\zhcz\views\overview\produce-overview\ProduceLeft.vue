<template>
  <div class="produce-overview-left">
    <BigBoxContainer>
      <BoxContainer style="height: calc(60% - 4px)">
        <template #header>
          <BoxHeader :title="title1" />
        </template>
        <template #content>
          <div class="container produce-data">
            <!-- 进水量 出水量indicatorsByTimeIntervalResp.data[0]value 图片是配置的 imgByTimeIntervalResps.sourceUniqueKey -->
            <ProduceDataTop
              :data-list="state.produceTopData"
              :data-resource-code="indexList?.index_LT1[0]?.groupCode"
            />
            <div class="chart-container">
              <div class="title-box">
                <div class="charts-title">{{ state.produceBottomData.title }}</div>
                <div class="select-box">
                  <Select
                    v-model:value="sludgeTime"
                    style="width: 5rem"
                    @change="handleSludgeChange"
                    class="big-screen-select"
                    popupClassName="big-screen-select-dropdown"
                  >
                    <SelectOption v-for="item in dataList" :value="item.value" :key="item.value">
                      {{ item.label }}
                    </SelectOption>
                  </Select>
                  <DatePicker
                    v-model:value="date"
                    :picker="getPicker"
                    class="date-picker big-screen-date-picker"
                    popupClassName="big-screen-date-picker-dropdown"
                    placeholder="选择日期"
                    :allowClear="false"
                    :showToday="false"
                    style="width: 126px !important"
                    :disabledDate="disabledDate"
                    @change="handleDateChange"
                  />
                </div>
              </div>
              <div class="chart-container-c">
                <!-- <template v-if="state.projectLoad">
                  <div style="background: unset"></div>
                </template> -->
                <template v-if="state.projectLoad">
                  <ProduceDataChart :data="state.produceBottomData" />
                </template>
                <DataEmpty v-else />
              </div>
            </div>
          </div>
        </template>
      </BoxContainer>
      <BoxContainer style="height: calc(40% - 4px)">
        <template #header>
          <BoxHeader :title="title2" />
        </template>
        <template #content>
          <div class="container quality-index">
            <div class="list-container">
              <IndexList
                type="blue"
                :title="title2_1"
                :data="state.rawWaterData"
                :loading="state.rawLoading"
                :data-resource-code="indexList?.index_LB1[0]?.groupCode"
              />
              <IndexList
                type="green"
                :title="title2_2"
                :data="state.factoryWaterData"
                :loading="state.factoryLoading"
                :data-resource-code="indexList?.index_LB2[0]?.groupCode"
              />
            </div>
          </div>
        </template>
      </BoxContainer>
    </BigBoxContainer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch, onUnmounted } from 'vue';
  import dayjs from 'dayjs';
  import { useIndexList, useTitleList } from '../hooks';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { mockDataList, mockChartData } from './data';
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import { flatMap } from 'lodash-es';
  import { useIntervalFn30S } from '/@zhcz/utils/useInterval';
  import BigBoxContainer from '../components/box-container/BigBoxContainer.vue';
  import BoxContainer from '../components/box-container/index.vue';
  import BoxHeader from '../components/box-container/BoxHeader.vue';
  import ProduceDataChart from '../components/echarts/ProduceDataChart.vue';
  import IndexList from './components/IndexList.vue';
  import ProduceDataTop from '../components/produce-data/index.vue';
  import { roundAndConvertCheckNullAndUnDef } from '/@zhcz/utils/number';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';
  import DataEmpty from '../components/data-empty/index.vue';

  const sludgeTime = ref<null | string>(null);
  const dataList = ref<{ label: string; value: string }[]>([]);
  async function getTimeList() {
    const res = await listSenceGroupByParent({
      groupCode: 'jqtsjqx',
      factoryId: 1,
      platformld: 1,
    });
    if (Object.keys(res).length) {
      // sludgeTime.value = Object.keys(res)[0];
      dataList.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      sludgeTime.value = dataList.value.find((val) => val.label === '月')?.value || null;
      const picker =
        pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '月'];
      if (picker === 'date') {
        date.value = dayjs().subtract(0, 'day');
      } else {
        date.value = dayjs().subtract(0, picker);
      }
      getProduceBottomData();
    }
  }
  enum pickerType {
    '日' = 'date',
    '月' = 'month',
    '年' = 'year',
  }
  const getPicker = computed(() => {
    if (sludgeTime.value) {
      return pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '日'];
    } else {
      return pickerType['日'];
    }
  });
  getTimeList();
  const handleSludgeChange = () => {
    const picker =
      pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '月'];
    if (picker === 'date') {
      date.value = dayjs().subtract(0, 'day');
    } else {
      date.value = dayjs().subtract(0, picker);
    }
    getProduceBottomData();
  };
  function disabledDate(current) {
    // 禁止选择今天以后的日期
    return current && current > dayjs().subtract(0, 'day');
  }

  const { indexList } = useIndexList();
  const { titleList } = useTitleList();

  const date = ref(dayjs().subtract(1, 'day'));
  const state = reactive({
    produceTopData: JSON.parse(JSON.stringify(mockDataList)),
    produceBottomData: {},
    rawWaterData: [],
    factoryWaterData: [],
    rawLoading: true,
    factoryLoading: true,
    projectLoad: true,
  });

  const title1 = computed(() => {
    return titleList.value.title_LT1;
  });
  const title2 = computed(() => {
    return titleList.value.title_LB;
  });
  const title2_1 = computed(() => {
    return titleList.value.title_LB_1;
  });
  const title2_2 = computed(() => {
    return titleList.value.title_LB_2;
  });

  function handleDateChange() {
    getProduceBottomData();
  }

  // 生产数据
  const getProduceTopData = async () => {
    const tempParams = indexList.value.index_LT1[0];
    const params = {
      startDateTime: null,
      endDateTime: null,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      ...tempParams,
      paramsData: JSON.stringify(params),
    };

    const { data } = await callResourceFunction(paramData);
    if (data && data.length) {
      state.produceTopData = data.map((i, index) => {
        const value = i.indicatorsByTimeIntervalResp.length
          ? i.indicatorsByTimeIntervalResp[0]?.data.reduce((prev, cur) => {
              return prev + cur.value;
            }, 0)
          : 0;
        return {
          indexName: i.indicatorsByTimeIntervalResp.length
            ? i.indicatorsByTimeIntervalResp[0]?.indexName
            : mockDataList[index].indexName,
          value: roundAndConvertCheckNullAndUnDef(value, 0),
          unitName: i.indicatorsByTimeIntervalResp.length
            ? i.indicatorsByTimeIntervalResp[0]?.unitName
            : mockDataList[index].unitName,
          src: i.imgByTimeIntervalResps[0]?.sourceUniqueKey,
          indexCode: i.indicatorsByTimeIntervalResp.length
            ? i.indicatorsByTimeIntervalResp[0]?.indexCode
            : '',
          // index: index + 1,
        };
      });
    } else {
      state.produceTopData = mockDataList;
    }
  };
  // 近七天数据曲线
  const getProduceBottomData = async () => {
    try {
      if (sludgeTime.value) {
        const picker =
          pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '日'];
        const startDate = dayjs(date.value).startOf(picker).format('YYYY-MM-DD 00:00:00');
        const endDataTime = dayjs(date.value).endOf(picker).format('YYYY-MM-DD 23:59:59');
        const tempParams = { resourceInterfaceId: '3', groupCode: sludgeTime.value };
        const params = {
          startDateTime: startDate,
          endDateTime: endDataTime,
          indexCodes: '@￥Resource',
          tenantId: '@￥TenantId',
          // type: 3,
        };
        const paramData = {
          ...tempParams,
          paramsData: JSON.stringify(params),
        };
        const { data } = await callResourceFunction(paramData);
        if (data && data.length) {
          state.projectLoad = true;
          const newData = JSON.parse(JSON.stringify(mockChartData));
          newData.title = titleList.value.title_LT2;
          if (data[0].data.length) {
            newData.chartOptions.xAxis.data = data[0].data.map((item) => {
              let row;
              if (picker === 'date') {
                row = dayjs(item.collectDateTime).format('HH:mm');
              } else if (picker === 'month') {
                row = dayjs(item.collectDateTime).format('MM-DD');
              } else if (picker === 'year') {
                row = dayjs(item.collectDateTime).format('MM') + '月';
              }
              return row;
            });
          }

          newData.chartOptions.series = data.map((item, index) => {
            const oldItem = mockChartData.chartOptions.series[index];
            let t_data = mockChartData.chartOptions.series[index].data;
            t_data = flatMap(item.data, 'value').map((item) =>
              roundAndConvertCheckNullAndUnDef(item, 0),
            );

            return {
              ...oldItem,
              name: item.indexName,
              data: t_data,
              unitName: item.unitName,
            };
          });
          state.produceBottomData = newData;
        } else {
          state.projectLoad = false;
          state.produceBottomData = mockChartData;
        }
      }
    } catch (err) {
      state.projectLoad = false;
    }
  };

  // 进水水质
  const getRawWaterData = async (first) => {
    try {
      const tempParams = indexList.value.index_LB1[0];
      const params = {
        startDateTime: dayjs().format('YYYY-MM-DD 00:00:00'),
        endDateTime: null,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };

      if (first) state.rawLoading = true;
      const { data } = await callResourceFunction(paramData);
      if (first) state.rawLoading = false;
      state.rawWaterData = data ? data : [];
    } catch (_) {
      state.rawLoading = false;
    }
  };

  // 出水水质
  const getFactoryWaterData = async (first) => {
    try {
      const tempParams = indexList.value.index_LB2[0];
      const params = {
        startDateTime: dayjs().format('YYYY-MM-DD 00:00:00'),
        endDateTime: null,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };

      if (first) state.factoryLoading = true;
      const { data } = await callResourceFunction(paramData);
      if (first) state.factoryLoading = false;
      state.factoryWaterData = data ? data : [];
    } catch (_) {
      state.factoryLoading = false;
    }
  };

  const getData = () => {
    getProduceTopData();
    getProduceBottomData();
  };

  const getWaterQualityData = (val) => {
    getRawWaterData(val);
    getFactoryWaterData(val);
  };

  watch(
    () => indexList.value,
    () => {
      getData();
      getWaterQualityData(true);
    },
  );

  const { pause } = useIntervalFn30S(getWaterQualityData);
  onUnmounted(() => {
    pause();
  });
</script>

<style lang="less" scoped>
  .produce-overview-left {
    width: 100%;
    height: 100%;

    .container {
      height: 100%;
      padding: 1rem 1rem 0 1rem;
    }

    .produce-data {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding: 0;

      .chart-container {
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;

        .chart-container-c {
          position: relative;
          flex: 1;
        }

        .title-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;

          .charts-title {
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
            line-height: 14px;
            text-align: left;
          }

          .select-box {
            flex: 1;
            display: flex;
            justify-content: end;
            gap: 0 12px;
          }
        }

        .date-picker {
          // position: absolute;
          z-index: 1000;
          // right: 0;
          // top: 0;
          // width: 160px !important;
        }
      }
    }

    :deep(.ant-picker) {
      .ant-picker-input > input {
        color: #fff;
      }

      .ant-picker-cell-disabled {
        // title {
        display: none;
        // }
      }
    }

    :deep(.ant-select-selection-item) {
      color: #fff;
    }

    .quality-index {
      padding: 1rem 0 0 0;

      @media screen and (max-width: 1742px) {
        padding: 1rem 1rem 0 1rem;
      }

      @media screen and (min-width: 1921px) {
        .list-container {
          gap: 0 1rem;
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media screen and (max-width: 1920px) {
        .list-container {
          justify-content: space-between;
          grid-template-columns: repeat(2, 1fr);
          gap: 0 1rem;
        }
      }

      @media screen and (max-width: 1440px) {
        .list-container {
          justify-content: space-between;
          grid-template-columns: repeat(2, 1fr);
          gap: 0 1rem;
        }
      }

      .list-container {
        display: grid;
        height: 100%;
        // height: 90.43%;
        // height: 76.43%;
      }

      .status {
        padding-top: 1rem;
        display: flex;
        justify-content: flex-end;
        column-gap: 1rem;

        .item {
          font-size: 0.875rem;
          font-weight: 400;
          color: #ffffff;

          &::before {
            margin-right: 0.5rem;
            display: inline-block;
            content: '';
            width: 0.625rem;
            height: 0.625rem;
            border-radius: 50%;
          }

          &_normal::before {
            background: #ffffff;
          }

          &_warn::before {
            background: #fec52d;
          }

          &_error::before {
            background: #fe392d;
          }
        }
      }
    }
  }
</style>
