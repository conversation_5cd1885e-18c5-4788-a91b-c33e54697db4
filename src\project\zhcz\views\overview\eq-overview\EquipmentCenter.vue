<template>
  <div class="equipment-center">
    <CenterBoxContainer>
      <template #info>
        <CenterInfo>
          <!-- 发起工单（近7天），变更为设备总数、 
           执行中工单（近7天），变更为设备正常数、 
           已完成工单（近7天），变更为设备故障数、-->
          <CenterInfoItem
            v-for="(item, index) in state.topData"
            :key="index"
            :data="item"
            :index="index"
            type="设备总览"
            :empty="state.topData.every((i) => i.value === null || i.value === undefined)"
          />
        </CenterInfo>
      </template>
      <template #model>
        <CenterModel v-if="state.bottomData.length" :dataList="state.bottomData" />
      </template>
    </CenterBoxContainer>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, onMounted, onUnmounted } from 'vue';
  import CenterBoxContainer from '../components/box-container/CenterBoxContainer.vue';
  import CenterInfo from '../components/center/center-info/index.vue';
  import CenterInfoItem from '../components/center/center-info/CenterInfoItem.vue';
  import CenterModel from '../components/center/center-model/index.vue';
  import { getEevReturnDomain } from '/@zhcz/utils/file/url';
  import { equipmentCenterEmptyData } from './data';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  // import { getGdInfo } from '/@zhcz/api/overview';
  import { useEmitt } from '/@/hooks/web/useEmitt';
  import dayjs from 'dayjs';
  import { getBiNumStatistic } from '/@zhcz/api/overview';

  const props = defineProps({
    isScene: {
      type: Boolean,
      default: true,
    },
  });

  const state = reactive({
    topData: JSON.parse(JSON.stringify(equipmentCenterEmptyData)),
    bottomData: [],
  });

  // 工单数据
  const getGdData = async () => {
    const tempParams = { resourceInterfaceId: '19', groupCode: 'sbzldpcj_zxsj' };
    const params = {
      startDateTime: dayjs().format('YYYY-MM-DD 00:00:00'),
      endDateTime: null,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      ...tempParams,
      paramsData: JSON.stringify(params),
    };
    // const data = await getGdInfo();
    // const { data } = await callResourceFunction(paramData);
    const res = props.isScene ? await callResourceFunction(paramData) : await getBiNumStatistic();
    const data = props.isScene ? res.data : res;
    if (props.isScene) {
      if (data && data.length) {
        state.topData = data.map((i, index) => {
          return {
            indexName: i.indicatorsByTimeIntervalResp.length
              ? i.indicatorsByTimeIntervalResp[0]?.indexName
              : equipmentCenterEmptyData[index].indexName,
            value: i.indicatorsByTimeIntervalResp.length
              ? i.indicatorsByTimeIntervalResp[0]?.data.reduce((prev, cur) => {
                  return prev + cur.value;
                }, 0)
              : 0,
            unitName: i.indicatorsByTimeIntervalResp.length
              ? i.indicatorsByTimeIntervalResp[0]?.unitName
              : equipmentCenterEmptyData[index].unitName,
            src: i.imgByTimeIntervalResps[0]?.sourceUniqueKey,
          };
        });
      }
    } else {
      state.topData = state.topData.map((item, index) => {
        return {
          ...item,
          value: index === 0 ? data.totalNum : index === 1 ? data.normalNum : data.fixNum,
        };
      });
    }
  };

  const getPicData = async () => {
    const params = {
      // startDateTime: dayjs().format('YYYY-MM-DD 00:00:00'),
      // endDateTime: null,
      // indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      resourceInterfaceId: '19',
      groupCode: 'sbzldpcj_tpz',
      paramsData: JSON.stringify(params),
    };

    const { data } = await callResourceFunction(paramData);
    if (data && data.length) {
      const newData = data.map((i) => {
        const index = i.indicatorsByTimeIntervalResp.map((j) => {
          return {
            indexName: j.indexName,
            unitName: j.unitName,
            value: j.data instanceof Array ? Number(j.data[0]?.value) || 0 : 0,
          };
        });
        return {
          id: i.imgByTimeIntervalResps[0].id,
          text: i.imgByTimeIntervalResps[0].displayName,
          src: getEevReturnDomain(i.imgByTimeIntervalResps[0].sourceUniqueKey),
          mediumType: i.imgByTimeIntervalResps[0].mediumType,
          index: index,
        };
      });
      state.bottomData = newData;
    } else {
      state.bottomData = [];
    }
  };

  function getData() {
    getGdData();
    getPicData();
  }

  let timer = null;
  onMounted(() => {
    getData();
    if (!timer) {
      timer = setInterval(getData, 1000 * 30);
    }
  });

  const { emitter } = useEmitt();
  emitter.on('bi:change-factory', () => {
    getData();
  });

  onUnmounted(() => {
    clearInterval(timer);
    timer = null;
    emitter.off('bi:change-factory');
  });
</script>

<style lang="less" scoped>
  .equipment-center {
    width: 100%;
    height: 100%;
  }
</style>
