import { FormSchema } from '/@/components/Table';
import dayjs from 'dayjs';

import { getDictTypeListApi } from '/@/api/admin/dict';
import { getUserListByPage } from '/@/api/admin/user';
import { DICT } from '/@/enums/dict';

export const fileSchemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'Id',
    show: false,
  },
  {
    field: 'fileName',
    component: 'Input',
    label: '文件名称',
    required: true,
  },
  {
    field: 'effectiveDeadline',
    component: 'DatePicker',
    label: '有效期',
    required: true,
    componentProps: {
      showTime: false,
    },
  },
  {
    field: 'publishDate',
    component: 'DatePicker',
    label: '发布日期',
    required: true,
    componentProps: {
      showTime: false,
    },
    defaultValue: dayjs(),
  },
  {
    field: 'fileList',
    component: 'Upload',
    label: '上传文件',
    slot: 'fileSlot',
    required: true,
    rules: [
      {
        required: true,
        message: '请上传文件',
      },
    ],
  },
];

export const peopleSchemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'Id',
    show: false,
  },
  {
    field: 'avatar',
    component: 'HUpload',
    label: '头像',
    slot: 'avatarSlot',
    colProps: { span: 24 },
    rules: [
      {
        required: true,
        validator: (_rule, arr) => {
          if (!arr || arr.length === 0) {
            return Promise.reject('请上传头像');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'person',
    component: 'Input',
    label: '人员',
    slot: 'personSlot',
    rules: [
      {
        required: true,
        validator: (_rule, arr) => {
          if (!arr || arr.length === 0) {
            return Promise.reject('请选择人员');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'phone',
    component: 'Input',
    label: '号码',
    componentProps: {
      disabled: true,
      placeholder: '默认自带',
    },
  },
  {
    field: 'department',
    label: '所属部门',
    component: 'TreeSelect',
    required: true,
    componentProps: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
      getPopupContainer: () => document.body,
      labelInValue: true,
    },
  },

  {
    field: 'role',
    component: 'Select',
    label: '角色',
    required: true,
    componentProps: {
      options: [
        {
          label: '组长',
          value: '1',
        },
        {
          label: '副组长',
          value: '2',
        },
        {
          label: '组员',
          value: '3',
        },
      ],
      labelInValue: true,
    },
  },
];

export const svgList = {
  pdf: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><g><g style="opacity:0.10000000149011612;"></g><g><g><rect class="svg-path" x="6.98046875" y="6.95703125" width="2.038900852203369" height="1.0424582958221436" rx="0" fill-opacity="1"/></g><g><path class="svg-path" d="M12.76877,4.838229999999999C12.76877,4.838229999999999,10.13725,2.229539,10.13725,2.229539C9.98879,2.0829399,9.78838,2,9.57897,2C9.57897,2,4.31582,2,4.31582,2C3.590028,2,3,2.585392,3,3.30433C3,3.30433,3,12.6956,3,12.6956C3,13.4151,3.590028,14,4.31582,14C4.31582,14,11.68418,14,11.68418,14C12.40997,14,13,13.4151,13,12.6956C13,12.6956,13,5.39129,13,5.39129C13,5.18416,12.91653,4.984859999999999,12.76877,4.838229999999999C12.76877,4.838229999999999,12.76877,4.838229999999999,12.76877,4.838229999999999ZM10.105039999999999,8.520150000000001C10.105039999999999,8.80762,9.86988,9.04189,9.57943,9.04189C9.57943,9.04189,6.9471,9.04345,6.9471,9.04345C6.9471,9.04345,6.9471,10.60868,6.9471,10.60868C6.9471,10.89718,6.71195,11.13041,6.42103,11.13041C6.13012,11.13041,5.89461,10.89718,5.89461,10.60868C5.89461,10.60868,5.89461,6.43479,5.89461,6.43479C5.89461,6.42537,5.89992,6.41754,5.90038,6.40815C5.9037299999999995,6.34816,5.912739999999999,6.28869,5.9355899999999995,6.23442C5.98904,6.10763,6.09052,6.00642,6.2181999999999995,5.95371C6.28204,5.92764,6.3505,5.91305,6.42103,5.91305C6.42103,5.91305,9.57897,5.91305,9.57897,5.91305C9.86988,5.91305,10.105039999999999,6.14679,10.105039999999999,6.43479C10.105039999999999,6.43479,10.105039999999999,8.520150000000001,10.105039999999999,8.520150000000001C10.105039999999999,8.520150000000001,10.105039999999999,8.520150000000001,10.105039999999999,8.520150000000001Z" fill-opacity="1"/></g></g></g></svg>`,
  doc: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><g><g style="opacity:0;"><rect class="svg-path" x="0" y="0" width="16" height="16" rx="2" fill="#FFFFFF" fill-opacity="1"/></g><g><path class="svg-path" d="M12.7689,4.83826C12.7689,4.83826,10.137319999999999,2.229562,10.137319999999999,2.229562C9.98891,2.0829537,9.78839,2,9.57894,2C9.57894,2,4.31579,2,4.31579,2C3.590515,2,3,2.585347,3,3.3043199999999997C3,3.3043199999999997,3,12.6957,3,12.6957C3,13.4151,3.590515,14,4.31579,14C4.31579,14,11.68421,14,11.68421,14C12.40995,14,13,13.4151,13,12.6957C13,12.6957,13,5.39129,13,5.39129C13,5.18414,12.91678,4.984859999999999,12.7689,4.83826C12.7689,4.83826,12.7689,4.83826,12.7689,4.83826ZM10.622620000000001,7.06608C10.622620000000001,7.06608,9.833079999999999,10.71825,9.833079999999999,10.71825C9.78568,10.9348,9.606290000000001,11.09862,9.384170000000001,11.12679C9.36206,11.1294,9.33995,11.13043,9.31783,11.13043C9.12048,11.13043,8.937339999999999,11.02089,8.84733,10.84244C8.84733,10.84244,8.002559999999999,9.16715,8.002559999999999,9.16715C8.002559999999999,9.16715,7.15733,10.84244,7.15733,10.84244C7.05736,11.04069,6.8426100000000005,11.15126,6.62049,11.12679C6.398429999999999,11.09862,6.21892,10.9348,6.171530000000001,10.71825C6.171530000000001,10.71825,5.382099999999999,7.06608,5.382099999999999,7.06608C5.32152,6.78432,5.50263,6.50679,5.78684,6.44678C6.07052,6.38415,6.35103,6.56577,6.41208,6.84748C6.41208,6.84748,6.886839999999999,9.045010000000001,6.886839999999999,9.045010000000001C6.886839999999999,9.045010000000001,7.53154,7.76675,7.53154,7.76675C7.70995,7.41305,8.29471,7.41305,8.47312,7.76675C8.47312,7.76675,9.11788,9.045010000000001,9.11788,9.045010000000001C9.11788,9.045010000000001,9.592590000000001,6.84748,9.592590000000001,6.84748C9.65363,6.56577,9.93207,6.38415,10.21789,6.44678C10.502089999999999,6.50679,10.683150000000001,6.78432,10.622620000000001,7.06608C10.622620000000001,7.06608,10.622620000000001,7.06608,10.622620000000001,7.06608Z" fill="#FFFFFF" fill-opacity="1"/></g></g></svg>`,
  txt: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><g><g style="opacity:0.10000000149011612;"></g><g><path class="svg-path" d="M3,3.09091Q3,2,4.11111,2L9.66667,2C9.81401,2,9.95532,2.0574675,10.0595,2.15976L12.83728,4.88703C12.94147,4.98933,13,5.12806,13,5.27273L13,12.9091Q13,14,11.88889,14L4.11111,14Q3,14,3,12.9091L3,3.09091ZM6.333880000000001,5.82045L9.66721,5.82045C9.974029999999999,5.82045,10.22277,6.06465,10.22277,6.3659C10.22277,6.66715,9.974029999999999,6.91136,9.66721,6.91136L8.55827,6.91136L8.55827,10.72727C8.55827,11.02852,8.30954,11.27273,8.00271,11.27273C7.69589,11.27273,7.44716,11.02852,7.44716,10.72727L7.44716,6.91136L6.333880000000001,6.91136C6.02705,6.91136,5.77832,6.66715,5.77832,6.3659C5.77832,6.06465,6.02705,5.82045,6.333880000000001,5.82045Z" fill-rule="evenodd" fill="#0B62CB" fill-opacity="1"/></g></g></svg>`,
  csv: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><g><g style="opacity:0.10000000149011612;"></g><g><path class="svg-path" d="M3,3.09091Q3,2,4.11111,2L9.66667,2C9.81401,2,9.95532,2.0574675,10.0595,2.15976L12.83728,4.88703C12.94147,4.98933,13,5.12806,13,5.27273L13,12.9091Q13,14,11.88889,14L4.11111,14Q3,14,3,12.9091L3,3.09091ZM8.28596,6.90909Q8.86532,6.90909,9.274650000000001,7.3093C9.37877,7.4111,9.51973,7.46825,9.66667,7.46825C9.97349,7.46825,10.22222,7.22404,10.22222,6.9228C10.22222,6.77774,10.16337,6.63865,10.058679999999999,6.53629Q9.32419,5.81818,8.28596,5.81818Q7.24772,5.81818,6.51323,6.53629Q5.77778,7.25535,5.77778,8.27273Q5.77778,9.290099999999999,6.51323,10.00916Q7.24772,10.72727,8.28596,10.72727Q9.32404,10.72727,10.058679999999999,10.00916C10.16337,9.9068,10.22222,9.76772,10.22222,9.62266C10.22222,9.32141,9.97349,9.077200000000001,9.66667,9.077200000000001C9.51973,9.077200000000001,9.37877,9.134360000000001,9.274650000000001,9.23616Q8.86532,9.63636,8.28596,9.63636Q7.70659,9.63636,7.29726,9.23616Q6.88889,8.83689,6.88889,8.27273Q6.88889,7.70856,7.29726,7.3093Q7.70659,6.90909,8.28596,6.90909Z" fill-rule="evenodd" fill="#0B62CB" fill-opacity="1"/></g></g></svg>`,
  excel: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><g><g style="opacity:0.10000000149011612;"></g><g><path class="svg-path" d="M3,3.09091Q3,2,4.11111,2L9.66667,2C9.81401,2,9.95532,2.0574675,10.0595,2.15976L12.83728,4.88703C12.94147,4.98933,13,5.12806,13,5.27273L13,12.9091Q13,14,11.88889,14L4.11111,14Q3,14,3,12.9091L3,3.09091ZM9.38889,11.27273L6.61111,11.27273C6.30429,11.27273,6.05556,11.02852,6.05556,10.72727L6.05556,6.36364C6.05556,6.06239,6.30429,5.81818,6.61111,5.81818L9.38889,5.81818C9.69571,5.81818,9.94444,6.06239,9.94444,6.36364C9.94444,6.66488,9.69571,6.90909,9.38889,6.90909L7.16667,6.90909L7.16667,8L9.38889,8C9.69571,8,9.94444,8.244209999999999,9.94444,8.545449999999999C9.94444,8.8467,9.69571,9.090910000000001,9.38889,9.090910000000001L7.16667,9.090910000000001L7.16667,10.18182L9.38889,10.18182C9.69571,10.18182,9.94444,10.42603,9.94444,10.72727C9.94444,11.02852,9.69571,11.27273,9.38889,11.27273Z" fill-rule="evenodd" fill="#0B62CB" fill-opacity="1"/></g></g></svg>`,
};

export const catalogueSchemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'Id',
    show: false,
  },
  {
    field: 'name',
    label: '部门名称',
    component: 'Input',
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    field: 'parentId',
    label: '上级部门',
    component: 'TreeSelect',
    required: true,
    componentProps: {
      fieldNames: {
        label: 'name',
        key: 'id',
        value: 'id',
      },
      getPopupContainer: () => document.body,
    },
    colProps: {
      span: 12,
    },
  },
  {
    label: '部门编码',
    field: 'code',
    component: 'Input',
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    field: 'sort',
    label: '排序',
    component: 'InputNumber',
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    label: '部门等级',
    field: 'catalogueLevelId',
    component: 'ApiSelect',
    colProps: {
      span: 12,
    },
    componentProps: {
      api: getDictTypeListApi,
      params: {
        type: DICT.DEPT_LEVEL,
      },
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'catalogueTypeId',
    component: 'ApiSelect',
    label: '部门类型',
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      api: getDictTypeListApi,
      params: {
        type: DICT.DEPT_TYPE,
      },
      getPopupContainer: () => document.body,
    },
  },
  {
    label: '部门主管',
    field: 'manager',
    component: 'ApiSelect',
    colProps: {
      span: 12,
    },
    componentProps: {
      api: getUserListByPage,
      resultField: 'records',
      labelField: 'username',
      valueField: 'userId',
      getPopupContainer: () => document.body,
    },
  },
];

export const remindSchemas: FormSchema[] = [
  {
    field: 'person',
    component: 'Input',
    label: '人员',
    slot: 'personSlot',
    rules: [
      {
        required: true,
        validator: (_rule, arr) => {
          if (!arr || arr.length === 0) {
            return Promise.reject('请选择人员');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
  },
  // {
  //   field: 'time',
  //   component: 'DatePicker',
  //   label: '提醒时间',
  //   componentProps: {
  //     // options: [],
  //   },
  // },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '推送备注',
    required: true,
    componentProps: {
      rows: 4,
    },
  },
];
