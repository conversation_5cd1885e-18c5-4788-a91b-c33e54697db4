import { ref } from 'vue';
import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Form/index';
import { getDictTypeListApi } from '/@/api/admin/dict';
import { DICT } from '../enums';
import { searchEquipmentTypeOptionsApi, searchFactoryListApi } from '/@zoology-wzb/api/device';

export const isViewPage = ref(false); //是否查看详情页
export const functionalCategoryOptiton = ref(); //功能类别下拉选项
export const salesStatusOptiton = ref(); //销售状态下拉选项
export const factoryOptiton = ref(); //使用单位下拉选项
/* 初始化下拉选项数据 */
export const initOptions = () => {
  searchFactoryListApi().then((res) => {
    factoryOptiton.value = res;
  });
  getDictTypeListApi({ type: 'sales_status' }).then((res) => {
    salesStatusOptiton.value = res;
  });
  getDictTypeListApi({ type: DICT.FUNCTIONAL_CATEGORY }).then((res) => {
    functionalCategoryOptiton.value = res;
  });
};

export const columns: BasicColumn[] = [
  {
    title: '设备名称',
    dataIndex: 'equipmentName',
    width: 230,
  },
  {
    title: '设备类型',
    dataIndex: 'equipmentTypeName',
  },
  {
    title: '设备型号',
    dataIndex: 'equipmentModelName',
  },
  {
    title: '功能类别',
    dataIndex: 'functionalCategoryVal',
  },
  {
    title: '使用单位',
    dataIndex: 'usingOrganizationName',
  },
  {
    title: '销售状态',
    dataIndex: 'salesStatusVal',
  },
  {
    title: '销售日期',
    dataIndex: 'salesDateStr',
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'equipmentExternalId',
    label: '设备标识',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'equipmentTypeId',
    label: '设备类型',
    component: 'ApiSelect',
    componentProps: {
      api: searchEquipmentTypeOptionsApi,
      labelField: 'equipmentTypeName',
      valueField: 'id',
      showSearch: true,
      params: { equipmentTypeName: '' },
      filterOption: (input, option) => {
        return option.label.indexOf(input) >= 0;
      },
    },
    colProps: { span: 6 },
  },
  {
    field: 'functionalCategoryKey',
    label: '功能类别',
    component: 'ApiSelect',
    componentProps: {
      options: functionalCategoryOptiton,
    },
    colProps: { span: 6 },
  },
  {
    field: 'salesStatusKey',
    label: '销售状态',
    component: 'ApiSelect',
    componentProps: {
      options: salesStatusOptiton,
    },
    colProps: { span: 6 },
  },
];
