<template>
  <PageWrapper dense contentFullHeight class="flex page">
    <div class="overview-box w-full h-full" :style="useWrapperStyle()">
      <div class="content">
        <Row :gutter="12" style="height: calc(50% - 6px)">
          <Col :span="16" style="height: 100%">
            <div class="content-item" :data-resource-code="indexCodeDataList[0].groupCode">
              <RingRatio />
            </div>
          </Col>
          <Col :span="8" style="height: 100%">
            <div class="content-item" :data-resource-code="indexCodeDataList[1].groupCode">
              <summarySimpleCard
                v-if="summarySimpleCard"
                :title="'药耗总览'"
                :bottomList="summaryData"
              >
                <template #headerText>
                  <div style="display: flex">
                    <span class="text">药耗总览</span>
                    <TRRightHeader @setsummarydata="setsummarydata" />
                  </div>
                </template>
                <template #summaryIcon>
                  <Icon icon="icon-park-outline:experiment" :size="13" color="var(--theme-color)" />
                </template>
                <template #summaryIconTwo>
                  <Icon icon="icon-park-outline:cycle-one" :size="13" color="var(--theme-color)" />
                </template>
              </summarySimpleCard>
            </div>
          </Col>
        </Row>
        <Row :gutter="12" style="height: calc(50% - 6px)">
          <Col :span="16">
            <div class="content-item">
              <SynthesizeConsumption />
            </div>
          </Col>
          <Col :span="8" style="height: 100%">
            <div class="content-item">
              <RankList
                :dayResourceInterfaceId="indexCodeDataList[4].resourceInterfaceId"
                :dayGroupCode="indexCodeDataList[4].groupCode"
                :monthResourceInterfaceId="indexCodeDataList[5].resourceInterfaceId"
                :monthGroupCode="indexCodeDataList[5].groupCode"
            /></div>
          </Col>
        </Row>
      </div>
    </div>
  </PageWrapper>
</template>
<script lang="ts">
  export default { name: 'CostManagementDrugManagementOverview' };
</script>
<script setup lang="ts" name="CostManagementDrugManagementOverview">
  import { onMounted, ref, shallowRef } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { useWrapperStyle } from '/@zhcz/hooks/useWrapperStyle';
  import { Icon } from '/@/components/Icon';
  import { Row, Col } from 'ant-design-vue';
  import SynthesizeConsumption from './components/SynthesizeConsumption.vue';
  import RingRatio from './components/RingRatio.vue';
  import RankList from './components/RankList.vue';
  import {
    components,
    loadComponents,
  } from '/@zhcz/components/HLCardComponent/src/combinationCards';

  import { rightHeaderComponents, loadHeaderComponents } from './components/rightHeader';
  import type { summaryListType } from '../../../../components/HLCardComponent/src/basicComponents/dataType';

  const indexCodeDataList = ref<{ resourceInterfaceId: string; groupCode: string }[]>([]);

  const initIndexCOdeDataList = () => {
    const initData = Array.from({ length: 6 }, () => {
      return {
        resourceInterfaceId: '',
        groupCode: '',
      };
    });
    indexCodeDataList.value = initData;
    console.log('indexCodeDataList', indexCodeDataList.value);
  };
  initIndexCOdeDataList();

  const setGroupCode = async () => {
    // 药耗总览
    indexCodeDataList.value[0].resourceInterfaceId = '3';
    indexCodeDataList.value[0].groupCode = 'yhzl_yhzl';
    // 药耗占比
    indexCodeDataList.value[1].resourceInterfaceId = '3';
    indexCodeDataList.value[1].groupCode = 'yhzl_yhzb';
    // 药耗月环比
    indexCodeDataList.value[2].resourceInterfaceId = '3';
    indexCodeDataList.value[2].groupCode = 'yhhyb_gt';
    indexCodeDataList.value[3].resourceInterfaceId = '3';
    indexCodeDataList.value[3].groupCode = 'yhhyb_yt';
    // 用药排行
    indexCodeDataList.value[4].resourceInterfaceId = '3';
    indexCodeDataList.value[4].groupCode = 'yyph_r';
    indexCodeDataList.value[5].resourceInterfaceId = '3';
    indexCodeDataList.value[5].groupCode = 'yyph_y';
    // 用药占比
  };
  const summarySimpleCard = shallowRef(null); // 上右一
  const lineSimpleCard = shallowRef(null); //  上左一
  const summaryData = ref<summaryListType[]>([]);
  const TRRightHeader = shallowRef(null); //   上右一
  const TLRightHeader = shallowRef(null); //上左一
  function setsummarydata(val) {
    summaryData.value = val.dataList;
  }
  onMounted(async () => {
    setGroupCode();
    // 确保组件加载完成
    await loadComponents();
    // 获取需要的组件
    summarySimpleCard.value = components.summarySimpleCard;
    // 上右一
    lineSimpleCard.value = components.linePlusCard;
    await loadHeaderComponents();
    // 获取需要的组件 上右一头部右侧组件
    TRRightHeader.value = rightHeaderComponents.TRRightHeader;
    // 获取需要的组件 上右一头部右侧组件
    TLRightHeader.value = rightHeaderComponents.TLRightHeader;
  });
</script>

<style lang="less" scoped>
  .overview-box {
    display: flex;
    flex-direction: column;
    // gap: 8px;
    // height: calc(100vh - 112px);
    overflow: hidden;

    .text {
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      line-height: 48px;
    }

    .content {
      // margin-top: 8px;
      display: flex;
      flex-direction: column;
      gap: 12px;
      flex: 1;
      overflow: hidden;
      height: 100%;

      .content-item {
        height: 100%;

        :deep(.ant-card) {
          box-shadow: none;
        }
      }
    }
  }
</style>
