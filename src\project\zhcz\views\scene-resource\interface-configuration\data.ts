import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getDictTypeListApi } from '/@/api/admin/dict';
import { RESOURCE_TYPE, DICT } from '/@zhcz/enums/sceneResource';
import { getResourceIndexPage } from '/@zhcz/api/config-center/scenes-group';

export const columns: BasicColumn[] = [
  {
    title: '接口名称',
    dataIndex: 'interfaceName',
  },
  {
    title: '来源名称',
    dataIndex: 'resourceIndexName',
  },
  {
    title: '来源类型',
    dataIndex: 'resourceTypeName',
  },
  {
    title: '接口类型',
    dataIndex: 'indexSourceName',
  },
  {
    title: '请求类型',
    dataIndex: 'requestMethod',
  },
  {
    title: 'URL',
    dataIndex: 'interfaceUrl',
  },
  {
    title: '数据库',
    dataIndex: 'databaseName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'interfaceName',
    component: 'Input',
    label: '接口名称',
    labelWidth: 68,
    colProps: { span: 6 },
  },
  {
    field: 'resourceType',
    label: '来源类型',
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.RESOURCE_TYPE },
      valueField: 'intValue',
    },
    colProps: { span: 6 },
  },
];

export const schemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'Id',
    show: false,
  },
  {
    field: 'resourceIndexId',
    component: 'ApiSelect',
    label: '接口来源',
    required: true,
    colProps: { span: 12 },
    componentProps: ({ formModel }) => {
      return {
        api: getResourceIndexPage,
        params: {
          current: 1,
          size: 1000,
        },
        resultField: 'records',
        labelField: 'indexName',
        valueField: 'id',
        onChange: async (_value, option) => {
          formModel.resourceType = option.resourceType;
          formModel.databaseType = option.databaseType;
          formModel.databaseIp = option.databaseIp;
          formModel.databasePort = option.databasePort;
        },
      };
    },
  },
  {
    field: 'interfaceName',
    component: 'Input',
    label: '接口名称',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'interfaceUrl',
    component: 'Input',
    label: 'URL',
    required: true,
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType !== RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'databaseName',
    component: 'Input',
    label: '数据库',
    required: true,
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType === RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'requestMethod',
    component: 'Select',
    label: '请求类型',
    required: true,
    componentProps: {
      options: [
        { label: 'GET', value: 'GET' },
        { label: 'POST', value: 'POST' },
        { label: 'DELETE', value: 'DELETE' },
        { label: 'PUT', value: 'PUT' },
      ],
    },
    defaultValue: 'GET',
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType !== RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'resourceType',
    label: '来源类型',
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.RESOURCE_TYPE },
      valueField: 'intValue',
      disabled: true,
    },
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.platformId;
    },
  },
  {
    field: 'databaseType',
    label: '数据库类型',
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.DATABASE_TYPE },
      valueField: 'intValue',
      disabled: true,
    },
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType === RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'databaseIp',
    component: 'Input',
    label: '主机地址',
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
    },
    ifShow: ({ values }) => {
      return values.resourceType === RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'databasePort',
    component: 'Input',
    label: '端口',
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
    },
    ifShow: ({ values }) => {
      return values.resourceType === RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'isConvert',
    component: 'Switch',
    label: 'js转化',
    slot: 'isConvert-slot',
    defaultValue: false,
  },
  {
    field: 'defaultBody',
    component: 'Input',
    label: '接口参数',
    slot: 'body-slot',
    required: true,
    defaultValue: `{
      "SearchText":"",
      "sourcelds":"@￥Resource"
}`,
    colProps: { span: 24 },
    ifShow: ({ values }) => {
      return values.resourceType !== RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'resultJsConvert',
    component: 'Input',
    label: '结果转换脚本',
    slot: 'js-convert-slot',
    required: true,
    colProps: { span: 24 },
    defaultValue: `function jsConvert(data) {
      var obj = JSON.parse(data)
      var arr = [];
      for(var i = 0 ; i < obj.length; i++) { 
        var temp = new Object();
        temp.itemKey = obj[i].key;
        temp.itemValue = obj[i].value;
        temp.Unit = 'mg/L';
        arr.push(temp);
      }
      
      var res = JSON.stringify(arr);
      
      return res;
    }`,
    ifShow: ({ values }) => {
      return values.resourceType !== RESOURCE_TYPE.DATABASE;
    },
  },
  // {
  //   field: 'interfaceScript',
  //   component: 'Input',
  //   label: '接口脚本',
  //   defaultValue: '',
  //   show: false,
  // },
  // {
  //   field: 'defaultHeader',
  //   component: 'Input',
  //   label: '默认请求头',
  //   defaultValue: '',
  //   show: false,
  // },
  {
    field: 'databaseSql',
    component: 'InputTextArea',
    label: '查询语句',
    slot: 'sql-query-slot',
    colProps: { span: 24 },
    componentProps: {
      rows: 4,
    },
    ifShow: ({ values }) => {
      return values.resourceType === RESOURCE_TYPE.DATABASE;
    },
  },
];
