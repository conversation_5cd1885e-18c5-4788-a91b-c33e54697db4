<template>
  <div class="sludge-device-device-monitor-detail-page h-full relative">
    <div class="page-wrapper">
      <div class="flex" style="overflow: hidden; height: 65%">
        <!-- 头部详情数据和大图 -->
        <TitleAndMainImg :detailInfo="detailInfo" />
        <div style="display: flex; flex-direction: column; overflow: hidden">
          <!-- 模式状态 -->
          <ModeStatusCard :list="msztList" :configGroupInfo="configGroupInfo" />
          <!-- 频率检测 -->
          <FrequencyMonitor
            @onDetail="showEchart"
            :list="plvjList"
            :configGroupInfo="configGroupInfo"
            style="margin-top: 12px"
          />
        </div>
      </div>
      <div class="flex pt-3" style="height: 35%">
        <!-- 数据检测 -->
        <DataMonitor @onDetail="showEchart" :list="sjjcList" :configGroupInfo="configGroupInfo" />
        <!-- 运行状态 -->
        <RunningStatus :list="yxztList" :configGroupInfo="configGroupInfo" class="ml-3 flex-1" />
      </div>
    </div>
    <div class="fix-bottom">
      <div class="back-btn">
        <a-button @click="back">返回</a-button>
      </div>
    </div>
    <!-- 指标折线图详情 -->
    <SensorIndicators :interval="60" @register="registerSensorIndicatorsModal" />
  </div>
</template>

<script lang="ts" setup>
  import TitleAndMainImg from './components/TitleAndMainImg.vue';
  import SensorIndicators from '/@zhcz/views/device-management/components/sensor-indicators/index.vue';
  import ModeStatusCard from './components/ModeStatusCard.vue';
  import FrequencyMonitor from './components/FrequencyMonitor.vue';
  import DataMonitor from './components/DataMonitor.vue';
  import RunningStatus from './components/RunningStatus.vue';
  import { ref } from 'vue';
  // import warning from '../../../assets/images/caution.png';
  import {
    searchEquipmentMonitorDetailApi,
    searchEquipmentConfigGroupInfoApi,
  } from '/@zoology-wzb/api/device';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { useModal } from '/@/components/Modal';
  import dayjs from 'dayjs';

  const emits = defineEmits(['back']);
  const props = defineProps({
    equipmentId: {
      type: Number,
      default: () => null,
    },
  });

  // 指标弹窗
  const [registerSensorIndicatorsModal, { openModal: openEchartModal }] = useModal();
  const showEchart = (record) => {
    openEchartModal(true, {
      indexCode: record.indexCode,
      indexName: record.indexName,
    });
  };
  /* 获取对应指标的样式配置信息 */
  const matchingConfig = (list, configGroupInfo) => {
    list?.forEach?.((item) => {
      const config = configGroupInfo.find((v) => v.associatedMetric == item.indexCode);
      item.rawName = item.indexName;
      if (config) {
        if (config.dataTypeKey == '1') {
          config?.styleConfigList?.forEach((styleInfo) => {
            if (styleInfo.detailValue == item.value) {
              item.indexName = styleInfo.detailLabel;
              if (styleInfo.detailLabel) item.tagName = styleInfo.detailLabel;
              try {
                item.style = JSON.parse(styleInfo.detailStyle);
              } catch (error) {}
            }
          });
        } else if (config.dataTypeKey == '2') {
          try {
            item.alertStyle = JSON.parse(config.alertStyle);
          } catch (error) {}
          try {
            item.normalStyle = JSON.parse(config.normalStyle);
          } catch (error) {}
        }
      }
      const config2 = configGroupInfo.find((v) => v.associatedMetric == item.indexCode2);
      if (config2) {
        try {
          item.alertStyle2 = JSON.parse(config2.alertStyle);
        } catch (error) {}
        try {
          item.normalStyle2 = JSON.parse(config2.normalStyle);
        } catch (error) {}
      }
    });
  };
  /* 指标接口请求参数 */
  const getIndicatorParams = (type, indexCodes = '@￥Resource', resourceInterfaceId = '12') => {
    const params = {
      groupCode: `${detailInfo.value.equipmentCode}_${type}`,
      resourceInterfaceId,
      paramsData: `{"startDateTime": "${halfHourAgo}","endDateTime": null,"indexCodes": "${indexCodes}", "tenantId": "@￥TenantId"\n  }`,
      jsConvert: true,
      renterId: '2024',
    };
    return params;
  };
  /* 获取模式状态数据 */
  const msztList = ref([]);
  const getMSZT = async () => {
    const res = await callResourceFunction(getIndicatorParams('MSZT'));
    msztList.value = res.data;
    matchingConfig(msztList.value, configGroupInfo.value);
  };
  /* 获取频率监测数据 */
  const plvjList = ref([]);
  const getPLJC = async () => {
    const res = await callResourceFunction(getIndicatorParams('PLJC', '', '1960583797602299905'));
    plvjList.value = res.data;
    matchingConfig(plvjList.value, configGroupInfo.value);
  };
  /* 获取数据监测数据 */
  const sjjcList = ref([]);
  const getSJJC = async () => {
    const res = await callResourceFunction(getIndicatorParams('SJJC'));
    sjjcList.value = res.data;
    matchingConfig(sjjcList.value, configGroupInfo.value);
  };
  /* 获取运行状态数据 */
  const yxztList = ref([]);
  const getYXZT = async () => {
    const res = await callResourceFunction(getIndicatorParams('YXZT'));
    matchingConfig(res.data, configGroupInfo.value);
    yxztList.value = res.data?.map?.((v) => {
      return {
        label: v.rawName,
        tag: {
          txt: v.tagName || '未运行',
          style: v.style || { color: '#333', backgroundColor: '#eeeff1' },
        },
      };
    });
    // {
    //   label: '电池电量',
    //   afterLabelImage: warning,
    //   tag: {
    //     type: 'info',
    //     txt: '未运行',
    //   },
    // },
  };
  /* 获取设备配置信息 */
  const configGroupInfo = ref();
  const getConfigGroupInfo = async () => {
    configGroupInfo.value = await searchEquipmentConfigGroupInfoApi(
      detailInfo.value.equipmentTypeId,
    );
  };
  /* 页面初始化 */
  const detailInfo = ref({}); //详情信息
  let halfHourAgo = ''; // 当前时间前半小时
  const init = async () => {
    detailInfo.value = await searchEquipmentMonitorDetailApi(props.equipmentId);
    halfHourAgo = dayjs().subtract(30, 'minute').format('YYYY-MM-DD HH:mm:ss');
    await getConfigGroupInfo();
    getMSZT();
    getPLJC();
    getSJJC();
    getYXZT();
  };
  init();
  // 返回
  const back = () => {
    emits('back');
  };
</script>
<style lang="less" scoped>
  .sludge-device-device-monitor-detail-page {
    .page-wrapper {
      display: flex;
      flex-direction: column;
      height: calc(100% - 44px);
    }

    .fix-bottom {
      position: absolute;
      bottom: -16px;
      left: 0;
      width: 100%;
      height: 60px;
      background: #ffffff;
      box-shadow: 0px -4px 4px 0px rgba(0, 0, 0, 0.08);
      border-radius: 0px 0px 4px 4px;
      display: flex;
      align-items: center;

      .back-btn {
        padding-left: 16px;

        :deep(.ant-btn) {
          width: 120px;
          height: 40px;
        }
      }
    }

    :deep(.card_in) {
      .base-header {
        padding: 0 12px;
        min-height: 32px;

        .text {
          font-size: 15px;
        }
      }

      .card-body {
        padding: 8px 12px;
      }
    }

    :deep(.card-table) {
      flex: 1;
      width: 100%;
      overflow-y: auto;
      // overflow-x: hidden;

      .ant-table-header {
        overflow: unset !important;
      }

      .ant-table-wrapper {
        padding: 0;
        margin: 0;
      }

      .ant-table-wrapper,
      .ant-spin-nested-loading,
      .ant-spin-container,
      .ant-table,
      .ant-table-container {
        height: 100% !important;
      }

      .ant-table-container {
        display: flex;
        flex-direction: column;
      }

      .ant-table-body {
        height: unset !important;
        max-height: unset !important;
        overflow-x: hidden !important;
      }
    }
  }
</style>
<style lang="less">
  .sludge-device-device-monitor-detail-page {
    .ant-table-cell {
      padding: 6px 8px !important;
    }
  }
</style>
