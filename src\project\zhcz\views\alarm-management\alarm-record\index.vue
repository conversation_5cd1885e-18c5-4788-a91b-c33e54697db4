<template>
  <div class="h-full">
    <BasicTable
      @register="registerTable"
      @selection-change="handleChangeSelection"
      @fetch-error="onFetchError"
    >
      <template #form-alarm-type="{ model, field }">
        <Select v-model:value="model[field]" placeholder="请选择">
          <SelectOption v-for="item in alarmTypeOptions" :key="item.id" :value="item.id">
            {{ item.name }}
          </SelectOption>
        </Select>
      </template>
      <template #form-start-time="{ model, field }">
        <div class="flex">
          <RadioGroup v-model:value="activeDateTag" class="flex-shrink-0 mr-4">
            <RadioButton
              v-for="item in dateTagData"
              :key="item.value"
              :value="item.value"
              @click="handleChangeDateType(item.value)"
              >{{ item.label }}
            </RadioButton>
          </RadioGroup>
          <RangePicker
            v-model:value="model[field]"
            @change="handleChangeRangeDate"
            :allowClear="true"
          />
        </div>
      </template>
      <template #headerTop>
        <div class="header-container">
          <div class="right-container">
            <div
              v-for="item in alarmLevelData"
              :key="item.value"
              class="item"
              :class="['item_' + item.value, getAlarmLevelClass(item), `${getDomain}`]"
              @click="handleChangeAlarmLevel(item.value)"
            >
              <div>
                <img :src="getAlarmLevelImage(item)" class="level-image" />
                <div>
                  <div class="tit">
                    {{ item.label }}
                  </div>
                  <div class="num-box">
                    <div class="num">{{ item.total }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="spacer"></div>
          <div class="left-container">
            <div
              v-for="(item, index) in orderTypeData"
              :key="item.value"
              :class="[
                'item',
                'item_' + (index + 1),
                {
                  ['item_active' + (index + 1)]: activeOrderType === item.value,
                  active: activeOrderType === item.value,
                },
                `${getDomain}`,
              ]"
              @click="handleChangeOrderType(item.value)"
            >
              <div class="h-full flex items-center p-4">
                <div :class="'icon icon_' + (index + 1)"> </div>
                <div class="tit-box">
                  <!-- <SvgIcon :name="'alarm-record-type' + index" color="var(--theme-color)" size="20" /> -->

                  <div class="tit">{{ item.label }}</div>
                  <div class="num">{{ item.total }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #tableTitle>
        <div class="table-tool-container">
          <a-button
            :icon="h(Icon, { icon: 'icon-park-outline:reduce-one' })"
            :loading="okLoading"
            :disabled="isDisabledBatchRelieve"
            danger
            ghost
            type="primary"
            @click="handleBatchRelieve"
          >
            批量解除
          </a-button>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'warnEventContent'">
          <span class="oper-btn" @click="previewDetail(record.id)">{{
            record.warnEventContent || '-'
          }}</span>
        </template>
        <template v-if="column.key === 'action'">
          <div class="flex gap-4">
            <!-- <span class="oper-btn" @click="previewDetail(record.id)">详情</span> -->
            <span class="oper-btn" @click="showInfo(record.warnEventConfigId)">规则</span>
            <span
              class="oper-btn"
              :style="{ opacity: record.eventStatus !== EventStatus.Untreated ? 0.5 : 1 }"
              @click="handleSolve(record)"
              >解除
            </span>
          </div>
        </template>
      </template>
    </BasicTable>
    <DetailDrawer @close="handleClose" @register="registerDetailDrawer" />
    <RuleInfoModal @register="registerRuleModal" />
    <EditModal @success="reload" @register="registerEditModal" />
  </div>
</template>

<script lang="ts" setup name="CollectionConfiguration">
  import { ref, h, onMounted, watch, onActivated, onDeactivated } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, useTable } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import DetailDrawer from './components/DetailDrawer.vue';
  import EditModal from './components/EditModal.vue';
  import { columns, searchFormSchema } from './data';
  import {
    getListByPage,
    getWarnEventLevel,
    getDetail,
    getWarnEventFlowApi,
    getWarnEventTypeList,
    batchConfirmEvent,
    getEventStatusStatistics,
    getEventConfigByIdApi,
  } from '/@zhcz/api/event-center';
  import { Select, SelectOption, RangePicker, RadioGroup, RadioButton } from 'ant-design-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { EventStatus } from './enums';
  import { IOrderType, IAlarmLevel } from './typing';
  import dayjs from 'dayjs';
  import { useIntervalFn } from '@vueuse/core';
  import RuleInfoModal from './components/RuleInfoModal.vue';
  import { useRouter } from 'vue-router';
  import { useDrawer } from '/@/components/Drawer';
  import { useDomain } from '/@/locales/useDomain';

  defineOptions({
    name: 'AlarmManagementAlarmRecord',
  });
  const { currentRoute } = useRouter();
  const { getDomain } = useDomain();

  const orderTypeData = ref<IOrderType[]>([]);
  const alarmLevelData = ref<IAlarmLevel[]>([]);
  const activeOrderType = ref(null);
  const activeAlarmLevel = ref(null);
  const alarmTypeOptions: any = ref([]);
  const isDisabledBatchRelieve = ref(true);
  const okLoading = ref(false);
  const isRefresh = ref(false);

  const dateTagData = ref([
    {
      label: '前日',
      value: -2,
    },
    {
      label: '昨日',
      value: -1,
    },
    {
      label: '今日',
      value: 0,
    },
  ]);
  const activeDateTag = ref<number | null>(0);
  // 是否只用更新部分数据
  const isUpdateTableData = ref(false);
  const selectRows = ref([]);
  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
  const [registerEditModal, { openModal: openEditModal }] = useModal();

  const [registerTable, { reload, setLoading, getSelectRows, getForm, setSelectedRowKeys }] =
    useTable({
      columns,
      rowSelection: {
        type: 'checkbox',
      },
      clickToRowSelect: false,
      api: getDataPageApi,
      fetchSetting: {
        pageField: 'current',
        sizeField: 'size',
      },
      showIndexColumn: false,
      showTableSetting: true,
      formConfig: {
        labelWidth: 68,
        schemas: searchFormSchema,
        showActionButtonGroup: false,
        // resetFunc: async () => {
        //   activeAlarmLevel.value = null;
        //   activeOrderType.value = null;
        //   activeDateTag.value = 0;
        //   selectRows.value = [];
        // },
      },
      useSearchForm: true,
      handleSearchInfoFn: (pageParams) => {
        return {
          ...pageParams,
          startDate: pageParams.dateValue
            ? dayjs(pageParams.dateValue[0]).format('YYYY-MM-DD')
            : null,
          endDate: pageParams.dateValue
            ? dayjs(pageParams.dateValue[1]).format('YYYY-MM-DD')
            : null,
          eventStatus: activeOrderType.value,
          eventLevel: activeAlarmLevel.value,
        };
      },
      afterFetch: async () => {
        if (isUpdateTableData.value) {
          isUpdateTableData.value = false;
          return;
        }
        await queryEventStatusStatistics();
        await queryWarnEventLevel();
        setSelectedRowKeys(selectRows.value);
      },
      actionColumn: {
        width: 102,
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
      },
      customLoading: true,
      immediate: true,
      rowKey: 'id',
    });
  // const [registerDetailDrawer, { openModal: openDetailDrawer }] = useModal();

  const [registerRuleModal, { openModal: openRuleModal }] = useModal();

  const { createMessage, createConfirm } = useMessage();

  const handleChangeRangeDate = () => {
    const formData = getForm().getFieldsValue();
    if (!formData.dateValue) {
      activeDateTag.value = null;
      return;
    }
    if (
      dayjs(formData.dateValue[0]).format('YYYY-MM-DD') !==
      dayjs(formData.dateValue[1]).format('YYYY-MM-DD')
    ) {
      activeDateTag.value = null;
      return;
    }
    const diff = dayjs(formData.dateValue[0]).diff(dayjs(), 'day');
    activeDateTag.value = diff;

    reload();
  };

  const handleChangeDateType = async (value) => {
    activeDateTag.value = value;
    await getForm().setFieldsValue({
      dateValue: [dayjs().add(value, 'day'), dayjs().add(value, 'day')],
    });
    reload();
  };

  async function getDataPageApi(...args) {
    setLoading(!isRefresh.value);

    // @ts-ignore
    const ret = await getListByPage(...args);
    setLoading(false);
    return ret;
  }

  const intervalFn = ref();

  intervalFn.value = useIntervalFn(async () => {
    isRefresh.value = true;
    await reload();
    isRefresh.value = false;
  }, 5 * 1000);

  watch(
    () => currentRoute.value.query,
    async (v: Indexable) => {
      if (v.detailParam) {
        const detailParam = JSON.parse(v.detailParam || '');

        setTimeout(async () => {
          await reload({ searchInfo: { id: detailParam.id } });

          await previewDetail(detailParam.id);
          intervalFn.value?.pause();
        }, 100);
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
  function handleClose() {
    intervalFn.value?.resume();
  }
  // 批量解除
  const handleBatchRelieve = () => {
    createConfirm({
      iconType: 'info',
      title: '提示',
      content: '是否确认批量解除报警？',
      onOk() {
        okLoading.value = true;
        const params = {
          ids: getSelectRows().map((item) => item.id),
        };
        batchConfirmEvent(params)
          .then(() => {
            reload();
            selectRows.value = [];
            createMessage.success('操作成功');
            isDisabledBatchRelieve.value = true;
            okLoading.value = false;
          })
          .catch(() => {
            selectRows.value = [];
            isDisabledBatchRelieve.value = true;
            okLoading.value = false;
          });
      },
    });
  };

  const handleChangeSelection = (data) => {
    isDisabledBatchRelieve.value =
      data.rows.some((item) => item.eventStatus !== EventStatus.Untreated) || !data.rows.length;
    selectRows.value = data.keys;
  };

  const handleChangeOrderType = (value) => {
    activeOrderType.value = activeOrderType.value === value ? null : value;
    isUpdateTableData.value = true;

    reload();
  };

  const handleChangeAlarmLevel = (value) => {
    if (activeAlarmLevel.value === value) {
      activeAlarmLevel.value = null;
    } else {
      activeAlarmLevel.value = value;
    }
    isUpdateTableData.value = true;
    reload();
  };

  const getAlarmLevelClass = (item) => {
    if (activeAlarmLevel.value !== item.value) {
      return '';
    }
    switch (item.value) {
      case 1:
        return 'red-active active';
      case 2:
        return 'orange-active active';
      case 3:
        return 'yellow-active active';
      case 4:
        return 'blue-active active';
      default:
        return 'active';
    }
  };

  const getAlarmLevelImage = (item) => {
    switch (item.value) {
      case 1:
        return new URL('../assets/images/red-alert.png', import.meta.url).toString();
      case 2:
        return new URL('../assets/images/orange-alert.png', import.meta.url).toString();
      case 3:
        return new URL('../assets/images/yellow-alert.png', import.meta.url).toString();
      case 4:
        return new URL('../assets/images/blue-alert.png', import.meta.url).toString();
      default:
        return '';
    }
  };

  const handleSolve = (item) => {
    if (item.eventStatus !== EventStatus.Untreated) {
      createMessage.warning('该报警已处理');
      return;
    }
    getDetail(item.id).then((data) => {
      openEditModal(true, {
        type: 'solve',
        record: data,
      });
    });
  };

  const previewDetail = async (id: string) => {
    const data = await getDetail(id);
    const ret = await getWarnEventFlowApi(id);
    data.eventFlowList = ret;
    openDetailDrawer(true, {
      type: 'detail',
      record: data,
    });
  };
  async function showInfo(warnEventConfigId: string) {
    try {
      const ret = await getEventConfigByIdApi(warnEventConfigId);

      openRuleModal(true, ret);
    } catch (error: any) {
      if (error!.msg) {
        createMessage.error(error!.msg);
      }
      console.error(error);
    }
  }

  // 报警状态 过滤
  const queryEventStatusStatistics = async (startDate: any = null, endDate: any = null) => {
    const formData = getForm().getFieldsValue();
    const params = {
      warnEventType: formData?.warnEventType || '',
      startDate: formData?.dateValue
        ? dayjs(formData.dateValue[0]).format('YYYY-MM-DD')
        : startDate,
      endDate: formData?.dateValue ? dayjs(formData.dateValue[1]).format('YYYY-MM-DD') : endDate,
      searchContent: formData?.searchContent || '',
    };
    const data = await getEventStatusStatistics(params);
    // data.items.unshift({
    //   value: null,
    //   label: '全部报警',
    //   total: data.total,
    // });
    if (orderTypeData.value.length === 0) {
      orderTypeData.value = data.items;
    } else {
      data.items.forEach((item) => {
        const index = orderTypeData.value.findIndex((item1) => item1.value === item.value);
        if (index !== -1) {
          orderTypeData.value[index].total = item.total;
        }
      });
    }
  };

  // 报警等级 过滤
  const queryWarnEventLevel = async (startDate: any = null, endDate: any = null) => {
    const formData = getForm().getFieldsValue();
    const params = {
      warnEventType: formData?.warnEventType || '',
      startDate: formData?.dateValue
        ? dayjs(formData.dateValue[0]).format('YYYY-MM-DD')
        : startDate,
      endDate: formData?.dateValue ? dayjs(formData.dateValue[1]).format('YYYY-MM-DD') : endDate,
      searchContent: formData?.searchContent || '',
    };
    const data = await getWarnEventLevel(params);
    data.forEach((item) => {
      if (item.value === 1) {
        item.activeImage = new URL('../assets/images/red-alert-active.png', import.meta.url);
      } else if (item.value === 2) {
        item.activeImage = new URL('../assets/images/orange-alert-active.png', import.meta.url);
      } else if (item.value === 3) {
        item.activeImage = new URL('../assets/images/yellow-alert-active.png', import.meta.url);
      } else if (item.value === 4) {
        item.activeImage = new URL('../assets/images/blue-alert-active.png', import.meta.url);
      }
    });
    if (alarmLevelData.value.length === 0) {
      alarmLevelData.value = data;
    } else {
      data.forEach((item) => {
        const index = alarmLevelData.value.findIndex((item1) => item1.value === item.value);
        if (index !== -1) {
          alarmLevelData.value[index].total = item.total;
        }
      });
    }
  };

  // 报警类型
  const queryWarnEventTypeList = async () => {
    const data = await getWarnEventTypeList();
    if (!data) {
      getForm().setFieldsValue({
        warnEventType: null,
      });
      return;
    }
    data.unshift({
      id: '',
      name: '全部',
    });
    alarmTypeOptions.value = data;
  };

  async function onFetchError(error) {
    console.log('fetch-error', error);
    setTimeout(async () => {
      await reload();
    }, 100);
  }

  onMounted(async () => {
    const startTime = dayjs().format('YYYY-MM-DD');
    const endTime = dayjs().format('YYYY-MM-DD');
    queryEventStatusStatistics(startTime, endTime);
    queryWarnEventLevel(startTime, endTime);
    await queryWarnEventTypeList();
  });

  onActivated(() => {
    // init();
    intervalFn.value?.resume();
  });

  onDeactivated(() => {
    clear();
  });

  function clear() {
    if (intervalFn.value) {
      intervalFn.value.pause();
    }
  }
</script>

<style scoped lang="less">
  :deep(.vben-basic-table) {
    .ant-table-wrapper {
      padding: 0 0 16px;
      background: #ffffff;

      .ant-table {
        background: #ffffff;

        .ant-table-title {
          padding: 0 !important;

          .header-container {
            display: grid;
            grid-template-columns: 1fr 1px 1fr;
            border-bottom: 1px solid @border-color-default;
            background: #ffffff;

            .left-container {
              padding: 16px;
              padding-left: 3px;
              display: flex;
              box-sizing: content-box;
              height: 72px;

              .icon {
                width: 44px;
                height: 44px;

                &_1 {
                  background: url(../assets/images/record.png) center/ 100% 100%;
                }

                &_2 {
                  background: url(../assets/images/record_1.png) center/ 100% 100%;
                }

                &_3 {
                  background: url(../assets/images/record_2.png) center/ 100% 100%;
                }
              }

              .item {
                width: 33.3%;
                padding: 2px;
                line-height: 1.3;
                border-radius: 4px;
                cursor: pointer;
                margin-left: 16px;
                border: 1px solid transparent;
                color: @text-color-bold;

                &:hover {
                  background: #f0f0f0;
                }

                &.active {
                  background: #f0f0f0;
                  border: 1px solid rgba(153, 153, 153, 0.56);
                }

                .tit-box {
                  margin-left: 12px;
                  text-align: left;
                }

                .tit {
                  margin-top: 6px;
                  margin-bottom: 3px;
                }

                .num {
                  font-size: 22px;
                  font-family: PingFang SC-Semibold, PingFang SC;
                  font-weight: 600;
                }
              }
            }

            .spacer {
              position: relative;

              &::before {
                content: '';
                display: block;
                width: 1px;
                height: 62px;
                background: #e9e9e9;
                position: absolute;
                left: 0;
                top: 24px;
              }
            }

            .right-container {
              display: flex;
              flex: 1;
              padding: 16px;
              padding-right: 3px;
              box-sizing: content-box;
              height: 72px;

              .item {
                width: 33.3%;
                padding: 2px;
                border-radius: 4px;
                cursor: pointer;
                margin-right: 16px;
                line-height: 1.3;
                border: 1px solid transparent;

                &:hover {
                  background: #f0f0f0;
                }

                &.active {
                  background: #f0f0f0;
                  border: 1px solid rgba(153, 153, 153, 0.56);
                }

                .unit {
                  color: @text-color-defined;
                }

                & > div {
                  display: flex;
                  align-items: center;
                  padding: 16px;
                  border-radius: 4px;
                  height: 100%;

                  img {
                    width: 44px;
                    height: 44px;
                    margin-right: 12px;
                  }

                  & > div {
                    overflow: hidden;
                    white-space: nowrap;

                    .tit {
                      text-overflow: ellipsis;
                      overflow: hidden;
                      margin-top: 6px;
                      margin-bottom: 3px;
                    }

                    .num-box {
                      display: flex;
                      align-items: baseline;

                      .num {
                        font-family: PingFang SC-Semibold, PingFang SC;
                        font-weight: 600;
                        font-size: 22px;

                        text-overflow: ellipsis;
                        overflow: hidden;
                      }
                    }
                  }
                }
              }

              // .item_1.aoa {
              //   background: rgba(255, 46, 46, 0.08);
              //   border: 1px solid transparent;
              // }

              // .item_1:hover,
              // .red-active {
              //   color: #ff2e2e;
              //   background: rgba(255, 46, 46, 0.08);
              //   border-radius: 4px 4px 4px 4px;
              //   border: 1px solid rgba(255, 46, 46, 0.56);

              //   .unit {
              //     color: #ff2e2e;
              //   }
              // }

              // .item_2.aoa {
              //   background: rgba(255, 140, 46, 0.08);
              //   border: 1px solid transparent;
              // }

              // .item_2:hover,
              // .orange-active {
              //   color: #fc7c22;
              //   background: rgba(255, 140, 46, 0.08);
              //   border: 1px solid rgba(255, 140, 46, 0.56);

              //   .unit {
              //     color: #fc7c22;
              //   }
              // }

              // .item_3.aoa {
              //   background: rgba(237, 199, 10, 0.08);
              //   border: 1px solid transparent;
              // }

              // .item_3:hover,
              // .yellow-active {
              //   color: #edc70a;
              //   background: rgba(237, 199, 10, 0.08);
              //   border: 1px solid rgba(237, 199, 10, 0.56);

              //   .unit {
              //     color: #edc70a;
              //   }
              // }

              // .blue-active {
              //   color: #3860ff;
              //   background: linear-gradient(
              //     180deg,
              //     rgba(56, 96, 255, 0.5),
              //     rgba(56, 96, 255, 0.2)
              //   ) !important;

              //   & > div {
              //     background: linear-gradient(180deg, #ebeffc 3%, #d6dfff 99%);
              //   }
              // }
            }
          }

          & > div > div:first-child {
            margin: 0 !important;
          }

          .table-tool-container {
            padding: 16px 16px 0 16px;
          }
        }

        .ant-table-container {
          height: calc(100% - 40px - 114px);
          padding: 12px 16px 0;

          .oper-btn {
            color: @theme-color;
            cursor: pointer;
          }
        }

        .ant-table-body .ant-table-cell-fix-right {
          background: #ffffff;
        }
      }

      .ant-pagination {
        padding: 0 16px;
      }
    }
  }
</style>
