<template>
  <Card
    class="flex flex-col flex-1 overflow-hidden tab-table-wrapper"
    :tab-list="tabListNoTitle"
    :active-tab-key="noTitleKey"
    @tab-change="(key) => onTabChange(key)"
  >
    <VxeBasicTable
      v-show="noTitleKey === '数据列表'"
      class="vxe-table-container"
      :style="{
        height: '100%',
      }"
      :columns="[
        {
          title: '时间',
          field: 'collectTime',
          width: 350,
          formatter: emptyFormatter,
        },
        {
          title: '指标名称',
          field: 'indexName',
          formatter: emptyFormatter,
        },
        {
          title: '指标数值',
          field: 'val',
          formatter: emptyFormatter,
        },
        {
          title: '指标单位',
          field: 'unit',
          formatter: emptyFormatter,
        },
      ]"
      :data="indicatorData"
      :border="'none'"
      :height="'100%'"
      showOverflow
      :stripe="false"
      :row-config="{
        keyField: 'id',
        isCurrent: false,
      }"
      :scrollY="{
        enabled: true,
        gt: 0,
      }"
      :proxy-config="{
        enabled: false,
      }"
      :column-config="{
        resizable: false,
        isHover: false,
        isCurrent: false,
      }"
      :toolbar-config="null"
    >
      <template #empty>
        <Empty />
      </template>
    </VxeBasicTable>
    <VxeBasicTable
      v-show="noTitleKey === '异常事件'"
      class="vxe-table-container"
      :style="{
        height: '100%',
      }"
      v-loading="loading"
      :columns="[
        {
          title: '报警类型',
          field: 'warnEventTypeTitle',
          formatter: emptyFormatter,
        },
        {
          title: '报警内容',
          field: 'eventContent',
          formatter: emptyFormatter,
        },
        {
          title: '限制值',
          field: 'limitValue',
          formatter: emptyFormatter,
        },
        {
          title: '报警值',
          field: 'warnValue',
          slots: {
            default: 'warnValue',
          },
        },
        {
          title: '报警时间',
          field: 'eventTime',
          formatter: emptyFormatter,
        },
        {
          title: '持续时间',
          field: 'durationTime',
          slots: {
            default: 'durationTime',
          },
        },
        {
          title: '报警等级',
          field: 'warnEventLevelTitle',
          slots: {
            default: 'warnEventLevelTitle',
          },
        },
        {
          title: '处理状态',
          field: 'eventStatusTitle',
          slots: {
            default: 'eventStatusTitle',
          },
        },
        {
          title: '自动解除',
          field: 'isAutoConfirm',
          slots: {
            default: 'isAutoConfirm',
          },
        },
        {
          title: '操作',
          field: 'action',
          fixed: 'right',
          slots: {
            default: 'action',
          },
        },
      ]"
      :data="eventData"
      :border="'none'"
      :height="'100%'"
      showOverflow
      :stripe="false"
      :row-config="{
        keyField: 'id',
        isCurrent: false,
      }"
      :scrollY="{
        enabled: true,
        gt: 0,
      }"
      :proxy-config="{
        enabled: false,
      }"
      :column-config="{
        resizable: false,
        isHover: false,
        isCurrent: false,
      }"
      :toolbar-config="null"
    >
      <template #warnValue="{ row }"
        >{{ row?.warnValue?.replace(/\n/g, '<br />') || '-' }}</template
      >
      <template #eventType="{ row }">
        {{ row.eventType === 1 ? '事件报警' : '指标报警' }}
      </template>
      <template #warnEventLevelTitle="{ row }">
        <AlarmLevelTag :text="row.warnEventLevelTitle" v-if="row.warnEventLevelTitle" />
        <span v-else>-</span>
      </template>
      <template #durationTime="{ row }">
        {{ getDuration(row.eventTime, row.releaseTime) || '-' }}
      </template>
      <template #eventStatusTitle="{ row }">
        <AlarmOrderTypeTag :text="row.eventStatusTitle" v-if="row.eventStatusTitle" />
        <span v-else>-</span>
      </template>
      <template #isAutoConfirm="{ row }">
        {{ row.isAutoConfirm ? '是' : '否' }}
      </template>
      <template #action="{ row }">
        <a-button type="link" @click.stop="viewDetail(row)" class="p-0"> 详情 </a-button>
      </template>
      <template #empty>
        <Empty />
      </template>
    </VxeBasicTable>
    <EditModal @success="reloadPage" @register="registerEditModal" />
  </Card>
</template>

<script setup lang="ts">
  import { ref, watch, toRaw, defineExpose, computed } from 'vue';
  import { Card } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { VxeBasicTable } from '/@/components/VxeTable';
  import { useModal } from '/@/components/Modal';
  import AlarmLevelTag from '/@aoa/views/alarm-management/alarm-record/components/AlarmLevelTag/index.vue';
  import AlarmOrderTypeTag from '/@aoa/views/alarm-management/alarm-record/components/AlarmOrderTypeTag/index.vue';
  import EditModal from '/@aoa/views/alarm-management/alarm-record/components/EditModal.vue';
  import { getEventDetailApi } from '/@zhcz/api/data-management';
  import type { IMonitorIndexList, IEventList } from '/@zhcz/api/data-management/model';
  import Empty from '/@/components/HEmpty';

  const props = defineProps({
    indexData: {
      type: Array as PropType<IMonitorIndexList[]>,
      default: () => [],
    },
    eventData: {
      type: Array as PropType<IEventList[]>,
      default: () => [],
    },
    activeTabKey: {
      type: String,
      default: '数据列表',
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:activeTabKey', 'reload']);

  const tabListNoTitle = [
    {
      key: '数据列表',
      tab: '数据列表',
    },
    {
      key: '异常事件',
      tab: '异常事件',
    },
  ];
  const emptyFormatter = ({ cellValue }) => cellValue || '-';

  const noTitleKey = ref(props.activeTabKey);
  watch(
    () => props.activeTabKey,
    () => {
      noTitleKey.value = props.activeTabKey;
    },
  );

  async function onTabChange(value: string) {
    noTitleKey.value = value;
    emit('update:activeTabKey', value);
  }

  // 计算秒数差，超过一天显示x天x小时x分钟x秒，超过一小时显示x小时x分钟x秒，超过一分钟显示x分钟x秒，不足一分钟显示x秒
  const getDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime).getTime();
    const end = endTime ? new Date(endTime).getTime() : new Date().getTime();
    const duration = end - start;
    const day = Math.floor(duration / (24 * 3600 * 1000));
    const leave1 = duration % (24 * 3600 * 1000);
    const hour = Math.floor(leave1 / (3600 * 1000));
    const leave2 = leave1 % (3600 * 1000);
    const minute = Math.floor(leave2 / (60 * 1000));
    const leave3 = leave2 % (60 * 1000);
    const second = Math.floor(leave3 / 1000);
    let result = '';
    if (day > 0) {
      result += `${day}天`;
    }
    if (hour > 0) {
      result += `${hour}小时`;
    }
    if (minute > 0) {
      result += `${minute}分钟`;
    }
    if (second > 0) {
      result += `${second}秒`;
    }
    return result;
  };

  const indicatorData = computed(() => {
    const newIndexData = toRaw(props.indexData)
      .map((item) => {
        const result = item.data.map((j) => ({
          ...j,
          indexName: item.indexName,
          unit: item.unit || '-',
          id: `${item.indexName}-${dayjs(j.collectTime).format('YYYY/MM/DD HH:mm:ss')}`,
          val: j.val || '-',
        }));
        return result;
      })
      .flat();

    return newIndexData;
  });

  // const errorEventData = computed(() => {
  //   return toRaw(props.eventData).map((item) => ({
  //     ...item,
  //     limitValue: item.limitValue || '-',
  //     warnValue: item.warnValue || '-',
  //   }));
  // });

  const [registerEditModal, { openModal }] = useModal();

  async function viewDetail(record: IEventList) {
    const data = await getEventDetailApi(record.warnEventId);
    openModal(true, {
      type: 'solve',
      record: data,
    });
  }

  function reloadPage() {
    emit('reload');
  }

  defineExpose({
    setTableData: () => {},
    scrollTo,
  });
</script>

<style lang="less" scoped>
  .tab-table-wrapper {
    border: none;
    background-color: #fcfcfc;
    border-radius: 0 0 4px 4px;

    :deep(.ant-card-head) {
      padding: 0 16px;
      border-bottom: none;
      position: relative;

      &::after {
        content: '';
        left: 16px;
        width: calc(100% - 32px);
        position: absolute;
        bottom: 0;
        height: 1px;
        background-color: #e9e9e9;
        z-index: 1;
      }

      .ant-tabs {
        z-index: 2;

        .ant-tabs-ink-bar {
          width: 28px !important;
          margin-left: 18px;
          height: 3px !important;
          border-radius: 2px !important;
        }
      }
    }

    :deep(.ant-card-body) {
      padding: 16px;
      flex: 1;
      overflow: hidden;

      .vxe-table-container {
        padding: 0;

        .vxe-table--render-default.border--inner .vxe-table--header-wrapper,
        .vxe-table--render-default.border--none .vxe-table--header-wrapper {
          background-color: #f1f4f7;
        }

        .vxe-table--header {
          border-bottom: 1px solid #d8d8d8;

          .vxe-header--column {
            font-weight: 400;

            &:first-of-type .vxe-cell {
              padding-left: 24px;
            }
          }
        }

        .vxe-body--row {
          .vxe-body--column {
            border-bottom: 1px solid #f5f6f7 !important;
            height: 49px !important;
            min-height: 49px !important;

            &:first-of-type .vxe-cell {
              padding-left: 24px !important;
            }

            .vxe-cell {
              padding: 0 8px !important;
            }
          }
        }

        .vxe-table--render-wrapper {
          background-color: #fcfcfc;

          .vxe-table--header {
            .vxe-header--column {
              // padding: 12px 0 !important;
              height: 46px !important;
              min-height: 46px !important;

              &:first-of-type {
                border-radius: 4px 0 0 0;
              }

              &:last-of-type {
                border-radius: 0 4px 0 0;
              }
            }
          }

          .vxe-table--body-wrapper {
            &::-webkit-scrollbar {
              display: none;
            }
          }
        }
      }

      .vxe-table .vxe-table--header-wrapper {
        color: #666;
      }

      .vxe-table--render-default {
        color: #666;
      }

      .vxe-table--render-default .vxe-table--body-wrapper > table {
        background-color: rgb(251, 251, 251);
      }

      .vxe-table--render-default .vxe-body--row.row--hover {
        background-color: #f0f0f0;
      }

      .vben-basic-table {
        .ant-table-wrapper {
          margin: 0;
          padding: 0;
        }
      }

      .vben-basic-table--empty .ant-table-wrapper .ant-table-body {
        padding-top: 1rem;
      }
    }

    :deep(.ant-tabs) {
      > .ant-tabs-nav::before,
      > div > .ant-tabs-nav::before {
        border-bottom: none;
      }
    }
  }
</style>
