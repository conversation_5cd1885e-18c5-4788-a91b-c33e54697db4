<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    title="样式编辑"
    width="45%"
  >
    <div class="container">
      <BasicForm @register="registerForm">
        <template #color="{ model, field }">
          <div class="flex">
            <ColorPicker
              class="color-picker-style"
              v-model:value="model[field]"
              is-custom
              input
              hex
            />
            <a-button @click.prevent="copyToClipboard(model[field])" class="ml-4"> 复制 </a-button>
          </div>
        </template>
      </BasicForm>
    </div>
    <template #footer>
      <a-button class="default-btn" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">保存</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ColorPicker } from '/@/components/ColorPicker';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { editStyleModalSchemas } from '../data';
  import { useMessage } from '/@/hooks/web/useMessage';

  const emit = defineEmits(['success', 'register']);
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setFieldsValue({ detailStyle: data.val || '{}' });
  });

  const [registerForm, { resetFields, setFieldsValue, validate, clearValidate }] = useForm({
    labelWidth: 100,
    schemas: editStyleModalSchemas,
    showActionButtonGroup: false,
  });

  const { createMessage } = useMessage();
  /* 复制颜色 */
  const copyToClipboard = (text) => {
    if (!text) return createMessage.warn('请先填写颜色');
    navigator.clipboard.writeText(text);
    createMessage.success('复制成功');
  };
  const handleSubmit = async () => {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      await handleCancel();
      emit('success', values.detailStyle);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  };

  const handleCancel = () => {
    resetFields();
    closeModal();
    clearValidate();
  };
</script>
