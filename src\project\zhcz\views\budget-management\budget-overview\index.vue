<template>
  <PageWrapper contentFullHeight contentClass="page">
    <div class="flex flex-1 gap-3">
      <BudgetTrends class="flex-1" />
      <BudgetComposition style="min-width: 540px" />
    </div>
    <div class="flex flex-1 gap-3">
      <BudgetForecast class="flex-1" />
      <BudgetRanking style="min-width: 540px" />
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { PageWrapper } from '/@/components/Page';
  import BudgetTrends from './components/BudgetTrends.vue';
  import BudgetComposition from './components/BudgetComposition.vue';
  import BudgetForecast from './components/BudgetForecast.vue';
  import BudgetRanking from './components/BudgetRanking.vue';
</script>

<style lang="less" scoped>
  .vben-page-wrapper {
    :deep(.page) {
      overflow-x: auto;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 12px;

      & > div {
        min-width: 1300px;
        min-height: 310px;
      }
    }
  }

  .ant-card {
    height: 100%;
    box-shadow: none;
    color: #333;

    :deep(.ant-card-head) {
      padding: 0 16px;
      font-size: 16px;
      min-height: unset;
      height: 48px;
      color: #333;
      border-bottom: 1px solid #e9e9e9;
    }

    :deep(.ant-card-body) {
      padding: 16px;
      height: ~'calc(100% - 48px)';
    }
  }
</style>
