export async function getDataApi(type) {
  await asleep(0.5);
  if (type === 'month') {
    return {
      total: 5545,
      list: [
        { indexName: '设备老化', value: 1, unitName: '个' },
        { indexName: '电气故障', value: 2, unitName: '个' },
        { indexName: '机械故障', value: 1, unitName: '个' },
        { indexName: '设备松动', value: 1, unitName: '个' },
      ],
    };
  } else if (type === 'year') {
    return {
      total: 5545,
      list: [
        // indexName: string;
        // indexCode: string;
        // unitName: string;
        // value: number;
        { indexName: '设备老化', value: 5, unitName: '个' },
        { indexName: '电气故障', value: 3, unitName: '个' },
        { indexName: '机械故障', value: 2, unitName: '个' },
        { indexName: '设备松动', value: 2, unitName: '个' },
        { indexName: '密封失败', value: 1, unitName: '个' },
        { indexName: '控制故障', value: 1, unitName: '个' },
        { indexName: '外部原因', value: 0, unitName: '个' },
        { indexName: '其他原因', value: 0, unitName: '个' },
      ],
    };
  }
}

async function asleep(val = 3) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, val * 1000);
  });
}
