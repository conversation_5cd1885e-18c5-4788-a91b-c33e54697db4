<template>
  <div class="device-operation-analysis">
    <div class="page-content">
      <!-- 左侧设备树 -->
      <div class="left-tree-section">
        <DeviceTree
          @select="handleDeviceSelect"
          @toggle-change="handleTreeToggle"
          ref="deviceTreeRef"
        />
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <!-- 表格区域 -->
        <BasicTable @register="registerTable" class="table-section">
          <template #tableTitle>
            <div class="table-actions">
              <a-button
                type="primary"
                :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
                @click="handleAdd"
                :disabled="!selectedDevice"
              >
                新增
              </a-button>
              <a-button
                :icon="h(Icon, { icon: 'icon-park-outline:upload' })"
                class="ml-4"
                @click="handleImport"
              >
                导出
              </a-button>
            </div>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'faultPhenomenon'">
              <span>{{ record.faultPhenomenon }}</span>
            </template>

            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: '编辑',
                    onClick: handleEdit.bind(null, record),
                  },
                  {
                    label: '删除',
                    color: 'error',
                    popConfirm: {
                      title: '是否确认删除',
                      confirm: handleDelete.bind(null, record),
                    },
                  },
                ]"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <FaultDeviceModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup name="DeviceOperationAnalysis">
  import { ref, computed, reactive, h } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import { getFaultDeviceListApi } from '/@zoology-wzb/api/device';
  import type { faultDeviceDetailType } from '/@zoology-wzb/api/device/type';
  import DeviceTree from './components/DeviceTree.vue';
  import FaultDeviceModal from './components/FaultDeviceModal.vue';
  import { columns, searchFormSchema } from './data';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import { DICT } from '../enums';

  const emergencyLevelOptions = ref<any>([]);
  const faultReasonOptions = ref<any>([]);

  async function getEmergencyLevelOptions() {
    const res = await getDictTypeListApi({ type: DICT.EQUIPMENT_FAILURE_URGENCY_LEVEL });
    emergencyLevelOptions.value = res;
  }

  async function getFaultReasonOptions() {
    const res = await getDictTypeListApi({ type: DICT.EQUIPMENT_FAILURE_REASON });
    faultReasonOptions.value = res;
  }
  getEmergencyLevelOptions();
  getFaultReasonOptions();

  defineOptions({
    name: 'DeviceOperationAnalysis',
  });

  const { createMessage } = useMessage();

  // 响应式数据
  const selectedDevice = ref<any>(null);
  const deviceTreeRef = ref();
  const isTreeShow = ref(true);

  // 导入处理
  const handleImport = () => {
    createMessage.success('导入功能待实现');
  };

  // 表格配置
  const [registerTable, { reload }] = useTable({
    title: '故障设备列表',
    api: getFaultDeviceListApi,
    columns,
    beforeFetch: (params) => {
      return {
        ...params,
        deviceId: selectedDevice.value?.id,
      };
    },
    afterFetch: (list) => {
      console.log('----> list', list);
      return list;
    },
    fetchSetting: {
      listField: 'list',
      totalField: 'total',
      pageField: 'page',
      sizeField: 'pageSize',
    },
    useSearchForm: true,
    formConfig: {
      schemas: searchFormSchema,
      labelWidth: 80,
      resetFunc: async () => {},
      // showAdvancedButton: false,
    },
    showTableSetting: true,
    bordered: true,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      key: 'action',
    },
  });

  // 弹窗配置
  const [registerModal, { openModal }] = useModal();

  // 处理设备选择
  const handleDeviceSelect = (node: any, selectedKeys: string[]) => {
    console.log('-----------》 选中的设备节点:', node);
    selectedDevice.value = node;
    reload();
  };

  // 处理树的切换
  const handleTreeToggle = (isShow: boolean) => {
    isTreeShow.value = isShow;
  };

  // 新增
  const handleAdd = () => {
    if (!selectedDevice.value) {
      createMessage.warning('请先选择设备类型');
      return;
    }
    openModal(true, {
      isUpdate: false,
    });
  };

  // 编辑
  const handleEdit = (record: faultDeviceDetailType) => {
    openModal(true, {
      record,
      isUpdate: true,
    });
  };

  // 删除
  const handleDelete = (record: faultDeviceDetailType) => {
    console.log('删除记录:', record);
    createMessage.success('删除成功');
    reload();
  };

  // 成功回调
  const handleSuccess = () => {
    reload();
  };
</script>

<style lang="less" scoped>
  .device-operation-analysis {
    height: 100%;
    padding: 0 16px 16px;
    width: 100%;

    .page-content {
      overflow: hidden;
      height: 100%;
      display: flex;
      width: 100%;
    }

    .left-tree-section {
      height: 100%;
    }

    .right-content {
      height: 100%;
      background: #fff;
      border-radius: 4px;
      overflow: hidden;
      transition: all 0.3s;
      margin-left: 0;
      display: flex;
      flex-direction: column;

      .search-section {
        margin-bottom: 16px;

        .search-row {
          display: flex;
          align-items: center;
          gap: 16px;
          flex-wrap: wrap;
        }

        .search-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .label {
            white-space: nowrap;
            font-size: 14px;
          }
        }

        .search-actions {
          display: flex;
          gap: 8px;
        }
      }

      .selected-device-info {
        background: #fffbe6;
        border: 1px solid #ffe58f;
        border-radius: 4px;
        padding: 8px 12px;
        margin-bottom: 16px;
        font-size: 14px;
        color: #d46b08;
      }

      .table-actions {
        display: flex;
        gap: 8px;
      }

      .fault-reason-section {
        .reason-list {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          .reason-item {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              background: #e6f7ff;
              border-color: #91d5ff;
              color: #1890ff;
            }
          }
        }
      }
    }
  }
</style>
