<template>
  <div class="chart" ref="chartRef"></div>
</template>

<script setup lang="ts">
  import { ref, Ref, PropType } from 'vue';
  import { cloneDeep, groupBy } from 'lodash-es';
  import { useECharts } from '/@/hooks/web/useECharts';

  interface Item {
    indexName: string;
    indexCode: string;
    unitName: string;
    data: { collectDateTime: string; value: number }[];
  }

  const props = defineProps({
    baseData: {
      type: Array as PropType<Item[]>,
      default: () => [],
    },
    monthData: {
      type: Array as PropType<Item[]>,
      default: () => [],
    },
    yearData: {
      type: Array as PropType<Item[]>,
      default: () => [],
    },
  });
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

  /**
   * 获取Y轴最大最小值
   * @param arr 数值数组
   * @returns { max: number, min: number }
   */
  function getYAxisInterval(arr: number[][]) {
    const maxArr = arr.map((item) => {
      const items = item.filter((i) => !Number.isNaN(i) && i !== null);
      return items.length ? Math.max(...items) : [1];
    });
    const minArr = arr.map((item) => {
      const items = item.filter((i) => !Number.isNaN(i) && i !== null);
      return items.length ? Math.min(...items) : [0];
    });

    let max = Math.max(...maxArr);
    const min = Math.min(...minArr);
    if (max === min) {
      max = max + 1;
    }
    const step = Math.ceil((max - min) / 6);
    return {
      max: Math.ceil(max + step),
      min: Math.round(min - step) < 0 ? 0 : Math.round(min - step),
    };
  }

  /**
   * 获取间距
   */
  function getDistance(yAxis: Recordable[], offset = 4) {
    const baseYAxis = yAxis.filter((i) => i.chartType === 'base');
    const leftArr = baseYAxis.filter((_, index) => index % 2 === 0);
    const rightArr = baseYAxis.filter((_, index) => index % 2 !== 0);

    const leftOffsets = leftArr.map((item) => {
      const maxLen = item.max.toString().length;
      const unitLen = item.name.length;
      return maxLen >= unitLen ? maxLen : unitLen;
    });

    const rightOffsets = rightArr.map((item) => {
      const maxLen = item.max.toString().length;
      const unitLen = item.name.length;
      return maxLen >= unitLen ? maxLen : unitLen;
    });

    const leftOffset = leftOffsets.reduce((pre, cur) => {
      return pre + cur;
    }, 0);

    const rightOffset = rightOffsets.reduce((pre, cur) => {
      return pre + cur;
    }, 0);

    return {
      left: baseYAxis.length < 2 ? 0 : leftOffset * offset,
      right: baseYAxis.length < 2 ? 0 : rightOffset * offset,
    };
  }

  function hexToRgba(hex: string, opacity = 1) {
    const h = hex.replace('#', '');
    const rgb: number[] = [];
    for (let i = 0; i < 3; i++) {
      rgb.push(parseInt(h.slice(i * 2, i * 2 + 2), 16));
    }
    return `rgba(${rgb.join(',')},${opacity})`;
  }

  /**
   * 获取series配置
   * @param data 指标数组
   * @param yAxis y轴数组
   */
  function getSeriesData(yAxis: Recordable[], xAxis: Recordable) {
    let result: any = [];
    const baseXAxis = xAxis.find((item) => item.chartType === 'base');
    const instance = getInstance();
    const color = instance?._theme.color || [];
    const baseResult = props.baseData.map((i, index) => {
      const yAxisIndex = !i.unitName
        ? yAxis.findIndex((item) => item.chartType === 'base' && !item.name)
        : yAxis.findIndex((item) => item.chartType === 'base' && item.name.includes(i.unitName));

      const xAxisIndex = xAxis.findIndex((item) => item.chartType === 'base');
      return {
        name: `${i.indexName}`,
        unitName: i.unitName,
        type: 'line',
        smooth: true,
        symbol: 'none',
        data: baseXAxis.data.map((_xitem, xindex) => {
          return i.data[xindex] ? parseFloat(i.data[xindex].value) : 0;
        }),
        zlevel: index,
        areaStyle: {
          origin: 'start',
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: hexToRgba(color[index % color.length], 0.2) },
              { offset: 1, color: hexToRgba(color[index % color.length], 0) },
            ],
          },
        },
        z: index,
        xAxisIndex,
        yAxisIndex,
      };
    });
    result = result.concat(cloneDeep(baseResult));
    if (props.monthData.length) {
      const monthXAxis = xAxis.find((item) => item.chartType === 'month');
      const monthResult = props.monthData.map((i, index) => {
        const yAxisIndex = !i.unitName
          ? yAxis.findIndex((item) => item.chartType === 'month' && !item.name)
          : yAxis.findIndex((item) => item.chartType === 'month' && item.name.includes(i.unitName));
        const xAxisIndex = xAxis.findIndex((item) => item.chartType === 'month');
        return {
          name: `${i.indexName}-环比`,
          unitName: i.unitName,
          type: 'line',
          smooth: true,
          symbol: 'none',
          data: monthXAxis.data.map((_xitem, xindex) => {
            return i.data[xindex] ? parseFloat(i.data[xindex].value) : 0;
          }),
          zlevel: index,
          areaStyle: {
            origin: 'start',
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: hexToRgba(color[index % color.length], 0.2) },
                { offset: 1, color: hexToRgba(color[index % color.length], 0) },
              ],
            },
          },
          z: index,
          xAxisIndex,
          yAxisIndex,
        };
      });
      result = result.concat(cloneDeep(monthResult));
    }
    if (props.yearData.length) {
      const yearXAxis = xAxis.find((item) => item.chartType === 'year');
      const yearResult = props.yearData.map((i, index) => {
        const yAxisIndex = !i.unitName
          ? yAxis.findIndex((item) => item.chartType === 'year' && !item.name)
          : yAxis.findIndex((item) => item.chartType === 'year' && item.name.includes(i.unitName));
        const xAxisIndex = xAxis.findIndex((item) => item.chartType === 'year');
        return {
          name: `${i.indexName}-同比`,
          unitName: i.unitName,
          type: 'line',
          smooth: true,
          symbol: 'none',
          data: yearXAxis.data.map((_xitem, xindex) => {
            return i.data[xindex] ? parseFloat(i.data[xindex].value) : 0;
          }),
          zlevel: index,
          areaStyle: {
            origin: 'end',
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: hexToRgba(color[index % color.length], 0.2) },
                { offset: 1, color: hexToRgba(color[index % color.length], 0) },
              ],
            },
          },
          z: index,
          xAxisIndex,
          yAxisIndex,
        };
      });
      result = result.concat(cloneDeep(yearResult));
    }
    return result;
  }

  /**
   * 获取Y轴配置
   * @param data 指标数组
   */
  function getYAxisData(data: Recordable[]) {
    if (!data.length) return [];
    function getOffset(index, max, unit, offset = 20) {
      const count = index < 2 ? 0 : Math.ceil(index / 2);
      const maxLen = max.toString().length;
      const unitLen = unit.length;
      if (maxLen >= unitLen) {
        return maxLen * count * offset;
      } else {
        return unitLen * count * offset;
      }
    }

    let result: any = [];
    const config = data.map((item, index) => {
      const max = item.max || 1;
      const min = item.min || 0;

      const offset = getOffset(index, item.max, item.unitName);

      return {
        type: 'value',
        name: item.unitName && item.unitName !== 'null' ? `单位(${item.unitName})` : '',
        nameTextStyle: {
          color: '#999999',
          align: 'center',
          overflow: 'truncate',
          padding: [0, 0, 0, item.unitName.length * 4],
        },
        min,
        max,
        interval: (max - min) / 5,
        position: index % 2 ? 'right' : 'left',
        offset,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#999999',
          fontSize: '12px',
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#E5E6EB',
            type: 'dashed',
          },
        },
        gridIndex: 0,
        chartType: 'base',
      };
    });
    result = result.concat(cloneDeep(config));
    if (props.monthData.length) {
      config.forEach((item) => {
        item.gridIndex++;
        item.chartType = 'month';
      });
      result = result.concat(cloneDeep(config));
    }
    if (props.yearData.length) {
      config.forEach((item) => {
        item.gridIndex++;
        item.chartType = 'year';
      });
      result = result.concat(cloneDeep(config));
    }
    return result;
  }

  /**
   * 获取Y轴配置项
   * @param data 指标数组
   */
  function getYAxisConfigItem() {
    const baseGroup = groupBy(cloneDeep(props.baseData), 'unitName');
    const monthGroup = groupBy(cloneDeep(props.monthData), 'unitName');
    const yearGroup = groupBy(cloneDeep(props.yearData), 'unitName');
    const result = Object.keys(baseGroup).map((key) => {
      let dataArr = baseGroup[key].map((i) => i.data.map((j) => parseFloat(j.value)));
      if (props.monthData.length) {
        dataArr = dataArr.concat(
          monthGroup[key].map((i) => i.data.map((j) => parseFloat(j.value))),
        );
      }
      if (props.yearData.length) {
        dataArr = dataArr.concat(yearGroup[key].map((i) => i.data.map((j) => parseFloat(j.value))));
      }

      return {
        unitName: key,
        ...getYAxisInterval(dataArr),
      };
    });
    return result;
  }

  /**
   * 获取X轴配置
   * @param data 指标数组
   */
  function getXAxisData() {
    const result: any = [];
    const config: any = {
      type: 'category',
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: '#999999',
        fontSize: '12px',
      },
      data: props.baseData[0].data.map((item) => item.collectDateTime) || [],
      gridIndex: 0,
      chartType: 'base',
    };
    result.push(cloneDeep(config));
    if (props.monthData.length) {
      config.data = props.monthData[0].data.map((item) => item.collectDateTime) || [];
      config.gridIndex++;
      config.chartType = 'month';
      result.push(cloneDeep(config));
    }
    if (props.yearData.length) {
      config.data = props.yearData[0].data.map((item) => item.collectDateTime) || [];
      config.gridIndex++;
      config.chartType = 'year';
      result.push(cloneDeep(config));
    }

    return result;
  }

  /**
   * 渲染Echarts
   */
  function renderEcharts() {
    const xAxis = getXAxisData();
    const group = getYAxisConfigItem();
    const yAxis = getYAxisData(group);
    const series = getSeriesData(yAxis, xAxis);
    const option: any = {
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
      },
      legend: {
        show: true,
        icon: 'circle',
        top: 10,
        width: '80%',
        scroll: true,
        itemHeight: 11,
        textStyle: {
          color: '#999999',
          fontSize: '12px',
        },
      },
      axisPointer: {
        link: [
          {
            xAxisIndex: 'all',
          },
        ],
      },
      xAxis,
      yAxis,
      series,
    };
    if (xAxis.length === 1) {
      option.grid = [
        {
          left: getDistance(yAxis).left,
          right: getDistance(yAxis).right,
          bottom: 0,
          containLabel: true,
        },
      ];
    } else if (xAxis.length === 2) {
      option.grid = [
        {
          left: getDistance(yAxis).left,
          right: getDistance(yAxis).right,
          top: '15%',
          height: '35%',
          containLabel: true,
        },
        {
          left: getDistance(yAxis).left,
          right: getDistance(yAxis).right,
          top: '64%',
          height: '35%',
          containLabel: true,
        },
      ];
    } else if (xAxis.length === 3) {
      option.grid = [
        {
          left: getDistance(yAxis).left,
          right: getDistance(yAxis).right,
          top: '15%',
          height: '20%',
          containLabel: true,
        },
        {
          left: getDistance(yAxis).left,
          right: getDistance(yAxis).right,
          top: '46%',
          height: '20%',
          containLabel: true,
        },
        {
          left: getDistance(yAxis).left,
          right: getDistance(yAxis).right,
          top: '79%',
          height: '20%',
          containLabel: true,
        },
      ];
    }

    const isShowDataZoom = series.some((item) => item.data.length > 1000);
    if (isShowDataZoom) {
      option.dataZoom = [
        {
          show: true,
          start: 0,
          end: 10,
          xAxisIndex: xAxis.map((_item, index) => index),
        },
      ];
    }

    setOptions(option);
  }

  defineExpose({
    renderEcharts,
  });
</script>

<style lang="less" scoped>
  .chart {
    height: 100%;
    width: 100%;
  }
</style>
