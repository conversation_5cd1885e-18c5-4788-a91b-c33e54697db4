import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Form/index';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: '计划名称',
    dataIndex: 'planName',
  },
  {
    title: '设备类别',
    dataIndex: 'eqTypeName',
  },
  {
    title: '保养类别',
    dataIndex: 'maintenanceTypeName',
  },
  {
    title: '是否启用',
    dataIndex: 'isEnabled',
  },
  {
    title: '维护保养项目',
    dataIndex: 'maintenanceProjectNameList',
  },
  {
    title: '执行计划',
    dataIndex: 'executionDescription',
    width: 300,
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'eqType',
    label: '设备类别',
    component: 'Input',
    slot: 'eqType',
    colProps: { span: 6 },
    labelWidth: 68,
  },
  {
    field: 'maintenanceTypeId',
    label: '保养类别',
    component: 'Input',
    slot: 'type',
    colProps: { span: 6 },
  },
];
// const disabledDate = (current: Dayjs) => {
//   return current && current > dayjs().endOf('day');
// };
const disabledDate = () => {
  // console.log('current', current);
  // const [startDate, endDate] = dateRange.value || [];
  // // 如果已经选择了开始时间和结束时间，并且当前日期的月份与已选的不一致，则禁用
  // if (startDate && endDate) {
  //   const selectedMonth = startDate.month();
  //   return current.month() !== selectedMonth;
  // }
  // 如果只选择了开始时间，判断当前日期是否在本月之外，或者大于当天结束时间
  // if (startDate) {
  //   const selectedMonth = startDate.month();
  // return current.month() !== selectedMonth || current.isAfter(now.endOf('day'));
  // }
};
export const schemas: FormSchema[] = [
  {
    field: 'time',
    label: '导出日期(不能跨月)',
    component: 'RangePicker',
    labelWidth: 150,
    componentProps: {
      showTime: false,
      disabledDate,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    defaultValue: [dayjs().startOf('day'), dayjs().endOf('day')],
    required: true,
    colProps: { span: 12 },
  },
  // {
  //   field: 'blank1',
  //   component: 'Input',
  //   label: '',
  //   colProps: {
  //     span: 24,
  //   },
  //   render: () => {
  //     return h(
  //       'div',
  //       {
  //         style: {
  //           borderBottom: '1px solid #eeeff1',
  //           paddingBottom: '6px',
  //           display: 'flex',
  //           alignItems: 'center',
  //         },
  //       },
  //       [
  //         h(
  //           'span',
  //           {
  //             style: {
  //               width: '4px',
  //               height: '14px',
  //               background: 'var(--theme-color)',
  //               display: 'inline-block',
  //             },
  //           },
  //           '',
  //         ),
  //         h(
  //           'span',
  //           { style: { paddingLeft: '8px', fontSize: '16px', fontWeight: 600 } },
  //           '当班记事',
  //         ),
  //       ],
  //     );
  //   },
  // },
  // {
  //   field: 'planFactoryId',
  //   component: 'Select',
  //   label: '单位',
  //   required: true,
  //   slot: 'planFactoryId',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'planName',
  //   component: 'Input',
  //   label: '维保名称',
  //   slot: 'planName',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'maintenanceTypeId',
  //   component: 'Select',
  //   label: '保养类别',
  //   required: true,
  //   slot: 'name',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'type',
  //   component: 'ApiSelect',
  //   label: '保养类型',
  //   required: true,
  //   slot: 'type',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'eqType',
  //   component: 'Select',
  //   label: '设备类别',
  //   slot: 'eqType',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'blank2',
  //   component: 'Input',
  //   label: '',
  //   colProps: {
  //     span: 24,
  //   },
  //   render: () => {
  //     return h(
  //       'div',
  //       {
  //         style: {
  //           borderBottom: '1px solid #eeeff1',
  //           paddingBottom: '6px',
  //           display: 'flex',
  //           alignItems: 'center',
  //         },
  //       },
  //       [
  //         h(
  //           'span',
  //           {
  //             style: {
  //               width: '4px',
  //               height: '14px',
  //               background: 'var(--theme-color)',
  //               display: 'inline-block',
  //             },
  //           },
  //           '',
  //         ),
  //         h(
  //           'span',
  //           { style: { paddingLeft: '8px', fontSize: '16px', fontWeight: 600 } },
  //           '基本信息',
  //         ),
  //       ],
  //     );
  //   },
  // },
  // {
  //   field: 'dispatchUserIds',
  //   component: 'HChooseUser',
  //   label: '派单工人',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'responsibilityUserIds',
  //   component: 'HChooseUser',
  //   label: '工作责任人',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'executorUserIds',
  //   component: 'HChooseUser',
  //   label: '执行人',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'acceptanceUserIds',
  //   component: 'HChooseUser',
  //   label: '验收人',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'scheduledTimes',
  //   component: 'InputNumber',
  //   label: '计划工时(小时)',
  //   required: true,
  //   componentProps: {
  //     min: 0,
  //     precision: 0,
  //   },
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'remark',
  //   component: 'InputTextArea',
  //   label: '工单描述',
  //   required: true,
  //   colProps: {
  //     span: 24,
  //   },
  // },
  // // {
  // //   field: 'reason',
  // //   component: 'InputTextArea',
  // //   label: '原因分析',
  // //   colProps: {
  // //     span: 24,
  // //   },
  // // },
  // {
  //   field: 'project',
  //   component: 'Input',
  //   label: '保养项目',
  //   slot: 'project-table',
  //   colProps: {
  //     span: 24,
  //   },
  // },
  // {
  //   field: 'blank4',
  //   component: 'Input',
  //   label: '',
  //   colProps: {
  //     span: 24,
  //   },
  //   render: () => {
  //     return h(
  //       'div',
  //       {
  //         style: {
  //           borderBottom: '1px solid #eeeff1',
  //           paddingBottom: '6px',
  //           display: 'flex',
  //           alignItems: 'center',
  //         },
  //       },
  //       [
  //         h(
  //           'span',
  //           {
  //             style: {
  //               width: '4px',
  //               height: '14px',
  //               background: 'var(--theme-color)',
  //               display: 'inline-block',
  //             },
  //           },
  //           '',
  //         ),
  //         h(
  //           'span',
  //           { style: { paddingLeft: '8px', fontSize: '16px', fontWeight: 600 } },
  //           '设置周期',
  //         ),
  //       ],
  //     );
  //   },
  // },
  // {
  //   field: 'planStartTime',
  //   component: 'DatePicker',
  //   label: '开始日期',
  //   required: true,
  //   slot: 'planStartTime',
  //   componentProps: {
  //     picker: 'date',
  //     format: 'YYYY-MM-DD',
  //     valueFormat: 'YYYY-MM-DD 00:00:00',
  //     allowClear: true,
  //     showTime: false,
  //     disabledDate: (current: Dayjs) => current && current < dayjs().startOf('day'),
  //   },
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'planEndTime',
  //   component: 'DatePicker',
  //   label: '结束日期',
  //   slot: 'planEndTime',
  //   componentProps: {
  //     picker: 'date',
  //     format: 'YYYY-MM-DD',
  //     valueFormat: 'YYYY-MM-DD 00:00:00',
  //     allowClear: true,
  //     showTime: false,
  //     disabledDate: (current: Dayjs) => current && current < dayjs().startOf('day'),
  //   },
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'cronExpression',
  //   component: 'InputTextArea',
  //   label: '表达式',
  //   required: true,
  //   slot: 'cronExpression',
  //   colProps: {
  //     span: 24,
  //   },
  // },
  // {
  //   field: 'executionDescription',
  //   component: 'InputTextArea',
  //   label: '计划描述',
  //   required: true,
  //   componentProps: {
  //     disabled: true,
  //     placeholder: '由cron表达式生成',
  //   },
  //   colProps: {
  //     span: 24,
  //   },
  // },
];

export const projectColumns: BasicColumn[] = [
  {
    title: '保养项目',
    dataIndex: 'name',
    ellipsis: false,
  },
];
