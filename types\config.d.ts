import { MenuTypeEnum, MenuModeEnum, TriggerEnum, MixSidebarTriggerEnum } from '/@/enums/menuEnum';
import {
  ContentEnum,
  PermissionModeEnum,
  ThemeEnum,
  RouterTransitionEnum,
  SettingButtonPositionEnum,
  SessionTimeoutProcessingEnum,
} from '/@/enums/appEnum';

import { CacheTypeEnum } from '/@/enums/cacheEnum';

export type LocaleType = 'zh_CN' | 'en' | 'ru' | 'ja' | 'ko';

export interface MenuSetting {
  bg: string;
  fixed: boolean;
  collapsed: boolean;
  siderHidden: boolean;
  canDrag: boolean;
  show: boolean;
  hidden: boolean;
  split: boolean;
  menuWidth: number;
  mode: MenuModeEnum;
  type: MenuTypeEnum;
  theme: ThemeEnum;
  topMenuAlign: 'start' | 'center' | 'end';
  trigger: TriggerEnum;
  accordion: boolean;
  closeMixSidebarOnChange: boolean;
  collapsedShowTitle: boolean;
  mixSideTrigger: MixSidebarTriggerEnum;
  mixSideFixed: boolean;
  /** 顶部菜单展示 */
  showTopMenu: boolean;
}

export interface MultiTabsSetting {
  cache: boolean;
  canDrag: boolean;
  className: string;
}

/** Tabs栏配置 */
export interface TypeTabsSetting {
  /** Turn on */
  show: boolean;
  /** Turn on quick actions */
  showQuick: boolean;
  /** Whether to show the refresh button */
  showRedo: boolean;
  /** Whether to show the collapse button */
  showFold: boolean;

  tabsType: string;
}

export interface HeaderSetting {
  bg: string;
  fixed: boolean;
  show: boolean;
  theme: ThemeEnum;
  // Turn on full screen
  showFullScreen: boolean;
  // Whether to show the lock screen
  useLockPage: boolean;
  // Show document button
  showDoc: boolean;
  // Show message center button
  showNotice: boolean;
  showSearch: boolean;
  // show userDropDown
  showUserDropDown: boolean;
  // show SettingDrawer
  showSettingDrawer: boolean;
}

export interface LocaleSetting {
  showPicker: boolean;
  domain: string;
  // Current language
  locale: LocaleType;
  // default language
  fallback: LocaleType;
  // available Locales
  availableLocales: LocaleType[];
}

export interface TransitionSetting {
  //  Whether to open the page switching animation
  enable: boolean;
  // Route basic switching animation
  basicTransition: RouterTransitionEnum;
  // Whether to open page switching loading
  openPageLoading: boolean;
  // Whether to open the top progress bar
  openNProgress: boolean;
}

export interface ApplicationConfig {
  // Storage location of permission related information
  permissionCacheType: CacheTypeEnum;
  // Whether to show the configuration button
  showSettingButton: boolean;
  // Whether to show the theme switch button
  showDarkModeToggle: boolean;
  // Configure where the button is displayed
  settingButtonPosition: SettingButtonPositionEnum;
  // Permission mode
  permissionMode: PermissionModeEnum;
  // Session timeout processing
  sessionTimeoutProcessing: SessionTimeoutProcessingEnum;
  // The main interface is displayed in full screen, the menu is not displayed, and the top
  fullContent: boolean;
  // Multi-tab settings
  multiTabsSetting: MultiTabsSetting;

  // content width
  contentMode: ContentEnum;
  // pageLayout whether to enable keep-alive
  openKeepAlive: boolean;
  // Lock screen time
  lockTime: number;
  // Use error-handler-plugin
  useErrorHandle: boolean;
  // Whether to open back to top
  useOpenBackTop: boolean;
  // Is it possible to embed iframe pages
  canEmbedIFramePage: boolean;
  // Whether to delete unclosed messages and notify when switching the interface
  closeMessageOnSwitch: boolean;
  // Whether to cancel the http request that has been sent but not responded when switching the interface.
  removeAllHttpPending: boolean;

  tableSearchLabel: boolean;
  formPlaceholder?: string;
}

export interface ThemeConfig {
  // Whether to display the logo
  showLogo: boolean;
  // Whether to show the global footer
  showFooter: boolean;
  // Show breadcrumbs
  showBreadCrumb: boolean;
  // Show breadcrumb icon
  showBreadCrumbIcon: boolean;
  // Website gray mode, open for possible mourning dates
  grayMode: boolean;
  // Whether to turn on the color weak mode
  colorWeak: boolean;
  // Theme color
  themeColor: string;
  tableSearchLabel: boolean;

  formPlaceholder?: string;

  // menuType: MenuTypeEnum;
  headerSetting: HeaderSetting;
  // menuSetting
  menuSetting: MenuSetting;
  // Animation configuration
  transitionSetting: TransitionSetting;
  /** 左侧菜单模式配置 */
  sidebarTabsSetting: TypeTabsSetting;
  /** 左侧菜单混合模式配置 */
  mixSidebarTabsSetting: TypeTabsSetting;
  /** 顶部菜单混合模式配置 */
  mixTabsSetting: TypeTabsSetting;
  /** 顶部菜单模式配置 */
  topMenuTabsSetting: TypeTabsSetting;
  /** 平台菜单配置 */
  platformTabsSetting: TypeTabsSetting;
  /** 多系统菜单配置 */
  moreSystemTabsSetting: TypeTabsSetting;
}

export type ProjectConfig = ApplicationConfig & ThemeConfig;

export interface GlobConfig {
  // Site title
  title: string;
  // Service interface url
  apiUrl: string;
  // Upload url
  uploadUrl?: string;
  //  Service interface url prefix
  urlPrefix?: string;
  // Project abbreviation
  shortName: string;
}
export interface GlobEnvConfig {
  // Site title
  VITE_GLOB_APP_TITLE: string;
  // Service interface url
  VITE_GLOB_API_URL: string;
  // Service interface url prefix
  VITE_GLOB_API_URL_PREFIX?: string;
  // Project abbreviation
  VITE_GLOB_APP_SHORT_NAME: string;
  // Upload url
  VITE_GLOB_UPLOAD_URL?: string;
  // 工艺流程编辑器服务
  VITE_GLOB_EDITOR_API_URL: string;
  // 厂站摄像头服务
  VITE_GLOB_CAMERA_API_URL: string;
  // 天气服务
  VITE_GLOB_WEATHER_API_URL: string;
  // 地理位置服务
  VITE_GLOB_GEO_API_URL: string;
  // 项目启动与打包的项目
  VITE_GLOB_PROJECT: string;
  // 工艺流程上传文件地址
  VITE_GLOB_UPLOAD_EQUIPMENT_FIRE_PATH: string;
  // 工艺流程文件地址
  VITE_GLOB_EQUIPMENT_FIRE_PATH;
}
