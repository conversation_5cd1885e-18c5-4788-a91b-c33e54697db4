<template>
  <BasicModal
    v-bind="$attrs"
    width="60%"
    @register="registerModal"
    title="选择设备"
    @cancel="handleClose"
    @ok="handleOk"
    destoryOnClose
  >
    <div class="container-device flex">
      <div class="tree-container">
        <div class="search-container">
          <Input
            v-model:value="searchVal"
            placeholder="请输入"
            @change="findNodeByName(treeRawData, searchVal)"
          >
            <template #prefix>
              <Icon icon="icon-park-outline:search" color="#999999" :size="14" />
            </template>
          </Input>
        </div>
        <div class="tree-content">
          <Tree
            :tree-data="treeData"
            :fieldNames="treeFieldNames"
            blockNode
            defaultExpandAll
            v-model:selectedKeys="selectedKeys"
            @select="handleSelectNode"
            v-if="treeData.length"
          >
            <template #title="node">
              <div class="flex items-center justify-between">
                <div style="overflow: hidden; text-overflow: ellipsis">
                  <Tooltip>
                    <template #title>{{ node.name }}</template>
                    {{ node.name }}
                    <span v-if="selectMap[node.virtualId] && selectMap[node.virtualId].length">
                      (<span class="font-bold">{{ selectMap[node.virtualId].length }}</span
                      >)
                    </span>
                  </Tooltip>
                </div>
              </div>
            </template>
          </Tree>
          <HEmpty v-else class="empty" />
        </div>
      </div>
      <div ref="tableBoxRef" class="table-box">
        <div class="flex mb-3">
          <div class="flex-1 leading-[2.5]">
            已选：<span class="theme-color" v-if="selectedInfo"
              >{{ selectMap[selectedInfo.virtualId]?.length || 0 }}
            </span>
            个设备
          </div>
          <Input
            class="w-[45%]"
            v-model:value="searchTableVal"
            placeholder="请输入"
            @change="handleSearch"
          >
            <template #prefix>
              <Icon icon="icon-park-outline:search" color="#999999" :size="14" />
            </template>
          </Input>
        </div>
        <div class="table-container">
          <BasicTable @register="registerTable" @selection-change="handleChangeSelection" />
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup name="MapInfoModal">
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { Tooltip, Input, Tree } from 'ant-design-vue';
  import Icon from '/@/components/Icon';
  import { BasicTable, useTable } from '/@/components/Table';
  import { useDebounceFn } from '@vueuse/core';

  import { getDevicePageListApi, getTechnologyUnitTreeApi } from '/@zhcz/api/device-management';
  // cloneDeep
  // import { sleep } from '/@/utils';

  import { columns } from './data';
  const treeFieldNames = {
    title: 'name',
    key: 'virtualId',
    children: 'childrenNodes',
  };
  const emits = defineEmits(['ok', 'register', 'cancel']);

  const searchVal = ref('');
  const searchTableVal = ref('');
  const treeData = ref<any[]>([]);
  const treeRawData = ref<any[]>([]);
  const selectedKeys = ref<string[]>([]);
  const selectedInfo = ref<Indexable>();
  const selectIds = ref<any>([]);
  const [registerModal, { closeModal, changeLoading }] = useModalInner(async (data) => {
    try {
      changeLoading(true);

      await init(data);
    } catch (error) {
      throw error;
    } finally {
      changeLoading(false);
    }
  });

  const [registerTable, { reload, setSelectedRowKeys }] = useTable({
    columns,
    api: getListApi,
    rowKey: 'id',
    inset: true,
    immediate: false,
    showIndexColumn: false,
    isCanResizeParent: true,
    rowSelection: {
      columnWidth: 50,
    },
  });

  const selectMap = ref({});
  async function init(data) {
    selectIds.value = data.tableSelected || [];
    selectMap.value = {};
    const trees = await getTechnologyUnitTreeApi({
      eqIds: data.tableSelected,
      showTuTypeFlag: false,
    });

    generateVirtualIds(trees);

    treeRawData.value = trees;
    treeData.value = trees;
    const treeItem = setTreeFirstNode();
    selectedInfo.value = treeItem;
    reload({
      searchInfo: {
        treeNodeId: treeItem.id,
        treeNodeType: treeItem.treeNodeType,
        treeNodePaths: treeItem.treeNodePaths,
      },
    });
    setSelectedRowKeys(selectIds.value);
    setTimeout(() => {
      selectMap.value[selectedInfo.value!.virtualId] = treeItem.eqIds;
    });
  }
  async function getListApi(data) {
    if (selectedInfo.value) {
      data.treeNodeId = selectedInfo.value.id;
      data.treeNodeType = selectedInfo.value.treeNodeType;
      data.treeNodePaths = selectedInfo.value.treeNodePaths;
    }
    const ret = await getDevicePageListApi(data);
    return ret;
  }
  function setTreeFirstNode() {
    const treeItem = treeData.value[0];
    if (!treeItem.childrenNodes || treeItem.childrenNodes.length === 0) {
      selectedKeys.value = [treeItem.virtualId];
      return treeItem;
    }

    const leavesWithDepth = collectLeaves(treeItem, 0); // 收集所有叶子节点及层级

    if (leavesWithDepth.length > 0) {
      // 找到最大深度的叶子节点
      const maxDepth = Math.max(...leavesWithDepth.map((l) => l.depth));
      const deepestLeaves = leavesWithDepth.filter((l) => l.depth === maxDepth);

      // 取第一个遇到的
      const targetLeaf = deepestLeaves[0].node;

      selectedKeys.value = [targetLeaf.virtualId]; // 设置选中
      return targetLeaf;
    }
  }

  // 辅助函数：收集所有叶子节点及其层级
  function collectLeaves(node: any, currentDepth: number): { node: any; depth: number }[] {
    const leaves: { node: any; depth: number }[] = [];
    if (node.childrenNodes.length === 0) {
      // 当前节点是叶子节点
      leaves.push({ node, depth: currentDepth });
    } else {
      // 递归子节点
      node.childrenNodes.forEach((child) => {
        leaves.push(...collectLeaves(child, currentDepth + 1));
      });
    }
    return leaves;
  }

  function generateVirtualIds(nodes: Indexable[]) {
    nodes.forEach((node) => {
      // 处理 treeNodePaths 转字符串
      const treeNodePaths = JSON.stringify(node.treeNodePaths);
      // 生成 virtualId
      node.virtualId = `${node.id}-${node.treeNodeType}-${treeNodePaths}`;
      selectMap.value[node.virtualId] = node.eqIds || [];
      // 递归处理子节点
      if (node.childrenNodes && node.childrenNodes.length > 0) {
        generateVirtualIds(node.childrenNodes);
      }
    });
  }
  const handleSearch = useDebounceFn(() => {
    const param = selectedInfo.value;

    reload({
      searchInfo: {
        keywords: searchTableVal.value,
        treeNodeId: param?.id,
        treeNodeType: param?.treeNodeType,
        treeNodePaths: param?.treeNodePaths,
      },
    });
  }, 200);
  function handleChangeSelection(v) {
    selectIds.value = v.keys;
    selectMap.value[selectedInfo.value?.virtualId] = v.keys;
  }

  // 切换树
  const handleSelectNode = (keys, e) => {
    selectedKeys.value = keys;
    selectedInfo.value = e.node;
    reload({
      searchInfo: {
        keywords: searchTableVal.value,
        treeNodeId: e.node.id,
        treeNodeType: e.node.treeNodeType,
        treeNodePaths: e.node.treeNodePaths,
      },
    });
    setSelectedRowKeys(selectMap.value[selectedInfo.value?.virtualId]);
  };

  const findNodeByName = (tree, val) => {
    if (!val) {
      treeData.value = treeRawData.value;
      return;
    }
    // const result: any[] = [];
    // function search(node) {
    //   if (node.name.includes(val)) {
    //     result.push(node);
    //   } else {
    //     if (node.childrenNodes && node.childrenNodes.length > 0) {
    //       node.childrenNodes.forEach(search);
    //     }
    //   }
    // }
    // tree.forEach(search);
    treeData.value = filterTree(tree, val);
  };

  function filterTree(nodes, val) {
    const matchedNodes = new Set();
    const parentNodes = new Set();

    // Step 1: Collect all matched nodes and their direct parents
    const traverse = (node, parent) => {
      if (node.name.includes(val)) {
        matchedNodes.add(node);
        if (parent) parentNodes.add(parent); // Only collect direct parent
      }
      if (node.childrenNodes) {
        node.childrenNodes.forEach((child) => traverse(child, node));
      }
    };

    nodes.forEach((node) => traverse(node, null));

    // Step 2: Build new tree with only direct parents and matched children
    const filteredParents = Array.from(parentNodes);
    const result = filteredParents.map((parent: Indexable) => ({
      ...parent,
      childrenNodes: parent.childrenNodes.filter((child) => matchedNodes.has(child)),
    }));

    // Add root nodes that are matched themselves (no parent)
    const rootMatches = nodes.filter((node) => matchedNodes.has(node));
    return [...result, ...rootMatches];
  }
  const handleClose = () => {
    closeModal();
    emits('cancel');
  };
  const handleOk = async () => {
    const allDevices = Object.values(selectMap.value).flat();
    const uniqueDevices = Array.from(new Set(allDevices));

    emits('ok', uniqueDevices);

    closeModal();
  };
</script>
<style lang="less" scoped>
  .container-device {
    height: 55vh;
  }

  .tree-container {
    width: 260px;
    flex-shrink: 0;
    border: 1px solid @border-color-base;
    transition: all 0.3s;
    border-radius: 4px;

    .search-container {
      padding: 16px 16px 0;
    }

    .tree-content {
      padding: 0 16px 16px;
      margin-top: 12px;
      overflow-y: auto;
      height: calc(100% - 80px);
      overflow-x: hidden;

      .empty {
        height: 100%;
      }

      :deep(.ant-tree) {
        // background: #fcfcfc;

        .ant-tree-node-content-wrapper {
          overflow: hidden;

          &.ant-tree-node-selected {
            .ant-tree-title {
              color: var(--theme-color);
            }
          }
        }
      }
    }
  }

  .table-box {
    position: relative;
    border-radius: 4px;
    flex: 1;
    width: calc(100% - 276px);
    margin-left: 16px;
    padding: 16px;
    border: 1px solid @border-color-base;

    .table-container {
      height: calc(100% - 47px);

      ::v-deep(.ant-table-wrapper) {
        padding: 0;
        margin: 0;
      }
    }
  }
</style>
