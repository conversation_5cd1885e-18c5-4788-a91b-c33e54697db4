<template>
  <div v-if="list?.length" class="warning-layer">
    <div @click="emits('itemClick', item)" v-for="item in list" :key="item" class="title">
      <img :src="warning" />
      <div class="txt" :title="'item.title'">{{ item.title }}</div>
      <Icon icon="icon-park-outline:right" :size="16" color="#fff" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { Icon } from '/@/components/Icon';
  import warning from '../../../../assets/images/warning.png';

  const emits = defineEmits(['itemClick']);
  defineProps({
    list: {
      type: Array,
      default: () => [],
    },
  });
</script>
<style lang="less" scoped>
  .warning-layer {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    // padding: 12px 12px 0;
    // background: rgba(0, 0, 0, 0.2);
    color: #fff;
    line-height: 1;
    height: 100%;
    border-radius: 4px;
    z-index: 999;
    overflow: auto;

    .title {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      padding: 0 8px;
      height: 32px;
      min-height: 32px;
      min-width: 150px;
      max-width: 300px;
      width: max-content;
      background: url(../../../../assets/images/warning-title-bg.png) center/ 100% no-repeat;
      border-radius: 4px;
      cursor: pointer;

      img {
        margin-right: 8px;
        width: 16px;
        height: 16px;
      }

      .txt {
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
</style>
