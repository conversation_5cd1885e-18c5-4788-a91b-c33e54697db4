<template>
  <div class="energy-consumption-overview-left">
    <BigBoxContainer>
      <BoxContainer style="height: calc(60% - 4px)">
        <template #header>
          <BoxHeader :title="title1" />
        </template>
        <template #content>
          <div class="container produce-data">
            <ProduceDataTop
              :length="state.produceTopData.length"
              :data-list="state.produceTopData"
            />
            <div class="chart-container">
              <!-- 耗数据曲线，同时支持日、月、年过滤 -->
              <div class="title-box">
                <div class="charts-title">{{ state.produceBottomData.title }}</div>
                <div class="select-box">
                  <Select
                    v-model:value="sludgeTime"
                    style="width: 5rem"
                    @change="handleSludgeChange"
                    class="big-screen-select"
                    popupClassName="big-screen-select-dropdown"
                  >
                    <SelectOption v-for="item in dataList" :value="item.value" :key="item.value">
                      {{ item.label }}
                    </SelectOption>
                  </Select>
                  <DatePicker
                    v-model:value="produceDate"
                    :picker="getPicker"
                    class="date-picker big-screen-date-picker"
                    popupClassName="big-screen-date-picker-dropdown"
                    placeholder="选择日期"
                    :allowClear="false"
                    :showToday="false"
                    style="width: 126px !important"
                    :disabledDate="disabledDate"
                    @change="handleProduceDateChange"
                  />
                </div>
              </div>
              <div class="chart-container-c">
                <!-- <template v-if="state.projectLoad">
                  <div style="background: unset"></div>
                </template> -->
                <template v-if="state.projectLoad">
                  <ProduceDataChart :data="state.produceBottomData" />
                </template>
                <template v-else>
                  <DataEmpty />
                </template>
              </div>
              <!-- <ProduceDataChart
                v-if="
                  state.produceBottomData &&
                  state.produceBottomData.chartOptions.series[0].data.length
                "
                :data="state.produceBottomData"
              />
              <DataEmpty v-else /> -->
            </div>
          </div>
        </template>
      </BoxContainer>
      <BoxContainer
        style="height: calc(40% - 4px)"
        :data-resource-code="indexList?.index_LB1[0]?.groupCode"
      >
        <template #header>
          <BoxHeader :title="title2">
            <template #right>
              <!-- 日生产能耗，变更为生产能耗，支持日、月、年过滤 -->
              <div class="select-box">
                <Select
                  v-model:value="sludgeTime2"
                  style="width: 5rem"
                  @change="handleSludgeChange2"
                  class="big-screen-select"
                  popupClassName="big-screen-select-dropdown"
                >
                  <SelectOption v-for="item in dataList2" :value="item.value" :key="item.value">
                    {{ item.label }}
                  </SelectOption>
                </Select>
                <DatePicker
                  v-model:value="otherDate"
                  :picker="getPicker2"
                  class="date-picker big-screen-date-picker"
                  popupClassName="big-screen-date-picker-dropdown"
                  style="width: 126px !important"
                  placeholder="选择日期"
                  :allowClear="false"
                  :showToday="false"
                  :disabledDate="disabledDate2"
                  @change="handleOtherDateChange"
                />
              </div>
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div v-if="state.consumptionData.length" class="consumption">
            <ConsumptionItem
              v-for="(item, index) in state.consumptionData"
              :className="`item${getClass(index)}`"
              :key="index"
              :data="item"
            />
          </div>
          <div v-else class="consumption">
            <DataEmpty />
          </div>
        </template>
      </BoxContainer>
    </BigBoxContainer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { flatMap } from 'lodash-es';
  import { mockDataList, mockChartData, mockConsumptionData, otherIconData } from './data';
  import { Select, SelectOption, DatePicker } from 'ant-design-vue';
  import { useIndexList, useTitleList } from '../hooks';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { getEevReturnDomain } from '/@zhcz/utils/file/url';
  import { roundAndConvertCheckNullAndUnDef } from '/@zhcz/utils/number';

  import BigBoxContainer from '../components/box-container/BigBoxContainer.vue';
  import BoxContainer from '../components/box-container/index.vue';
  import BoxHeader from '../components/box-container/BoxHeader.vue';
  import ProduceDataChart from '../components/echarts/ProduceDataChart.vue';
  import ProduceDataTop from '../components/produce-data/index.vue';
  import ConsumptionItem from '../components/consumption-item/index.vue';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';
  import DataEmpty from '../components/data-empty/index.vue';

  function disabledDate(current) {
    // 禁止选择今天以后的日期
    return current && current > dayjs().subtract(0, 'day');
  }
  function disabledDate2(current) {
    // 禁止选择今天以后的日期
    // const picker =
    //   pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    return current && current > dayjs().subtract(0, 'day');
  }
  function getClass(index) {
    let item = '';
    let coIndex = index + 1;
    switch (coIndex % 4) {
      case 1:
        item = 'On';
        break;
      case 2:
        item = 'Tw';
        break;
      case 3:
        item = 'Th';
        break;
      case 0:
        item = 'Fo';
        break;
    }
    return item;
  }
  const { indexList } = useIndexList();
  const { titleList } = useTitleList();

  const produceDate = ref(dayjs().subtract(0, 'day'));
  const otherDate = ref(dayjs().subtract(1, 'day'));
  const state = reactive({
    produceTopData: JSON.parse(JSON.stringify(mockDataList)),
    produceBottomData: JSON.parse(JSON.stringify(mockChartData)),
    consumptionData: JSON.parse(JSON.stringify(mockConsumptionData)),
    projectLoad: true,
  });
  const sludgeTime = ref<null | string>(null);
  const sludgeTime2 = ref<null | string>(null);
  const dataList = ref<{ label: string; value: string }[]>([]);
  const dataList2 = ref<{ label: string; value: string }[]>([]);
  async function getTimeList() {
    const res = await listSenceGroupByParent({
      groupCode: 'NH_QT_SJQX',
      factoryId: 1,
      platformld: 1,
    });
    if (Object.keys(res).length) {
      // sludgeTime.value = Object.keys(res)[0];
      dataList.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      sludgeTime.value = dataList.value[0]?.value || null;
      const picker =
        pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '日'];
      // produceDate.value = dayjs().subtract(1, picker);
      if (picker === 'date') {
        produceDate.value = dayjs().subtract(0, 'day');
      } else {
        produceDate.value = dayjs().subtract(0, picker);
      }
      getProduceBottomData();
    }
  }
  async function getTimeList2() {
    const res = await listSenceGroupByParent({
      groupCode: 'nhzl_scnh2.0',
      factoryId: 1,
      platformld: 1,
    });
    if (Object.keys(res).length) {
      sludgeTime2.value = Object.keys(res)?.[0];
      dataList2.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      const picker =
        pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
      // otherDate.value = dayjs().subtract(1, picker);
      if (picker === 'date') {
        otherDate.value = dayjs().subtract(1, 'day');
      } else if (picker === 'month') {
        otherDate.value = dayjs().subtract(1, picker);
      } else {
        otherDate.value = dayjs().subtract(0, picker);
      }
      getConsumptionData();
    }
  }
  getTimeList2();
  enum pickerType {
    '日' = 'date',
    '月' = 'month',
    '年' = 'year',
  }
  const getPicker = computed(() => {
    if (sludgeTime.value) {
      return pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '日'];
    } else {
      return pickerType['日'];
    }
  });
  const getPicker2 = computed(() => {
    if (sludgeTime2.value) {
      return pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    } else {
      return pickerType['日'];
    }
  });
  getTimeList();
  const handleSludgeChange = () => {
    const picker =
      pickerType[dataList.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    // produceDate.value = dayjs().subtract(1, picker);
    if (picker === 'date') {
      produceDate.value = dayjs().subtract(0, 'day');
    } else {
      produceDate.value = dayjs().subtract(0, picker);
    }
    getProduceBottomData();
  };
  const handleSludgeChange2 = () => {
    const picker =
      pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    // otherDate.value = dayjs().subtract(1, picker);
    if (picker === 'date') {
      otherDate.value = dayjs().subtract(1, 'day');
    } else if (picker === 'month') {
      otherDate.value = dayjs().subtract(1, picker);
    } else {
      otherDate.value = dayjs().subtract(0, picker);
    }
    getConsumptionData();
  };
  const title1 = computed(() => {
    return titleList.value.title_LT1;
  });
  const title2 = computed(() => {
    return titleList.value.title_LB;
  });

  //电耗统计
  const getProduceTopData = async () => {
    const tempParams = indexList.value.index_LT1[0];
    const params = {
      // startDateTime: null,
      // endDateTime: null,
      startDateTime: dayjs().startOf('month').format('YYYY-MM-DD 00:00:00'),
      endDateTime: dayjs().endOf('month').format('YYYY-MM-DD 23:59:59'),
      indexCodes: '',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      ...tempParams,
      paramsData: JSON.stringify(params),
    };

    const { data } = await callResourceFunction(paramData);
    if (data && data.length) {
      state.produceTopData = data.map((i, index) => {
        const value = i.indicatorsByTimeIntervalResp.length
          ? i.indicatorsByTimeIntervalResp[0]?.data.reduce((prev, cur) => {
              return prev + cur.value;
            }, 0)
          : 0;
        return {
          indexName: i.indicatorsByTimeIntervalResp.length
            ? i.indicatorsByTimeIntervalResp[0]?.indexName
            : mockDataList[index].indexName,
          value: roundAndConvertCheckNullAndUnDef(value, 0),
          unitName: i.indicatorsByTimeIntervalResp.length
            ? i.indicatorsByTimeIntervalResp[0]?.unitName
            : mockDataList[index].unitName,
          src: i.imgByTimeIntervalResps[0]?.sourceUniqueKey,
        };
      });
    } else {
      state.produceTopData = mockDataList;
    }
  };

  // 近七天数据曲线
  const getProduceBottomData = async () => {
    try {
      // const endDataTime = dayjs(produceDate.value).format('YYYY-MM-DD 23:59:59');
      // const tempParams = indexList.value.index_LT2[0];
      const picker =
        pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '日'];
      const startDate = dayjs(produceDate.value).startOf(picker).format('YYYY-MM-DD 00:00:00');
      const endDataTime = dayjs(produceDate.value).endOf(picker).format('YYYY-MM-DD 23:59:59');
      const tempParams = { resourceInterfaceId: '3', groupCode: sludgeTime.value };
      const params = {
        startDateTime: startDate,
        endDateTime: endDataTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };
      const { data } = await callResourceFunction(paramData);
      if (data && data.length) {
        state.projectLoad = true;
        const newData = JSON.parse(JSON.stringify(mockChartData));
        newData.title = titleList.value.title_LT2;
        if (data[0].data.length) {
          newData.chartOptions.xAxis.data = data[0].data.map((item) => {
            let row;
            if (picker === 'date') {
              row = dayjs(item.collectDateTime).format('HH:mm');
            } else if (picker === 'month') {
              row = dayjs(item.collectDateTime).format('MM-DD');
            } else if (picker === 'year') {
              row = dayjs(item.collectDateTime).format('MM') + '月';
            }
            return row;
          });
        }

        newData.chartOptions.series = data.map((item, index) => {
          const oldItem = mockChartData.chartOptions.series[index];
          let t_data = mockChartData.chartOptions.series[index].data;
          t_data = flatMap(item.data, 'value').map((item) =>
            roundAndConvertCheckNullAndUnDef(item, 0),
          );

          return {
            ...oldItem,
            name: item.indexName,
            data: t_data,
            unitName: item.unitName,
          };
        });
        state.produceBottomData = newData;
      } else {
        state.projectLoad = false;
        state.produceBottomData = mockChartData;
      }
    } catch (_) {
      state.projectLoad = false;
    }
  };
  const consumptionDataLoad = ref<Boolean>(true);
  const getConsumptionData = async () => {
    const picker =
      pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    const startDate = dayjs(otherDate.value).startOf(picker).format('YYYY-MM-DD 00:00:00');
    const endDataTime = dayjs(otherDate.value).endOf(picker).format('YYYY-MM-DD 23:59:59');
    const tempParams = { resourceInterfaceId: '3', groupCode: sludgeTime2.value };
    if (!sludgeTime2.value) {
      return;
    }
    // const tempParams = indexList.value.index_LB1[0];
    const params = {
      startDateTime: startDate,
      endDateTime: endDataTime,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      ...tempParams,
      paramsData: JSON.stringify(params),
    };
    const { data } = await callResourceFunction(paramData);
    if (data && data.length) {
      consumptionDataLoad.value = true;
      state.consumptionData = data.map((i, index) => {
        return {
          indexName: i.indexName ? i.indexName : state.consumptionData[index].indexName,
          value: i.data.length ? i.data[0]?.value : 0,
          unitName: i.unitName ? i.unitName : state.consumptionData[index].unitName,
          src: getEevReturnDomain(i.data[0]?.sourceUniqueKey) || otherIconData[index],
        };
      });
    } else {
      consumptionDataLoad.value = false;
      state.consumptionData = mockConsumptionData;
    }
  };

  const handleProduceDateChange = () => {
    getProduceBottomData();
  };

  const handleOtherDateChange = () => {
    getConsumptionData();
  };
  const getData = () => {
    getProduceTopData();
    // getProduceBottomData();
    getConsumptionData();
  };

  watch(
    () => indexList.value,
    () => {
      getData();
    },
  );
</script>

<style lang="less" scoped>
  .energy-consumption-overview-left {
    width: 100%;
    height: 100%;

    :deep(.ant-picker) {
      .ant-picker-input > input {
        color: #fff;
      }
    }

    .container {
      height: 100%;
      // padding: 0 1rem;
    }

    .produce-data {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      // padding: 0 1rem;

      .chart-container {
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;

        .chart-container-c {
          position: relative;
          flex: 1;
        }

        .title-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;

          .charts-title {
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
            line-height: 14px;
            text-align: left;
          }

          .select-box {
            flex: 1;
            display: flex;
            justify-content: end;
            gap: 0 12px;
          }
        }

        .date-picker {
          // position: absolute;
          z-index: 1000;
          // right: 0;
          // top: 0;
          // width: 160px !important;
        }
      }
    }

    .select-box {
      flex: 1;
      display: flex;
      justify-content: end;
      gap: 0 12px;
    }

    :deep(.ant-select-selection-item) {
      color: #fff;
    }

    .consumption {
      // padding: 2.5rem 1rem 0 0.5rem;
      height: 100%;
      padding-top: 16px;
      display: grid;
      gap: 1rem;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      overflow-y: auto;

      // @media screen and (min-width: 1921px) {
      //   .consumption-item {
      //     justify-content: center;
      //   }
      // }

      // @media screen and (max-width: 1440px) {
      //   padding: 2.8rem 0.5rem 0 0.5rem;
      // }

      &::-webkit-scrollbar {
        width: 0px;
        height: 0px;
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: #808695;
        border-radius: 0.25rem;
      }
    }
  }
</style>
