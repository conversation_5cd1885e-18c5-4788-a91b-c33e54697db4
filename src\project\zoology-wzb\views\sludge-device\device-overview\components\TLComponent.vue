<template>
  <div class="tlc">
    <LineSimpleCard :title="'累计处理量'" v-bind="{ empty, bottomList: indexList, loading }">
      <template #headerRight>
        <div class="header-right">
          <div>
            <Select
              v-if="dataList.length"
              v-model:value="dataType"
              palceholder="请选择"
              style="width: 100px"
              @change="handleChangeIndicator"
            >
              <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
                {{ item.label }}
              </SelectOption>
            </Select>
          </div>
          <div>
            <Select
              style="width: 80px"
              v-model:value="dateType"
              @change="getIndicatorList"
              v-if="dateList.length"
            >
              <SelectOption v-for="item in dateList" :key="item.value" :value="item.value">
                {{ item.label }}
              </SelectOption>
            </Select>
          </div>

          <DatePicker
            @change="getData"
            v-if="dateList.find((item) => item.value === dateType)?.label === '日'"
            valueFormat="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model:value="date1"
            placeholder="请选择"
            style="width: 200px"
            :allowClear="false"
            :disabledDate="disabledDate"
          />
          <DatePicker
            @change="getData"
            v-if="dateList.find((item) => item.value === dateType)?.label === '月'"
            style="width: 200px"
            v-model:value="date"
            picker="month"
            valueFormat="YYYY-MM"
            :disabledDate="disabledMonthDate"
          />
        </div>
      </template>
    </LineSimpleCard>
  </div>
</template>
<script lang="ts" setup>
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import { LineSimpleCard } from '/@zhcz/components/HLCardComponent';
  import { ref, nextTick } from 'vue';
  import dayjs from 'dayjs';
  // import { mockRingRatioData } from '../data';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';
  const loading = ref(false);
  const empty = ref(false);

  const date1 = ref(dayjs().format('YYYY-MM-DD'));
  const date = ref(dayjs().subtract(0, 'month').format('YYYY-MM'));
  const dataType = ref<any>(null);
  const dateType = ref<any>(null);
  const dateList = ref<OptionItem[]>([]);
  type OptionItem = {
    label: string;
    value: string;
  };
  const dataList = ref<OptionItem[]>([]);
  async function getIndicatorList() {
    try {
      empty.value = false;
      loading.value = true;
      let startDateTime = dayjs(date1.value).format('YYYY-MM-DD 00:00:00');
      let endDateTime = dayjs(date1.value).format('YYYY-MM-DD 23:59:59');
      let paramsPre = {
        startDateTime:
          dateList.value.find((item) => item.value === dateType.value)?.label === '日'
            ? startDateTime
            : dayjs(date.value).subtract(0, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'),
        endDateTime:
          dateList.value.find((item) => item.value === dateType.value)?.label === '日'
            ? endDateTime
            : dayjs(date.value).subtract(0, 'month').endOf('month').format('YYYY-MM-DD 23:59:59'),
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      let paramDataPre = {
        resourceInterfaceId: '3',
        groupCode: dateType.value,
        paramsData: JSON.stringify(paramsPre),
      };
      const res = await callResourceFunction(paramDataPre);
      if (res.data.length) {
        empty.value = false;
        loading.value = false;
        dataType.value = res.data.find((item) => item.indexCode)?.indexCode || '';
        dataList.value = res.data.map((item) => ({
          value: item.indexCode,
          label: item.indexName,
        }));
        getData();
      } else {
        loading.value = false;
        empty.value = true;
      }
    } catch (error) {
      loading.value = false;
      empty.value = true;
      console.log('error', error);
    }
  }
  async function getTimeList() {
    try {
      const res = await listSenceGroupByParent({
        groupCode: 'yhzl2_qst',
        factoryId: 1,
        platformld: 1,
      });
      if (Object.keys(res).length) {
        empty.value = false;
        dateType.value = Object.keys(res)[0];
        dateList.value = Object.keys(res).map((item) => ({
          value: item,
          label: res[item],
        }));
        getIndicatorList();
      } else {
        empty.value = true;
      }
    } catch (err) {
      empty.value = true;
    }
  }
  function handleSetVisitChart() {}

  const indexList = ref<any[]>([]);

  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }
  function disabledMonthDate(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('day');
  }

  const isEmptyData = ref(true);
  const getData = async () => {
    try {
      loading.value = true;
      empty.value = false;
      // 当日、当月
      let startDateTime = dayjs(date1.value).format('YYYY-MM-DD 00:00:00');
      let endDateTime = dayjs(date1.value).format('YYYY-MM-DD 23:59:59');
      let paramsPre = {
        startDateTime:
          dateList.value.find((item) => item.value === dateType.value)?.label === '日'
            ? startDateTime
            : dayjs(date.value).subtract(0, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'),
        endDateTime:
          dateList.value.find((item) => item.value === dateType.value)?.label === '日'
            ? endDateTime
            : dayjs(date.value).subtract(0, 'month').endOf('month').format('YYYY-MM-DD 23:59:59'),
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      let paramDataPre = {
        resourceInterfaceId: '3',
        groupCode:
          dateList.value.find((item) => item.value === dateType.value)?.label === '日'
            ? 'yhzl2_qst_r'
            : 'yhzl2_qst_y',
        paramsData: JSON.stringify(paramsPre),
      };
      const preData = await callResourceFunction(paramDataPre);

      // 前一日、前一月
      startDateTime = dayjs(date1.value).subtract(1, 'day').format('YYYY-MM-DD 00:00:00');
      endDateTime = dayjs(date1.value).subtract(1, 'day').format('YYYY-MM-DD 23:59:59');
      paramsPre = {
        startDateTime:
          dateList.value.find((item) => item.value === dateType.value)?.label === '日'
            ? startDateTime
            : dayjs(date.value).subtract(1, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'),
        endDateTime:
          dateList.value.find((item) => item.value === dateType.value)?.label === '日'
            ? endDateTime
            : dayjs(date.value).subtract(1, 'month').endOf('month').format('YYYY-MM-DD 23:59:59'),
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      paramDataPre = {
        resourceInterfaceId: '3',
        groupCode:
          dateList.value.find((item) => item.value === dateType.value)?.label === '日'
            ? 'yhzl2_qst_r'
            : 'yhzl2_qst_y',
        paramsData: JSON.stringify(paramsPre),
      };
      const preDataBefore = await callResourceFunction(paramDataPre);
      loading.value = false;
      let dataOne;
      let dataTwo;
      if (preData && preData.data.length && preDataBefore && preDataBefore.data.length) {
        dataOne = preData.data.find((item: any) => item.indexCode === dataType.value);
        dataTwo = preDataBefore.data.find((item: any) => item.indexCode === dataType.value);
        // console.log('单耗趋势data', data);
        let XAxisData: string[] = [];
        if (dateList.value.find((item) => item.value === dateType.value)?.label === '日') {
          let x = 0;
          while (x < 24) {
            if (x < 10) {
              XAxisData.push(`0${x}:00`);
            } else {
              XAxisData.push(`${x}:00`);
            }
            x++;
          }
        } else if (dateList.value.find((item) => item.value === dateType.value)?.label === '月') {
          let x = 1;
          let maxD;
          if (
            dayjs(date.value).endOf('month').format('DD') >
            dayjs(date.value).subtract(1, 'month').endOf('month').format('DD')
          ) {
            maxD = dayjs(date.value).endOf('month').format('DD');
          } else {
            maxD = dayjs(date.value).subtract(1, 'month').endOf('month').format('DD');
          }
          maxD = Number(maxD) + 1;
          while (x < maxD) {
            if (x < 10) {
              XAxisData.push(`0${x}`);
            } else {
              XAxisData.push(`${x}`);
            }
            x++;
          }
        }
        interface arrItem {
          collectDateTime?: string;
          value?: number;
        }
        const copyDataArr = [addXais(XAxisData, dataOne.data), addXais(XAxisData, dataTwo.data)];
        function addXais(data: string[], data2: []) {
          return data.map((item) => {
            const copyArr: arrItem[] = data2.filter((i: arrItem) => {
              if (dateList.value.find((item) => item.value === dateType.value)?.label === '日') {
                return item === dayjs(i.collectDateTime).format('HH:mm');
              } else if (
                dateList.value.find((item) => item.value === dateType.value)?.label === '月'
              ) {
                return item === dayjs(i.collectDateTime).format('DD');
              }
            });
            return copyArr.length > 0 ? copyArr[0]?.value : '';
          });
        }
        const XAxis = XAxisData.map((i) => {
          return dateList.value.find((item) => item.value === dateType.value)?.label === '日'
            ? i
            : `${i}日`;
        });
        indexList.value = [];
        [dataOne, dataTwo].forEach((value, index) => {
          const { indexCode, unitName } = value;
          let indexName = '';
          if (index === 0) {
            indexName =
              dateList.value.find((item) => item.value === dateType.value)?.label === '日'
                ? '今日单耗'
                : '当月单耗';
          } else if (index === 1) {
            indexName =
              dateList.value.find((item) => item.value === dateType.value)?.label === '日'
                ? '昨日单耗'
                : '上月单耗';
          }
          indexList.value.push({
            unitName,
            indexName,
            indexCode,
            data: copyDataArr[index],
            XAxis,
          });
        });
        console.log('indexList.value', XAxisData, indexList.value);
        if (indexList.value[0].data?.length) {
          isEmptyData.value = false;
          empty.value = false;
          await nextTick();
          handleSetVisitChart();
        } else {
          isEmptyData.value = true;
          empty.value = true;
        }
      } else {
        isEmptyData.value = true;
        empty.value = true;
      }
    } catch (err) {
      isEmptyData.value = true;
      empty.value = true;
      loading.value = false;
    }
  };

  const handleChangeIndicator = () => {
    getData();
  };
  getTimeList();
</script>
<style lang="less" scoped>
  .tlc {
    background-color: #fcfcfc;
    height: 100%;
    overflow: hidden;
    border-radius: 4px;

    .header-right {
      display: flex;
      gap: 0 16px;
    }
  }
</style>
