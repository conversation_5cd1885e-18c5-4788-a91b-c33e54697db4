<template>
  <div style="height: 100%">
    <LineSimpleCard :title="'累计处理量'" v-bind="{ empty, bottomList: indexList, loading }" />
  </div>
</template>
<script lang="ts" setup>
  import { LineSimpleCard } from '/@zhcz/components/HLCardComponent';
  import { ref, nextTick } from 'vue';
  import dayjs from 'dayjs';
  import { getDataStatByParentGroupCodeApi } from '/@zhcz/api/scenes-group';
  const loading = ref(false);
  const empty = ref(false);

  const indexList = ref<any[]>([
    {
      unitName: '吨',
      indexName: '当月单耗',
      // indexCode: 'Z_YJSH_S_D_SUM',
      data: ['', 623.75, 1096.52],
      XAxis: ['01月', '02月', '03月'],
    },
    {
      unitName: 'kg',
      indexName: '上月单耗',
      indexCode: 'Z_YJSH_S_D_SUM',
      data: [
        1102.69,
        1068.56,
        1064.96,
        1077.86,
        1070.22,
        '',
        665.52,
        1097.7,
        '',
        589.02,
        1061.3,
        1085.87,
        1071.97,
        1099.51,
        1082.97,
        1099.86,
        1029.35,
        806.37,
        1064.11,
        1063.14,
        1078.1,
        1067.76,
        1073.21,
        949.83,
        1082.21,
        1102.4,
        1095.6,
        1114.95,
        1081.94,
        1084.89,
        1064.21,
      ],
      XAxis: [
        '01日',
        '02日',
        '03日',
        '04日',
        '05日',
        '06日',
        '07日',
        '08日',
        '09日',
        '10日',
        '11日',
        '12日',
        '13日',
        '14日',
        '15日',
        '16日',
        '17日',
        '18日',
        '19日',
        '20日',
        '21日',
        '22日',
        '23日',
        '24日',
        '25日',
        '26日',
        '27日',
        '28日',
        '29日',
        '30日',
        '31日',
      ],
    },
  ]);
  // 基础数据
  const indexItemTemp = {
    unitName: '吨',
    indexName: '',
    data: [],
    XAxis: [],
  };

  const months = dayjs().subtract(11, 'month').format('YYYY-MM-01 HH:mm:ss'); //当前往前11个月的月份
  const isEmptyData = ref(true);
  const getData = async () => {
    try {
      loading.value = true;
      empty.value = false;
      const paramDataPre = {
        groupCode: 'SBWNCLL',
        startTime: months,
        endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      };
      const res = await getDataStatByParentGroupCodeApi(paramDataPre);
      loading.value = false;
      if (res && res?.length) {
        if (res[0].indicatorDataList?.length) {
          isEmptyData.value = false;
          empty.value = false;
          await nextTick();
        } else {
          isEmptyData.value = true;
          empty.value = true;
        }
        const data = [];
        const XAxis = [];
        indexList.value = res.map((item) => {
          const obj = {
            ...indexItemTemp,
          };
          obj.indexName = item.groupName;
          item.indicatorDataList?.forEach?.((indicator) => {
            data.push(indicator.value || '');
            XAxis.push(`${indicator.time?.split?.('-')?.[1]}月`);
          });
          obj.data = data;
          obj.XAxis = XAxis;
          return obj;
        });
      } else {
        isEmptyData.value = true;
        empty.value = true;
      }
    } catch (err) {
      isEmptyData.value = true;
      empty.value = true;
      loading.value = false;
    }
  };

  getData();
</script>
