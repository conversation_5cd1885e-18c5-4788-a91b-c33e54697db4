import { FormSchema } from '/@/components/Table';
import { BasicColumn } from '/@/components/Table';
import { MODE } from '/@/components/CodeEditor';

/* 设备列表 */
export const columns: BasicColumn[] = [
  {
    title: '业务名称',
    dataIndex: 'configName',
    width: 100,
  },
  {
    title: '数据类型',
    dataIndex: 'dataTypeVal',
    width: 100,
  },
  {
    title: '修改人',
    dataIndex: 'updateBy',
    customRender: ({ record }) => {
      return record?.updateBy || record?.createBy;
    },
    width: 100,
  },
  {
    title: '修改时间',
    dataIndex: 'updateTimeStr',
    customRender: ({ record }) => {
      return record?.updateTimeStr || record?.createTimeStr;
    },
    width: 100,
  },
];
/* 设备列表搜索 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'configName',
    label: '业务名称',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/* 新增、编辑配置弹框*/
export const editStyleModalSchemas: FormSchema[] = [
  {
    slot: 'color',
    field: 'color',
    component: 'Input',
    label: '颜色选择器',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'detailStyle',
    label: '样式内容',
    component: 'CodeEditor',
    colProps: { lg: 24 },
    defaultValue: '{}',
    required: true,
    helpAfter: true,
    helpMessage: [
      '{',
      '"color": "#333", /* 设置字体颜色为深灰色 */',
      '"background-color": "#f0f0f0", /* 设置背景颜色为浅灰色 */',
      '"border": "1px solid #ccc" /* 设置边框颜色为灰色 */',
      '}',
    ],
    componentProps: () => {
      return {
        rows: 4,
        class: 'h-40',
        mode: MODE.JSON,
      };
    },
  },
];
