<template>
  <!-- style="max-width: 1125px" -->
  <div :class="getWrapClass" :style="warpStyle">
    <Tabs
      type="editable-card"
      size="small"
      :animated="false"
      :hideAdd="true"
      :tabBarGutter="3"
      :activeKey="activeKeyRef"
      @change="handleChange"
      @edit="handleEdit"
    >
      <!-- <TabPane :closable="!(item && item.meta && item.meta.affix)"> -->
      <TabPane
        class="tab-name"
        v-for="item in getTabsState"
        :key="item.query ? item.fullPath : item.path"
      >
        <template #tab>
          <TabContent :tabItem="item" />
        </template>
      </TabPane>

      <template #rightExtra>
        <div :class="`${prefixCls}-extra-content mt-1 -mr-4.5`">
          <FavoritesPage
            v-if="
              projectCode === 'zhcz' ||
              projectCode === 'hlxb' ||
              projectCode === 'aoa' ||
              projectCode === 'STDD' ||
              projectCode === 'WNSB'
            "
          />
          <TabRedo v-if="getShowRedo" :theme="router.currentRoute.value.meta.bgType" />
          <TabContent
            isExtra
            :tabItem="$route"
            v-if="getShowQuick"
            :theme="router.currentRoute.value.meta.bgType"
          />
          <FoldButton v-if="getShowFold" :theme="router.currentRoute.value.meta.bgType" />
        </div>
      </template>
    </Tabs>
  </div>
</template>
<script lang="ts">
  import type { RouteLocationNormalized, RouteMeta } from 'vue-router';

  import { defineComponent, computed, unref, ref } from 'vue';

  import { Tabs } from 'ant-design-vue';
  import TabContent from './components/TabContent.vue';
  import FoldButton from './components/FoldButton.vue';
  import TabRedo from './components/TabRedo.vue';
  import FavoritesPage from '/@zhcz/layouts/header/FavoritesPage.vue';

  import { useGo } from '/@/hooks/web/usePage';
  import { TabsTypeEnum } from '/@/enums/menuEnum';

  import { useMultipleTabStore } from '/@/store/modules/multipleTab';
  import { useUserStore } from '/@/store/modules/user';

  import { initAffixTabs, useTabsDrag } from './useMultipleTabs';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';

  import { REDIRECT_NAME } from '/@/router/constant';
  import { listenerRouteChange } from '/@/logics/mitt/routeChange';

  import { useRouter } from 'vue-router';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';

  import { useDomainStoreWithOut } from '/@/store/modules/domain';

  export default defineComponent({
    name: 'MultipleTabs',
    components: {
      TabRedo,
      FoldButton,
      Tabs,
      TabPane: Tabs.TabPane,
      TabContent,
      FavoritesPage,
    },
    setup() {
      const affixTextList = initAffixTabs();
      const activeKeyRef = ref('');

      useTabsDrag(affixTextList);
      const tabStore = useMultipleTabStore();
      const userStore = useUserStore();
      const router = useRouter();
      const { getHasTopMenu } = useMenuSetting();

      const { prefixCls } = useDesign('multiple-tabs');
      const go = useGo();
      const { getShowQuick, getShowRedo, getShowFold, getClass, getCardTabsType, getTabsType } =
        useMultipleTabSetting();

      const getTabsState = computed(() => {
        return tabStore.getTabList.filter((item) => !item.meta?.hideTab);
      });

      const unClose = computed(() => unref(getTabsState).length === 1);

      const getWrapClass = computed(() => {
        const isCardType = unref(getCardTabsType);
        const isTransitionCard = unref(getTabsType) === TabsTypeEnum.T_CARD;
        const isRadiousCard = unref(getTabsType) === TabsTypeEnum.RADIUS_CARD;

        const { tabTheme, bgType } = router.currentRoute.value.meta;

        return [
          prefixCls,
          {
            [`${prefixCls}--card-type`]: isCardType,
            [`${prefixCls}--card-type_transition`]: isTransitionCard,
            [`${prefixCls}--card-type_radius`]: isRadiousCard,
            [`${prefixCls}--hide-close`]: unref(unClose),
            [`${prefixCls}--no-top-menu`]: !unref(getHasTopMenu),
          },

          unref(getClass),
          { 'tabs-dark': tabTheme === 'dark' },
          // 0 浅色主题 1深色主题
          {
            'tabs-custom': bgType === 1,
            'tabs-custom_radius': isRadiousCard && bgType === 1,
          },
        ];
      });
      const warpStyle = computed(() => {
        const { bgType } = router.currentRoute.value.meta;
        return {
          '--tab-split-bg-color': bgType === 1 ? '#6f7c95' : '#e6ecf3',
          '--tab-split-color': bgType === 1 ? 'rgba(255, 255, 255, 0.32)' : '#c2c2c2',
        };
      });

      listenerRouteChange((route) => {
        const { name } = route;
        if (name === REDIRECT_NAME || !route || !userStore.getToken) {
          return;
        }

        const { path, fullPath, meta = {} } = route;

        const { currentActiveMenu, hideTab } = meta as RouteMeta;
        const isHide = !hideTab ? null : currentActiveMenu;
        const p = isHide || fullPath || path;

        if (activeKeyRef.value !== p) {
          activeKeyRef.value = p as string;
        }

        if (isHide) {
          const findParentRoute = router
            .getRoutes()
            .find((item) => item.path === currentActiveMenu);
          findParentRoute && tabStore.addTab(findParentRoute as unknown as RouteLocationNormalized);
        } else {
          tabStore.addTab(unref(route));
        }
      });

      function handleChange(activeKey: any) {
        activeKeyRef.value = activeKey;

        go(activeKey, false);
      }

      // Close the current tab
      function handleEdit(targetKey: string) {
        // Added operation to hide, currently only use delete operation
        if (unref(unClose)) {
          return;
        }

        tabStore.closeTabByKey(targetKey, router);
      }

      const domainStore = useDomainStoreWithOut();
      const projectCode = computed(() => domainStore.getProjectCode);

      return {
        getWrapClass,
        warpStyle,
        handleEdit,
        handleChange,
        activeKeyRef,
        getTabsState,
        getShowQuick,
        getShowRedo,
        getShowFold,
        router,
        prefixCls,
        projectCode,
      };
    },
  });
</script>
<style lang="less">
  @import './index.less';
</style>
