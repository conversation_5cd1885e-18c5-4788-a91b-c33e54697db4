<template>
  <div class="energy-consumption-overview-right">
    <BigBoxContainer>
      <BoxContainer
        style="height: calc(48% - 4px)"
        :data-resource-code="indexList?.index_RT[0]?.groupCode"
      >
        <template #header>
          <!-- 分类用电 -->
          <BoxHeader :title="title1">
            <template #right>
              <!-- 日能耗用电比例，变更为分类用电比例，图表中显示具体数值同时也显示占比，同时支持日、月、年过滤 -->
              <div class="select-box">
                <Select
                  v-model:value="sludgeTime"
                  style="width: 5rem"
                  @change="handleSludgeChange"
                  class="big-screen-select"
                  popupClassName="big-screen-select-dropdown"
                >
                  <SelectOption v-for="item in dataList" :value="item.value" :key="item.value">
                    {{ item.label }}
                  </SelectOption>
                </Select>
                <DatePicker
                  v-model:value="proportionDate"
                  :picker="getPicker"
                  class="date-picker big-screen-date-picker"
                  popupClassName="big-screen-date-picker-dropdown"
                  style="width: 126px !important"
                  placeholder="选择日期"
                  :allowClear="false"
                  :showToday="false"
                  :disabledDate="disabledDate"
                  @change="handleElectricDateChange"
                />
              </div>
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div class="container electricity-rate">
            <!-- <template v-if="state.piedataLoad"> <div style="background: unset"></div></template> -->
            <template v-if="state.piedataLoad">
              <ElectricityProportionChart :data="state.proportionData" />
            </template>
            <template v-else>
              <DataEmpty />
            </template>
          </div>
        </template>
      </BoxContainer>
      <BoxContainer
        style="height: calc(52% - 4px)"
        :data-resource-code="indexList?.index_RB[0]?.groupCode"
      >
        <template #header>
          <BoxHeader :title="title2">
            <template #right>
              <!-- 能耗数据，变更为能耗趋势数据，近30天下拉框，变更为近一周、近一月、近一年过滤 -->
              <Select
                v-model:value="dateType"
                style="width: 120px"
                @change="handleEnergyDateChange"
                class="big-screen-select"
                popupClassName="big-screen-select-dropdown"
              >
                <SelectOption v-for="item in timeList" :value="item.value" :key="item.value">
                  {{ item.label }}
                </SelectOption>
              </Select>
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div class="container energy">
            <!-- <template v-if="state.consumptioLoad">
              <div style="background: unset"></div>
            </template> -->
            <template v-if="state.consumptioLoad">
              <WaterQualityChart :data="state.consumptionData" :time-type="dateType" />
            </template>
            <template v-else>
              <DataEmpty />
            </template>
          </div>
        </template>
      </BoxContainer>
    </BigBoxContainer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { flatMap } from 'lodash-es';
  import BigBoxContainer from '../components/box-container/BigBoxContainer.vue';
  import BoxContainer from '../components/box-container/index.vue';
  import BoxHeader from '../components/box-container/BoxHeader.vue';
  import ElectricityProportionChart from './components/echarts/ElectricityProportionChart.vue';
  import WaterQualityChart from '../components/echarts/WaterQualityChart.vue';
  import DataEmpty from '../components/data-empty/index.vue';
  import { useIndexList, useTitleList } from '../hooks';
  import { mockElectricityChartData, mockEnergyChartData, timeList } from './data';
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';

  function disabledDate(current) {
    // 禁止选择今天以后的日期
    // const picker =
    //   pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '日'];
    return current && current > dayjs().subtract(0, 'day');
  }

  const { indexList } = useIndexList();
  const { titleList } = useTitleList();

  const proportionDate = ref(dayjs().subtract(1, 'day'));
  const dateType = ref('7');

  const state = reactive({
    proportionData: {},
    piedataLoad: true,
    consumptionData: {},
    consumptioLoad: true,
  });

  const title1 = computed(() => {
    return titleList.value.title_RT;
  });
  const title2 = computed(() => {
    return titleList.value.title_RB1;
  });
  const sludgeTime = ref<null | string>(null);
  const dataList = ref<{ label: string; value: string }[]>([]);
  async function getTimeList() {
    const res = await listSenceGroupByParent({
      groupCode: 'R_NH_YD',
      factoryId: 1,
      platformld: 1,
    });
    // console.log('res.data', res);
    if (Object.keys(res).length) {
      sludgeTime.value = Object.keys(res)[0];
      dataList.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      // sludgeTime.value = dataList.value[0]?.value || null;
      const picker =
        pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '日'];
      if (picker === 'date') {
        proportionDate.value = dayjs().subtract(1, 'day');
      } else if (picker === 'month') {
        proportionDate.value = dayjs().subtract(1, picker);
      } else {
        proportionDate.value = dayjs().subtract(0, picker);
      }
      getProportionData();
    }
  }
  enum pickerType {
    '日' = 'date',
    '月' = 'month',
    '年' = 'year',
  }
  const getPicker = computed(() => {
    if (sludgeTime.value) {
      return pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '日'];
    } else {
      return pickerType['日'];
    }
  });
  getTimeList();
  const handleSludgeChange = () => {
    const picker =
      pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '日'];
    // proportionDate.value = dayjs().subtract(0, picker);
    if (picker === 'date') {
      proportionDate.value = dayjs().subtract(1, 'day');
    } else if (picker === 'month') {
      proportionDate.value = dayjs().subtract(1, picker);
    } else {
      proportionDate.value = dayjs().subtract(0, picker);
    }
    getProportionData();
  };
  const getProportionData = async () => {
    try {
      if (!sludgeTime.value) {
        return;
      }
      const picker =
        pickerType[dataList.value.find((i) => i.value === sludgeTime.value)?.label || '日'];
      const startDate = dayjs(proportionDate.value).startOf(picker).format('YYYY-MM-DD 00:00:00');
      const endDataTime = dayjs(proportionDate.value).endOf(picker).format('YYYY-MM-DD 23:59:59');
      const tempParams = {
        resourceInterfaceId: '1914928351570112514',
        groupCode: sludgeTime.value,
      };
      const params = {
        startDateTime: startDate,
        endDateTime: endDataTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        jsConvert: true,
        paramsData: JSON.stringify(params),
      };
      const { data } = await callResourceFunction(paramData);
      if (data && data.length) {
        const seriesData = data.map((i) => {
          return {
            name: i.indexName,
            value: i.value || '--',
            unitName: i.unitName,
            totalvalue: i.totalvalue || '--',
          };
        });

        state.proportionData = {
          series: [{ data: seriesData }],
        };
        state.piedataLoad = true;
      } else {
        state.piedataLoad = false;
        state.proportionData = mockElectricityChartData;
      }
    } catch (_) {
      state.piedataLoad = false;
    }
  };

  const getConsumptionData = async () => {
    try {
      const collDate = Number(dateType.value) - 1;
      const tempParams = indexList.value.index_RB[0];
      const endDataTime = dayjs().subtract(0, 'd').format('YYYY-MM-DD 23:59:59');
      const params = {
        startDateTime: dayjs(endDataTime).subtract(collDate, 'd').format('YYYY-MM-DD 00:00:00'),
        endDateTime: endDataTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
        type: '3',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };
      const { data } = await callResourceFunction(paramData);
      // state.consumptioLoad = false;
      if (data && data.length) {
        const newData = JSON.parse(JSON.stringify(mockEnergyChartData));
        newData.title = titleList.value.title_RB1;
        newData.chartOptions.xAxis.data = data[0].data.map((item) =>
          dayjs(item.collectDateTime).format('MM-DD'),
        );

        newData.chartOptions.series = data.map((item, index) => {
          const oldItem = mockEnergyChartData.chartOptions.series[index];
          let t_data = mockEnergyChartData.chartOptions.series[index]?.data;
          if (item.data.length) {
            t_data = flatMap(item.data, 'value');
          }
          return {
            ...oldItem,
            name: item.indexName,
            data: t_data,
            unitName: item.unitName,
          };
        });
        state.consumptionData = newData;
        state.consumptioLoad = true;
      } else {
        state.consumptioLoad = false;
        state.consumptionData = [];
      }
    } catch (_) {
      state.consumptioLoad = false;
    }
  };

  const getData = () => {
    // getProportionData();
    getConsumptionData();
  };

  const handleElectricDateChange = () => {
    getProportionData();
  };

  const handleEnergyDateChange = () => {
    getConsumptionData();
  };

  watch(
    () => indexList.value,
    () => {
      getData();
    },
  );
</script>

<style lang="less" scoped>
  .energy-consumption-overview-right {
    width: 100%;
    height: 100%;

    .select-box {
      flex: 1;
      display: flex;
      justify-content: end;
      gap: 0 12px;
    }

    :deep(.ant-select-selection-item) {
      color: #fff;
    }

    .container {
      position: relative;
      padding-top: 1rem;
      height: 100%;
    }

    .electricity {
      padding: 1rem 0.5rem 0.5rem 1rem;

      @media screen and (max-height: 900px) {
        padding: 1rem 0.5rem 0 1rem;
      }
    }

    .electricity-rate {
      // padding: 0rem 0.5rem 0.5rem 1rem;
      // @media screen and (max-height: 900px) {
      //   padding: 0rem 0.5rem 0 1rem;
      // }
      // padding: 0rem 0rem 0.5rem 0rem;
      // @media screen and (max-height: 900px) {
      //   padding: 0rem 0rem 0 0rem;
      // }
    }

    :deep(.ant-picker) {
      .ant-picker-input > input {
        color: #fff;
      }
    }

    :deep(.ant-select-single) {
      .ant-select-selector {
        .ant-select-selection-item {
          color: #fff;
        }
      }
    }
  }
</style>
