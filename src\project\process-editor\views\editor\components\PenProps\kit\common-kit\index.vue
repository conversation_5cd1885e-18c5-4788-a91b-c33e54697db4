<template>
  <div>
    <a-form
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
      :colon="false"
      labelAlign="left"
      autocomplete="off"
    >
      <a-form-item label="关联指标">
        <a-button style="width: 100%" @click="handelIndicator"> 配置 </a-button>
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 24 }" v-show="tags?.length" class="tag-item">
        <a-space direction="vertical" style="width: 100%; overflow: hidden">
          <div
            v-for="(item, tagIndex) in tags"
            :key="tagIndex"
            class="item"
            style="width: 100%; display: flex; gap: 16px"
          >
            <a-tag style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ item.indicatorName }}({{ item.indicatorCode }})
            </a-tag>
            <span class="text" @click="copy(item.indicatorCode)">复 制</span>
          </div>
        </a-space>
      </a-form-item>
      <a-form-item label="呈现表达式">
        <a-button style="width: 100%" @click="codeModalVisible = true"> ... </a-button>
      </a-form-item>

      <a-upload
        v-model:file-list="fileList"
        :action="uploadUrl"
        list-type="picture-card"
        class="avatar-uploader"
        :before-upload="beforeUpload"
        :headers="{
          Authorization: `${getToken()}`,
          'X-De-Token': `${getToken()}`,
          'Tenant-Id': `${tenantId}`,
        }"
        @preview="handlePreview"
      >
        <div v-if="fileList.length < 8">
          <plus-outlined />
          <div style="margin-top: 8px">上传</div>
        </div>
        <a-modal
          :open="previewVisible"
          :title="previewTitle"
          :footer="null"
          @cancel="handlePreviewCancel"
        >
          <img alt="example" style="width: 100%" :src="previewImage" />
        </a-modal>
      </a-upload>
    </a-form>

    <CodeEditorModal
      ref="editorRef"
      :show="codeModalVisible"
      @confirm="codeModalVisible = false"
      @cancel="codeModalVisible = false"
      @createTemplate="createTemplate"
      :activePen="activePen"
      :value="displayScript"
      @change="displayScript = $event"
    />
    <TagAllModal @register="registerTagModal" @success="handleAddIndicatorApi" setInitCheckedList />
  </div>
</template>

<script>
  import CodeEditorModal from '/@process-editor/views/editor/components/PenProps/CodeEditorModal.vue';
  import TagAllModal from '/@zhcz/views/device-management/device-assets/equipment-ledger-new/modules/sensor/components/TagAllModal.vue';
  import { Form, FormItem, Space, Tag, Upload, Modal } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';

  import { useModal } from '/@/components/Modal';
  import { updatePropData } from '../utils';
  import { isImgPen } from '/@process-editor/core/share';
  const AForm = Form;
  const AFormItem = FormItem;
  const ASpace = Space;
  const ATag = Tag;
  const AUpload = Upload;
  const AModal = Modal;

  export default {};
</script>

<script setup>
  import { ref, watch, onMounted } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getToken } from '/@/utils/auth';
  import { TENANTID_KEY } from '/@/enums/cacheEnum';
  import { createLocalStorage } from '/@/utils/cache';
  import { addResourcePrefix, deleteResourcePrefix } from '/@process-editor/utils/index';

  import useUpload from '/@process-editor/hooks/useUpload';
  const ls = createLocalStorage();

  const tenantId = ls.get(TENANTID_KEY) || '';

  const props = defineProps({
    data: {
      type: Object,
      default: () => {},
    },
    index: {
      type: Number,
      default: 0,
    },
    activePen: {
      type: Object,
      default: () => {},
    },
    script: {
      type: String,
      default: '',
    },
    productDataInfos: {
      type: Array,
      default: () => [],
    },
    update: {
      type: Boolean,
      required: true,
    },
  });

  const fileList = ref([]);
  const {
    uploadUrl,
    previewVisible,
    previewImage,
    previewTitle,
    beforeUpload,
    handlePreview,
    handlePreviewCancel,
  } = useUpload();

  const emits = defineEmits(['update:data', 'update:script', 'update:productDataInfos']);

  const { createMessage } = useMessage();

  const [registerTagModal, { openModal }] = useModal();
  const tags = ref([]);
  const displayScript = ref('');
  const editorRef = ref();
  const UPDATE_DATA = 'update:data';

  watch(
    () => props.update,
    async (newVal) => {
      if (newVal) {
        initData();
        updataFileList();
      }
    },
    {
      immediate: true,
    },
  );

  function updataFileList() {
    if (Array.isArray(props.data.businessData?.images)) {
      fileList.value = props.data.businessData.images.map((i, index) => ({
        uid: index,
        name: i.imageName,
        status: 'done',
        url: addResourcePrefix(i.imagePath),
      }));
    } else {
      fileList.value = [];
    }
  }
  watch(
    () => fileList.value,
    (newVal) => {
      updateFormDataByProp((data) => {
        data.businessData.images = newVal
          .map((i) => {
            let url = i.url || i.response?.data;
            if (!url) return;
            return {
              imagePath: deleteResourcePrefix(url),
              ImageName: '',
            };
          })
          .filter((i) => i);
      });
    },
  );

  watch(
    () => displayScript.value,
    () => {
      emits('update:script', displayScript.value);
    },
  );

  onMounted(() => {
    initData();
    updataFileList();
  });

  function initData() {
    displayScript.value = props.script || '';
    editorRef.value?.setValue?.(props.script);
    if (!props.activePen?.id || !meta2d) return;
    const pens = meta2d.find(props.activePen.id);
    if (Array.isArray(pens)) {
      const pen = pens[0];
      tags.value = pen.dataForm?.indicatorCodes || [];
      updateFormDataByProp((data) => {
        data.positionX = pen.x;
        data.positionY = pen.y;
      });
    }
  }

  const handleAddIndicatorApi = async (list) => {
    tags.value =
      list.map((v) => ({
        indicatorCode: v.indicatorCode,
        indicatorName: v.indicatorName,
        sourceUniqueKey: v.sourceUniqueKey,
        displayName: v.displayName,
      })) || [];

    meta2d.setValue({
      id: props.activePen.id,
      dataForm: {
        indicatorCodes: tags.value,
      },
    });
    emits(
      'update:productDataInfos',
      tags.value.map((item) => ({
        dataUniqueId: item.indicatorCode,
        dataValue: '',
      })),
    );
  };
  function handelIndicator() {
    openModal(true, {
      selectNode: tags.value || [],
    });
  }
  function copy(value) {
    const input = document.createElement('input');
    input.setAttribute('readonly', 'readonly');
    input.setAttribute('value', value);
    document.body.appendChild(input);
    input.select();
    input.setSelectionRange(0, 9999);
    if (document.execCommand('copy')) {
      document.execCommand('copy');
      createMessage.success('复制成功');
    }
    document.body.removeChild(input);
  }

  const codeModalVisible = ref(false);

  const imgPenTemp = `
    /**
      * imgStatus 展示图片 0默认,1事件的所绑定的图 | lineStatus 绑定管线的动画 0关闭,1启动
      *  示例'{"imgStatus":1,"lineStatus":1}'
      */
      if (getV('指标CODE') > 100) {
        return '{}';
      } else if (getV('指标CODE') > 50) {
        return '{}';
      } else {
        return '{}';
      }
    `;

  const btnPenTemp = `
    /**
      * pipelineFlow 绑的管线是否流动 | background 背景色 | disabled 是否禁用 | text 文字 | textColor 文字颜色
      *  示例'{"background":"#40a9ff","disabled":true,"text":"确定","textColor":"#fff","pipelineFlow":true}'
      */
      if (getV('指标CODE') > 100) {
        return '{}';
      } else if (getV('指标CODE') > 50) {
        return '{}';
      } else {
        return '{}';
      }
    `;
  function createTemplate() {
    let template = btnPenTemp;
    if (isImgPen(props.activePen)) {
      template = imgPenTemp;
    }
    if (tags.value?.length == 1)
      template = template.replace(/指标CODE/g, tags.value[0].indicatorCode);
    editorRef.value.setValue(template);
  }

  function updateFormDataByProp(cb) {
    const params = [props.data, emits, UPDATE_DATA];
    updatePropData(params, cb);
  }
</script>

<style lang="less" scoped></style>
