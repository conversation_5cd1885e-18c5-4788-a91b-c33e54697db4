<template>
  <BasicModal
    destroyOnClose
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    :title="getTitle"
    width="40%"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent, ref, computed, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from './data';
  import { useMessage } from '/@/hooks/web/useMessage';

  import { BasicModal, useModalInner } from '/@/components/Modal';

  import { addOrUpdateScheduleGroupApi, getScheduleGroupApi } from '/@zhcz/api/scheduling';

  export default defineComponent({
    name: 'ModifyModal',
    components: { BasicModal, BasicForm },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const isUpdate = ref(true);
      const updateId = ref();
      let updateInfo = {};

      const { createMessage } = useMessage();

      const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
        labelWidth: 80,
        baseColProps: { span: 24 },
        schemas: formSchema,
        showActionButtonGroup: false,
      });
      const [registerDrawer, { setModalProps, closeModal }] = useModalInner(async (data) => {
        resetFields();
        setModalProps({ confirmLoading: false });

        isUpdate.value = !!data?.isUpdate;
        updateId.value = undefined;

        if (unref(isUpdate)) {
          updateId.value = data?.record.id;
          const ret = await getScheduleGroupApi(updateId.value);

          updateInfo = ret;
          setFieldsValue({
            ...ret,
          });
        }
        // updateSchema({
        //   field: 'dictType',
        //   label: '班组编码',
        //   dynamicDisabled: unref(isUpdate),
        //   component: 'Input',
        // });
      });

      const getTitle = computed(() => (!unref(isUpdate) ? '新增班组' : '编辑班组'));

      async function handleSubmit() {
        try {
          const values = await validate();
          setModalProps({ confirmLoading: true });
          console.log(values);
          values.id = updateId.value;
          const params = Object.assign(updateInfo, values);
          await addOrUpdateScheduleGroupApi(params);
          closeModal();
          createMessage.success('操作成功');
          emit('success');
        } finally {
          setModalProps({ confirmLoading: false });
        }
      }

      return {
        registerDrawer,
        registerForm,
        getTitle,
        handleSubmit,
      };
    },
  });
</script>
