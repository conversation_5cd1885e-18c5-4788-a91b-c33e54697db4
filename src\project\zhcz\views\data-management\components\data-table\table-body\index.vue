<template>
  <div class="table-body">
    <div
      :class="['item', { item_time: item.type === 'time' }]"
      v-for="(item, index) in _data_"
      :key="index"
    >
      <template v-if="item.type === 'time'">
        <TimeColumn
          :data="item.data"
          :allData="_data_"
          :dataTimeType="dataTimeType"
          ref="timeColumnRef"
          @copy="handleCopy"
          @log="handleCreate"
        />
      </template>
      <template v-if="item.type === 'index'">
        <IndexColumn
          @indexLog="handleIndexLog"
          @defaultIndex="() => setDefaultIndex()"
          @setIndexVal="setIndexVal"
          :indexVal="indexVal"
          :indexKey="index"
          :data="item.data"
          :ref="(el) => (currentCols[index] = el)"
        />
      </template>
    </div>
    <IndexDetail @register="indexRegister" />
    <LogDetail @register="register" @submit="handleSubmit" />
    <EditModal :dataType="'hours'" @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { ref, PropType, inject, Ref, computed, watch } from 'vue';
  import dayjs, { ManipulateType } from 'dayjs';
  import TimeColumn from './TimeColumn.vue';
  import IndexColumn from './IndexColumn.vue';
  import LogDetail from './LogDetail.vue';
  import IndexDetail from './IndexDetail.vue';
  import { useDrawer } from '/@/components/Drawer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDataManagementStore } from '/@zhcz/store/modules/data-management';
  import { getLogApi, createLogApi, getNewIndexChangeLogApi } from '/@zhcz/api/data-management';
  import type { Data } from '../types';
  import { BasicQueryParamsSymbol } from '../../../injectionSymbols';
  import { SAVE_REPORT_DATA } from '/@zhcz/views/data-management/constant';
  import { useEmitt } from '/@/hooks/web/useEmitt';
  import EditModal from '/@zhcz/views/scheduling-management/chronicles-of-shift/EditModal.vue';
  import { useModal } from '/@/components/Modal';
  const [registerModal, { openModal: openModal }] = useModal();
  const handleCreate = (data) => {
    // console.log('data', data);
    if (data.timeName) {
      openModal(true, {
        ...data,
        isEdit: true,
      });
    } else {
      openLog(data);
    }
  };
  const indexVal = ref(-1);
  function setIndexVal(index: number) {
    indexVal.value = index;
  }
  const handleSuccess = async () => {
    // reload();
    // updateFactory();
  };
  const currentCols = new Array(100);

  const props = defineProps({
    timeColumnWidth: {
      type: String,
      default: '201px',
    },
    data: {
      type: Array as PropType<Data[]>,
      default: () => [
        {
          title: '小时',
          type: 'time',
          data: [],
        },
        {
          title: '生化池1#鼓风机正向电量（kW·h）',
          type: 'index',
          data: [],
        },
      ],
    },
    dataTimeType: {
      type: Number,
      default: Infinity,
    },
  });

  const dataManagementStore = useDataManagementStore();

  const _data_ = ref<Data[]>(props.data);
  watch(
    () => props.data,
    () => {
      console.log('props.dataTimeType', props.dataTimeType);
      _data_.value = props.data;
    },
  );

  const { createMessage } = useMessage();

  const basicQueryParams = inject(BasicQueryParamsSymbol) as Ref<Recordable>;

  const [indexRegister, { openDrawer: openIndexDrawer }] = useDrawer();

  async function handleIndexLog(info) {
    const params = {
      tenantId: basicQueryParams.value.tenantId as string,
      indecatorCode: info.indicatorCode as string,
      timeSlot: info.provideTime,
    };
    const data = await getNewIndexChangeLogApi(params);
    openIndexDrawer(true, {
      logList: data,
      indexInfo: info,
    });
  }

  const timeColumnRef = ref();

  function setDefaultIndex() {
    timeColumnRef.value[0].activeIndex = -1;
  }

  const dateType = computed(() => {
    const typeMap = new Map<number, ManipulateType>([
      [2, 'hours'],
      [3, 'days'],
      [4, 'months'],
    ]);

    return typeMap.get(basicQueryParams.value.timeType);
  });
  const { emitter } = useEmitt();

  function handleCopy(info) {
    const provideTime = dayjs(info.item.date)
      .subtract(1, dateType.value)
      .format('YYYY-MM-DD HH:mm:ss');
    const indexData = _data_.value.filter((item) => item.type === 'index');
    const previousLineData = indexData
      .map((item) => {
        const data = item.data;
        return data.filter((j) => j.provideTime === provideTime);
      })
      .flat();

    _data_.value.forEach((item, itemIndex) => {
      item.data.forEach((indictor, indictorIndex) => {
        const node = previousLineData.find((previous) => {
          const _provideTime_ = dayjs(previous.provideTime)
            .add(1, dateType.value)
            .format('YYYY-MM-DD HH:mm:ss');
          let flag =
            _provideTime_ === indictor.provideTime &&
            previous.indicatorCode === indictor.indicatorCode;

          return flag;
        });
        if (node) {
          let currentCol = currentCols[itemIndex];
          let newData = currentCol.newData[indictorIndex];

          const newNode: Recordable = {
            ...node,
            oldValue: indictor.value,
            provideTime: indictor.provideTime,
          };
          // let currentValue = _data_.value[itemIndex].data[indictorIndex].value;
          let currentValue = newData.value;
          if (currentValue === '' || currentValue === null) {
            _data_.value[itemIndex].data[indictorIndex] = newNode;
            if (newNode.value !== newNode.oldValue) {
              const modifyData = dataManagementStore.getModifyIndexData;
              const index = modifyData.findIndex(
                (item) =>
                  item.indicatorCode === newNode.indicatorCode &&
                  item.provideTime === newNode.provideTime,
              );
              if (index > -1) {
                modifyData[index].value = newNode.value;
              } else {
                modifyData.push(newNode);
              }
              dataManagementStore.setModifyIndexData(modifyData);
              emitter.emit(SAVE_REPORT_DATA + basicQueryParams.value.timeType, newNode);
            }
          }
        }
      });
    });
  }

  const provideTime = ref('');
  const [register, { openDrawer, closeDrawer }] = useDrawer();
  async function openLog(record: Recordable) {
    provideTime.value = record.date;
    const params = {
      factoryId: basicQueryParams.value.factoryId,
      tenantId: basicQueryParams.value.tenantId,
      timeType: basicQueryParams.value.timeType,
      provideTime: dayjs(record.date).format('YYYY-MM-DD HH:mm:ss'),
    };
    const data = await getLogApi(params);
    openDrawer(true, data);
  }

  async function handleSubmit(content: string) {
    const params = {
      factoryId: basicQueryParams.value.factoryId,
      tenantId: basicQueryParams.value.tenantId,
      timeType: basicQueryParams.value.timeType,
      provideTime: provideTime.value,
      content,
    };
    await createLogApi(params);
    createMessage.success('添加日志成功');
    closeDrawer();
  }
</script>

<style lang="less" scoped>
  .table-body {
    // flex: 1;
    display: flex;
    align-items: start;
    // gap: 10px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    .item {
      flex-shrink: 0;
      width: 210px;
      min-height: 100%;
      height: 100%;
      position: relative;
      z-index: 1;

      &_time {
        width: v-bind(timeColumnWidth);
        position: sticky;
        left: 0;
        background: #ffffff;
        z-index: 2;
      }
    }

    // .item_hmtime {
    //   width: 170px;
    // }
  }
</style>
