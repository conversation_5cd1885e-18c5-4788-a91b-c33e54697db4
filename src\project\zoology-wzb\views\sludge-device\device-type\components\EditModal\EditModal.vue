<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    width="45%"
  >
    <div class="container">
      <BasicForm ref="formRef" @register="registerForm">
        <!-- 型号表格 -->
        <template #equipmentModelList="{ field, model }">
          <ModelTable
            @handleAddModel="handleAddModel"
            @handleDeleteModel="handleDeleteModel"
            :styleInfo="model[field] || []"
          />
        </template>
      </BasicForm>
    </div>
    <template #footer>
      <a-button class="default-btn" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">保存</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import ModelTable from './ModelTable.vue';
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { editModalSchemas } from './data';
  import { searchEquipmentTypeDeatilApi, saveEquipmentTypeApi } from '/@zoology-wzb/api/device';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const title = ref('');
  const editId = ref();
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });
    const isEdit = data.id || data.id === 0;
    title.value = isEdit ? '编辑类型' : '新增类型';
    editId.value = data.id;
    if (isEdit) {
      const res = await searchEquipmentTypeDeatilApi(data.id);
      setFieldsValue(res);
    } else {
      setFieldsValue({
        equipmentTypeCode: `SBLX-${new Date().getTime()}`,
      });
    }
  });

  const formRef = ref();
  const [registerForm, { resetFields, setFieldsValue, validate, clearValidate }] = useForm({
    labelWidth: 100,
    schemas: editModalSchemas,
    showActionButtonGroup: false,
  });
  const handleAddModel = () => {
    if (!formRef.value.formModel?.equipmentModelList)
      formRef.value.formModel.equipmentModelList = [];
    formRef.value.formModel.equipmentModelList.push({
      equipmentModelName: '',
      equipmentModelSort: '0',
    });
  };
  const handleDeleteModel = (index) => {
    if (!formRef.value.formModel?.equipmentModelList?.length) return;
    formRef.value.formModel.equipmentModelList.splice(index, 1);
  };
  const handleSubmit = async () => {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      if (editId.value) values.id = editId.value;
      await saveEquipmentTypeApi(values);
      await handleCancel();
      createMessage.success('操作成功');
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  };

  const handleCancel = () => {
    resetFields();
    closeModal();
    clearValidate();
  };
</script>
<style lang="less" scoped></style>
