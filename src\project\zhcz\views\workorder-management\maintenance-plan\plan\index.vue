<template>
  <div class="plan-content h-full">
    <BasicTable
      @register="registerList"
      @fetch-success="onFetchSuccess"
      @selection-change="handleChangeSelection"
    >
      <template #tableTitle>
        <div>
          <a-button
            :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
            @click="handleCreate"
            type="primary"
            >新增</a-button
          >
          <a-button
            danger
            ghost
            type="primary"
            class="ml-4"
            :disabled="selectRowKeys.length === 0"
            :icon="h(Icon, { icon: 'icon-park-outline:delete-one' })"
            @click="handleMultipleDelete"
          >
            删除
          </a-button>
        </div>
      </template>
      <template #form-eqType="{ model, field }">
        <Select
          v-model:value="model[field]"
          placeholder="全部"
          :options="eqCategoryOptions"
          allowClear
          class="eqTypeSelect"
        />
      </template>
      <template #form-type="{ model, field }">
        <Select
          v-model:value="model[field]"
          placeholder="全部"
          :options="maintenanceNameOptions"
          allowClear
          class="maintenanceNameSelect"
        />
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
        <template v-if="column.key === 'isEnabled'">
          <Switch
            v-model:checked="record.isEnabled"
            @change="() => handeleChangeSwitch(record.id)"
          />
        </template>
        <template v-if="column.key === 'maintenanceProjectNameList'">
          <div
            v-for="(item, index) in record.maintenanceProjectNameList"
            :key="index"
            style="white-space: break-spaces"
          >
            {{ item || item.children }}
          </div>
        </template>
        <template v-if="column.key === 'executionDescription'">
          <div
            v-for="(item, index) in toggleExecutionDescription(record.executionDescription)"
            :key="index"
            style="white-space: pre-wrap; word-break: break-all"
          >
            {{ item || '-' }}
          </div>
        </template>
      </template>
    </BasicTable>
    <EditModal
      :overhaulTypeOptions="overhaulTypeOptions"
      :eqCategoryOptions="eqCategoryOptions"
      @register="registerModal"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, h } from 'vue';
  import { Select, Switch } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema } from './data';
  import EditModal from './EditModal.vue';
  import {
    getMaintenancePlanPageList,
    deleteMaintenancePlan,
    getMaintenanceTypesList,
    changeEnabledMaintenancePlan,
    multipleDeleteMaintenancePlan,
  } from '/@zhcz/api/workorder-management';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import { MAINTENANCE } from '/@zhcz/enums/maintenance';

  const props = defineProps({
    activeKey: {
      type: String,
      default: () => '',
    },
  });

  const maintenanceNameOptions = ref<{ value: string; label: string }[]>([]);
  const overhaulTypeOptions = ref<{ value: string; label: string }[]>([]);
  const eqCategoryOptions = ref<{ value: string; label: string }[]>([]);
  const selectRowKeys = ref<number[]>([]);

  const { createMessage, createConfirm } = useMessage();

  const [registerList, { reload, getForm, clearSelectedRowKeys }] = useTable({
    api: getMaintenancePlanPageList,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'size',
    },
    afterFetch: (data) => {
      clearSelectedRowKeys();
      return data;
    },
    columns: columns,
    rowKey: 'id',
    rowSelection: {
      type: 'checkbox',
    },
    clickToRowSelect: false,
    formConfig: {
      labelWidth: 85,
      schemas: searchFormSchema,
    },
    showIndexColumn: true,
    indexColumnProps: {
      width: 100,
    },
    showTableSetting: false,
    bordered: false,
    useSearchForm: true,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });
  const [registerModal, { openModal: openModal }] = useModal();

  function handleChangeSelection({ keys }) {
    selectRowKeys.value = keys;
  }
  function handleCreate() {
    openModal(true, {
      isEdit: false,
    });
  }

  function handleSuccess() {
    reload();
  }
  function handleEdit(record) {
    openModal(true, {
      data: record,
      isEdit: true,
    });
  }

  // 批量删除
  async function handleMultipleDelete() {
    const params = { ids: selectRowKeys.value };
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '是否确认删除',
      onOk: async () => {
        await multipleDeleteMaintenancePlan(params);
        reload();
      },
    });
  }

  async function handleDelete(record) {
    await deleteMaintenancePlan({ id: record.id });
    createMessage.success('删除成功');
    reload();
  }

  const handeleChangeSwitch = async (id) => {
    try {
      await changeEnabledMaintenancePlan({ id });
      reload();
    } catch (_) {
      reload();
    }
  };

  const onFetchSuccess = () => {
    const { setFieldsValue, getFieldsValue } = getForm();
    const values = getFieldsValue();
    setFieldsValue({
      eqType: values.eqType || undefined,
      maintenanceTypeId: values.maintenanceTypeId || undefined,
    });
  };

  const toggleExecutionDescription = (string) => {
    const str = !string ? [] : string.split('\n');
    return str;
  };

  // 导出
  // const handleExport = () => {};

  // 导入
  // const handleImport = () => {};
  // 检修类别
  const getMaintenanceName = async () => {
    const data = await getMaintenanceTypesList();
    const res = data.map((item) => {
      return {
        value: item.id,
        label: item.name,
        projectList: item.projectNameList || [],
        type: String(item.type),
      };
    });
    maintenanceNameOptions.value = res;
  };

  // 设备类别
  const getEqCategory = async () => {
    const res = await getDictTypeListApi({
      type: MAINTENANCE.EQUIPMENT_CATEGORY,
    });
    eqCategoryOptions.value = res;
  };

  // 检修类型
  const getOverhaulType = async () => {
    overhaulTypeOptions.value = await getDictTypeListApi({
      type: MAINTENANCE.OVERHAUL_TYPE,
    });
  };

  getMaintenanceName();
  getEqCategory();
  getOverhaulType();

  watch(
    () => props.activeKey,
    (val) => {
      if (val === '1') {
        getMaintenanceName();
        reload();
      }
    },
  );
</script>
<style lang="less" scoped>
  .maintenanceNameSelect,
  .eqTypeSelect {
    :deep(.ant-select-selection-placeholder) {
      color: #333;
    }
  }
</style>
