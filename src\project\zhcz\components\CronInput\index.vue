<template>
  <div class="ant-cron-input">
    <a-tabs v-model:activeKey="tabType">
      <a-tab-pane key="quick">
        <template #tab>
          <span>常用项</span>
        </template>
        <a-radio-group v-model:value="quickValue" class="cron-radio-group">
          <a-radio
            class="cron-radio"
            v-for="qItem in quickItems"
            :value="qItem.value"
            :key="qItem.value"
            >{{ qItem.label }}</a-radio
          >
        </a-radio-group>
      </a-tab-pane>
      <a-tab-pane key="custom">
        <template #tab>
          <span>自定义</span>
        </template>
        <a-tabs type="card" v-model:activeKey="tabActive" class="custom-tabs">
          <a-tab-pane key="second" v-if="item.includes('second')">
            <template #tab>
              <span>{{ text.Seconds.name }}</span>
            </template>
            <a-radio-group v-model:value="second.cronEvery" class="cron-radio-group">
              <a-radio class="cron-radio" value="1">{{ text.Seconds.every }}</a-radio>
              <a-radio class="cron-radio" value="2">
                <span class="cron-radio-content">
                  {{ text.Seconds.interval[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="second.incrementIncrement"
                    :min="1"
                    :max="60"
                    :status="''"
                    :disabled="second.cronEvery !== '2'"
                  />
                  {{ text.Seconds.interval[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="second.incrementStart"
                    :min="0"
                    :max="59"
                    :status="''"
                    :disabled="second.cronEvery !== '2'"
                  />
                  {{ text.Seconds.interval[2] || '' }}
                </span>
              </a-radio>
              <a-radio class="cron-radio long" value="3">
                <span class="cron-radio-content">
                  {{ text.Seconds.specific }}
                  <a-select
                    class="cron-form-item"
                    mode="tags"
                    v-model:value="second.specificSpecific"
                    style="margin-right: 0"
                    :status="''"
                    :disabled="second.cronEvery !== '3'"
                  >
                    <a-select-option v-for="val in 60" :key="val" :value="String(val - 1)">{{
                      val - 1
                    }}</a-select-option>
                  </a-select>
                </span>
              </a-radio>
              <a-radio class="cron-radio" value="4">
                <span class="cron-radio-content">
                  {{ text.Seconds.cycle[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="second.rangeStart"
                    :min="0"
                    :max="59"
                    :status="''"
                    :disabled="second.cronEvery !== '4'"
                  />
                  {{ text.Seconds.cycle[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="second.rangeEnd"
                    :min="0"
                    :max="59"
                    :status="''"
                    :disabled="second.cronEvery !== '4'"
                  />
                  {{ text.Seconds.cycle[2] || '' }}
                </span>
              </a-radio>
            </a-radio-group>
          </a-tab-pane>
          <a-tab-pane key="minute" v-if="item.includes('minute')">
            <template #tab>
              <span>{{ text.Minutes.name }}</span>
            </template>
            <a-radio-group v-model:value="minute.cronEvery" class="cron-radio-group">
              <a-radio class="cron-radio" value="1">{{ text.Minutes.every }}</a-radio>
              <a-radio class="cron-radio" value="2">
                <span class="cron-radio-content">
                  {{ text.Minutes.interval[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="minute.incrementIncrement"
                    :min="1"
                    :max="60"
                    :status="''"
                    :disabled="minute.cronEvery !== '2'"
                  />
                  {{ text.Minutes.interval[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="minute.incrementStart"
                    :min="0"
                    :max="59"
                    :status="''"
                    :disabled="minute.cronEvery !== '2'"
                  />
                  {{ text.Minutes.interval[2] || '' }}
                </span>
              </a-radio>
              <a-radio class="cron-radio long" value="3">
                <span class="cron-radio-content">
                  {{ text.Minutes.specific }}
                  <a-select
                    class="cron-form-item"
                    mode="multiple"
                    v-model:value="minute.specificSpecific"
                    style="margin-right: 0"
                    :status="''"
                    :disabled="minute.cronEvery !== '3'"
                  >
                    <a-select-option v-for="val in 60" :key="val" :value="String(val - 1)">{{
                      val - 1
                    }}</a-select-option>
                  </a-select>
                </span>
              </a-radio>
              <a-radio class="cron-radio" value="4">
                <span class="cron-radio-content">
                  {{ text.Minutes.cycle[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="minute.rangeStart"
                    :min="0"
                    :max="59"
                    :status="''"
                    :disabled="minute.cronEvery !== '4'"
                  />
                  {{ text.Minutes.cycle[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="minute.rangeEnd"
                    :min="0"
                    :max="59"
                    :status="''"
                    :disabled="minute.cronEvery !== '4'"
                  />
                  {{ text.Minutes.cycle[2] || '' }}
                </span>
              </a-radio>
            </a-radio-group>
          </a-tab-pane>
          <a-tab-pane key="hour" v-if="item.includes('hour')">
            <template #tab>
              <span>{{ text.Hours.name }}</span>
            </template>
            <a-radio-group v-model:value="hour.cronEvery" class="cron-radio-group">
              <a-radio class="cron-radio" value="1">{{ text.Hours.every }}</a-radio>
              <a-radio class="cron-radio" value="2">
                <span class="cron-radio-content">
                  {{ text.Hours.interval[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="hour.incrementIncrement"
                    :min="1"
                    :max="23"
                    :status="''"
                    :disabled="hour.cronEvery !== '2'"
                  />
                  {{ text.Hours.interval[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="hour.incrementStart"
                    :min="0"
                    :max="23"
                    :status="''"
                    :disabled="hour.cronEvery !== '2'"
                  />
                  {{ text.Hours.interval[2] || '' }}
                </span>
              </a-radio>
              <a-radio class="cron-radio long" value="3">
                <span class="cron-radio-content">
                  {{ text.Hours.specific }}
                  <a-select
                    class="cron-form-item"
                    mode="multiple"
                    v-model:value="hour.specificSpecific"
                    style="margin-right: 0"
                    :status="''"
                    :disabled="hour.cronEvery !== '3'"
                  >
                    <a-select-option v-for="val in 24" :key="val" :value="String(val - 1)">{{
                      val - 1
                    }}</a-select-option>
                  </a-select>
                </span>
              </a-radio>
              <a-radio class="cron-radio" value="4">
                <span class="cron-radio-content">
                  {{ text.Hours.cycle[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="hour.rangeStart"
                    :min="0"
                    :max="23"
                    :status="''"
                    :disabled="hour.cronEvery !== '4'"
                  />
                  {{ text.Hours.cycle[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="hour.rangeEnd"
                    :min="0"
                    :max="23"
                    :status="''"
                    :disabled="hour.cronEvery !== '4'"
                  />
                  {{ text.Hours.cycle[2] || '' }}
                </span>
              </a-radio>
            </a-radio-group>
          </a-tab-pane>
          <a-tab-pane key="day" v-if="item.includes('day')">
            <template #tab>
              <span>{{ text.Day.name }}</span>
            </template>
            <a-radio-group v-model:value="day.cronEvery" class="cron-radio-group">
              <a-radio class="cron-radio" value="8">{{ text.Day.noSet }}</a-radio>
              <a-radio class="cron-radio" value="1">{{ text.Day.every }}</a-radio>
              <a-radio class="cron-radio" value="2">
                <span class="cron-radio-content">
                  {{ text.Day.interval[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="day.incrementIncrement"
                    :min="1"
                    :max="31"
                    :status="''"
                    :disabled="day.cronEvery !== '2'"
                  />
                  {{ text.Day.interval[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="day.incrementStart"
                    :min="1"
                    :max="31"
                    :status="''"
                    :disabled="day.cronEvery !== '2'"
                  />
                  {{ text.Day.interval[2] }}
                </span>
              </a-radio>
              <a-radio class="cron-radio long" value="3">
                <span class="cron-radio-content">
                  {{ text.Day.specific }}
                  <a-select
                    class="cron-form-item"
                    mode="tags"
                    v-model:value="day.specificSpecific"
                    style="margin-right: 0"
                    :status="''"
                    :disabled="day.cronEvery !== '3'"
                  >
                    <a-select-option v-for="val in 31" :key="val" :value="String(val)">{{
                      val
                    }}</a-select-option>
                  </a-select>
                </span>
              </a-radio>
              <a-radio class="cron-radio" value="4">{{ text.Day.lastDay }}</a-radio>
              <a-radio class="cron-radio" value="5">{{ text.Day.lastWeekday }}</a-radio>
              <a-radio class="cron-radio" value="6">
                <span class="cron-radio-content">
                  <span v-show="!lang || lang === 'cn'">{{ text.Day.beforeEndMonth[0] }}</span>
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="day.cronDaysBeforeEomMinus"
                    :min="1"
                    :max="31"
                    :status="''"
                    :disabled="day.cronEvery !== '6'"
                  />
                  <span v-show="!lang || lang === 'cn'">{{ text.Day.beforeEndMonth[1] }}</span>
                  <span v-show="lang === 'en'">{{ text.Day.beforeEndMonth[0] }}</span>
                </span>
              </a-radio>
              <a-radio class="cron-radio" value="7">
                <span class="cron-radio-content">
                  {{ text.Day.nearestWeekday[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="day.cronDaysNearestWeekday"
                    :min="1"
                    :max="31"
                    :status="''"
                    :disabled="day.cronEvery !== '7'"
                  />
                  {{ text.Day.nearestWeekday[1] }}
                </span>
              </a-radio>
            </a-radio-group>
          </a-tab-pane>
          <a-tab-pane key="month" v-if="item.includes('month')">
            <template #tab>
              <span>{{ text.Month.name }}</span>
            </template>
            <a-radio-group v-model:value="month.cronEvery" class="cron-radio-group">
              <a-radio class="cron-radio" value="1">{{ text.Month.every }}</a-radio>
              <a-radio class="cron-radio" value="2">
                <span class="cron-radio-content">
                  {{ text.Month.interval[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="month.incrementIncrement"
                    :min="1"
                    :max="12"
                    :status="''"
                    :disabled="month.cronEvery !== '2'"
                  />
                  {{ text.Month.interval[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="month.incrementStart"
                    :min="1"
                    :max="12"
                    style="margin-right: 1em"
                    :status="''"
                    :disabled="month.cronEvery !== '2'"
                  />
                  {{ text.Month.interval[2] }}
                </span>
              </a-radio>
              <a-radio class="cron-radio long" value="3">
                <span class="cron-radio-content">
                  {{ text.Month.specific }}
                  <a-select
                    class="cron-form-item"
                    mode="multiple"
                    v-model:value="month.specificSpecific"
                    style="margin-right: 0"
                    :status="''"
                    :disabled="month.cronEvery !== '3'"
                  >
                    <a-select-option v-for="val in 12" :key="val" :value="String(val)">{{
                      val
                    }}</a-select-option>
                  </a-select>
                </span>
              </a-radio>
              <a-radio class="cron-radio" value="4">
                <span class="cron-radio-content">
                  {{ text.Month.cycle[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="month.rangeStart"
                    :min="1"
                    :max="12"
                    :status="''"
                    :disabled="month.cronEvery !== '4'"
                  />
                  {{ text.Month.cycle[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="month.rangeEnd"
                    :min="1"
                    :max="12"
                    :status="''"
                    :disabled="month.cronEvery !== '4'"
                  />
                  {{ text.Month.cycle[2] }}
                </span>
              </a-radio>
            </a-radio-group>
          </a-tab-pane>
          <a-tab-pane key="week" v-if="item.includes('week')">
            <template #tab>
              <span>{{ text.Week.name }}</span>
            </template>
            <a-radio-group v-model:value="week.cronEvery" class="cron-radio-group">
              <a-radio class="cron-radio" value="5">{{ text.Week.noSet }}</a-radio>
              <a-radio class="cron-radio long" value="2">
                <span class="cron-radio-content">
                  {{ text.Day.specific }}
                  <a-select
                    class="cron-form-item"
                    mode="tags"
                    v-model:value="week.specificSpecific"
                    style="margin-right: 0"
                    :status="''"
                    :disabled="week.cronEvery !== '2'"
                  >
                    <a-select-option v-for="val in weekValueList" :key="val" :value="val">{{
                      text.Week.select[weekValueList.indexOf(val)]
                    }}</a-select-option>
                  </a-select>
                </span>
              </a-radio>
              <a-radio class="cron-radio long" value="3">
                <span class="cron-radio-content">
                  {{ text.Week.someWeekday[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="week.cronNthDayNth"
                    :min="1"
                    :max="5"
                    :status="''"
                    :disabled="week.cronEvery !== '3'"
                  />
                  <span v-show="!lang || lang === 'cn'">{{ text.Week.someWeekday[1] }}</span>
                  <a-select
                    class="cron-form-item"
                    v-model:value="week.cronNthDayDay"
                    style="margin-right: 0"
                    :status="''"
                    @click.stop
                    @click.prevent
                    :disabled="week.cronEvery !== '3'"
                  >
                    <a-select-option
                      v-for="val in weekValueList"
                      :key="val"
                      :value="val"
                      @click.stop
                      >{{ text.Week.select[weekValueList.indexOf(val)] }}</a-select-option
                    >
                  </a-select>
                  <span v-show="lang === 'en'">{{ text.Week.someWeekday[1] }}</span>
                </span>
              </a-radio>
              <a-radio class="cron-radio long" value="4">
                <span class="cron-radio-content">
                  {{ text.Week.lastday[0] }}
                  <a-select
                    class="cron-form-item"
                    v-model:value="week.cronLastSpecificDomDay"
                    :status="''"
                    @click.stop
                    @click.prevent
                    :disabled="week.cronEvery !== '4'"
                  >
                    <a-select-option v-for="val in weekValueList" :key="val" :value="val">{{
                      text.Week.select[weekValueList.indexOf(val)]
                    }}</a-select-option>
                  </a-select>
                  {{ text.Week.lastday[1] || '' }}
                </span>
              </a-radio>
            </a-radio-group>
          </a-tab-pane>
          <a-tab-pane key="year" v-if="item.includes('year')">
            <template #tab>
              <span>{{ text.Year.name }}</span>
            </template>
            <a-radio-group v-model:value="year.cronEvery" class="cron-radio-group">
              <a-radio class="cron-radio" value="5">{{ text.Year.noSet }}</a-radio>
              <a-radio class="cron-radio" value="1">{{ text.Year.every }}</a-radio>
              <a-radio class="cron-radio" value="2">
                <span class="cron-radio-content">
                  {{ text.Year.interval[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="year.incrementIncrement"
                    :min="1"
                    :max="99"
                    :status="''"
                    :disabled="year.cronEvery !== '2'"
                  />
                  {{ text.Year.interval[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="year.incrementStart"
                    :min="yearStart"
                    :max="yearStart + 100"
                    :status="''"
                    :disabled="year.cronEvery !== '2'"
                  />
                  {{ text.Year.interval[2] }}
                </span>
              </a-radio>
              <a-radio class="cron-radio long" value="3">
                <span class="cron-radio-content">
                  {{ text.Year.specific }}
                  <a-select
                    class="cron-form-item"
                    mode="tags"
                    v-model:value="year.specificSpecific"
                    style="margin-right: 0"
                    :status="''"
                    :disabled="year.cronEvery !== '3'"
                  >
                    <a-select-option
                      v-for="val in 100"
                      :key="val"
                      :value="String(yearStart + val - 1)"
                      >{{ yearStart + val - 1 }}</a-select-option
                    >
                  </a-select>
                </span>
              </a-radio>
              <a-radio class="cron-radio" value="4">
                <span class="cron-radio-content">
                  {{ text.Year.cycle[0] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="year.rangeStart"
                    :min="yearStart"
                    :max="yearStart + 100"
                    :status="''"
                    :disabled="year.cronEvery !== '4'"
                  />
                  {{ text.Year.cycle[1] }}
                  <a-input-number
                    class="cron-form-item"
                    v-model:value="year.rangeEnd"
                    :min="yearStart"
                    :max="yearStart + 100"
                    style="margin-right: 0"
                    :status="''"
                    :disabled="year.cronEvery !== '4'"
                  />
                </span>
              </a-radio>
            </a-radio-group>
          </a-tab-pane>
        </a-tabs>
      </a-tab-pane>
    </a-tabs>
    <div class="ant-cron-bottom">
      <span class="ant-cron-result">{{ cron }}</span>
    </div>
    <div class="ant-cron-extra">
      <slot></slot>
    </div>
  </div>
</template>
<script lang="ts" setup name="CronInput">
  import { computed, ref, reactive, watch } from 'vue';
  import { range, defaultQuickItems } from './Util';
  import Language from './language/index';
  import {
    InputNumber,
    Radio,
    RadioGroup,
    Select,
    Tabs,
    TabPane,
    SelectOption,
  } from 'ant-design-vue';

  const AInputNumber = InputNumber;
  const ARadio = Radio;
  const ARadioGroup = RadioGroup;
  const ASelect = Select;
  const ASelectOption = SelectOption;
  const ATabs = Tabs;
  const ATabPane = TabPane;

  const emit = defineEmits(['input', 'change']);

  const props = defineProps({
    lang: {
      type: String,
      default: 'cn',
    },
    value: {
      type: String,
      default: '0 0 8 * * ?',
    },
    item: {
      type: Array,
      default: () => ['second', 'minute', 'hour', 'day', 'month', 'week', 'year'],
    },
    quickItems: {
      type: Array as any,
      default: () => defaultQuickItems,
    },
    weekByNum: {
      type: Boolean,
      default: false,
    },
    sundayIndex: {
      type: Number,
      default: 0,
    },
    yearStart: {
      type: Number,
      default: new Date().getFullYear(),
    },
  });
  const quickValue = ref('0 0 8 * * ?');
  const tabType = ref('quick');
  const tabActive = ref('second');
  const weekEnum = ['7', '1', '2', '3', '4', '5', '6'];
  const exps = ref([
    { type: 'second', expression: '' },
    { type: 'minute', expression: '' },
    { type: 'hour', expression: '' },
    { type: 'day', expression: '' },
    { type: 'month', expression: '' },
    { type: 'week', expression: null },
    { type: 'year', expression: '' },
  ]);

  const second = reactive({
    cronEvery: '',
    incrementStart: '0',
    incrementIncrement: '1',
    rangeStart: '1',
    rangeEnd: '2',
    specificSpecific: [],
  });
  const minute = reactive({
    cronEvery: '',
    incrementStart: '0',
    incrementIncrement: '1',
    rangeStart: '1',
    rangeEnd: '2',
    specificSpecific: [],
  });
  const hour = reactive({
    cronEvery: '',
    incrementStart: '0',
    incrementIncrement: '1',
    rangeStart: '1',
    rangeEnd: '2',
    specificSpecific: [],
  });
  const day = reactive({
    cronEvery: '',
    incrementStart: '1',
    incrementIncrement: '1',
    rangeStart: '',
    rangeEnd: '',
    specificSpecific: [],
    cronDaysBeforeEomMinus: '',
    cronDaysNearestWeekday: '',
  });
  const week = reactive({
    cronEvery: '',
    specificSpecific: [],
    cronNthDayDay: '', // 本月的周几
    cronNthDayNth: '1', // 本月的第几个
    cronLastSpecificDomDay: '',
  });
  const month = reactive({
    cronEvery: '',
    incrementStart: '1',
    incrementIncrement: '1',
    rangeStart: '',
    rangeEnd: '',
    specificSpecific: [],
  });
  const year = reactive({
    cronEvery: '',
    incrementStart: String(props.yearStart),
    incrementIncrement: '1',
    rangeStart: String(props.yearStart),
    rangeEnd: String(props.yearStart),
    specificSpecific: [],
  });
  const output = reactive({
    second: '',
    minute: '',
    hour: '',
    day: '',
    month: '',
    Week: '',
    year: '',
  });

  const text = computed(() => Language[props.lang || 'cn']);
  const weekNumEnum = computed(() => range(props.sundayIndex, 7).map((item) => String(item)));
  const weekValueList = computed(() => (props.weekByNum ? weekNumEnum : weekEnum));
  const secondsText = computed(() => {
    let seconds = '';
    let cronEvery = second.cronEvery;
    switch (cronEvery.toString()) {
      case '1':
        seconds = '*';
        break;
      case '2':
        seconds = second.incrementStart + '/' + second.incrementIncrement;
        break;
      case '3':
        second.specificSpecific.map((val) => {
          seconds += val + ',';
        });
        seconds = seconds.slice(0, -1);
        break;
      case '4':
        seconds = second.rangeStart + '-' + second.rangeEnd;
        break;
    }
    return seconds;
  });
  const minutesText = computed(() => {
    let minutes = '';
    let cronEvery = minute.cronEvery;
    switch (cronEvery.toString()) {
      case '1':
        minutes = '*';
        break;
      case '2':
        minutes = minute.incrementStart + '/' + minute.incrementIncrement;
        break;
      case '3':
        minute.specificSpecific.map((val) => {
          minutes += val + ',';
        });
        minutes = minutes.slice(0, -1);
        break;
      case '4':
        minutes = minute.rangeStart + '-' + minute.rangeEnd;
        break;
    }
    return minutes;
  });
  const hoursText = computed(() => {
    let hours = '';
    let cronEvery = hour.cronEvery;
    switch (cronEvery.toString()) {
      case '1':
        hours = '*';
        break;
      case '2':
        hours = hour.incrementStart + '/' + hour.incrementIncrement;
        break;
      case '3':
        hour.specificSpecific.map((val) => {
          hours += val + ',';
        });
        hours = hours.slice(0, -1);
        break;
      case '4':
        hours = hour.rangeStart + '-' + hour.rangeEnd;
        break;
    }
    return hours;
  });
  const daysText = computed(() => {
    let days = '';
    let cronEvery = day.cronEvery;
    switch (cronEvery.toString()) {
      case '1':
        days = '*';
        break;
      case '2':
        days = [day.incrementStart, day.incrementIncrement].join('/');
        break;
      case '3':
        day.specificSpecific.map((val) => {
          days += val + ',';
        });
        days = days.slice(0, -1);
        break;
      case '4':
        days = 'L';
        break;
      case '5':
        days = 'LW';
        break;
      case '6':
        days = 'L-' + day.cronDaysBeforeEomMinus;
        break;
      case '7':
        days = day.cronDaysNearestWeekday + 'W';
        break;
      case '8':
        days = '?';
        break;
    }
    return days;
  });

  const weeksText = computed(() => {
    let weeks = '';
    let cronEvery = week.cronEvery;
    switch (cronEvery.toString()) {
      case '2':
        week.specificSpecific.map((val) => {
          weeks += val + ',';
        });
        weeks = weeks.slice(0, -1);
        break;
      case '3':
        weeks = [week.cronNthDayDay, week.cronNthDayNth].join('#');
        break;
      case '4':
        weeks = week.cronLastSpecificDomDay + 'L';
        break;
      default:
        weeks = '?';
    }
    return weeks;
  });
  const monthsText = computed(() => {
    let months = '';
    let cronEvery = month.cronEvery;
    switch (cronEvery.toString()) {
      case '1':
        months = '*';
        break;
      case '2':
        months = [month.incrementStart, month.incrementIncrement].join('/');
        break;
      case '3':
        month.specificSpecific.map((val) => {
          months += val + ',';
        });
        months = months.slice(0, -1);
        break;
      case '4':
        months = [month.rangeStart, month.rangeEnd].join('-');
        break;
    }
    return months;
  });
  const yearsText = computed(() => {
    let years = '';
    let cronEvery = year.cronEvery;
    switch (cronEvery.toString()) {
      case '1':
        years = '*';
        break;
      case '2':
        years = [year.incrementStart, year.incrementIncrement].join('/');
        break;
      case '3':
        year.specificSpecific.map((val) => {
          years += val + ',';
        });
        years = years.slice(0, -1);
        break;
      case '4':
        years = [year.rangeStart, year.rangeEnd].join('-');
        break;
      case '5':
        years = '';
        break;
    }
    return years;
  });

  const cron = computed(() => {
    return `${secondsText.value || '*'} ${minutesText.value || '*'} ${hoursText.value || '*'} ${
      daysText.value || '*'
    } ${monthsText.value || '*'} ${weeksText.value || '?'} ${yearsText.value}`.trim();
  });

  const resolveComma = (expressionType, expression, type = '3') => {
    const specificSpecific = expression.split(',');
    switch (expressionType) {
      case 'year':
        year.cronEvery = type;
        year.specificSpecific = specificSpecific;
        return;
      case 'month':
        month.cronEvery = type;
        month.specificSpecific = specificSpecific;
        return;
      case 'hour':
        hour.cronEvery = type;
        hour.specificSpecific = specificSpecific;
        return;
      case 'minute':
        minute.cronEvery = type;
        minute.specificSpecific = specificSpecific;
        return;
      case 'second':
        second.cronEvery = type;
        second.specificSpecific = specificSpecific;
        return;
      case 'week':
        week.cronEvery = type;
        week.specificSpecific = specificSpecific;
        return;
      case 'day':
        day.cronEvery = type;
        day.specificSpecific = specificSpecific;
        return;
    }
  };

  const resolveWeek = (str) => {
    const matchCronLastSpecificDomDay = /[(SUN)|(MON)|(TUE)|(WED)|(THU)|(FRI)|(SAT)]+L/;
    if (str.indexOf(',') >= 0) {
      week.cronEvery = '2';
      resolveComma('week', str, '2');
    } else if (str.indexOf('#') >= 0) {
      week.cronEvery = '3';
      let range = str.split('#');
      week.cronNthDayDay = range[0];
      week.cronNthDayNth = range[1];
    } else if (matchCronLastSpecificDomDay.test(str)) {
      week.cronEvery = '4';
      week.cronLastSpecificDomDay = str.match(matchCronLastSpecificDomDay)[0].substr(0, 3);
    } else if (/\dL/.test(str)) {
      week.cronEvery = '4';
      week.cronLastSpecificDomDay = str.match(/(\d)L/)[1];
    } else {
      week.cronEvery = '5';
    }
  };

  const resolveStar = (expressionType, type = '1') => {
    switch (expressionType) {
      case 'year':
        year.cronEvery = type;
        return;
      case 'month':
        month.cronEvery = type;
        return;
      case 'hour':
        hour.cronEvery = type;
        return;
      case 'minute':
        minute.cronEvery = type;
        return;
      case 'second':
        second.cronEvery = type;
        return;
      case 'week':
        week.cronEvery = type;
        return;
      case 'day':
        day.cronEvery = type;
        return;
    }
  };

  const resolveSlash = (expressionType, expression, type = '2') => {
    let range = expression.split('/');
    switch (expressionType) {
      case 'year':
        year.cronEvery = type;
        year.incrementStart = range[0];
        year.incrementIncrement = range[1];
        return;
      case 'month':
        month.cronEvery = type;
        month.incrementStart = range[0];
        month.incrementIncrement = range[1];
        return;
      case 'hour':
        hour.cronEvery = type;
        hour.incrementStart = range[0];
        hour.incrementIncrement = range[1];
        return;
      case 'minute':
        minute.cronEvery = type;
        minute.incrementStart = range[0];
        minute.incrementIncrement = range[1];
        return;
      case 'second':
        second.cronEvery = type;
        second.incrementStart = range[0];
        second.incrementIncrement = range[1];
        return;
      case 'week':
        week.cronEvery = type;
        return;
      case 'day':
        day.cronEvery = type;
        day.incrementStart = range[0];
        day.incrementIncrement = range[1];
        return;
    }
  };

  const resolveDay = (str) => {
    if (str == '*') {
      resolveStar('day');
    } else if (str.indexOf('/') >= 0) {
      resolveSlash(day, str, '2');
    } else if (str == 'L') {
      day.cronEvery = '4';
    } else if (str == 'LW') {
      day.cronEvery = '5';
    } else if (/L-\d+/.test(str)) {
      day.cronEvery = '6';
      day.cronDaysBeforeEomMinus = Number(str.match(/L-(\d+)/)[1]);
    } else if (/\d+W/.test(str)) {
      day.cronEvery = '7';
      day.cronDaysNearestWeekday = Number(str.match(/(\d+)W/))[1];
    } else if (str == '?') {
      day.cronEvery = '8';
    } else {
      resolveComma('day', str, '3');
    }
  };

  const resolveLine = (expressionObj, expression, type = '4') => {
    expressionObj.cronEvery = type;
    let range = expression.split('-');
    expressionObj.rangeStart = range[0];
    expressionObj.rangeEnd = range[1];
  };

  // 年，月，时，分，秒
  const commonParser = (expressionType, str) => {
    if (str == null || str == '') {
      // 年一般省略
      switch (expressionType) {
        case 'year':
          year.cronEvery = '5';
          year.specificSpecific = [];
          return;
        case 'month':
          month.cronEvery = '5';
          month.specificSpecific = [];
          return;
        case 'hour':
          hour.cronEvery = '5';
          hour.specificSpecific = [];
          return;
        case 'minute':
          minute.cronEvery = '5';
          minute.specificSpecific = [];
          return;
        case 'second':
          second.cronEvery = '5';
          second.specificSpecific = [];
          return;
      }
    } else if (str == '*') {
      resolveStar(expressionType);
    } else if (str.indexOf('-') >= 0) {
      resolveLine(expressionType, str);
    } else if (str.indexOf('/') >= 0) {
      resolveSlash(expressionType, str);
    } else {
      resolveComma(expressionType, str);
    }
  };

  const resolveExpression = () => {
    if (!props.value?.length) return;
    let expList = props.value.split(' ');
    if (expList.length < 6) {
      new Error('表达式格式不正确');
    }
    for (let i = 0; i < expList.length; i++) {
      exps.value[i].expression = expList[i];
    }

    exps.value.forEach((exp) => {
      output[exp.type] = exp.expression;
      switch (exp.type) {
        case 'year':
        case 'month':
        case 'hour':
        case 'minute':
        case 'second':
          commonParser(exp.type, exp.expression);
          return;
        case 'week':
          resolveWeek(exp.expression);
          return;
        case 'day':
          resolveDay(exp.expression);
          return;
      }
    });
  };

  resolveExpression();

  watch(
    () => props.value,
    (val) => {
      quickValue.value = val;
      val !== cron.value && resolveExpression();
    },
    {
      immediate: true,
    },
  );
  watch(
    () => props.item,
    (val) => {
      val.indexOf(tabActive.value) === -1 && (tabActive.value = val[0] || null);
    },
    { immediate: true },
  );
  watch(
    () => props.weekByNum,
    () => {
      week.cronEvery = '5';
    },
  );
  watch(
    () => cron.value,
    (val) => {
      emit('input', val);
      emit('change', val);
    },
  );
  watch(
    () => quickValue.value,
    (val) => {
      emit('input', val);
      emit('change', val);
    },
  );
</script>
<style lang="less" scoped>
  .language {
    position: absolute;
    right: 25px;
    z-index: 1;
  }

  .ant-cron-input {
    width: 100%;
    min-width: 350px;

    :deep(.custom-tabs) {
      .ant-tabs-nav {
        &:before {
          border-bottom-color: transparent;
        }
      }

      .ant-tabs-nav-list {
        width: 480px;

        .ant-tabs-tab {
          padding: 0;
          width: 60px;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0;
          border-radius: 0;
          background-color: #fff;
          border: 1px solid #d9d9d9;

          & + .ant-tabs-tab {
            border-left-color: transparent;
          }

          &:first-child {
            border-radius: 4px 0px 0px 4px;
          }

          &:nth-last-child(2) {
            border-radius: 0px 4px 4px 0px;
          }

          &.ant-tabs-tab-active {
            border-color: var(--theme-color);
            border-bottom-color: var(--theme-color);
          }
        }
      }
    }
  }

  .cron-radio-group {
    display: flex;
    flex-direction: column;
    padding: 0.5em;
  }

  .cron-radio {
    display: flex;
    align-items: center;
    margin: 0 0 12px 0;
    font-family: 'PingFang SC, PingFang SC';
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 14px;

    :deep(.ant-input-number-handler) {
      flex: auto !important;
    }
  }

  .cron-radio :last-child {
    flex: 1;
  }

  .cron-radio-content {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .cron-radio-content .cron-form-item {
    margin: 0 1em;
    flex: 1;
  }

  .ant-cron-bottom {
    width: 100%;
    text-align: center;
    margin-top: 5px;
    position: relative;
  }

  .ant-cron-extra {
    width: 100%;
  }

  .ant-cron-result {
    font-family: 'PingFang SC, PingFang SC';
    font-weight: 600;
    font-size: 18px;
    color: #333333;
    line-height: 18px;
  }
</style>
