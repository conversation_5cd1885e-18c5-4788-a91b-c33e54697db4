import { defZhczHttp } from '/@/utils/http/axios';

enum Api {
  GetSenceGroupIndicatorTree = '/data-sence/groupInfo/getSenceGroupIndicatorTree',
  GetResourceIndexPage = '/data-sence/resourceIndex/page',
  AddOrUpdateResourceIndex = '/data-sence/resourceIndex/addOrUpdate',
  DeleteResourceIndexs = '/data-sence/resourceIndex/del/',
  GetResourceInterfacePage = '/data-sence/resourceInterface/page',
  AddOrUpdateResourceInterface = '/data-sence/resourceInterface/addOrUpdate',
  DeleteResourceFunction = '/data-sence/resourceInterface/del/',
  GetSenceGroupTree = '/data-sence/groupInfo/getSenceGroupTree',
  AddOrUpdateSence = '/data-sence/senceInfo/addOrUpdate',
  DeleteSences = '/data-sence/senceInfo/del/',
  AddOrUpdateGroup = '/data-sence/groupInfo/addOrUpdate',
  DeleteGroups = '/data-sence/groupInfo/del/',
  GetGroupImage = '/data-sence/groupInfo/getGroupImage',
  GetDataStatByParentGroupCode = '/data-sence/groupInfo/getDataStatByParentGroupCode',
  GetResourceInfoPages = '/data-sence/displayResourceInfo/page',
  CallResourceIndexUrl = '/data-sence/resourceIndex/callResourceIndexUrl',
  AddOrUpdateDisplayResourceInfos = '/data-sence/displayResourceInfo/batchAddOrUpdate',
  GetResourceInfos = '/data-sence/displayResourceInfo/list',
  DeleteDisplayResourceInfos = '/data-sence/displayResourceInfo/batchDelete',
  CallResourceFunction = '/data-sence/resourceInterface/callResourceFunction',
  CallResourceGroupFunction = '/data-sence/resourceInterface/callResourceGroupFunction',

  // 电耗-峰平谷
  GetFpgIndicatorCodeList = '/data/basicData/fgp/indicatorCode/list',
  GetFpgDataList = '/data/basicData/fgp/list',
}

// 获取场景指标树
export const getSenceGroupIndicatorTree = (params) =>
  defZhczHttp.get<any>(
    {
      url: Api.GetSenceGroupIndicatorTree,
      params,
    },
    { isTransformResponse: false },
  );

// 资源分组预览
export const callResourceGroupFunction = (params) =>
  defZhczHttp.post<any>(
    {
      url: Api.CallResourceGroupFunction,
      params,
    },
    { isTransformResponse: false },
  );

// 资源接口预览
export const callResourceFunction = (params) =>
  defZhczHttp.post<any>(
    {
      url: Api.CallResourceFunction,
      params,
    },
    { isTransformResponse: false },
  );

// 获取资源列表 分页
export const getResourceInfoPages = (params) =>
  defZhczHttp.get<any>(
    {
      url: Api.GetResourceInfoPages,
      params,
    },
    { isTransformResponse: false },
  );

// 解绑资源
export const deleteDisplayResourceInfos = (params) =>
  defZhczHttp.post<any>(
    {
      url: Api.DeleteDisplayResourceInfos,
      params,
    },
    { isTransformResponse: false },
  );

// 删除分组
export const deleteGroups = (id) =>
  defZhczHttp.post<any>(
    {
      url: Api.DeleteGroups + id,
    },
    { isTransformResponse: false },
  );

// 删除场景
export const deleteSences = (id) =>
  defZhczHttp.post<any>(
    {
      url: Api.DeleteSences + id,
    },
    { isTransformResponse: false },
  );

/* 通过分组编码获取其顶层父节点图片资源 */
export const getGroupImageApi = (params) =>
  defZhczHttp.post({
    url: Api.GetGroupImage,
    params,
  });
/* 通过分组编码获取子分组的数据统计 */
export const getDataStatByParentGroupCodeApi = (params) =>
  defZhczHttp.post({
    url: Api.GetDataStatByParentGroupCode,
    params,
  });
// 删除接口
export const deleteResourceFunction = (id) =>
  defZhczHttp.post<any>(
    {
      url: Api.DeleteResourceFunction + id,
    },
    { isTransformResponse: false },
  );

// 删除资源索引
export const deleteResourceIndexs = (id) =>
  defZhczHttp.post<any>(
    {
      url: Api.DeleteResourceIndexs + id,
    },
    { isTransformResponse: false },
  );

// 获取已绑资源列表
export const getResourceInfos = (params) =>
  defZhczHttp.get<any>(
    {
      url: Api.GetResourceInfos,
      params,
    },
    { isTransformResponse: false },
  );

// 调用资源索引url
export const callResourceIndexUrl = (params) =>
  defZhczHttp.get<any>(
    {
      url: Api.CallResourceIndexUrl,
      params,
    },
    { isTransformResponse: false },
  );

// 绑定资源
export const addOrUpdateDisplayResourceInfos = (params) =>
  defZhczHttp.post<any>(
    {
      url: Api.AddOrUpdateDisplayResourceInfos,
      params,
    },
    { isTransformResponse: false },
  );

// 获取场景分组树
export const getSenceGroupTree = () =>
  defZhczHttp.get<any>({
    url: Api.GetSenceGroupTree,
  });

// 添加或更新分组
export const addOrUpdateGroup = (params) =>
  defZhczHttp.post<any>(
    {
      url: Api.AddOrUpdateGroup,
      params,
    },
    { isTransformResponse: false },
  );

// 添加或更新场景
export const addOrUpdateSence = (params) =>
  defZhczHttp.post<any>(
    {
      url: Api.AddOrUpdateSence,
      params,
    },
    { isTransformResponse: false },
  );

// 添加或更新接口
export const addOrUpdateResourceInterface = (params) =>
  defZhczHttp.post<any>(
    {
      url: Api.AddOrUpdateResourceInterface,
      params,
    },
    { isTransformResponse: false },
  );

// 获取接口列表
export const getResourceInterfacePage = (params) =>
  defZhczHttp.get<any>(
    {
      url: Api.GetResourceInterfacePage,
      params,
    },
    { isTransformResponse: false },
  );

// 添加或更新资源索引
export const addOrUpdateResourceIndex = (params) =>
  defZhczHttp.post<any>(
    {
      url: Api.AddOrUpdateResourceIndex,
      params,
    },
    { isTransformResponse: false },
  );

// 获取资源索引列表
export const getResourceIndexPage = (params) =>
  defZhczHttp.get<any>(
    {
      url: Api.GetResourceIndexPage,
      params,
    },
    { isTransformResponse: false },
  );

// 电耗-峰平谷
export const getFpgIndicatorCodeList = () =>
  defZhczHttp.get<any>({
    url: Api.GetFpgIndicatorCodeList,
  });

export const getFpgDataList = (params) =>
  defZhczHttp.post<any>({
    url: Api.GetFpgDataList,
    params,
  });
