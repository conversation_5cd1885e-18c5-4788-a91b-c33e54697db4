<template>
  <BasicModal
    width="700px"
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup name="FaultDeviceModal">
  import { computed, ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { addOrEditFaultDeviceApi } from '/@zoology-wzb/api/device';
  import { getFaultDeviceFormSchema } from '../data';

  defineOptions({
    name: 'FaultDeviceModal',
  });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const isUpdate = ref(true);
  const rowId = ref('');

  const [
    registerForm,
    {
      setFieldsValue,
      updateSchema,
      resetFields,
      validate,
      removeSchemaByField,
      appendSchemaByField,
    },
  ] = useForm({
    labelWidth: 80,
    schemas: [],
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    const schemas = getFaultDeviceFormSchema(
      isUpdate.value,
      isUpdate.value ? data.record.type : undefined,
    );
    let keys = schemas.map((i) => i.field);
    removeSchemaByField(keys);
    appendSchemaByField(schemas, undefined);

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    } else {
      // 新增时设置设备信息
      setFieldsValue({});
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增故障设备' : '编辑故障设备'));

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      const params = {
        ...values,
        type: unref(isUpdate) ? 'edit' : 'add',
      };
      await addOrEditFaultDeviceApi(params);
      closeModal();
      emit('success', { isUpdate: unref(isUpdate), values: { ...values, id: rowId.value } });
      createMessage.success(unref(isUpdate) ? '编辑成功' : '新增成功');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
