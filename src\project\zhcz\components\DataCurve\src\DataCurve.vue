<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    :title="modalType === 'single' ? '单指标曲线' : '多指标曲线'"
    :width="modalType === 'single' ? '1000px' : '1272px'"
    :minHeight="674"
    :loading="loading"
    :wrapClassName="warpClassName"
    :showOkBtn="false"
    :useWrapper="false"
    :canFullscreen="false"
    cancelText="关闭"
    forceRender
  >
    <div class="content-container">
      <div v-if="modalType !== 'single'" class="tree-content">
        <Tree
          v-model:expandedKeys="expandedKeys"
          :tree-data="indicatorTreeData"
          :fieldNames="treeFieldNames"
          blockNode
          multiple
          @select="handleSelectNode"
        >
          <template #switcherIcon="{ expanded }">
            <img :src="expanded ? treeItemChecked : treeItemIcon" />
          </template>
        </Tree>
      </div>
      <div style="flex: 1">
        <div class="tool-container">
          <div class="left-box">
            <div>
              <div style="width: 70px">时间跨度：</div>
              <Select v-model:value="dateType" style="width: 80px">
                <Select.Option value="date">天</Select.Option>
                <Select.Option value="month">月</Select.Option>
                <Select.Option value="year">年</Select.Option>
              </Select>
            </div>
            <div style="margin: 0 16px 0 24px">
              <div style="width: 70px; flex-shrink: 0">起止时间：</div>
              <RangePicker
                v-model:value="dateValue"
                :picker="dateType"
                @open-change="handleChangeDate"
                :allowClear="false"
              />
            </div>
            <!-- <div class="date-tool-box">
            <LeftOutlined />
            <div class="date-text">今日</div>
            <RightOutlined />
          </div> -->
          </div>
          <div class="right-box">
            <div class="icon refresh-icon" @click="handleRefresh"></div>
            <Dropdown v-model:visible="dropdownVisible">
              <div class="icon filter-icon"></div>
              <template #overlay>
                <Menu>
                  <!-- @click="dropdownVisible = true" -->
                  <Menu.Item>
                    <Checkbox v-model:checked="lastMonthChecked" @change="handleToggleMoreEcharts">
                      环比
                    </Checkbox>
                  </Menu.Item>
                  <Menu.Item>
                    <Checkbox v-model:checked="lastYearChecked" @change="handleToggleMoreEcharts">
                      年同比
                    </Checkbox>
                  </Menu.Item>
                </Menu>
              </template>
            </Dropdown>
            <div v-if="baseChartData.length" class="icon table-icon" @click="toggleShowTable"></div>
          </div>
        </div>
        <!-- 单指标容器 -->
        <div v-if="modalType === 'single'" class="chart-container">
          <div ref="baseChartRef"></div>
          <div v-show="lastMonthChecked" ref="lastMonthChartRef"></div>
          <div v-show="lastYearChecked" ref="lastYearChartRef"></div>
        </div>
        <!-- 多指标容器 -->
        <div v-else style="max-height: 520px; overflow-y: scroll">
          <div
            :ref="(el) => (eRefArr[item.key] = el)"
            v-for="item in echartsList"
            :key="item.key"
            style="height: 180px"
          ></div>
        </div>
        <div
          v-show="isShowTable"
          class="table-container"
          style="
            position: absolute;
            top: 50px;
            right: 16px;
            background: white;
            color: rgb(102, 102, 102);
            width: 360px;
            border-radius: 4px;
          "
          :style="{ width: sumColumnWidth }"
        >
          <div
            style="
              display: flex;
              background: #f5f8fc;
              box-shadow: inset 0px 0px 12px 0px rgba(56, 96, 255, 0.12);
              border-radius: 4px 4px 0 0;
              font-weight: 600;
              white-space: nowrap;
            "
          >
            <div
              v-show="field.isShow"
              v-for="field in tableColumns"
              :key="field.dataIndex"
              :style="{
                width: field.width + 'px',
                padding: '12px 8px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }"
            >
              {{ field.title }}
            </div>
          </div>
          <VScroll :itemHeight="42" :items="tableData" :height="500">
            <template #default="{ item }">
              <div style="display: flex; border-bottom: 1px solid #f5f6f7; white-space: nowrap">
                <div
                  v-show="field.isShow"
                  v-for="field in tableColumns"
                  :key="field.dataIndex"
                  :style="{ width: field.width + 'px', padding: '12px 8px' }"
                >
                  {{ item[field.dataIndex] }}
                </div>
              </div>
            </template>
          </VScroll>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref, defineProps, computed } from 'vue';
  import type { Ref } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import type { EChartsOption } from 'echarts';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { RangePicker, Select, Dropdown, Menu, Checkbox, Tree } from 'ant-design-vue';
  // import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';
  import { cloneDeep } from 'lodash-es';
  import { callResourceFunction, getSenceGroupIndicatorTree } from '/@zhcz/api/scenes-group';
  import { VScroll } from '/@/components/VirtualScroll/index';
  import treeItemChecked from '/@zhcz/assets/images/tree-item-checked.png';
  import treeItemIcon from '/@zhcz/assets/images/tree-item-icon.png';
  import echarts from '/@/utils/lib/echarts';
  import { baseOptions } from './data';

  const props = defineProps({
    // 单指标/多指标
    modalType: {
      type: String,
      default: 'single',
      validator: (val: string) => ['single', 'multi'].includes(val),
    },
    // 主题
    theme: {
      type: String,
      default: 'light',
      validator: (val: string) => ['light', 'dark'].includes(val),
    },
    groupInfo: {
      type: Object,
      default: () => ({
        groupCode: '',
        resourceInterfaceId: '',
        paramsData: '',
        jsConvert: false,
      }),
    },
  });

  // 基础图表
  const baseChartRef = ref<HTMLDivElement | null>(null);
  const { setOptions: setBaseChartOptions, resize: baseChartResize } = useECharts(
    baseChartRef as Ref<HTMLDivElement>,
  );
  const baseChartData = ref([]);
  const baseChartOption: any = ref(cloneDeep(baseOptions));
  // 环比图表
  const lastMonthChartRef = ref<HTMLDivElement | null>(null);
  const { setOptions: setLastMonthChartOptions, resize: lastMonthChartResize } = useECharts(
    lastMonthChartRef as Ref<HTMLDivElement>,
  );
  const lastMonthChartData = ref([]);
  const lastMonthChartOption: any = ref(cloneDeep(baseOptions));
  // 年同比图表
  const lastYearChartRef = ref<HTMLDivElement | null>(null);
  const { setOptions: setLastYearChartOptions, resize: lastYearChartResize } = useECharts(
    lastYearChartRef as Ref<HTMLDivElement>,
  );
  const lastYearChartData = ref([]);
  const lastYearChartOption: any = ref(cloneDeep(baseOptions));

  const dateType: any = ref('date');
  const dateValue: any = ref([dayjs(), dayjs()]);
  const dropdownVisible = ref(false);
  const lastMonthChecked = ref(false);
  const lastYearChecked = ref(false);
  const isShowTable = ref(false);
  const tableColumns = ref([
    {
      title: '时间',
      dataIndex: 'collectDateTime',
      width: 180,
      isShow: true,
    },
    {
      title: '',
      dataIndex: 'value',
      width: 180,
      isShow: true,
    },
    {
      title: '环比值',
      dataIndex: 'lastMonthValue',
      width: 100,
      isShow: false,
    },
    {
      title: '年同比值',
      dataIndex: 'lastYearValue',
      width: 100,
      isShow: false,
    },
  ]);
  const tableData: any = ref([]);
  const loading = ref(false);
  const indicatorTreeData = ref([]);
  // const selectedNodes = ref([]);
  const expandedKeys = ref([]);
  const treeFieldNames = {
    title: 'label',
    key: 'value',
    children: 'children',
  };
  const echartsList: any = ref([]);
  // const echartsRefObj: any = ref({});
  const eRefArr: any = ref({});

  const sumColumnWidth = computed(() => {
    let sum = 0;
    tableColumns.value.forEach((item) => {
      if (item.isShow) {
        sum += item.width;
      }
    });
    return sum + 'px';
  });

  const [register, { openModal }] = useModal();

  const warpClassName = computed(() => {
    return 'data-curve-modal' + ' ' + `modal-container-${props.theme}`;
  });

  // 环比/年同比切换
  async function handleToggleMoreEcharts() {
    if (lastMonthChecked.value) {
      const result = await queryIndicatorData('lastMonth');
      setChartOptions('lastMonth', result);
    } else {
      lastMonthChartData.value = [];
      refreshChartOption();
      mergeTableData();
    }
    if (lastYearChecked.value) {
      const result = await queryIndicatorData('lastYear');
      setChartOptions('lastYear', result);
    } else {
      lastYearChartData.value = [];
      refreshChartOption();
      mergeTableData();
    }
  }

  // 注册多指标echarts
  // function registerEchart(dom, key) {
  //   console.log('registerEchart', dom);
  //   if (!dom || echartsRefObj.value[key]) {
  //     console.log('return');
  //     return;
  //   }
  //   echartsRefObj.value[key] = {
  //     chartInstance: echarts.init(dom),
  //     chartOption: cloneDeep(baseOptions),
  //     chartData: [],
  //   };
  //   queryIndicatorData(key);
  // }

  // 多指标请求
  // async function queryIndicatorData1(key) {
  //   const findNode = echartsList.value.find((item) => item.value === key);
  //   const params = {
  //     groupCode: findNode.groupCode,
  //     resourceInterfaceId: findNode.resourceInterfaceId,
  //     jsConvert: false,
  //     paramsData: JSON.stringify({
  //       factoryid: 2001,
  //       startDateTime: getRangeDate().beginDate,
  //       endDateTime: getRangeDate().endDate,
  //       indexCodes: findNode.indexCode,
  //     }),
  //   };
  //   await callResourceFunction(params).then((res) => {
  //     const result = res.data[0];
  //     echartsRefObj.value[key].chartOption.series[0].name = result.indexName;
  //     echartsRefObj.value[key].chartOption.xAxis.data = result.data.map(
  //       (item) => item.collectDateTime,
  //     );
  //     echartsRefObj.value[key].chartOption.series[0].data = result.data.map((item) => item.value);
  //     echartsRefObj.value[key].chartData = result.data;
  //     echartsRefObj.value[key].chartInstance.setOption(
  //       echartsRefObj.value[key].chartOption as EChartsOption,
  //     );
  //   });
  // }

  function handleSelectNode(_selectedKeys, e) {
    if (e.node.selected) {
      echartsList.value.splice(echartsList.value.indexOf(e.node), 1);
      // delete echartsRefObj.value[e.node.key];
    } else {
      echartsList.value.push(e.node);
    }
    const params = {
      groupCode: e.node.groupCode,
      resourceInterfaceId: e.node.resourceInterfaceId,
      jsConvert: false,
      paramsData: JSON.stringify({
        factoryid: 2001,
        startDateTime: getRangeDate().beginDate,
        endDateTime: getRangeDate().endDate,
        indexCodes: e.node.indexCode,
      }),
    };
    callResourceFunction(params).then((res) => {
      const result = res.data[0];
      const option = {
        grid: {
          width: '94%',
          left: '4%',
          top: 25,
        },
        legend: {
          show: true,
        },
        xAxis: {
          type: 'category',
          data: [],
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            formatter: function (value) {
              return dayjs(value).format('HH:mm');
            },
          },
          axisPointer: {
            show: true,
          },
        },
        yAxis: {},
        series: [
          {
            name: '',
            data: [],
            type: 'line',
            color: '',
            symbol: 'none',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '',
                  },
                  {
                    offset: 1,
                    color: '',
                  },
                ],
              },
            },
          },
        ],
      };
      option.series[0].name = result.indexName;
      option.series[0].color = '#2D82FE';
      option.series[0].areaStyle.color.colorStops[0].color = 'rgba(45,130,254,0.2)';
      option.series[0].areaStyle.color.colorStops[1].color = 'rgba(45,130,254,0)';
      option.xAxis.data = result.data.map((item) => item.collectDateTime);
      option.series[0].data = result.data.map((item) => item.value);
      const chartInstance = echarts.init(eRefArr.value[e.node.key]);
      chartInstance.setOption(option as EChartsOption);
    });
  }

  function processTreeNodes(data) {
    data.forEach((node) => {
      node.selectable = false;
      if (node.children && node.children.length > 0) {
        processTreeNodes(node.children);
      }
      if (node.indicators && node.indicators.length > 0) {
        node.children = node.children || [];
        node.children = node.children.concat(
          node.indicators.map((item) => ({
            label: item.name,
            value: node.groupCode + item.code,
            indexCode: item.code,
            groupCode: node.groupCode,
            resourceInterfaceId: node.defaultInterfaceId,
            selectable: true,
          })),
        );
      }
    });
  }

  // 查询指标树
  function queryIndicatorTree() {
    getSenceGroupIndicatorTree({ platformId: 1 }).then((res) => {
      processTreeNodes(res.data);
      indicatorTreeData.value = res.data;
      console.log(indicatorTreeData.value);
    });
  }

  // 处理table数据
  function mergeTableData() {
    tableColumns.value.forEach((item) => {
      if (item.dataIndex === 'lastMonthValue') {
        item.isShow = lastMonthChecked.value;
      }
      if (item.dataIndex === 'lastYearValue') {
        item.isShow = lastYearChecked.value;
      }
    });
    tableData.value = baseChartData.value.map((item: any) => ({
      collectDateTime: item.collectDateTime,
      value: item.value,
      lastMonthValue: 0,
      lastYearValue: 0,
    }));
    lastMonthChartData.value.forEach((item: any) => {
      tableData.value.push({
        collectDateTime: dayjs(item.collectDateTime)
          .subtract(1, 'month')
          .format('YYYY-MM-DD HH:mm:ss'),
        value: 0,
        lastMonthValue: item.value,
        lastYearValue: 0,
      });
    });
    lastYearChartData.value.forEach((item: any) => {
      tableData.value.push({
        collectDateTime: dayjs(item.collectDateTime)
          .subtract(1, 'year')
          .format('YYYY-MM-DD HH:mm:ss'),
        value: 0,
        lastMonthValue: 0,
        lastYearValue: item.value,
      });
    });
  }

  function toggleShowTable() {
    isShowTable.value = !isShowTable.value;
  }

  async function handleRefresh() {
    const result = await queryIndicatorData('base');
    setChartOptions('base', result);
    if (lastMonthChecked.value) {
      const result = await queryIndicatorData('lastMonth');
      setChartOptions('lastMonth', result);
    }
    if (lastYearChecked.value) {
      const result = await queryIndicatorData('lastYear');
      setChartOptions('lastYear', result);
    }
  }

  function handleChangeDate(status) {
    if (status) {
      return;
    }
    handleRefresh();
  }

  function getRangeDate() {
    let beginDate, endDate;
    if (dateType.value === 'date') {
      beginDate = dayjs(dateValue.value[0]).startOf('day');
      endDate = dayjs(dateValue.value[1]).endOf('day');
    } else {
      beginDate = dayjs(dateValue.value[0]).startOf(dateType.value);
      endDate = dayjs(dateValue.value[1]).endOf(dateType.value);
    }
    return { beginDate, endDate };
  }

  /**
   * 设置图表配置
   * @param type base:默认曲线 lastMonth:环比曲线 lastYear:年同比曲线
   * @param data 指标数据对象
   */
  function setChartOptions(type = 'base', result: any = {}) {
    const option: any = cloneDeep(baseOptions);
    const themeColor = props.theme === 'dark' ? '#fff' : '#999';
    option.series[0].name = result.indexName;
    if (type !== 'base') {
      option.series[0].name += type === 'lastMonth' ? '-环比' : '-年同比';
    }
    option.xAxis.axisLabel.color = themeColor;
    option.yAxis.nameTextStyle.color = themeColor;
    option.yAxis.axisLabel.color = themeColor;
    option.xAxis.axisLine.lineStyle.color = themeColor;
    option.legend.textStyle.color = themeColor;
    option.dataZoom.textStyle.color = themeColor;
    if (result.data.length) {
      option.xAxis.data = result.data.map((item) => item.collectDateTime);
      option.series[0].data = result.data.map((item) => item.value);
    } else {
      const beginDate =
        type === 'base'
          ? getRangeDate().beginDate
          : getRangeDate().beginDate.subtract(1, type === 'lastMonth' ? 'month' : 'year');
      const endDate =
        type === 'base'
          ? getRangeDate().endDate
          : getRangeDate().endDate.subtract(1, type === 'lastMonth' ? 'month' : 'year');
      const days = endDate.diff(beginDate, 'day');
      for (let i = 0; i <= days; i++) {
        option.xAxis.data.push(dayjs(beginDate).add(i, 'day').format('YYYY-MM-DD HH:mm:ss'));
        option.series[0].data.push(0);
      }
    }
    if (type === 'base') {
      option.series[0].color = '#2D82FE';
      option.series[0].areaStyle.color.colorStops[0].color = 'rgba(45,130,254,0.2)';
      option.series[0].areaStyle.color.colorStops[1].color = 'rgba(45,130,254,0)';
      baseChartOption.value = option;
      tableColumns.value[1].title = result.indexName;
      baseChartData.value = result.data;
    } else if (type === 'lastMonth') {
      option.series[0].color = '#1FC3A4';
      option.series[0].areaStyle.color.colorStops[0].color = 'rgba(31,195,164,0.2)';
      option.series[0].areaStyle.color.colorStops[1].color = 'rgba(31,195,164,0)';
      lastMonthChartOption.value = option;
      lastMonthChartData.value = result.data;
    } else if (type === 'lastYear') {
      option.series[0].color = '#FEC52D';
      option.series[0].areaStyle.color.colorStops[0].color = 'rgba(254,197,45,0.2)';
      option.series[0].areaStyle.color.colorStops[1].color = 'rgba(254,197,45,0)';
      lastYearChartOption.value = option;
      lastYearChartData.value = result.data;
    }
    refreshChartOption();
    mergeTableData();
  }

  /**
   * 单指标查询数据
   * @param type base:默认曲线 lastMonth:环比曲线 lastYear:年同比曲线
   */
  async function queryIndicatorData(type = 'base') {
    const params: any = {
      groupCode: props.groupInfo.groupCode,
      resourceInterfaceId: props.groupInfo.resourceInterfaceId,
      jsConvert: props.groupInfo.jsConvert,
      paramsData: {
        factoryid: 2001,
        startDateTime: getRangeDate().beginDate.format('YYYY-MM-DD HH:mm:ss'),
        endDateTime: getRangeDate().endDate.format('YYYY-MM-DD HH:mm:ss'),
        indexCodes: props.groupInfo.indexCodes,
      },
    };
    if (type !== 'base') {
      params.paramsData.startDateTime = getRangeDate()
        .beginDate.subtract(1, type === 'lastMonth' ? 'month' : 'year')
        .format('YYYY-MM-DD HH:mm:ss');
      params.paramsData.endDateTime = getRangeDate()
        .endDate.subtract(1, type === 'lastMonth' ? 'month' : 'year')
        .format('YYYY-MM-DD HH:mm:ss');
    }
    params.paramsData = JSON.stringify(params.paramsData);
    let result: any = [];
    await callResourceFunction(params).then((res) => {
      if (res.code === 0) {
        result = res.data[0];
      }
    });

    return result;
  }

  // 刷新图表数据
  function refreshChartOption() {
    let domHeight = 540;
    let domCount = 1;
    if (lastMonthChecked.value) {
      domCount++;
    }
    if (lastYearChecked.value) {
      domCount++;
    }
    domHeight = domHeight / domCount;
    baseChartRef.value!.style.height = domHeight + 'px';
    baseChartOption.value.grid.height = domHeight - 50 + 'px';
    baseChartResize();
    setBaseChartOptions(baseChartOption.value as EChartsOption);
    if (lastMonthChecked.value) {
      lastMonthChartRef.value!.style.height = domHeight + 'px';
      lastMonthChartOption.value.grid.height = domHeight - 50 + 'px';
      lastMonthChartResize();
      setLastMonthChartOptions(lastMonthChartOption.value as EChartsOption);
    }
    if (lastYearChecked.value) {
      lastYearChartRef.value!.style.height = domHeight + 'px';
      lastYearChartOption.value.grid.height = domHeight - 50 + 'px';
      lastYearChartResize();
      setLastYearChartOptions(lastYearChartOption.value as EChartsOption);
    }
  }

  async function openCurve() {
    openModal();
    if (props.modalType === 'single') {
      const result = await queryIndicatorData('base');
      setChartOptions('base', result);
    } else {
      queryIndicatorTree();
    }
  }

  defineExpose({
    openCurve,
  });
</script>

<style lang="less" scoped>
  .content-container {
    display: flex;
    border-top: 1px solid #f5f6f7;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    // padding: 24px;
    padding: 18px;

    .tree-content {
      width: 270px;
      flex-shrink: 0;

      :deep(.ant-tree) {
        background: transparent;

        .ant-tree-treenode {
          height: 40px;
          align-items: center;
          padding: 0;

          .ant-tree-switcher {
            width: auto;
            min-width: 12px;
            display: flex;
            align-items: center;
          }

          .ant-tree-node-content-wrapper {
            line-height: 40px;
            padding: 0 12px;
            margin-left: 8px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            .active-box {
              display: flex;
              align-items: center;
              justify-content: space-between;

              span {
                width: 85%;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
          }

          .ant-tree-node-selected {
            background: none;
            color: @theme-color;
          }
        }
      }
    }

    .tool-container {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .left-box {
        display: flex;
        align-items: center;

        & > div {
          display: flex;
          align-items: center;
        }

        .date-tool-box {
          height: 32px;
          border: 1px solid #e9e9e9;
          border-radius: 2px;

          .anticon {
            width: 40px;
            color: #999;
            cursor: pointer;
          }

          .date-text {
            width: 60px;
            text-align: center;
            line-height: 32px;
            border-left: 1px solid #e9e9e9;
            border-right: 1px solid #e9e9e9;
          }
        }
      }

      .right-box {
        display: flex;
        align-items: center;

        .icon {
          width: 16px;
          height: 16px;
          margin-left: 16px;
          cursor: pointer;

          &:hover {
            background-size: 16px 16px;
          }
        }

        .refresh-icon {
          background: url('./assets/refresh.png') no-repeat center;
          background-size: 16px 16px;

          &:hover {
            background: url('./assets/refresh_hover.png') no-repeat center;
            background-size: 16px 16px;
          }
        }

        .filter-icon {
          background: url('./assets/filter.png') no-repeat center;
          background-size: 16px 16px;

          &:hover {
            background: url('./assets/filter_hover.png') no-repeat center;
            background-size: 16px 16px;
          }
        }

        .table-icon {
          background: url('./assets/table.png') no-repeat center;
          background-size: 16px 16px;

          &:hover {
            background: url('./assets/table_hover.png') no-repeat center;
            background-size: 16px 16px;
          }
        }

        .full-screen-icon {
          background: url('./assets/full_screen.png') no-repeat center;
          background-size: 16px 16px;

          &:hover {
            background: url('./assets/full_screen_hover.png') no-repeat center;
            background-size: 16px 16px;
          }
        }
      }
    }

    .chart-container {
      padding: 20px 0;
      position: relative;

      & > div:not(:last-child) {
        margin-bottom: 24px;
      }

      .table-container {
        position: absolute;
        width: 40%;
        right: -2%;
        top: 0;
      }
    }
  }
</style>

<style lang="less">
  .data-curve-modal {
    line-height: 1;

    .ant-modal .ant-modal-content {
      padding: 24px 0 16px;

      .vben-basic-title {
        text-indent: 24px;
        line-height: 1;
      }

      .ant-modal-footer {
        margin-top: 16px;
        padding: 0 24px;

        & > div {
          display: flex;
          justify-content: flex-end;
        }

        button {
          display: flex;
          align-items: center;
          padding: 14px 28px;
          height: 40px;
          border-radius: 2px;
        }
      }
    }
  }

  .modal-container {
    &-dark {
      .ant-modal-content {
        background: #24282f;
        color: white;

        .vben-basic-modal-close > span {
          color: white;
        }

        .ant-modal-header {
          background: none;

          .vben-basic-title {
            color: white;
          }
        }

        .content-container {
          border-top: 1px solid rgba(255, 255, 255, 0.1);

          .tool-container {
            .ant-select-single.ant-select-open .ant-select-selection-item {
              color: white;
            }

            .ant-select-selector {
              background: none;
              border: 1px solid rgba(255, 255, 255, 0.2);
              color: white;
            }

            .ant-select-arrow {
              color: rgba(255, 255, 255, 0.6);
            }

            .ant-picker {
              background: none;
              border: 1px solid rgba(255, 255, 255, 0.2);

              .ant-picker-input input,
              .ant-picker-suffix {
                color: rgba(255, 255, 255, 0.6);
              }
            }
          }
        }

        .ant-modal-footer {
          button {
            color: white;
          }

          .ant-btn-default {
            border-color: rgba(255, 255, 255, 0.2);
            background: none;
          }
        }
      }
    }
  }
</style>
