<template>
  <div class="MIB-early-warning">
    <weatherBar :updata="updataTime" />
    <earlyWarningScale :updata="updataTime" class="early_left" />
    <messageAlarm :updata="updataTime" />
    <centerImage :updata="updataTime" />
    <div class="content-sections">
      <solution :updata="updataTime" />
      <futurePredictions :updata="updataTime" />
      <onlineMonitoringOfAlgae :updata="updataTime" />
      <historicalMonitoringData :updata="updataTime" />
    </div>
    <div class="footer">
      <img src="./assets/images/water-technology.png" alt="" srcset="" />
    </div>
  </div>
</template>

<script lang="ts" setup name="MIBEarlyWarning">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { useIntervalFn } from '@vueuse/core';
  import weatherBar from './component/weather-bar.vue';
  import messageAlarm from './component/message_alarm.vue';
  import centerImage from './component/center-image.vue';
  import earlyWarningScale from './component/early-warning-scale.vue';
  import solution from './component/solution.vue';
  import futurePredictions from './component/future-predictions.vue';
  import onlineMonitoringOfAlgae from './component/online-monitoring-of-algae.vue';
  import historicalMonitoringData from './component/historical-monitoring-data.vue';

  const updataTime = ref<number>(1);
  const getAllData = () => {
    updataTime.value += 1;
  };
  const { resume, pause } = useIntervalFn(getAllData, 6 * 1000);
  onUnmounted(() => {
    pause();
  });
  onMounted(() => {
    resume();
  });
</script>

<style lang="less" scoped>
  .MIB-early-warning {
    // width: 100vw;
    width: 1080px;
    // min-width: 1080px;
    height: 1920px;
    // min-height: 800px;
    margin: 0 auto;
    background: url('./assets/images/center-bg.png') no-repeat center center;
    // background: linear-gradient(180deg, #ffffff 0%, #d8e8f9 100%);
    border-radius: 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    padding: 0;
    overflow-y: auto;
    position: relative;

    .early_left {
      position: absolute;
      top: 13.95%;
      left: 36px;
    }

    /* 响应式适配 */
    // @media (max-width: 1200px) {
    //   min-width: 100vw;
    //   padding: 0;

    //   .title-section .main-title {
    //     font-size: 24px;
    //   }

    //   .content-sections {
    //     padding: 0 16px 16px;
    //     gap: 16px;
    //   }

    //   .footer {
    //     padding: 16px 0;

    //     .footer-logo {
    //       .logo-text {
    //         font-size: 24px;
    //       }

    //       .logo-desc {
    //         font-size: 14px;
    //       }
    //     }
    //   }
    // }

    .content-sections {
      width: 100%;
      max-width: 1600px;
      padding: 0 24px;
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .footer {
      width: 100%;
      height: 64px;
      margin: auto;
      text-align: center;

      & > img {
        height: 100%;
      }
    }
  }

  // .weather-bar {
  //   width: 100%;
  //   max-width: 1600px;
  //   min-height: 60px;
  //   margin-bottom: 12px;
  // }

  // .center-image {
  //   width: 100%;
  //   max-width: 900px;
  //   min-height: 400px;
  //   margin: 0 auto 16px auto;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  // }

  // .solution,
  // .future-predictions,
  // .algae-monitoring,
  // .historical-monitoring-data {
  //   width: 100%;
  //   max-width: 1600px;
  //   margin: 0 auto 16px auto;
  //   border-radius: 16px;
  //   box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  //   background: #fff;
  //   padding: 24px 32px;
  //   box-sizing: border-box;
  // }

  // @media (max-width: 1200px) {
  //   .solution,
  //   .future-predictions,
  //   .algae-monitoring,
  //   .historical-monitoring-data {
  //     padding: 12px 8px;
  //     border-radius: 8px;
  //   }

  //   .center-image {
  //     min-height: 200px;
  //   }
  // }
</style>
