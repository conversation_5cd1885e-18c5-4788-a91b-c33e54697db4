<template>
  <div
    class="produce-data-chart"
    :style="`width: 100%; height: ${psHeight ? psHeight + '%' : 'calc(100%)'}`"
  >
    <div ref="chartRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch, nextTick } from 'vue';
  import { uniqBy } from 'lodash-es';
  import { useECharts } from '/@zhcz/hooks/useECharts';
  import { hasMax, roundFun, getEchartFontSize } from '../../utils';
  import { roundAndConvert } from '/@zhcz/utils/number';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({
        title: '近12小时余氯曲线',
        chartOptions: {
          xAxis: {
            data: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00'],
          },
          series: [
            {
              name: '进水量',
              color: '#2D82FE',
              data: [5, 11, 8, 9, 7, 10, 12],
              unitName: 'm³',
              areaColor: ['rgba(45, 130, 254, 0.3)', 'transparent'],
            },
          ],
        },
      }),
    },
    psHeight: {
      type: Number,
      default: 0,
    },
    tooltipWidth: {
      type: Number,
      default: 0,
    },
  });

  const chartRef: any = ref(null);
  const { setOptions, echarts } = useECharts(chartRef);
  function getMaxValue(arr) {
    const max = Math.max(...arr);
    // 这样处理是为了不让最大值刚好到坐标轴最顶部
    return Math.ceil(max / 8) * 10;
  }
  function getMinValue(arr) {
    const min = Math.min(...arr);
    // 这样处理是为了不让最大值刚好到坐标轴最底部
    return Math.floor(min / 12) * 10;
  }
  // 返回字符串ascii码
  function getStrAscii(str) {
    let result = '';
    for (let i = 0; i < str.length; i++) {
      result += str.charCodeAt(i);
    }
    return Number(result);
  }
  async function renderChart() {
    try {
      const { title, chartOptions } = props.data;
      console.log('title', title, chartOptions);
      if (chartOptions && chartOptions.series) {
        const isW = hasMax(chartOptions.series);
        let yAxis: any = {
          type: 'value',
          offset: -5,
          nameTextStyle: {
            fontSize: getEchartFontSize(14),
            color: '#fff',
            // padding: chartOptions.yAxis ? chartOptions.yAxis.nameTextStyle.padding : [0, 0, -5, 0],
          },
          splitNumber: 4,
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255,255,255,0.2)',
              type: 'dashed',
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: getEchartFontSize(14),
            },
            formatter(value) {
              const val = isW ? `${roundFun(value / 10000, 2)}万` : value;
              return val;
            },
          },
        };
        let yAxisData: any[] = uniqBy(chartOptions.series, 'unitName').sort(
          (a: any, b: any) => getStrAscii(a.unitName) - getStrAscii(b.unitName),
        );
        // console.log('yAxisData', yAxisData);
        const Data1Merge = chartOptions.series
          .filter((i) => i.unitName === yAxisData[0].unitName)
          .map((item) => item.data)
          .flatMap((arr) => arr);
        let max1 = 0;
        let max2 = 0;
        if (yAxisData.length >= 2) {
          const Data2Merge = chartOptions.series
            .filter((i) => i.unitName === yAxisData[1].unitName)
            .map((item) => item.data)
            .flatMap((arr) => arr);
          // const min1 = getMinValue(Data1Merge, 'y1');
          // max1 = getMaxValue(Data1Merge, 'y1') || 1;
          // const min2 = getMinValue(Data2Merge, 'y2');
          // max2 = getMaxValue(Data2Merge, 'y2') || 1;
          const min1 = getMinValue(Data1Merge);
          max1 = getMaxValue(Data1Merge) || 1;
          const min2 = getMinValue(Data2Merge);
          max2 = getMaxValue(Data2Merge) || 1;
          yAxis = [];
          // console.log('max1', max1, 'max2', max2);
          yAxisData = yAxisData.filter((item) => item.unitName);
          const y1 = {
            type: 'value',
            // name: yAxisData[0].unitName ? `${yAxisData[0].unitName}` : '',
            nameTextStyle: {
              fontSize: getEchartFontSize(14),
              color: '#fff',
              align: 'center',
              // padding: [0, 0, 0, 0],
            },
            min: min1,
            max: max1,
            splitNumber: 4,
            interval: (max1 - min1) / 4,
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255,255,255,0.2)',
                type: 'dashed',
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#fff',
                fontSize: getEchartFontSize(14),
              },
              formatter(value) {
                const val = isW ? `${roundFun(value / 10000, 2)}万` : value;
                return val;
              },
            },
          };
          const y2 = {
            type: 'value',
            // name: yAxisData[1].unitName ? `${yAxisData[1].unitName}` : '',
            nameTextStyle: {
              fontSize: getEchartFontSize(14),
              color: '#fff',
              align: 'center',
              // padding: [0, 0, -5, 0],
            },
            splitNumber: 4,
            interval: (max2 - min2) / 4,
            min: min2,
            max: max2,
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255,255,255,0.2)',
                type: 'dashed',
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#fff',
                fontSize: getEchartFontSize(14),
              },
              formatter(value) {
                const val = isW ? `${roundFun(value / 10000, 2)}万` : value;
                return val;
              },
            },
          };
          yAxis.push(...[y1, y2]);
        } else {
          // yAxis.name = yAxisData[0]?.unitName ? `${yAxisData[0].unitName}` : '';
        }
        const series = chartOptions.series.map((item) => {
          const index = yAxisData.findIndex((i) => i.unitName === item.unitName);
          const result: any = {
            name: item.name,
            color: item.color,
            data: item.data.map((i) => (i !== null ? roundAndConvert(Number(i), 2) : null)),
            type: 'line',
            smooth: true,
            // data.symbol = 'none';
            symbol: 'none',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: item.areaColor[0],
                },
                {
                  offset: 1,
                  color: item.areaColor[1],
                },
              ]),
            },
          };
          if (index !== -1) {
            result.yAxisIndex = index;
          }
          return result;
        });
        // console.log('series', series);
        const option = {
          title: {
            left: 0,
            // text: title,
            textStyle: {
              color: '#ffffff',
              fontWeight: '400',
              fontSize: getEchartFontSize(14),
            },
            padding: [8, 0, 5, 0],
          },
          tooltip: {
            trigger: 'axis',
            backgroundColor: '#132b4e',
            borderColor: '#132b4e',
            textStyle: {
              color: '#fff',
            },
            formatter: (params) => {
              // console.log('params', params);
              let item;
              if (Array.isArray(params)) {
                // 适用折线柱状堆叠的多条数据
                item = params.filter((item) => item.value !== undefined);
              } else {
                item = [params];
              }
              // console.log('item', item);
              if (item.length) {
                let htmlStr = ``;
                item.forEach((val, index) => {
                  const isW = hasMax(Number(val.value) ? Number(val.value) : 0);
                  const value = isW ? `${roundFun(val.value / 10000, 2)}万` : val.value;
                  if (Array.isArray(params)) {
                    // 适用折线柱状堆叠的多条数据
                    const unitName =
                      chartOptions.series.find((i) => val.seriesName === i.name)?.unitName || '';
                    htmlStr += `<div>${index === 0 ? val.name : ''}</div>
                    ${val.marker} <span style="display: inline-block; width: 50px;">${
                      val.seriesName
                    }</span><span  style="display: inline-block; width: ${
                      props.tooltipWidth ? props.tooltipWidth + 'px' : '100px'
                    }; font-weight: bold; text-align: right;">${value}</span><span  style="padding-left: 4px">${unitName}</span>`;
                  } else {
                    // 饼图
                    const unitName =
                      chartOptions.series[0].data.find((i) => val.name === i.name)?.unitName || '';
                    htmlStr += `
                    ${val.marker} <span style="display: inline-block; width: 30px;">${
                      val.name
                    }</span><span  style="display: inline-block; width: ${
                      props.tooltipWidth ? props.tooltipWidth + 'px' : '100px'
                    }; font-weight: bold; text-align: right;">${value}</span><span  style="padding-left: 4px">${unitName}</span>`;
                  }
                });
                return htmlStr;
              }
              return;
            },
          },
          legend: {
            show: true,
            type: 'scroll',
            icon: 'circle',
            top: -5,
            left: Array.isArray(yAxis) ? 'center' : yAxis.name ? 120 : 'center',
            itemGap: 16,
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
              fontSize: getEchartFontSize(14),
              color: '#fff',
              // padding: [3, 5]
            },
            pageTextStyle: {
              color: '#fff',
            },
            pageIconColor: '#fff',
            pageIconInactiveColor: 'rgba(255,255,255, 0.56)',
          },
          grid: {
            left: Array.isArray(yAxis) ? 0 : yAxis.name ? 0 : 10,
            right: Array.isArray(yAxis) && yAxis.length === 2 ? 0 : 25,
            top: 30,
            // top: Array.isArray(yAxis) ? 80 : 70,
            bottom: 0,
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: true,
              data: chartOptions.xAxis.data,
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.8)',
                  type: 'solid',
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  color: '#ffffff',
                  fontSize: getEchartFontSize(14),
                },
              },
              axisPointer: {
                type: 'shadow',
              },
            },
          ],
          yAxis: yAxis,
          series: series,
        };
        console.log('option', series, option);
        await nextTick();
        setOptions(option);
      }
    } catch (error) {
      console.log('error', error);
    }
  }
  watch(
    () => props.data,
    async () => {
      renderChart();
    },
  );
  onMounted(() => {
    renderChart();
  });
</script>

<style lang="less" scoped>
  .produce-data-chart {
    position: relative;
    // z-index: 999;
  }
</style>
