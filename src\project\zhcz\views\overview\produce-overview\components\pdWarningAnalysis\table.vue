<template>
  <div class="index-list" :class="props.className">
    <div class="table-head">
      <div class="thead" v-for="(item, index) in tableHead" :key="index">{{ item.name }}</div>
    </div>
    <div class="content">
      <template v-if="!loading">
        <template v-if="data.length">
          <div class="item" v-for="(item, index) in data" :key="index">
            <div class="item-left">
              <div class="label" @mouseenter="showTooltip">
                <Tooltip>
                  <template #title>{{ item.topic || '-' }}</template>
                  {{ item.topic || '-' }}
                </Tooltip>
              </div>
            </div>
            <div
              :class="{
                'item-content': true,
                warning_O: item.level && item.level.indexOf('一') !== -1,
                warning_tw: item.level && item.level.indexOf('二') !== -1,
                warning_th: item.level && item.level.indexOf('三') !== -1,
              }"
            >
              <div class="label">{{ item.level }}</div>
            </div>
            <div class="item-right">
              <span class="time">{{ item.time }}</span>
            </div>
          </div>
        </template>
        <span v-else>暂无数据</span>
      </template>
      <template v-else>
        <div style="background: unset"></div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { Tooltip } from 'ant-design-vue';
  function showTooltip(e) {
    if (e.target.clientWidth >= e.target.scrollWidth) {
      e.target.style.pointerEvents = 'none';
    }
  }
  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    data: {
      type: Array as PropType<any[]>,
      default: () => [
        {
          time: '12/26 19:48',
          topic: 'x事件',
          level: '一级报警',
        },
        {
          time: '12/26 19:48',
          topic: 'x事件',
          level: '二级报警',
        },
        {
          time: '12/26 19:48',
          topic: 'x事件',
          level: '三级报警',
        },
        {
          time: '12/26 19:48',
          topic: 'x事件',
          level: '一级报警',
        },
        {
          time: '12/26 19:48',
          topic: 'x事件',
          level: '二级报警',
        },
        {
          time: '12/26 19:48',
          topic: 'x事件',
          level: '三级报警',
        },
        {
          time: '12/26 19:48',
          topic: 'x事件',
          level: '一级报警',
        },
        {
          time: '12/26 19:48',
          topic: 'x事件',
          level: '二级报警',
        },
        {
          time: '12/26 19:48',
          topic: 'x事件',
          level: '三级报警',
        },
        {
          time: '12/26 19:48',
          topic: 'x事件',
          level: '一级报警',
        },
      ],
    },
    tableHead: {
      type: Array as PropType<{ name: string }[]>,
      default: () => [{ name: '报警名称' }, { name: '报警级别' }, { name: '报警时间' }],
    },
    className: {
      type: String,
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });
</script>

<style lang="less" scoped>
  .index-list {
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .table-head {
      padding: 13px 16px;
      display: flex;
      // height: 40px;
      background: rgba(0, 96, 235, 0.24);
      border-radius: 4px 4px 4px 4px;
      justify-content: space-between;

      .thead {
        // flex: 1;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 14px;
        text-align: left;

        &:nth-child(1) {
          flex: 1;
        }

        &:nth-child(3),
        &:nth-child(2) {
          width: 86px;
        }
      }
    }

    .content {
      flex: 1;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 0px;
      }

      .item {
        height: 48px;
        background: transparent;
        color: #ffffff;
        line-height: 14px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        padding: 0 16px;

        .item-left,
        .item-content,
        .item-right {
          font-size: 14px;
          text-align: left;
          display: flex;
          overflow: hidden;
        }

        .item-content,
        .item-right {
          width: 86px;
        }

        .item-left {
          flex: 1;

          .label {
            padding-right: 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .warning_O {
          .label {
            width: auto;
            text-align: center;
            padding: 5px 8px;
            background: rgba(255, 38, 38, 0.4);
            border-radius: 4px 4px 4px 4px;
          }
        }

        .warning_tw {
          .label {
            text-align: center;
            padding: 5px 8px;
            background: rgba(255, 133, 19, 0.4);
            border-radius: 4px 4px 4px 4px;
          }
        }

        .warning_th {
          .label {
            text-align: center;
            padding: 5px 8px;
            background: rgba(229, 183, 0, 0.4);
            border-radius: 4px 4px 4px 4px;
          }
        }
      }
    }
  }
</style>
