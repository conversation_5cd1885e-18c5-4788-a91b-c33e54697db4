<template>
  <PageWrapper contentFullHeight fixed-height>
    <div class="page">
      <div class="search-form">
        <BasicForm @register="registerForm" />
      </div>
      <div class="tab-container">
        <Tabs>
          <TabPane key="1" tab="历史视频">
            <div class="content-container">
              <div class="item-box">
                <img src="../../assets/images/history-test1.png" />
                <img class="play-btn" src="../../assets/images/video-play.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test2.png" />
                <img class="play-btn" src="../../assets/images/video-play.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test3.png" />
                <img class="play-btn" src="../../assets/images/video-play.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test4.png" />
                <img class="play-btn" src="../../assets/images/video-play.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test5.png" />
                <img class="play-btn" src="../../assets/images/video-play.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test6.png" />
                <img class="play-btn" src="../../assets/images/video-play.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test7.png" />
                <img class="play-btn" src="../../assets/images/video-play.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test8.png" />
                <img class="play-btn" src="../../assets/images/video-play.png" />
              </div>
            </div>
          </TabPane>
          <TabPane key="2" tab="历史照片">
            <div class="content-container">
              <div class="item-box">
                <img src="../../assets/images/history-test5.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test8.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test7.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test4.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test1.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test2.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test6.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test3.png" />
              </div>
              <div class="item-box">
                <img src="../../assets/images/history-test2.png" />
              </div>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup>
  import { PageWrapper } from '/@/components/Page';
  import { searchFormSchema } from './data';
  import { BasicForm, useForm } from '/@/components/Form';
  import { Tabs, TabPane } from 'ant-design-vue';

  defineOptions({
    name: 'SecurityManagementSafetyHelmetHistoryRecord',
  });

  const [registerForm] = useForm({
    schemas: searchFormSchema,
    showActionButtonGroup: true,
    baseColProps: { span: 24 },
    actionColOptions: {
      span: 12,
      style: {
        paddingLeft: '16px',
        textAlign: 'right',
      },
    },
  });
</script>

<style scoped lang="less">
  .page {
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: #f2f3f5;
    height: 100%;

    // :deep(.ant-form > .ant-row .ant-col:last-child) {
    //   text-align: left;
    // }

    .search-form {
      padding: 0 16px;
      height: 64px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      background-color: #fff;
      flex-shrink: 0;

      // :deep(.ant-row) {
      //   .ant-col {
      //     &:last-child {
      //       text-align: right !important;
      //       flex: 1;
      //       max-width: 100%;

      //       .ant-btn {
      //         &:last-child {
      //           margin-right: 0 !important;
      //         }
      //       }
      //     }
      //   }
      // }

      :deep(.ant-form) {
        width: 100%;

        .ant-form-item {
          margin-bottom: 0;
        }

        .ant-btn-primary {
          background: rgba(43, 99, 161, 0.05);
          color: @primary-color;
          text-shadow: none;

          &:hover {
            background: @theme-color;
            color: #fff;
          }
        }

        .ant-btn:last-child {
          &:hover,
          &:active,
          &:focus {
            background: #f8f8f8;
            color: #333;
            border: 1px solid #e9e9e9;
          }
        }
      }
    }

    .tab-container {
      flex: 1;
      background: #fff;
      height: 100%;
      border-radius: 4px;
      overflow: hidden;

      :deep(.ant-tabs) {
        height: 100%;

        .ant-tabs-nav {
          padding-left: 16px;
          margin-bottom: 0;
        }

        .ant-tabs-content {
          height: 100%;
        }

        .ant-tabs-tabpane {
          height: 100%;
          overflow: auto;
        }
      }

      .content-container {
        display: flex;
        flex-wrap: wrap;
        padding: 8px;

        .item-box {
          width: calc(100% / 4);
          height: 240px;
          padding: 8px;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .play-btn {
            position: absolute;
            width: 48px;
            height: 48px;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
          }
        }
      }
    }
  }
</style>
