<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    :width="700"
  >
    <BasicForm @register="registerForm">
      <template #fileSlot="{ model, field }">
        <!-- <Upload
          v-model:file-list="model[field]"
          :customRequest="uploadApi"
          :multiple="!isEdit"
          accept=".csv, .txt, .doc, .pdf, .excel"
          :maxCount="uploadMaxCount"
        >
          <a-button type="primary" ghost :icon="h(Icon, { icon: 'icon-park-outline:plus' })">
            上传文件
          </a-button>
          <span class="tips">仅支持csv、txt、doc、PDF、excel格式上传</span>
          <template #itemRender="{ file, actions }">
            <div class="upload-item">
              <span :style="file.status === 'error' ? 'color: red' : ''">{{ file.name }}</span>
              <CloseOutlined @click="actions.remove" />
            </div>
          </template>
        </Upload> -->
        <HUpload
          v-model:value="model[field]"
          :dragger="false"
          :isDownload="false"
          accept=".csv,.txt,.doc,.pdf,.xlsx,xls"
          mode="list"
          direction="vertical"
          :maxNumber="1"
          :span="24"
          :gutter="0"
          tips="仅支持csv、txt、doc、PDF、excel格式上传"
        />
      </template>
    </BasicForm>
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">保存</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup name="EditModal">
  import { computed, ref } from 'vue';

  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicForm, useForm } from '/@/components/Form';
  import { fileSchemas } from '../data';

  import { addOrUpdateFileApi } from '/@zhcz/api/security-management/safety-management-agency';

  import { HUpload } from '/@/components/Upload';

  const title = computed(() => (isEdit.value ? '编辑' : '新增'));
  const isEdit = ref(false);
  const okLoading = ref(false);
  const deptId = ref('');
  const recordData: any = ref({});
  // const uploadMaxCount = ref(100);

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 94,
    baseColProps: {
      span: 24,
    },
    schemas: fileSchemas,
    showActionButtonGroup: false,
  });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    isEdit.value = data.isEdit;
    deptId.value = data.deptId;
    if (isEdit.value) {
      recordData.value = data.record;
      // uploadMaxCount.value = 1;
      setFieldsValue({
        ...data.record,
        fileList: [
          {
            uid: data.record.id,
            name: data.record.fileRealName,
            status: 'done',
            url: data.record.fileUrl,
          },
        ],
      });
    }
  });

  // async function uploadApi({ file }) {
  //   const res: any = await comFileUploadRemindingApi(
  //     {
  //       file,
  //     },
  //     function onUploadProgress(progressEvent: ProgressEvent) {
  //       const complete = ((progressEvent.loaded / progressEvent.total) * 100) | 0;
  //       file.percent = complete;
  //     },
  //   );
  //   const values = getFieldsValue();
  //   values['fileList'][values['fileList'].length - 1].url = res.data.url;
  //   setFieldsValue({
  //     fileList: values['fileList'],
  //   });
  // }

  async function handleSubmit() {
    try {
      okLoading.value = true;
      const values = await validate();
      console.log('values', values);
      values.urlNameList = values.fileList.map((item: any) => {
        return {
          fileRealName: item?._file?.file.name || recordData.value.fileRealName,
          fileUrl: item.url,
        };
      });
      values.deptId = deptId.value;
      // return
      await addOrUpdateFileApi(values);
      const msg = isEdit.value ? '编辑成功' : '新增成功';
      createMessage.success(msg);
      handleCancel();
      emit('success');
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    closeModal();
    resetFields();
  }
</script>

<style lang="less" scoped>
  :deep(.ant-upload-list-item-container) {
    font-size: 12px;
    margin-top: 12px;

    .upload-item {
      display: flex;
      height: 28px;
      align-items: center;
      padding: 0 12px;
      background: #eeeff1;
      border-radius: 4px;
      justify-content: space-between;
      .anticon {
        font-size: 12px;
        cursor: pointer;
        color: #999;
      }
    }
  }
  .tips {
    font-size: 12px;
    color: #999;
    margin-left: 12px;
  }
</style>
