<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    destroyOnClose
    width="870px"
  >
    <BasicForm @register="registerForm">
      <template #body-slot="{ model, field }">
        <JsonEditor
          ref="bodyJsonEditorRef"
          v-model:modelValue="model[field]"
          language="json"
          style="height: 120px; border: 1px solid #d9d9d9; border-radius: 4px"
        />
      </template>
      <template #js-convert-slot="{ model, field }">
        <JsonEditor
          ref="jsConvertJsonEditorRef"
          v-model:modelValue="model[field]"
          style="height: 350px; border: 1px solid #d9d9d9; border-radius: 4px"
        />
      </template>
      <template #sql-query-slot="{ model, field }">
        <JsonEditor
          ref="sqlQueryJsonEditorRef"
          v-model:modelValue="model[field]"
          style="height: 200px; border: 1px solid #d9d9d9; border-radius: 4px"
          language="sql"
        />
      </template>
      <template #isConvert-slot="{ model, field }">
        <Switch
          v-model:checked="model[field]"
          checked-children="转化"
          un-checked-children="不转化"
        />
      </template>
    </BasicForm>
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">保存</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup name="EditModal">
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicForm, useForm } from '/@/components/Form';
  import { schemas } from '../data';
  import { Switch } from 'ant-design-vue';
  import { addOrUpdateResourceInterface } from '/@zhcz/api/config-center/scenes-group';
  import { JsonEditor } from '/@/components/JsonEditor';
  import { RESOURCE_TYPE } from '/@zhcz/enums/sceneResource';

  const title = computed(() => (isEdit.value ? '编辑接口' : '新增接口'));
  const isEdit = ref(false);
  const okLoading = ref(false);
  const bodyJsonEditorRef = ref<any>(null);
  const jsConvertJsonEditorRef = ref<any>(null);
  const sqlQueryJsonEditorRef = ref<any>(null);

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 110,
    baseColProps: {
      span: 24,
    },
    schemas,
    showActionButtonGroup: false,
  });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const [registerModal, { closeModal }] = useModalInner((data) => {
    isEdit.value = data.isEdit;
    if (isEdit.value) {
      setFieldsValue({
        ...data.record,
      });
      bodyJsonEditorRef.value?.handleSetValue(data.record.defaultBody);
      jsConvertJsonEditorRef.value?.handleSetValue(data.record.resultJsConvert);
      sqlQueryJsonEditorRef.value?.handleSetValue(data.record.databaseSql);
    }
  });

  async function handleSubmit() {
    try {
      okLoading.value = true;
      const values = await validate();
      const data = {
        ...values,
      };
      if (values.resourceType === RESOURCE_TYPE.DATABASE) {
        data.interfaceUrl = '';
      }
      await addOrUpdateResourceInterface(data);
      const msg = isEdit.value ? '编辑成功' : '新增成功';
      createMessage.success(msg);
      handleCancel();
      emit('success');
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    closeModal();
    resetFields();
  }
</script>
