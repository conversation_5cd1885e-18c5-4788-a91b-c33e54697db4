<template>
  <div ref="chartRef" style="width: 100%; height: 100%"></div>
</template>
<script lang="ts" setup>
  import { onMounted, ref, Ref, watch } from 'vue';
  import dayjs from 'dayjs';
  import { useECharts } from '/@/hooks/web/useECharts';

  const props = defineProps({
    data: {
      type: Object as any,
      default: () => {},
    },
  });

  const chartRef = ref<HTMLDivElement | null>(null);
  const echartData = ref<{ collectDateTime: string; value: number }[]>([]);
  const unit = ref('');
  const indexName = ref('');

  const { setOptions, echarts } = useECharts(chartRef as Ref<HTMLDivElement>);

  const renderEchart = () => {
    const xAxis = echartData.value.map(
      (item) => `${Number(dayjs(item.collectDateTime).format('MM'))}月`,
    );
    const dataList = echartData.value.map((i) => i.value);
    let maxdata = Math.max(...dataList);
    if (isNaN(maxdata)) maxdata = 0;
    const max = Math.ceil(maxdata / 8) * 8;
    const option = {
      legend: {
        show: false,
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          return `${params[0].name}<br/> ${params[0].marker}${
            params[0].seriesName
          }&nbsp;&nbsp;&nbsp;&nbsp;${
            params[0].value === null || params[0].value === undefined ? '-' : params[0].value
          }${
            params[0].value === null || params[0].value === undefined || !unit.value
              ? ''
              : unit.value
          }`;
        },
      },
      grid: {
        top: 5,
        left: '0',
        right: max ? 0 : 15,
        bottom: '0',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: xAxis,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#999999',
              fontSize: 14,
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          // name: unit.value ? `单位（${unit.value}）` : '',
          name: '',
          nameTextStyle: {
            color: '#999',
            fontSize: 14,
            padding: [0, 0, 0, 22],
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          max: max ? max : 8,
          splitNumber: 4,
          interval: max ? max / 4 : 2,
          min: 0,
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#999999',
              fontSize: 14,
            },
          },
        },
      ],
      series: [
        {
          name: indexName.value,
          type: 'line',
          color: 'rgba(46, 123, 255, 1)',
          // lineStyle: {
          //   shadowColor: 'rgba(46, 123, 255, 1)',
          //   shadowBlur: 6,
          // },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(46, 123, 255, 0.24)',
              },
              {
                offset: 1,
                color: 'rgba(46, 123, 255, 0.08)',
              },
            ]),
          },
          symbolSize: 0,
          // emphasis: {
          //   focus: 'series',
          // },
          data: dataList,
        },
      ],
    };
    setOptions(option as any);
  };

  onMounted(() => {
    unit.value = props.data?.unit;
    echartData.value = props.data?.itemDataList || [];
    if (echartData.value.length) {
      renderEchart();
    }
  });

  watch(
    () => props.data,
    () => {
      unit.value = props.data?.unit;
      indexName.value = props.data?.indexName;
      echartData.value = props.data?.itemDataList || [];
      renderEchart();
    },
    {
      deep: true,
    },
  );
</script>
