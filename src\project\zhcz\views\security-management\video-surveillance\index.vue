<template>
  <PageWrapper dense contentFullHeight fixedHeight>
    <div class="page">
      <div class="left-container">
        <div class="tit"> 摄像头地址 </div>
        <div class="classify-container">
          <div
            v-for="(item, index) in classifyList"
            :key="index"
            class="left-item"
            :class="{ active: currentClassify === index }"
            @click="handleChangeClassify(index, item.total)"
          >
            <span
              class="icon"
              :style="{ background: currentClassify === index ? 'var(--theme-color)' : '#999' }"
            ></span>
            <span class="con">{{ item.title }} ({{ item.total }})</span>
          </div>
        </div>
      </div>
      <div class="video-monitoring">
        <div class="video" v-loading="loading" v-if="pathUrl.length">
          <XgVideo :col="2" :row="2" :videos="pathUrl" />
        </div>
        <Empty v-else />
      </div>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { Empty } from '/@zhcz/components/Empty';
  import { XgVideo } from '/@zhcz/components/XgVideo';

  const pathUrl = ref<string[]>([]);
  const loading = ref(false);

  const getPathUrl = (length: number) => {
    pathUrl.value = Array.from({ length }, () => 'http://************:8080/live/livestream-1.flv');
  };

  // const pageIndex = ref(1);
  // const pageSize = ref(10);
  async function getCameraList(params = {}) {
    loading.value = true;
    console.log('params', params);
    try {
      // const res = await getCameraInfoPageList({
      //   pageIndex: pageIndex.value,
      //   pageSize: pageSize.value,
      //   ...params,
      // });
      // pathUrl.value = res.records?.map((i) => i.playerUrl) || [];
      getPathUrl(4);
    } finally {
      loading.value = false;
    }
  }

  const classifyList = [
    {
      title: '粗格栅',
      total: 4,
    },
    {
      title: '细格栅',
      total: 2,
    },
    {
      title: '生物池',
      total: 3,
    },
    {
      title: '二沉池',
      total: 5,
    },
    {
      title: '高效沉淀池',
      total: 1,
    },
    {
      title: '紫外消毒',
      total: 4,
    },
    {
      title: '鼓风机房',
      total: 3,
    },
    {
      title: '加药间',
      total: 3,
    },
  ];
  const currentClassify = ref(0);

  const handleChangeClassify = (index, total) => {
    currentClassify.value = index;
    // pathUrl.value.length = total;
    // Array.from({ length: total }).forEach((_i, index) => {
    //   pathUrl.value[index] = pathUrl.value[0];
    // });
    getPathUrl(total);
  };

  onMounted(() => {
    getCameraList();
    handleChangeClassify(0, classifyList[0].total);
  });
</script>

<style scoped lang="less">
  .page {
    padding: 0 16px 16px 16px;
    display: flex;
    height: 100%;

    .left-container {
      width: 224px;
      border-right: 1px solid #d8d8d8;
      background: #fff;
      border-radius: 4px 0 0 4px;

      .tit {
        padding: 16px 0 16px 12px;
        line-height: 1;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        border-bottom: 1px solid #e9e9e9;
      }

      .classify-container {
        padding: 16px 12px 0;
      }

      .left-item {
        display: flex;
        align-items: center;
        background: #fff;
        margin-bottom: 8px;
        border-radius: 4px;
        cursor: pointer;
        padding: 20px 24px;
        height: 44px;

        &:hover:not(.active) {
          background: #f0f0f0;
        }

        .icon {
          width: 16px;
          height: 16px;
          mask: url('../assets/images/classify1.svg') 0 0 / 16px no-repeat;
        }

        .con {
          font-weight: 500;
          color: #333333;
          margin-left: 8px;
        }

        .num {
          font-weight: 600;
          color: @theme-color;
          font-size: 20px;
          font-family: PingFang SC-Semibold, PingFang SC;
        }
      }

      .active {
        background: @theme-color-12p;

        .con {
          color: @theme-color;
        }

        .num {
          color: #fff;
        }
      }
    }
  }

  .video-monitoring {
    background: #fff;
    overflow-x: hidden;
    flex: 1;
    border-radius: 0 4px 4px 0;

    .search {
      padding-left: 16px;
      position: relative;

      .search-button {
        position: absolute;
        top: 0;
        left: 68%;
      }
    }

    .line {
      width: 100%;
      padding: 0 20px;
      height: 1px;
      background: #f5f6f7;
    }

    .video {
      width: 100%;
      height: 100%;

      :deep(.player-box) {
        .grid-box {
          padding: 6px;
        }
      }
    }

    .page {
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
</style>
