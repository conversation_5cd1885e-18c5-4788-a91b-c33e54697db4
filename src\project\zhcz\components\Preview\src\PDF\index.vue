<template>
  <BasicModal
    title="预览"
    @register="registerModal"
    @cancel="cancel"
    @close="cancel"
    :footer="null"
    :minHeight="100"
    :z-index="1012"
  >
    <div class="pdf-preview-modal">
      <VuePdfEmbed
        :source="state.source"
        :scale="4"
        :style="scale"
        class="vue-pdf-embed"
        :page="state.pageNum"
      />
    </div>
    <div class="page-tool">
      <div class="page-tool-item" @click="lastPage">上一页</div>
      <div class="page-tool-item" @click="nextPage">下一页</div>
      <div class="page-tool-item">{{ state.pageNum }}/{{ state.numPages }}</div>
      <div class="page-tool-item" @click="pageZoomOut">放大</div>
      <div class="page-tool-item" @click="pageZoomIn">缩小</div>
    </div>
  </BasicModal>
</template>
<script setup lang="ts">
  import { reactive, onMounted, computed, watch } from 'vue';
  import VuePdfEmbed from 'vue-pdf-embed';
  import { createLoadingTask } from 'vue3-pdfjs/esm'; // 获得总页数
  import { BasicModal, useModal } from '/@/components/Modal';

  const props = defineProps({
    pdfUrl: {
      type: String || Object,
      required: true,
    },
    open: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits<{
    (e: 'update:open', value: boolean): void;
    (e: 'change'): void;
  }>();
  const [registerModal, { openModal }] = useModal();
  watch(
    () => props.open,
    (val) => {
      openModal(val);
    },
  );
  function cancel() {
    emit('update:open', false);
  }
  const state = reactive({
    source: props.pdfUrl, //预览pdf文件地址
    pageNum: 1, //当前页面
    scale: 1, // 缩放比例
    numPages: 0, // 总页数
  });

  const loadingTask = createLoadingTask(state.source);
  loadingTask.promise.then((pdf: { numPages: number }) => {
    state.numPages = pdf.numPages;
  });

  const scale = computed(() => `transform:scale(${state.scale})`);
  function lastPage() {
    if (state.pageNum > 1) {
      state.pageNum -= 1;
    }
  }
  function nextPage() {
    if (state.pageNum < state.numPages) {
      state.pageNum += 1;
    }
  }
  function pageZoomOut() {
    if (state.scale < 2) {
      state.scale += 0.1;
    }
  }
  function pageZoomIn() {
    if (state.scale > 1) {
      state.scale -= 0.1;
    }
  }
  onMounted(() => {});
</script>
<style lang="less" scoped>
  .pdf-preview {
    position: relative;
    padding: 20px 0;
    box-sizing: border-box;
    background-color: #e9e9e9;
    height: 50px;
    overflow: hidden;
  }

  .pdf-wrap {
    overflow-y: auto;
  }

  .vue-pdf-embed {
    text-align: center;
    border: 1px solid #e5e5e5;
    margin: 0 auto;
    box-sizing: border-box;
  }

  .pdf-preview-modal {
    position: relative;
    padding: 20px 0;
    max-height: 100vh;
    box-sizing: border-box;
  }

  .page-tool {
    position: absolute;
    bottom: 35px;
    padding-left: 15px;
    padding-right: 15px;
    display: flex;
    align-items: center;
    background: rgb(66, 66, 66);
    color: white;
    border-radius: 19px;
    z-index: 100;
    cursor: pointer;
    margin-left: 50%;
    transform: translateX(-50%);
  }

  .page-tool-item {
    padding: 8px 15px;
    padding-left: 10px;
    cursor: pointer;
  }
</style>
