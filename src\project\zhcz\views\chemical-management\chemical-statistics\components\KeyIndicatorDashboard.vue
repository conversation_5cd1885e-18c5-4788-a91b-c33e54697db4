<template>
  <Card :bordered="false" dis-hover :title="title">
    <template #extra>
      <div class="extra-wrap overflow-y-auto">
        <DatePicker
          v-model:value="formData.dayDate"
          picker="month"
          placeholder="请选择"
          valueFormat="YYYY-MM"
          :allowClear="false"
          class="!w-[auto]"
          :disabledDate="disabledDate"
          @change="handleChangeDayDate"
        />
      </div>
    </template>
    <div ref="chartRef" v-if="!isEmpty" class="overflow-auto" style="width: 100%; height: 100%">
      <DashboardList :list="dataList" />
    </div>

    <div v-else class="empty-box">
      <HEmpty />
    </div>
  </Card>
</template>

<script setup>
  import { DatePicker, Card } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { ref, reactive } from 'vue';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { roundAndConvertCheckNullAndUnDef } from '/@zhcz/utils/number';
  import DashboardList from './DashboardList.vue';

  const props = defineProps({
    title: { type: String, required: true },
    sourceData: { type: Array, default: () => [] },
  });
  console.log(props);
  let formData = reactive({
    dayDate: dayjs().format('YYYY-MM'),
  });

  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }
  const dataList = ref([]);

  let getParams = (pre = false) => {
    let paramsData = {
      startDateTime: dayjs(formData.dayDate)
        .subtract(pre ? 1 : 0, 'month')
        .startOf('month')
        .format('YYYY-MM-DD 00:00:00'),
      endDateTime: dayjs(formData.dayDate)
        .subtract(pre ? 1 : 0, 'month')
        .endOf('month')
        .format('YYYY-MM-DD 23:59:59'),
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    let params = {
      resourceInterfaceId: '3',
      groupCode: 'hytj_gjzbkb',
      paramsData: JSON.stringify(paramsData),
    };
    return params;
  };

  const isEmpty = ref(true);

  async function getData() {
    let params = getParams();
    let data;
    let preData;
    try {
      ({ data } = await callResourceFunction(params));
      ({ data: preData } = await callResourceFunction(getParams(true)));
    } catch (err) {
      isEmpty.value = true;
    }

    if (!data?.length) {
      isEmpty.value = true;
      return;
    }
    for (let item of data) {
      if (!item.data?.length) {
        isEmpty.value = true;
        return;
      }
    }
    isEmpty.value = false;
    let arr = data.map((item, index) => {
      let { indexName, unitName, data: list, minVal, maxVal } = item;
      let { data: preList } = preData[index];
      let total = list.length;
      let lastValue = list.at(-1).value;
      let commonObj = { indexName, unitName, total, lastValue };
      if (!minVal || !maxVal || !total) {
        let currentData = { ...commonObj, successCount: total, successPercent: 100, preStatus: 1 };
        return currentData;
      }
      let successCount = getSuccessCount(list, minVal, maxVal);

      let successPercent = roundAndConvertCheckNullAndUnDef((successCount / total) * 100, 2);

      let preStatus;
      // consol.log('-----> preData', preData, proList);
      if (prelist.length < 1) {
        preStatus = 1;
      } else {
        let preSuccessCount = getSuccessCount(preList, minVal, maxVal, true);
        let preSuccessPercent = roundAndConvertCheckNullAndUnDef(
          (preSuccessCount / preList.length) * 100,
          2,
        );
        preStatus =
          preSuccessPercent > successPercent ? -1 : preSuccessPercent < successPercent ? 1 : 0;
      }
      return { ...commonObj, successCount, successPercent, preStatus };
    });
    dataList.value = arr;
  }

  function getSuccessCount(list, minVal, maxVal) {
    return list.filter((item) => {
      let { value } = item;
      if (minVal && minVal > value) return false;
      if (maxVal && maxVal < value) return false;
      return true;
    }).length;
  }

  function handleChangeDayDate() {
    getData();
  }
  getData();
  // onMounted(getData);
</script>

<style lang="less" scoped></style>
