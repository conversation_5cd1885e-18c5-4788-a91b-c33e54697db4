<template>
  <div class="content-box">
    <CommonTitle :icon="gaojing1" text="告警中心" bottomLine="short" />
    <div class="content" v-if="list.length">
      <div class="header">
        <span>报警时间</span>
        <span>报警主题</span>
        <span>操作</span>
      </div>
      <div class="body">
        <div v-for="(item, index) in list" :key="index" class="item">
          <span class="time">{{ formatTime(item.creationTime) }}</span>
          <span class="text" :title="item.warnEventContent">{{ item.warnEventContent }}</span>
          <span class="action cursor-pointer" @click="handleManualOverride(item)"> 人工解除 </span>
        </div>
        <!-- <div class="item">
          <span class="time">30分钟前</span>
          <span class="text">1#生物池浊度偏高异常</span>
          <span class="action" style="color: #6fb5ff; cursor: pointer">人工解除</span>
        </div>
        <div class="item">
          <span class="time">40分钟前</span>
          <span class="text">1#生物池实时进水异常</span>
          <span class="action" style="color: #6fb5ff; cursor: pointer">人工解除</span>
        </div>
        <div class="item">
          <span class="time">1小时前</span>
          <span class="text">3#生物池pH偏低异常</span>
          <span class="action" style="color: #6fb5ff; cursor: pointer">人工解除</span>
        </div>
        <div class="item">
          <span class="time">30分钟前</span>
          <span class="text">1#生物池浊度偏高异常</span>
          <span class="action" style="color: #6fb5ff; cursor: pointer">人工解除</span>
        </div>
        <div class="item">
          <span class="time">40分钟前</span>
          <span class="text">1#生物池实时进水异常</span>
          <span class="action" style="color: #6fb5ff; cursor: pointer">人工解除</span>
        </div>
        <div class="item">
          <span class="time">1小时前</span>
          <span class="text">3#生物池pH偏低异常</span>
          <span class="action" style="color: #6fb5ff; cursor: pointer">人工解除</span>
        </div> -->
      </div>
    </div>
    <div class="content" v-else>
      <Empty />
    </div>
    <EditModal @success="getData()" @register="registerEditModal" />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import CommonTitle from './CommonTitle.vue';
  import gaojing1 from '../assets/gaojing1.png';
  import { getListByPage, getDetail } from '/@zhcz/api/event-center';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useModal } from '/@/components/Modal';
  import EditModal from './EditModal.vue';
  import { useEmitt } from '/@/hooks/web/useEmitt';
  import Empty from './Empty.vue';
  import { useIntervalFn } from '@vueuse/core';

  enum EventStatus {
    // 未处理
    Untreated = 10000,
    // 人工解除
    ManualRelieve = 10001,
    // 自动解除
    AutomaticRelieve = 10002,
    // 工单执行中
    WorkOrderExecution = 10003,
    // 工单已完成
    WorkOrderCompleted = 10004,
  }

  const { createMessage } = useMessage();

  const list = ref<Recordable[]>([]);
  async function getData() {
    const params = {
      current: 1,
      size: 1000,
      startDate: '',
      endDate: '',
      eventStatus: 10000,
    };
    const data = await getListByPage(params);
    list.value = data.records;
  }

  // 计算消息是多少时间前发送的
  function formatTime(dateString: string): string {
    // 检查输入是否为有效的日期字符串
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return '';
    }

    const now = new Date();
    const diff = now.getTime() - date.getTime(); // 计算时间差（毫秒）

    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    // 格式化输出
    if (seconds <= 10) {
      return '刚刚';
    } else if (seconds < 60) {
      return `${seconds}秒前`;
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else {
      return `${days}天前`;
    }
  }

  const [registerEditModal, { openModal: openEditModal }] = useModal();
  function handleManualOverride(record: Recordable) {
    if (record.eventStatus !== EventStatus.Untreated) {
      createMessage.warning('该报警已处理');
      return;
    }

    getDetail(record.id).then((data) => {
      openEditModal(true, {
        type: 'solve',
        record: data,
      });
    });
  }

  const { pause, resume } = useIntervalFn(getData, 5 * 1000);

  const { emitter } = useEmitt();

  emitter.on('bi:change-factory', async () => {
    pause();
    await getData();
    resume();
  });
</script>

<style lang="less" scoped>
  @import '../common.less';

  .content-box {
    padding: 10px 13px 12px;
    background: rgba(47, 148, 255, 0.1);
    height: 175px;
    overflow: hidden;
  }

  .content {
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    height: calc(100% - 27px);

    .header {
      display: flex;
      padding: 9px 13px;
      font-size: 11px;
      color: #dcedff;

      & > span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      & > span:first-child {
        display: inline-block;
        width: 47px;
      }

      & > span:nth-child(2) {
        display: inline-block;
        width: 113px;
        margin: 0 15px;
      }
    }

    .body {
      height: calc(100% - 29%);
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;

        &-thumb {
          background-color: rgba(0, 0, 0, 0.1);
          border-radius: 4px;
        }
      }

      .item {
        display: flex;
        padding: 9px 13px;
        font-size: 11px;
        color: #dcedff;

        &:nth-child(2n + 1) {
          background: rgba(47, 148, 255, 0.1);
        }

        & > span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        & > span:first-child {
          display: inline-block;
          width: 47px;
        }

        & > span:nth-child(2) {
          display: inline-block;
          width: 113px;
          margin: 0 15px;
        }

        .action {
          color: '#6FB5FF';
        }
      }
    }
  }
</style>
