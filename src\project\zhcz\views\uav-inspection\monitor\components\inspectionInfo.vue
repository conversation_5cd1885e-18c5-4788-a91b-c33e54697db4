<template>
  <div class="inspection-info-box">
    <div :bordered="false" style="border-radius: 4px 4px 0 0">
      <div class="title-box">
        <div class="header-box">
          <div class="left-border"></div>
          <div style="line-height: 1">巡检信息</div>
        </div>
      </div>
      <div class="info-box">
        <div class="title">
          <div>任务编号：</div>
          <div style="color: #333">UAVIST1746502895116</div>
          <div class="tag">有异常</div>
        </div>
        <div class="sub-title">
          <div>
            <span>计划巡检时间：</span>
            <span style="color: #333">2025-05-07 12:00:00</span>
          </div>
          <div>
            <span>实际巡检时间：</span>
            <span style="color: #333">2025-02-28 10:20:41</span>
          </div>
          <div>
            <span>任务状态：</span>
            <span style="color: var(--theme-color)">进行中</span>
          </div>
        </div>
        <div class="detail-box">
          <div
            v-for="(item, index) in detailList"
            :key="item.name"
            :class="['item', { 'un-last-item': index != detailList.length - 1 }]"
          >
            <div style="margin-bottom: 8px">{{ item.name }}</div>
            <div>
              <span class="value">{{ item.value }}</span>
              <span v-if="item.unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div :bordered="false" class="record-card-box">
      <div class="title-box">
        <div class="header-box">
          <div class="left-border"></div>
          <div style="line-height: 1">抓拍记录</div>
        </div>
      </div>
      <div class="record-pic-box">
        <div v-for="(item, index) in recordPicList" :key="index" class="record-item">
          <img src="../../assets/images/record.png" />
          <div class="right-box">
            <div class="flex items-center" style="margin-bottom: 12px">
              <div style="font-weight: 600">{{ item.name }}</div>
            </div>
            <div style="margin-bottom: 12px">
              <span style="color: #999">抓拍时间：</span>
              <span>{{ item.time }}</span>
            </div>
            <div>
              <span style="color: #999">经纬度：</span>
              <span>{{ item.location }}</span>
            </div>
          </div>
          <div :class="['fixed-tag', item.level]">{{ getLevelLabel(item.level) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref } from 'vue';
  import recordPng from '../../assets/images/record.png';

  const detailList = ref([
    {
      name: '总里程',
      value: '2.69',
      unit: 'km',
    },
    {
      name: '总节点数',
      value: '23',
      unit: '个',
    },
    {
      name: '任务间隔',
      value: '10',
      unit: '小时',
    },
    {
      name: '异常总数',
      value: '4',
      unit: '个',
    },
    {
      name: '负责人',
      value: '张三',
    },
    {
      name: '人工干预',
      value: '2',
      unit: '次',
    },
  ]);
  const recordPicList = ref([
    {
      img: recordPng,
      name: '烟火告警',
      level: 'one',
      time: '2025-05-07 12:00:00',
      location: '(113.93, 22.54)',
    },
    {
      img: recordPng,
      name: '悬浮物告警',
      level: 'two',
      time: '2025-05-07 12:00:00',
      location: '(113.93, 22.54)',
    },
    {
      img: recordPng,
      name: '外来人员告警',
      level: 'three',
      time: '2025-05-07 12:00:00',
      location: '(113.93, 22.54)',
    },
    {
      img: recordPng,
      name: '外来人员告警',
      level: 'three',
      time: '2025-05-07 12:00:00',
      location: '(113.93, 22.54)',
    },
  ]);

  const getLevelLabel = (v) => {
    switch (v) {
      case 'one':
        return '一级报警';
      case 'two':
        return '二级报警';
      case 'three':
        return '三级报警';
      default:
        return '';
    }
  };
</script>
<style lang="less" scoped>
  .inspection-info-box {
    display: flex;
    flex-direction: column;
    padding: 0 16px;
    height: 100%;
    background-color: #fff;
    border-radius: 4px;

    .title-box {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      min-height: 42px;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      border-bottom: 1px solid #e9e9e9;
    }

    .header-box {
      display: flex;
      align-items: center;

      // .left-border {
      //   margin-right: 8px;
      //   width: 4px;
      //   height: 14px;
      //   background-color: var(--theme-color);
      // }
    }

    .info-box {
      padding: 12px;
      font-size: 14px;
      line-height: 1;
      background: #f1f4f7;
      border-radius: 4px;

      .title {
        display: flex;
        align-items: center;
        color: #999;
        margin-bottom: 12px;
        overflow: hidden;
        white-space: nowrap;

        .tag {
          margin-left: 8px;
          padding: 4px;
          width: max-content;
          font-size: 12px;
          border-radius: 4px;
          line-height: 1;
          color: #fff;
          background-color: #ff522b;
        }
      }

      .sub-title {
        display: flex;
        align-items: center;
        color: #999;
        padding-bottom: 12px;
        border-bottom: 1px solid #d8d8d8;

        div {
          overflow: hidden;
          white-space: nowrap;
        }

        div:nth-child(2),
        div:nth-child(3) {
          margin-left: 44px;
        }
      }

      .detail-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 12px;
        color: #666;

        .item {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;

          &.un-last-item {
            margin-right: 16px;
            border-right: 1px solid #bebebe;
          }

          .value {
            margin-right: 4px;
            font-size: 14px;
            color: #333;
            font-weight: 600;
          }
        }
      }
    }

    .record-card-box {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      flex: 1;

      .record-pic-box {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        overflow: auto;
        flex: 1;

        .record-item {
          position: relative;
          display: flex;
          align-items: center;
          padding: 12px;

          width: calc(50% - 4px);
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          margin-bottom: 8px;

          img {
            width: 64px;
            height: 64px;
          }

          .right-box {
            margin-left: 16px;
            color: #333;
            line-height: 1;
            overflow: hidden;
            white-space: nowrap;
          }

          .fixed-tag {
            position: absolute;
            top: 0;
            right: 0;
            padding: 4px 8px;
            width: max-content;
            font-size: 12px;
            border-radius: 0 0 0 8px;
            line-height: 1;

            &.one {
              color: #ff2e2e;
              background-color: rgba(255, 46, 46, 0.12);
              border: 1px solid rgba(255, 46, 46, 0.56);
            }

            &.two {
              color: #fc7c22;
              background-color: rgba(255, 140, 46, 0.12);
              border: 1px solid rgba(255, 140, 46, 0.56);
            }

            &.three {
              color: #d4b100;
              background-color: rgba(237, 199, 10, 0.12);
              border: 1px solid rgba(212, 177, 0, 0.56);
            }
          }
        }
      }
    }
  }
</style>
