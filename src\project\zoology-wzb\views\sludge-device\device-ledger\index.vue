<template>
  <div :class="prefixCls" class="h-full">
    <div v-show="!showEditPage" class="h-full">
      <BasicTable @register="registerList" @selection-change="handleChangeSelection">
        <template #tableTitle>
          <div>
            <a-button
              :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
              @click="showEditPage = true"
              type="primary"
              >新增</a-button
            >
            <a-button
              :icon="h(Icon, { icon: 'icon-park-outline:delete' })"
              @click="handleBatchDelete"
              danger
              ghost
              type="primary"
              :disabled="!selectRowKeys.length"
              class="ml-4"
              >批量删除</a-button
            >
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  label: '编辑',
                  onClick: handleEdit.bind(null, record),
                },

                {
                  label: '删除',
                  popConfirm: {
                    title: '是否确认删除',
                    placement: 'left',
                    confirm: handleDelete.bind(null, record),
                  },
                },
              ]"
            />
          </template>
          <template v-if="column.key === 'enterpriseType'">
            {{ filterEnterpriseTypeLabel(record?.enterpriseType) }}
          </template>
          <template v-if="column.key === 'equipmentName'">
            <Tooltip>
              <template #title>{{ record.equipmentName }}</template>
              <span
                style="cursor: pointer; color: var(--theme-color)"
                @click="handleViewDetail(record)"
              >
                {{ record.equipmentName }}
              </span>
            </Tooltip>
          </template>
        </template>
      </BasicTable>
    </div>
    <div class="view-page" v-if="showEditPage">
      <EditPage
        :equipmentId="editEqId"
        :isViewPage="isViewPage"
        @back="editBackPage"
        @success="editSuccess"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import EditPage from './components/EditPage.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Icon } from '/@/components/Icon';
  import { Tooltip } from 'ant-design-vue';
  import { ref, h, onBeforeMount } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema, isViewPage, initOptions } from './data';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { searchEquipmentListApi, deleteEquipmentApi } from '/@zoology-wzb/api/device';

  const { prefixCls } = useDesign('sludge-device-ledger');
  const showEditPage = ref(false);
  const editEqId = ref(); //设备id

  const { createMessage, createConfirm } = useMessage();

  const [registerList, { reload }] = useTable({
    api: searchEquipmentListApi,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'size',
    },
    beforeFetch: (params) => {
      return {
        ...params,
      };
    },
    rowSelection: {
      type: 'checkbox',
    },
    rowKey: 'id',
    clickToRowSelect: false,
    columns: columns,
    formConfig: {
      schemas: searchFormSchema,
    },
    showIndexColumn: false,
    showTableSetting: false,
    bordered: false,
    useSearchForm: true,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  const selectRowKeys = ref<string[]>([]); //选中行id数组
  const handleChangeSelection = ({ keys }) => {
    selectRowKeys.value = keys;
  };
  /* 查看详情 */
  const handleViewDetail = (record) => {
    editEqId.value = record.id;
    isViewPage.value = true;
    showEditPage.value = true;
  };
  /* 从编辑页返回 */
  const editBackPage = () => {
    editEqId.value = null;
    showEditPage.value = false;
    isViewPage.value = false;
  };
  /* 编辑成功 */
  const editSuccess = () => {
    editBackPage();
    reload();
  };
  /* 打开编辑页 */
  function handleEdit(record: Recordable) {
    editEqId.value = record.id;
    showEditPage.value = true;
  }
  /* 批量删除 */
  const handleBatchDelete = async () => {
    if (selectRowKeys.value.length === 0) {
      createMessage.warning('请选择要删除的设备');
      return;
    }
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '是否确认删除',
      onOk: async () => {
        await deleteEquipmentApi(selectRowKeys.value);
        reload();
      },
    });
  };
  /* 删除 */
  async function handleDelete(record: Recordable) {
    showEditPage.value = true;
    await deleteEquipmentApi([record?.id]);
    reload();
    createMessage.success('删除成功');
  }

  onBeforeMount(() => {
    initOptions();
  });
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-sludge-device-ledger';

  .@{prefix-cls} {
    .view-page {
      height: 100%;
      padding: 0 16px 16px;
    }
  }
</style>
