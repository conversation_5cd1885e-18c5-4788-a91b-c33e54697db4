<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    width="45%"
  >
    <div :class="editModalFormRef?.formModel?.dataTypeKey == '1' ? 'fix-form-bottom-margin' : ''">
      <BasicForm ref="editModalFormRef" @register="registerForm">
        <!-- 关联指标表格 -->
        <template #indicators="{ model, field }">
          <Select
            mode="multiple"
            v-model:value="model[field]"
            :open="false"
            showArrow
            :max-tag-count="19"
            :maxTagTextLength="10"
            :fieldNames="{
              label: 'simpleName',
              value: 'indicatorCode',
            }"
            placeholder="请选择"
            :options="indicatorsOptions"
            @click="openTagsModal(true, { selectNode: indicatorsOptions || [] })"
            @focus="(e) => e.target.blur()"
            class="tag-select"
          >
            <template #suffixIcon>
              <Icon icon="icon-park-outline:edit" />
            </template>
          </Select>
        </template>
        <!-- 样式配置表格 -->
        <template #style="{ model, field }">
          <StyleTable
            @handleAddRow="handleAddStyleConfigRow"
            @handleEditStyle="handleEditStyle"
            @handleDeleteRow="handleDeleteStyleConfigRow"
            :styleInfo="model[field] || []"
          />
        </template>
        <template #normalStyle>
          <div class="pb-1">
            <a-button @click="handleEditStyle(null, 'normalStyle')">样式编辑</a-button>
          </div>
        </template>
        <template #alertStyle>
          <div class="pb-1">
            <a-button @click="handleEditStyle(null, 'alertStyle')">样式编辑</a-button>
          </div>
        </template>
      </BasicForm>
    </div>
    <template #footer>
      <a-button class="default-btn" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">保存</a-button>
    </template>

    <EditStyleModal @register="registerEditStyleModal" @success="handleEditStyleSuccess" />
    <TagAllModal @register="registerTagModal" @success="handleGetTagList" setInitCheckedList />
  </BasicModal>
</template>
<script lang="ts" setup>
  import StyleTable from './StyleTable.vue';
  import TagAllModal from '/@zhcz/views/device-management/device-assets/equipment-ledger-new/modules/sensor/components/TagAllModal.vue';
  import EditStyleModal from '../EditStyleModal.vue';
  import { Select } from 'ant-design-vue';
  import Icon from '/@/components/Icon';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { ref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { editModalSchemas, editModalFormRef } from './data';
  import { saveEquipmentConfigApi, searchEquipmentConfigDeatilApi } from '/@zoology-wzb/api/device';

  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();
  const title = ref('');
  const editId = ref();
  const equipmentTypeId = ref(); //设备类型id
  const [registerModal, { setModalProps, closeModal, changeLoading }] = useModalInner(
    async (data) => {
      try {
        setCustomStyle();
        const isEdit = data.id || data.id === 0;
        title.value = isEdit ? '编辑类型' : '新增类型';
        editId.value = data.id;
        equipmentTypeId.value = data.equipmentTypeId;
        if (isEdit) {
          changeLoading(true);
          updateSchema([
            {
              field: 'styleConfigList',
              show: false,
            },
            {
              field: 'normalStyle',
              show: false,
            },
            {
              field: 'alertStyle',
              show: false,
            },
          ]);
          const res = await searchEquipmentConfigDeatilApi(data.id);
          indicatorsOptions.value = res.associatedMetricInfoList.map((v) => {
            return {
              indicatorCode: v.indicatorCode,
              indicatorName: v.indicatorName,
              sourceUniqueKey: v.indicatorCode,
              displayName: v.indicatorName,
              simpleName: v.indicatorName,
            };
          });
          res.associatedMetricList = res.associatedMetricInfoList.map((v) => v.indicatorCode);
          setFieldsValue(res);
        } else {
          indicatorsOptions.value = [];
          setFieldsValue({ dataTypeKey: '1' });
        }
      } finally {
        changeLoading(false);
      }
    },
  );

  const [registerForm, { resetFields, setFieldsValue, validate, clearValidate, updateSchema }] =
    useForm({
      labelWidth: 100,
      schemas: editModalSchemas,
      showActionButtonGroup: false,
    });

  /* 默认样式配置 */
  const styleConfigItemTemp = {
    detailValue: '',
    detailLabel: '',
    detailStyle: `{
      "color": "#333",
      "background-color": "#f0f0f0",
      "border-color": "#ccc"
    }`,
  };
  /* 新增一行样式配置 */
  const handleAddStyleConfigRow = () => {
    if (!editModalFormRef.value.formModel?.styleConfigList)
      editModalFormRef.value.formModel.styleConfigList = [];
    editModalFormRef.value.formModel.styleConfigList.push({ ...styleConfigItemTemp });
  };
  /* 删除一行样式配置 */
  const handleDeleteStyleConfigRow = (index) => {
    if (!editModalFormRef.value.formModel?.styleConfigList?.length) return;
    editModalFormRef.value.formModel.styleConfigList.splice(index, 1);
  };
  let styleTableSelectedRowIndex = 0; //当前操作行
  const [registerEditStyleModal, { openModal: openEditStyleModal }] = useModal();
  let styleKeyType = 'styleConfigList'; //表单样式配置字段key
  /* css代码编辑弹框 */
  const handleEditStyle = (index, type = 'styleConfigList') => {
    styleTableSelectedRowIndex = index;
    styleKeyType = type;
    let val = '';
    if (editModalFormRef.value.formModel?.dataTypeKey == '1') {
      val = editModalFormRef.value.formModel?.[type]?.[styleTableSelectedRowIndex]?.detailStyle;
    } else if (editModalFormRef.value.formModel?.dataTypeKey == '2') {
      val = editModalFormRef.value.formModel?.[type];
    }
    openEditStyleModal(true, {
      val,
    });
  };
  // 保存样式配置值到表单
  const handleEditStyleSuccess = (val) => {
    if (editModalFormRef.value.formModel?.dataTypeKey == '1') {
      editModalFormRef.value.formModel.styleConfigList[styleTableSelectedRowIndex].detailStyle =
        val;
    } else {
      editModalFormRef.value.formModel[styleKeyType] = val;
    }
  };

  const indicatorsOptions = ref([]); //指标下拉选项
  const [registerTagModal, { openModal: openTagsModal }] = useModal();
  const handleGetTagList = (list) => {
    indicatorsOptions.value = list;
    setFieldsValue({ associatedMetricList: list.map((v) => v.indicatorCode) });
  };
  const handleSubmit = async () => {
    try {
      const values = await validate();
      if (!values.configCode) values.configCode = `SBPZ-${new Date().getTime()}`;
      if (editId.value) values.id = editId.value;
      values.equipmentTypeId = equipmentTypeId.value;
      if (values.dataTypeKey == '1') {
        delete values.normalStyle;
        delete values.alertStyle;
      } else {
        delete values.styleConfigList;
      }
      setModalProps({ confirmLoading: true });
      await saveEquipmentConfigApi(values);
      await handleCancel();
      createMessage.success('操作成功');
      emit('success');
    } catch (e) {
      console.log(e);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  };

  const handleCancel = () => {
    resetFields();
    closeModal();
    clearValidate();
  };

  /* 设置必填样式 */
  let isSetCustomStyle = false;
  const setCustomStyle = () => {
    if (isSetCustomStyle) return;
    const dom = document.querySelector('[for="form_item_styleConfigList"]');
    if (dom) {
      dom.classList.add('ant-form-item-required');
      isSetCustomStyle = true;
    }
  };
</script>
<style lang="less" scoped>
  .fix-form-bottom-margin {
    :deep(.basic-form-row) {
      .ant-col:nth-of-type(4) {
        .ant-form-item {
          margin-bottom: 0 !important;
        }
      }
    }
  }

  .tag-select {
    :deep(.ant-select-selector) {
      cursor: pointer !important;
    }
  }
</style>
