<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    destroyOnClose
    width="1100px"
    ref="modalRef"
  >
    <BasicForm @register="registerForm">
      <template #source-type-slot="{ model, field }">
        <Select
          v-model:value="model[field]"
          allow-clear
          :disabled="isEdit"
          placeholder="请选择"
          @change="handleChangeDataSource"
        >
          <Select.Option
            v-for="item in dataSourceOptions"
            :key="item.intValue"
            :value="item.intValue"
          >
            {{ item.label }}
          </Select.Option>
        </Select>
      </template>
      <template #time-type-slot="{ model, field }">
        <Select
          v-model:value="model[field]"
          allow-clear
          :disabled="isEdit || isDisabledTimeType"
          placeholder="请选择"
          @change="handleChangeTimeType"
        >
          <Select.Option
            v-for="item in timeTypeOptions"
            :key="item.intValue"
            :value="item.intValue"
            :disabled="item.disabled"
          >
            {{ item.label }}
          </Select.Option>
        </Select>
      </template>
      <template #data-slot="{ model, field }">
        <Select v-model:value="model[field]" allow-clear placeholder="请选择">
          <Select.Option
            v-for="item in dataTypeOptions"
            :key="item.intValue"
            :value="item.intValue"
          >
            {{ item.label }}
          </Select.Option>
        </Select>
      </template>
      <template #target-slot>
        <div class="content-container">
          <div>
            <Input v-model:value="inputVal" placeholder="请输入">
              <template #prefix>
                <img :src="searchIcon" />
              </template>
            </Input>
            <div class="select-container">
              <div>资源标识</div>
              <div>资源名称</div>
              <div class="checkAll-box">
                <span>全选</span>
                <Checkbox
                  @click="handleChangeCheckAll"
                  :checked="checkedAllState == 'checked'"
                  :indeterminate="checkedAllState == 'indeterminate'"
                />
              </div>
            </div>
            <div class="indicator-container">
              <VScroll :itemHeight="42" :items="filterTagetList" :height="155">
                <template #default="{ item }">
                  <div class="scroll-item">
                    <div>
                      <Tooltip>
                        <template #title>{{ item.sourceUniqueKey }}</template>
                        {{ item.sourceUniqueKey }}
                      </Tooltip>
                    </div>
                    <div>
                      <Tooltip>
                        <template #title>{{ item.displayName }}</template>
                        {{ item.displayName }}
                      </Tooltip>
                    </div>
                    <Checkbox v-model:checked="item.checked" @click="handleCheckIndicator(item)" />
                  </div>
                </template>
              </VScroll>
            </div>
          </div>
          <div>
            <div class="checked-header-container">
              <div>资源标识</div>
              <div>操作</div>
            </div>
            <Draggable
              group="form-draggable"
              class="checked-container"
              tag="div"
              :component-data="{
                tag: 'div',
                type: 'transition-group',
                name: 'list',
              }"
              ghostClass="moving"
              :animation="180"
              :list="checkedList"
              handle=".th-drag"
              item-key="key"
            >
              <template #item="{ element: item }">
                <div class="checked-item">
                  <div>
                    <Tooltip>
                      <template #title> {{ item.sourceUniqueKey }}</template>
                      {{ item.sourceUniqueKey }}
                    </Tooltip>
                  </div>

                  <div class="btns">
                    <div class="copy-btn" @click="handleCopyItem(item)">复制</div>
                    <div class="delete-btn" @click="handleDeleteItem(item)">删除</div>
                  </div>
                </div>
              </template>
            </Draggable>
            <div class="bottom-container">
              <span class="clear-btn" @click="clearList">清空</span>
            </div>
          </div>
        </div>
      </template>
      <template #formula-slot="{ model, field }">
        <AutoComplete
          v-model:value="model[field]"
          :options="expressionData"
          :filterOption="filterExpression"
          class="formula-textarea"
        >
          <template #option="item">
            <span>{{ item.text }}</span>
          </template>
          <Textarea style="height: 80px" @change="changeTextarea" />
        </AutoComplete>
        <Space class="my-1">
          <a-button v-if="assetsPath">
            <a :href="assetsPath" target="_blank">帮助文档</a>
          </a-button>
          <a-button
            v-if="model[field]"
            type="primary"
            @click="verifyVerformulaHandler(model[field])"
            :loading="checkLoading"
            >公式校验</a-button
          >
          <Alert
            class="tip-alert"
            v-if="formulaVerifyInfo.code !== null && model[field]"
            :message="formulaVerifyInfo.message"
            :type="formulaVerifyInfo.type"
            show-icon
          />
        </Space>
      </template>
    </BasicForm>
    <!-- <a-button
      v-if="isEdit"
      class="mt-6"
      type="primary"
      @click="handleAddTable"
      :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
    >
      新增
    </a-button> -->
    <div
      class="tables-container"
      v-for="(tableItem, idx) in tableData"
      :key="tableItem.groupCode"
      style="margin-top: 12px"
    >
      <BasicTable
        :ref="(el) => setTableRef(el, tableItem.groupCode)"
        :columns="columns"
        :data-source="tableItem.nodeGroupRelations"
        :showIndexColumn="false"
        :pagination="false"
        :actionColumn="{
          width: 110,
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
        }"
        :canResize="false"
        style="padding: 0"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'indicatorCode'">
            <!-- <Tooltip v-if="tableItem.disabledEdit && record.flag !== '1'">
              <template #title> {{ record.indicatorCode }}</template>
              {{ record.indicatorCode }}
            </Tooltip>
            <Input else v-model:value="record.indicatorCode" /> -->
            <Tooltip>
              <template #title> {{ record.indicatorCode }}</template>
              {{ record.indicatorCode }}
            </Tooltip>
          </template>
          <template v-if="column.key === 'dataTimeType'">
            <Select
              v-model:value="record.dataTimeType"
              :options="tableItem.timeTypeOptions"
              :disabled="tableItem.disabledEdit && record.flag !== '1'"
              style="width: 150px"
              @change="handleChangeRowTimeType(record, tableItem.groupCode, tableItem)"
            />
          </template>
          <template v-if="column.key === 'dataCalcType'">
            <Select
              v-model:value="record.dataCalcType"
              :options="defaultCalcTypeOptions"
              style="width: 150px"
              @change="handleSetRowCode(record, tableItem.groupCode, tableItem)"
            />
          </template>
          <template v-if="column.key === 'mergeType'">
            <Select
              v-model:value="record.mergeType"
              :options="defaultMergeTypeOptions"
              :disabled="tableItem.disabledEdit && record.flag !== '1'"
              :allowClear="true"
              style="width: 150px"
              @change="handleSetRowMergeType(record, tableItem.groupCode)"
            />
          </template>
          <template v-if="column.key === 'indicatorName'">
            <Tooltip v-if="tableItem.disabledEdit">
              <template #title> {{ record.indicatorName }}</template>
              {{ record.indicatorName }}
            </Tooltip>
            <Input else v-model:value="record.indicatorName" />
          </template>
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  label: '删除',
                  onClick: handleRemoveRow.bind(null, tableItem.groupCode, record, idx),
                  disabled: tableItem.disabledEdit && record.flag !== '1',
                },
                {
                  label: '添加',
                  onClick: handleAddRow.bind(null, tableItem.groupCode, idx),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button
        v-if="!isEdit && !tableData.length"
        type="primary"
        @click="handleGenerate"
        :loading="okLoading"
      >
        生成
      </a-button>
      <a-button v-else type="primary" @click="handleSubmit" :loading="okLoading"> 保存 </a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup name="EditModal">
  import { computed, ref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form';
  import Draggable from 'vuedraggable';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    Input,
    Checkbox,
    Select,
    Tooltip,
    Textarea,
    Space,
    Alert,
    message,
  } from 'ant-design-vue';
  // import { Icon } from '/@/components/Icon';
  import { VScroll } from '/@/components/VirtualScroll/index';
  import searchIcon from '/@/assets/images/search-icon.png';
  import { schemas } from '../data';
  import {
    addIndicator,
    checkFormula,
    updateIndicator,
    getTagIndexList,
    getIndicatorExpression,
  } from '/@zhcz/api/config-center/monitoring-points';
  import { callResourceIndexUrl } from '/@zhcz/api/config-center/scenes-group';
  import AutoComplete from 'ant-design-vue/es/auto-complete';
  import { DICT } from '/@/enums/dict';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import { createLocalStorage } from '/@/utils/cache';
  import { FACTORY_KEY } from '/@/enums/cacheEnum';
  import { useUserStore } from '/@/store/modules/user';
  import { getParamKeyApi } from '/@/api/admin/param';

  const title = computed(() => (isEdit.value ? '编辑监测点' : '新增监测点'));
  const isEdit = ref(false);
  const okLoading = ref(false);
  const checkLoading = ref(false);
  const dataSourceOptions: any = ref([]);
  const timeTypeOptions: any = ref([]);
  const isDisabledTimeType = ref(false);
  const dataTypeOptions: any = ref([]);
  const defaultTimeTypeOptions: any = [
    {
      value: 1,
      label: '分钟数据',
      unitEn: '_M',
      unitZh: '_分钟',
    },
    {
      value: 2,
      label: '小时数据',
      unitEn: '_H',
      unitZh: '_小时',
    },
    {
      value: 3,
      label: '天数据',
      unitEn: '_D',
      unitZh: '_天',
    },
    {
      value: 4,
      label: '月数据',
      unitEn: '_L',
      unitZh: '_月',
    },
    {
      value: 5,
      label: '年数据',
      unitEn: '_Y',
      unitZh: '_年',
    },
  ];
  const defaultCalcTypeOptions: any = ref([
    {
      value: 0,
      label: '累加值',
      unitEn: '_SUM',
      unitZh: '_累加值',
    },
    {
      value: 1,
      label: '平均值',
      unitEn: '_AVG',
      unitZh: '_平均值',
    },
    {
      value: 2,
      label: '最大值',
      unitEn: '_MAX',
      unitZh: '_最大值',
    },
    {
      value: 3,
      label: '最小值',
      unitEn: '_MIN',
      unitZh: '_最小值',
    },
    {
      value: 4,
      label: '期初值',
      unitEn: '_FIRST',
      unitZh: '_期初值',
    },
    {
      value: 5,
      label: '期末值',
      unitEn: '_LAST',
      unitZh: '_期末值',
    },
    {
      value: 6,
      label: '计数值',
      unitEn: '_COUNT',
      unitZh: '_计数值',
    },
  ]);
  const defaultMergeTypeOptions = ref([
    {
      value: -1,
      label: '不计算',
      unitEn: '_NOOP',
      unitZh: '_不计算',
    },
    {
      value: 0,
      label: '累加值',
      unitEn: '_SUM',
      unitZh: '_累加值',
    },
    {
      value: 1,
      label: '平均值',
      unitEn: '_AVG',
      unitZh: '_平均值',
    },
    {
      value: 2,
      label: '最大值',
      unitEn: '_MAX',
      unitZh: '_最大值',
    },
    {
      value: 3,
      label: '最小值',
      unitEn: '_MIN',
      unitZh: '_最小值',
    },
    {
      value: 4,
      label: '期初值',
      unitEn: '_FIRST',
      unitZh: '_期初值',
    },
    {
      value: 5,
      label: '期末值',
      unitEn: '_LAST',
      unitZh: '_期末值',
    },
    {
      value: 6,
      label: '计数值',
      unitEn: '_COUNT',
      unitZh: '_计数值',
    },
  ]);
  const tableData: any = ref([]);
  const columns = [
    {
      title: '监测点CODE',
      dataIndex: 'indicatorCode',
    },
    {
      title: '时间类型',
      dataIndex: 'dataTimeType',
    },
    {
      title: '计算类型',
      dataIndex: 'dataCalcType',
    },
    {
      title: '合计类型',
      dataIndex: 'mergeType',
    },
    {
      title: '名称',
      dataIndex: 'indicatorName',
    },
  ];
  const tableRefArr: any = ref({});
  const tableListData: any = ref([]);
  const expressionData: any = ref([]);
  const inputVal = ref('');
  const indicatorList: any = ref([]);
  const checkedList = ref<any[]>([]);
  const ls = createLocalStorage();
  const userStore = useUserStore();
  const factoryId = ref(ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId);

  const [registerForm, { setFieldsValue, resetFields, validate, getFieldsValue, updateSchema }] =
    useForm({
      labelWidth: 80,
      baseColProps: {
        span: 24,
      },
      schemas,
      showActionButtonGroup: false,
    });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const assetsPath = ref('');

  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    console.log('useModalInner', data);
    isEdit.value = data.isEdit;
    clearList();
    if (isEdit.value) {
      updateSchema([
        {
          field: 'indicatorCode',
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'searchTarget',
          ifShow: data.record.sourceType === 1,
        },
        {
          field: 'formula',
          ifShow: data.record.sourceType === 1,
        },
        {
          field: 'dbSource',
          componentProps: {
            disabled: true,
          },
        },
      ]);
      setFieldsValue({
        ...data.record,
        dataCalcType: `${data.record.dataCalcType}`,
        mergeType: data.record.mergeType ? String(data.record.mergeType) : data.record.mergeType,
      });
      tableData.value = data.record.nodeRelationByGroupCodes.map((item) => {
        return {
          nodeGroupRelations: item.nodeGroupRelations.map((i) => {
            return {
              ...i,
              flag: '',
            };
          }),
          groupCode: item.groupCode,
          disabledEdit: true,
          timeTypeOptions: defaultTimeTypeOptions,
        };
      });
      tableListData.value = data.record.nodeRelationByGroupCodes.map(
        (item) => item.nodeGroupRelations.length,
      );
    }
    dataSourceOptions.value = await getDictTypeListApi({
      type: DICT.MONITORING_POINT_DATA_SOURCE,
    });
    await getDictTypeListApi({
      type: DICT.MONITORING_POINT_DATA_TIME_TYPE,
    }).then((data) => {
      timeTypeOptions.value = data.map((item) => {
        return {
          ...item,
          disabled: false,
        };
      });
    });
    dataTypeOptions.value = await getDictTypeListApi({
      type: DICT.MONITORING_POINT_DATA_TYPE,
    });
    await getIndicatorExpression().then((data) => {
      expressionData.value = data.map((item) => {
        return {
          value: item.paramsDescripts?.[0].example,
          text: item.functionName + ' ' + item.functionNameScript,
        };
      });
    });
    await getParamKeyApi('monitoring_point_help_path').then((data) => {
      assetsPath.value = data;
    });
    await getResourceIndexPage();
  });

  function filterExpression(value, option) {
    return option.text.toLowerCase().indexOf(value.toLowerCase()) > -1;
  }

  function handleChangeTimeType() {
    const values = getFieldsValue();
    if (
      !values.indicatorCode ||
      values.sourceType === undefined ||
      values.dataCalcType === undefined ||
      values.dataTimeType === undefined
    ) {
      return;
    }
    tableData.value = [];
    handleGenerate();
  }

  function handleChangeDataSource(value) {
    if (value === 0) {
      // 为采集时时间类型仅支持实时数据
      setFieldsValue({
        dataTimeType: 0,
      });
    } else if (value === 1) {
      // 为计算时时间类型不支持实时数据
      if (getFieldsValue().dataTimeType === 0) {
        setFieldsValue({
          dataTimeType: undefined,
        });
      }
    }
    timeTypeOptions.value.forEach((item) => {
      if (item.value === '0') {
        item.disabled = value === 1;
      }
    });
    isDisabledTimeType.value = value === 0;
    updateSchema([
      {
        field: 'searchTarget',
        ifShow: value === 1,
      },
      {
        field: 'formula',
        ifShow: value === 1,
      },
    ]);
  }

  function setTableRef(el, groupCode) {
    if (!el || !groupCode) return;
    tableRefArr.value[groupCode] = el;
  }

  // function handleAddTable() {
  //   if (tableData.value.length && !tableData.value[tableData.value.length - 1].disabledEdit) {
  //     createMessage.warning('请编辑完当前表格，再新增新表格');
  //     return;
  //   }
  //   tableData.value.push({
  //     nodeGroupRelations: [
  //       {
  //         dataTimeType: undefined,
  //         dataCalcType: undefined,
  //         indicatorCode: getFieldsValue().indicatorCode,
  //         indicatorName: getFieldsValue().indicatorName,
  //         flag: '1',
  //       },
  //     ],
  //     groupCode: 'add',
  //     disabledEdit: false,
  //     timeTypeOptions: defaultTimeTypeOptions.map((item) => {
  //       return {
  //         ...item,
  //         disabled: false,
  //       };
  //     }),
  //   });
  //   scrollBottom();
  // }

  function handleChangeRowTimeType(record, groupCode, tableItem) {
    let dataSource = tableRefArr.value[groupCode].getDataSource();
    const findItem = tableData.value.find((item) => item.groupCode === groupCode);
    findItem.timeTypeOptions = defaultTimeTypeOptions.map((item) => {
      return {
        ...item,
        disabled: dataSource.some((sourceItem) => sourceItem.dataTimeType === item.value),
      };
    });

    handleSetRowCode(record, groupCode, tableItem);
    if (tableItem?.disabledEdit) {
      const rawCodesList = tableItem.nodeGroupRelations.map((item) => item.indicatorCode);
      const newData = dataSource.filter((item) => !rawCodesList.includes(item.indicatorCode));
      const filterDataSource = dataSource.filter((item) =>
        rawCodesList.includes(item.indicatorCode),
      );

      dataSource = [...filterDataSource, ...newData];
      newData.sort((a, b) => a.dataTimeType - b.dataTimeType);
    } else {
      dataSource.sort((a, b) => a.dataTimeType - b.dataTimeType);
    }

    tableRefArr.value[groupCode].setTableData([...dataSource]);
  }

  // 重组行监测点code、name
  function handleSetRowCode(record, groupCode, tableItem) {
    const dataSource = tableRefArr.value[groupCode].getDataSource();

    let timeUnitEn = '';
    let timeUnitZh = '';
    let dataCalcUnitEn = '';
    let dataCalcUnitZh = '';
    if (record.dataTimeType !== undefined) {
      const timeTypeOption = defaultTimeTypeOptions.find(
        (item) => item.value === record.dataTimeType,
      );
      timeUnitEn = timeTypeOption?.unitEn;
      timeUnitZh = timeTypeOption?.unitZh;
    }
    if (record.dataCalcType !== undefined) {
      const dataCalcTypeOption = defaultCalcTypeOptions.value.find(
        (item) => item.value === record.dataCalcType,
      );
      dataCalcUnitEn = dataCalcTypeOption?.unitEn;
      dataCalcUnitZh = dataCalcTypeOption?.unitZh;
    }
    record.indicatorCode = getFieldsValue().indicatorCode + timeUnitEn + dataCalcUnitEn;
    record.indicatorName = getFieldsValue().indicatorName + timeUnitZh + dataCalcUnitZh;

    const rawCodesList = tableItem.nodeGroupRelations.map((item) => item.indicatorCode);

    dataSource.forEach((item) => {
      if (tableItem?.disabledEdit) {
        if (
          item.dataTimeType &&
          item.dataTimeType > record.dataTimeType &&
          !rawCodesList.includes(item.indicatorCode)
        ) {
          item.dataCalcType = record.dataCalcType;
          const timeTypeOption = defaultTimeTypeOptions.find((i) => i.value === item.dataTimeType);
          const timeUnitEn = timeTypeOption?.unitEn;
          const timeUnitZh = timeTypeOption?.unitZh;

          const dataCalcTypeOption = defaultCalcTypeOptions.value.find(
            (i) => i.value === item.dataCalcType,
          );
          const dataCalcUnitEn = dataCalcTypeOption?.unitEn;
          const dataCalcUnitZh = dataCalcTypeOption?.unitZh;
          item.indicatorCode = getFieldsValue().indicatorCode + timeUnitEn + dataCalcUnitEn;
          item.indicatorName = getFieldsValue().indicatorName + timeUnitZh + dataCalcUnitZh;
        }
      } else {
        if (item.dataTimeType && item.dataTimeType > record.dataTimeType) {
          item.dataCalcType = record.dataCalcType;
          const timeTypeOption = defaultTimeTypeOptions.find((i) => i.value === item.dataTimeType);
          const timeUnitEn = timeTypeOption?.unitEn;
          const timeUnitZh = timeTypeOption?.unitZh;

          const dataCalcTypeOption = defaultCalcTypeOptions.value.find(
            (i) => i.value === item.dataCalcType,
          );
          const dataCalcUnitEn = dataCalcTypeOption?.unitEn;
          const dataCalcUnitZh = dataCalcTypeOption?.unitZh;
          item.indicatorCode = getFieldsValue().indicatorCode + timeUnitEn + dataCalcUnitEn;
          item.indicatorName = getFieldsValue().indicatorName + timeUnitZh + dataCalcUnitZh;
        }
      }
    });
    tableRefArr.value[groupCode].setTableData([...dataSource]);
  }

  function handleSetRowMergeType(record, groupCode) {
    const dataSource = tableRefArr.value[groupCode].getDataSource();
    console.log('dataSource', record, dataSource);
  }

  async function handleAddRow(groupCode, index) {
    const dataSource = tableRefArr.value[groupCode].getDataSource();
    if (dataSource.length === defaultTimeTypeOptions.length) {
      createMessage.warning('已达最大值');
      return;
    }
    dataSource.push({
      dataTimeType: undefined,
      dataCalcType: undefined,
      mergeType: undefined,
      indicatorCode: getFieldsValue().indicatorCode,
      code: getFieldsValue().indicatorName,
      flag: isEdit.value ? '1' : '',
    });
    tableRefArr.value[groupCode].setTableData([...dataSource]);
    tableListData.value[index] = tableRefArr.value[groupCode].getDataSource().length;
    await tableRefArr.value[groupCode].reload();
    scrollBottom();
  }

  function handleRemoveRow(groupCode, record, index) {
    const dataSource = tableRefArr.value[groupCode].getDataSource();
    if (!isEdit.value && dataSource.length === 1) {
      createMessage.warning('至少需要一条数据');
      return;
    }
    const findIndex = dataSource.findIndex((item) => item === record);
    dataSource.splice(findIndex, 1);
    tableRefArr.value[groupCode].setTableData([...dataSource]);
    if (dataSource.length === 0) {
      tableData.value = tableData.value.filter((item) => item.groupCode !== groupCode);
    }
    const findItem = tableData.value.find((item) => item.groupCode === groupCode);
    if (findItem) {
      const timeOpt = findItem.timeTypeOptions.find((item) => item.value === record.dataTimeType);
      if (timeOpt) timeOpt['disabled'] = false;
    }

    tableListData.value[index] = tableRefArr.value[groupCode].getDataSource().length;
  }

  const formulaVerifyInfo = reactive<{
    code: null | number;
    type: 'warning' | 'success' | 'error' | 'info' | undefined;
    message: string;
  }>({
    code: null,
    type: undefined,
    message: '',
  });
  const verifyVerformulaHandler = async (value) => {
    checkLoading.value = true;
    const params = {
      formula: value,
      factoryId: factoryId.value,
      isNull: getFieldsValue().isNull,
      dataTimeType: getFieldsValue().dataTimeType,
    };
    try {
      const data = await checkFormula(params);
      formulaVerifyInfo.code = 1;
      formulaVerifyInfo.type = 'success';
      formulaVerifyInfo.message = `校验通过，结果为${data.result}`;
    } catch (_) {
      formulaVerifyInfo.code = 0;
      formulaVerifyInfo.type = 'error';
      formulaVerifyInfo.message = `校验不通过`;
    } finally {
      checkLoading.value = false;
    }
  };

  const changeTextarea = () => {
    formulaVerifyInfo.code = null;
    formulaVerifyInfo.type = undefined;
    formulaVerifyInfo.message = '';
  };

  // 生成table数据
  async function handleGenerate() {
    const values = getFieldsValue();
    console.log('values', values);
    if (!values.indicatorCode) {
      createMessage.warning('请填写测点编码');
      return;
    }
    if (values.sourceType === undefined) {
      createMessage.warning('请选择数据来源');
      return;
    }
    if (values.dataTimeType === undefined) {
      createMessage.warning('请选择时间类型');
      return;
    }
    try {
      okLoading.value = true;
      const data = await getTagIndexList({
        indicatorCode: values.indicatorCode,
        dataTimeType: values.dataTimeType,
        dataCalcType: values.dataCalcType,
        sourceType: values.sourceType,
      });
      if (!data.length) {
        createMessage.warning('数据生成失败，请选择其他时间类型');
        return;
      }
      tableData.value.push({
        nodeGroupRelations: data.map((item) => {
          return {
            ...item,
            dataCalcType: undefined,
            flag: '',
          };
        }),
        groupCode: 'add',
        disabledEdit: false,
        timeTypeOptions: defaultTimeTypeOptions.map((item) => {
          return {
            ...item,
            disabled: data.some((sourceItem) => sourceItem.dataTimeType === item.value),
          };
        }),
      });
    } finally {
      okLoading.value = false;
    }
  }

  async function handleSubmit() {
    try {
      okLoading.value = true;
      const values = await validate();
      values.nodeGroupRelations = [];
      values.dataCalcType = parseInt(values.dataCalcType);
      let flag = false;
      Object.keys(tableRefArr.value).forEach((key) => {
        const dataSource = tableRefArr.value[key].getDataSource();
        if (
          dataSource.some(
            (item) =>
              item.dataTimeType === undefined ||
              item.dataCalcType === undefined ||
              !item.indicatorCode ||
              !item.indicatorName,
          )
        ) {
          flag = true;
        }
        const data = dataSource.map((item, index) => {
          return {
            ...item,
            sortId: index + 1,
          };
        });
        values.nodeGroupRelations.push({
          groupCode: key === 'add' ? '' : key,
          nodeRelationByGroupCodes: data,
        });
      });
      if (flag) {
        createMessage.warning('请填写完整数据');
        return;
      }
      if (values.sourceType == 1) await verifyVerformulaHandler(values.formula);
      if (values.sourceType == 1 && formulaVerifyInfo.code != 1) {
        return;
      }
      if (!isEdit.value) {
        values.nodeGroupRelations = values.nodeGroupRelations[0].nodeRelationByGroupCodes;
      }
      values.factoryId = factoryId.value;
      values.gatherCode = values.gatherCode ?? values.indicatorCode;
      const postUrl = isEdit.value ? updateIndicator : addIndicator;
      await postUrl(values);
      createMessage.success('操作成功');
      handleCancel();
      emit('success');
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    closeModal();
    resetFields();
    tableData.value = [];
    tableRefArr.value = {};
    formulaVerifyInfo.code = null;
    formulaVerifyInfo.type = undefined;
    formulaVerifyInfo.message = '';
  }

  const getResourceIndexPage = async () => {
    indicatorList.value = await callResourceIndexUrl({
      resourceIndexId: 1,
      factoryId: factoryId.value,
    });
  };

  const checkedAllState = computed(() => {
    if (!checkedList.value.length || filterTagetList.value.every((item: any) => !item.checked))
      return 'checkEmpty';
    if (
      filterTagetList.value.every((item: any) => item.checked) &&
      checkedList.value.length >= filterTagetList.value.length
    ) {
      return 'checked';
    }
    return 'indeterminate';
  });

  const filterTagetList = computed(() => {
    let result = indicatorList.value;
    if (inputVal.value.trim()) {
      const inputValArr = inputVal.value.split(' ').filter((item) => item);
      result = result.filter((item: any) => {
        return inputValArr.every((i) => {
          return (
            item.displayName.toLocaleLowerCase().includes(i.toLocaleLowerCase()) ||
            item.sourceUniqueKey.toLocaleLowerCase().includes(i.toLocaleLowerCase())
          );
        });
      });
    }
    return result;
  });

  // 切换全选
  const handleChangeCheckAll = () => {
    const checked = ['checkEmpty', 'indeterminate'].includes(checkedAllState.value);
    filterTagetList.value.forEach((item: any) => {
      item.checked = checked;
    });
    checkedList.value = indicatorList.value.filter((item: any) => item.checked);
  };

  const handleCopyItem = async (item: any) => {
    if (navigator.clipboard && window.isSecureContext) {
      try {
        await navigator.clipboard.writeText(item.sourceUniqueKey);
        message.success('复制成功！');
      } catch (_) {
        message.error('复制失败！');
      }
    } else {
      const textArea = document.createElement('textarea');
      textArea.value = item.sourceUniqueKey;
      textArea.style.position = 'absolute';
      textArea.style.opacity = '0';
      textArea.style.left = '-9999999px';
      textArea.style.top = '-9999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      document.execCommand('copy') ? message.success('复制成功！') : message.error('复制失败！');
    }
  };

  const handleDeleteItem = (item: any) => {
    indicatorList.value.find((i: any) => i.sourceUniqueKey === item.sourceUniqueKey).checked =
      false;
    checkedList.value = checkedList.value.filter(
      (i: any) => i.sourceUniqueKey !== item.sourceUniqueKey,
    );
  };

  const handleCheckIndicator = (item: any) => {
    item.checked = !item.checked;
    if (item.checked) {
      checkedList.value.push(JSON.parse(JSON.stringify(item)));
    } else {
      checkedList.value = checkedList.value.filter(
        (i: any) => i.sourceUniqueKey !== item.sourceUniqueKey,
      );
    }
  };

  const clearList = () => {
    indicatorList.value.forEach((item: any) => {
      item.checked = false;
    });
    checkedList.value = [];
  };

  const modalRef = ref<any>(null);
  const scrollBottom = () => {
    modalRef.value?.modalWrapperRef.scrollBottom();
  };
</script>

<style lang="less" scoped>
  .tip-alert {
    height: 32px;
  }

  .formula-textarea {
    &.ant-select-disabled {
      :deep(.ant-select-selector) {
        padding-left: 0;
      }
    }

    :deep(.ant-select-selector) {
      padding-left: 0 !important;
    }
  }

  .content-container {
    display: flex;
    border: 1px solid #e9e9e9;
    height: 260px;
    color: #666;
    border-radius: 4px;

    ::-webkit-scrollbar {
      height: 0 !important;
      width: 0 !important;
      background: transparent;
    }

    & > div {
      width: 50%;
      padding: 12px;

      &:first-child {
        width: calc(50% - 50px);
      }

      &:last-child {
        width: calc(50% + 50px);
        border-left: 1px solid #e9e9e9;
        position: relative;
      }
    }

    .ant-input-affix-wrapper {
      padding: 4px 11px;

      .ant-input-prefix {
        img {
          width: 14px;
        }
      }
    }

    .select-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12px;
      font-weight: 500;
      color: #333;

      & > div {
        flex-shrink: 0;

        &:nth-child(1) {
          width: 160px;
        }

        &:nth-child(2) {
          width: 170px;
        }
      }

      .checkAll-box {
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          margin-right: 8px;
        }

        img {
          cursor: pointer;
        }
      }
    }

    .indicator-container {
      margin-top: 17px;

      .scroll-item {
        display: flex;
        justify-content: space-between;

        & > * {
          flex-shrink: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        & > div {
          &:nth-child(1) {
            width: 160px;
          }

          &:nth-child(2) {
            width: 170px;
          }
        }

        .ant-checkbox-wrapper {
          width: 52px;
          justify-content: flex-end;
        }
      }
    }

    .checked-header-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 500;
      line-height: 1;
      margin-bottom: 16px;
      color: #333;

      & > div {
        // &:nth-child(1) {
        //   width: 180px;
        // }

        &:nth-child(2) {
          width: 72px;
        }
      }
    }

    .checked-container {
      width: 100%;
      height: 173px;
      overflow-y: auto;

      .checked-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        width: 100%;

        .btns {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 0 16px;
        }

        & > * {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        & > div {
          // &:nth-child(1) {
          //   width: 180px;
          // }

          &:nth-child(2) {
            width: 72px;
          }
        }

        .edit-item {
          cursor: text;
          position: relative;
          width: 100%;
          display: flex;
          align-items: center;

          .name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            display: inline-block;
            line-height: 1;
            flex: 1;
          }

          .icon {
            display: none;
          }

          &:hover {
            color: @theme-color;

            .icon {
              display: block;
            }
          }
        }

        .copy-btn {
          cursor: pointer;
          color: @theme-color;
        }

        .delete-btn {
          cursor: pointer;
          color: @theme-color;
        }
      }
    }

    .bottom-container {
      position: absolute;
      bottom: 0;
      height: 35px;
      border-top: 1px solid #e9e9e9;
      left: 0;
      width: 100%;
      line-height: 35px;
      padding: 0 12px;
      display: flex;
      justify-content: flex-end;

      .tips {
        color: #999999;
      }

      .clear-btn {
        color: @theme-color;
        cursor: pointer;
      }
    }
  }

  .tables-container {
    :deep(.vben-basic-table) {
      .ant-table-wrapper {
        margin: 0;
      }
    }
  }
</style>
