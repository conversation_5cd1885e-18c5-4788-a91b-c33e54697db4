<template>
  <div class="h-full">
    <BasicTable v-show="!show" @register="registerTable">
      <template #tableTitle>
        <a-button
          type="primary"
          :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
          @click="handleAdd"
        >
          新增
        </a-button>

        <BasicImport
          @success="handleImport"
          :api="sleepOneSecond"
          :isModalTip="false"
          okText="识别"
          :accept="['jpeg', 'jpg', 'png', 'svg', 'gif']"
        >
          <!-- <a-button :icon="h(Icon, { icon: 'icon-park-outline:upload' })" class="ml-4">
            上传
          </a-button> -->
        </BasicImport>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <!-- {
            label: '分享',
            onClick: handleShare.bind(null, record),
          }, -->
          <TableAction
            :actions="[
              {
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '删除',
                onClick: handleDelete.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ChartPage
      v-if="show"
      v-model:show="show"
      :chartTitle="chartTitle"
      :chartData="chartData"
      :configData="configData"
      :allProjects="allProjects"
      style="height: 100%"
      @success="reload"
    />
    <TemplateModal @success="reload" @register="registerEditModal" />
    <ShareModal @register="registerShareModal" title="上传化验单" />
  </div>
</template>

<script lang="ts" setup name="CheckList">
  import { ref, h } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { columns, searchFormSchema, recordTestData, sleepOneSecond } from './data';
  import { useMessage } from '/@/hooks/web/useMessage';
  import ChartPage from './components/ChartPage.vue';
  import {
    getAllAssaysProjects,
    getAllAssayDocumentsByPage,
    removeAssayDocument,
    getAssayTemplateDetail,
  } from '/@zhcz/api/chemical';
  import TemplateModal from './components/TemplateModal.vue';
  import ShareModal from './components/ShareModal.vue';
  // import BasicImport from '/@/components/ImportButton/src/BasicImport.vue';
  import { BasicImport } from '/@zhcz/components/ImportButton';

  defineOptions({
    name: 'ChemicalManagementChecklist',
  });

  let allProjects = ref([]);
  async function getProjects() {
    allProjects.value = (await getAllAssaysProjects({})) || [];
  }
  getProjects();

  const { createMessage, createConfirm } = useMessage();
  const [registerTable, { reload }] = useTable({
    columns: columns(handleDetail),
    api: getAllAssayDocumentsByPage,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'size',
    },
    showIndexColumn: true,
    formConfig: {
      schemas: searchFormSchema,
    },
    showTableSetting: true,
    useSearchForm: true,
    handleSearchInfoFn: (data) => {
      data = data || {};
      return {
        ...data,
        startDate: data.createdTime?.length ? data.createdTime[0] : null,
        endDate: data.createdTime?.length ? data.createdTime[1] : null,
        createdTime: null,
      };
    },
    actionColumn: {
      width: 110,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  const show = ref(false);
  const chartTitle: any = ref('');
  const chartData: any = ref([]);
  const configData = ref({});
  const [registerEditModal, { openModal: openEditModal }] = useModal();
  // const [registerShareModal, { openModal: openShareModal }] = useModal();
  const [registerShareModal] = useModal();

  function handleAdd() {
    openEditModal(true, {
      isEdit: false,
    });
  }

  function handleEdit(record: Recordable) {
    openEditModal(true, {
      isEdit: true,
      record,
    });
  }

  // function handleShare(record) {
  //   openShareModal(true, { record });
  // }

  async function handleDetail(record: Recordable) {
    chartData.value = [];
    chartTitle.value = record.title;
    const config = JSON.parse(record.config);
    if (!config) {
      createMessage.error('化验单数据未录入');
      return;
    }
    const templateData = await getAssayTemplateDetail(record.templateId);
    const chartConfig = JSON.parse(templateData.chartConfig);
    if (!chartConfig) {
      createMessage.error('化验模板未配置图表');
      return;
    }
    configData.value = config;
    if (config.rowDenormalized) {
      const filterColsData = config.colsData.filter((item) => item.value !== '检测项');
      const filterTableData = config.tableData.filter((item) => item.nodeId);
      filterColsData.forEach((item, index) => {
        if (index === 0) return;
        const obj = {
          indexName: item.value,
          unitName: item.unit || '',
          data: filterTableData.map((i) => {
            return {
              collectDateTime: i.value,
              value: i.cells[index].value || 0,
            };
          }),
        };
        chartData.value.push(obj);
      });
    } else {
      config.tableData.forEach((item) => {
        const obj = {
          indexName: item.cells[0].value,
          unitName: item.cells[0].unit || '',
          data: item.cells.map((i, index) => {
            return {
              collectDateTime: config.colsData[index].value,
              value: i.value || 0,
            };
          }),
        };
        obj.data.shift();
        chartData.value.push(obj);
      });
    }
    console.log(chartData.value);
    chartData.value.forEach((item) => {
      const itemConfig = chartConfig.find((i) => i.label === item.indexName);
      item.chartType = itemConfig?.chartType || 'line';
      item.color = itemConfig?.color || '';
    });
    show.value = true;
  }

  async function handleDelete(record: Recordable) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '是否确认删除',
      onOk: async () => {
        await removeAssayDocument(record.id);
        createMessage.success('操作成功');
        reload();
      },
    });
  }
  // 图片解析处理逻辑
  function handleImport() {
    openEditModal(true, {
      isEdit: false,
      isTest: true, // 图片上传解析，静态页面，全部假数据
      record: recordTestData,
    });
  }
</script>
