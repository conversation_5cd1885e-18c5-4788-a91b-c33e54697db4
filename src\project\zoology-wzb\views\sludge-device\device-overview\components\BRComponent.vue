<template>
  <div class="brc">
    <RankingSimpleCard
      v-bind="{ bottomList: indexList, empty, loading, indexType: Boolean(true) }"
      :title="'报警记录'"
    >
      <template #headerRight>
        <div class="header-right">
          <div>
            <Select
              v-model:value="dataType"
              @change="getData"
              style="width: 80px"
              v-if="dataList.length"
            >
              <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
                {{ item.label }}
              </SelectOption>
            </Select>
          </div>

          <DatePicker
            v-if="dataList.find((item) => item.value === dataType)?.label === '月'"
            @change="getData"
            v-model:value="date"
            picker="month"
            valueFormat="YYYY-MM"
            :disabledDate="disabledMonthDate"
          />
          <DatePicker
            v-if="dataList.find((item) => item.value === dataType)?.label === '日'"
            @change="getData"
            valueFormat="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model:value="date1"
            placeholder="请选择"
            :allowClear="false"
            :disabledDate="disabledDate"
          />
        </div>
      </template>
    </RankingSimpleCard>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { RankingSimpleCard } from '/@zhcz/components/HLCardComponent';
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';

  const loading = ref(false);
  const empty = ref(false);
  type OptionItem = {
    label: string;
    value: string;
  };
  const dataList = ref<OptionItem[]>([]);
  const dataType = ref<any>(null);
  const date = ref(dayjs().subtract(1, 'month').format('YYYY-MM'));
  const date1 = ref(dayjs().subtract(1, 'day').format('YYYY-MM-DD'));

  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);
  async function getTimeList() {
    try {
      loading.value = true;
      const res = await listSenceGroupByParent({
        groupCode: 'yhzl2_yypx',
        factoryId: 1,
        platformld: 1,
      });
      if (Object.keys(res).length) {
        empty.value = false;
        dataType.value = Object.keys(res)[0];
        dataList.value = Object.keys(res).map((item) => ({
          value: item,
          label: res[item],
        }));
        getData();
      } else {
        empty.value = true;
        loading.value = false;
      }
    } catch (error) {
      empty.value = true;
      loading.value = false;
      console.log('error', error);
    }
  }
  function disabledDate(current) {
    return current && current > dayjs().subtract(0, 'day').endOf('day');
  }
  function disabledMonthDate(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('day');
  }

  const getData = async () => {
    try {
      loading.value = true;
      empty.value = false;
      const startDateTime =
        dataList.value.find((item) => item.value === dataType.value)?.label === '日'
          ? dayjs(date1.value).format('YYYY-MM-DD 00:00:00')
          : dayjs(date.value).startOf('month').format('YYYY-MM-DD 00:00:00');
      const endDateTime =
        dataList.value.find((item) => item.value === dataType.value)?.label === '日'
          ? dayjs(date1.value).format('YYYY-MM-DD 23:59:59')
          : dayjs(date.value).endOf('month').format('YYYY-MM-DD 23:59:59');
      const params = {
        startDateTime: startDateTime,
        endDateTime: endDateTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        resourceInterfaceId: '5',
        jsConvert: true,
        groupCode: dataType.value,
        paramsData: JSON.stringify(params),
      };
      indexList.value = [];
      const { data } = await callResourceFunction(paramData);
      if (data && data.length) {
        loading.value = false;
        empty.value = false;
        const newData = data.map((item) => {
          return {
            indexName: item.indexName,
            indexCode: item.indexCode,
            unitName: item.unitName,
            value: Number(item.total) ? Number(item.total) : '-',
          };
        });
        indexList.value = BubbleSort(newData);
      } else {
        empty.value = true;
        loading.value = false;
      }
    } catch (err) {
      empty.value = true;
      loading.value = false;
      console.log(err);
    }
  };

  function BubbleSort(ary) {
    for (var i = 0; i < ary.length - 1; i++) {
      for (var j = i + 1; j < ary.length; j++) {
        var current = ary[i];
        if (Number(current.value) < Number(ary[j].value)) {
          var tmp = ary[j];
          ary[j] = current;
          ary[i] = tmp;
        }
      }
    }
    return ary;
  }
  getTimeList();
</script>
<style lang="less" scoped>
  .brc {
    background-color: #fcfcfc;
    height: 100%;
    overflow: hidden;
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
    border-right: 1px solid #d8d8d8;

    .header-right {
      display: flex;
      gap: 0 16px;
    }
  }
</style>
