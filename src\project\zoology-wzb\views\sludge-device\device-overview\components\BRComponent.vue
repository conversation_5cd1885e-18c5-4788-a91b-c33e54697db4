<template>
  <HLCard title="报警记录" :styleData="{ borderRadius: '4px' }">
    <template #defaultBody>
      <TableSimple
        :dataSource="list"
        :columns="tableColumns"
        :showIndexColumn="false"
        :pagination="false"
      />
    </template>
  </HLCard>
</template>
<script lang="ts" setup>
  import { HLCard } from '/@/project/zhcz/components/HLCard';
  import { TableSimple } from '/@zhcz/components/HLCardTable';
  import { ref } from 'vue';
  import { getListByPage } from '/@zhcz/api/event-center';
  import dayjs from 'dayjs';

  const list = ref([]);
  const tableColumns = ref([
    {
      title: '报警名称',
      dataIndex: 'title',
    },
    {
      title: '所属水厂',
      dataIndex: 'eventStatus',
      width: 110,
    },
    {
      title: '报警类型',
      dataIndex: 'warnEventTypeTitle',
      width: 90,
    },
    {
      title: '报警时间',
      dataIndex: 'creationTime',
      customRender: ({ record }) => {
        return dayjs(record.creationTime).format('YYYY-MM-DD');
      },
      width: 110,
    },
  ]);

  const getData = async () => {
    try {
      const params = {
        current: 1,
        size: 99999,
        // eventStatus: 10000,
      };
      const res = await getListByPage(params);
      list.value = res.records;
    } catch (e) {}
  };
  getData();
</script>
<style lang="less" scoped>
  :deep(.card-body) {
    .card-table {
      flex: 1;
      width: 100%;
      overflow-y: auto;
      // overflow-x: hidden;

      .ant-table-header {
        overflow: unset !important;
      }

      .ant-table-wrapper {
        padding: 0;
        margin: 0;
      }

      .ant-table-wrapper,
      .ant-spin-nested-loading,
      .ant-spin-container,
      .ant-table,
      .ant-table-container {
        height: 100% !important;
      }

      .ant-table-container {
        display: flex;
        flex-direction: column;
      }

      .ant-table-body {
        height: unset !important;
        max-height: unset !important;
        overflow-x: hidden !important;
      }
    }
  }
</style>
