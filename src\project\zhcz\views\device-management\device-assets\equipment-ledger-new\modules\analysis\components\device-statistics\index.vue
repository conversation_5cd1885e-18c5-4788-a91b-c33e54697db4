<template>
  <div ref="chartRef" style="width: 100%; height: 100%"></div>
</template>
<script lang="ts" setup>
  import { PropType, ref, Ref, onMounted, watch } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import dayjs from 'dayjs';
  import { flatten } from 'lodash-es';

  const props = defineProps({
    data: {
      type: Array as any,
      default: () => [],
    },
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '100%',
    },
  });

  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);

  const echartData = ref<
    { indexName: string; unitName: number; data: { collectDateTime: string; value: number }[] }[]
  >([]);

  const roundFun = (value, n) => {
    let result = Math.round(value * Math.pow(10, n)) / Math.pow(10, n) + '';

    const rs = result.indexOf('.');

    if (rs < 0 && n > 0) {
      let i = 0;
      let suffix = '.';
      while (i < n) {
        suffix += '0';
        i++;
      }

      result += suffix;
    }

    return result;
  };

  const renderEchart = () => {
    const xAxis = echartData.value[0].data.map(
      (item) => `${dayjs(item.collectDateTime).format('MM-DD')}`,
    );
    const unit = echartData.value[0].unitName;
    const list = flatten(echartData.value.map((i) => i.data));
    let dataList: number[] = [];
    if (list.length) {
      dataList = list.map((i) => Number(i.value));
    } else {
      dataList = [0];
    }
    let maxdata = Math.max(...dataList);
    if (isNaN(maxdata)) maxdata = 0;
    const max = Math.ceil(maxdata / 8) * 8;
    const isW = maxdata >= 10 ** 4;

    const option = {
      legend: {
        show: false,
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          return `${params[0].name}<br/> ${params[0].marker}${
            params[0].seriesName
          }&nbsp;&nbsp;&nbsp;&nbsp;${
            params[0].value === null || params[0].value === undefined ? '-' : params[0].value
          }${params[0].value === null || params[0].value === undefined || !unit ? '' : unit}`;
        },
      },
      grid: {
        // top: maxdata ? 30 : 10,
        top: 5,
        left: 5,
        right: max ? 5 : 20,
        bottom: 0,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        data: xAxis,
        axisLine: {
          show: true,

          lineStyle: {
            color: '#E9E9E9',
            type: 'solid',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          // padding: [0, 5, 0, 0],
          textStyle: {
            color: '#999999',
            fontSize: 14,
          },
        },
      },
      yAxis: {
        type: 'value',
        // name: unit ? `单位（${unit}）` : '',
        name: '',
        nameTextStyle: {
          color: '#999',
          fontSize: 14,
          padding: [0, 0, 0, 40],
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E9E9E9',
            type: 'solid',
          },
        },
        max: max ? max : 10,
        min: 0,
        splitNumber: 4,
        interval: max ? max / 4 : 2,
        splitLine: {
          show: true,
          lineStyle: {
            color: '#E9E9E9',
            type: 'dashed',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
            fontSize: 14,
          },
          formatter(value) {
            const val = isW ? `${roundFun(value / 10000, 1)}万` : value;
            return val;
          },
        },
      },
      series: echartData.value.map((item) => {
        return {
          name: item.indexName,
          type: 'bar',
          color: 'rgba(46, 123, 255, 0.80)',
          barMaxWidth: 24,
          symbolSize: 8,
          symbol: 'circle',
          emphasis: {
            focus: 'series',
          },
          data: item.data.map((i) =>
            i.value !== null && i.value !== undefined ? Number(i.value).toFixed(2) : null,
          ),
        };
      }),
    };
    setOptions(option as any);
  };

  onMounted(() => {
    echartData.value = props.data;

    if (echartData.value.length) {
      renderEchart();
    }
  });

  watch(
    () => props.data,
    () => {
      echartData.value = props.data;
      renderEchart();
    },
    {
      deep: true,
    },
  );
</script>
