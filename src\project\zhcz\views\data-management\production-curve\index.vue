<template>
  <div class="page h-full">
    <div class="content-container">
      <div class="tree-content" :class="{ 'show-tree-content': showTree }">
        <div class="tit">生产指标</div>
        <Tree
          v-model:checked-keys="checkedKeys"
          :tree-data="indicatorTreeData"
          blockNode
          checkable
          defaultExpandAll
          @check="handleSelectNode"
          v-if="indicatorTreeData.length"
        >
          <template #title="node">
            <div style="overflow: hidden; text-overflow: ellipsis">
              <Tooltip>
                <template #title>{{ node.groupName || node.title }}</template>
                {{ node.groupName || node.title }}
              </Tooltip>
            </div>
          </template>
        </Tree>
        <div class="empty" v-if="indicatorTreeData.length <= 0"><HEmpty /></div>
      </div>
      <div class="resize" @mousedown="startDrag"> ⋮ </div>
      <div class="toggle-show" :class="{ 'toggle-show-right': showTree }" @click="toggleTree">
        <RightOutlined v-show="!showTree" />
        <LeftOutlined v-show="showTree" />
      </div>
      <div ref="rightBoxRef" class="right-container">
        <div class="search-container">
          <div class="search-item">
            <div class="label">
              <span style="color: red">*</span>
              时间：
            </div>
            <Select
              v-if="rightBoxWidth <= 1200"
              v-model:value="searchForm.type"
              class="mr-4"
              style="width: 200px"
              @change="handleSearch"
            >
              <Select.Option v-for="item in timeList" :key="item.value" :value="item.value">
                {{ item.label }}
              </Select.Option>
            </Select>
            <RadioGroup v-else v-model:value="searchForm.type" class="mr-4" @change="handleSearch">
              <RadioButton :value="item.value" v-for="item in timeList" :key="item.value">
                {{ item.label }}
              </RadioButton>
            </RadioGroup>
            <RangePicker
              v-show="searchForm.type === -1"
              v-model:value="dateValue"
              :disabled-date="disabledDate"
              :allowClear="false"
              @change="handleSearch"
            />
          </div>
          <!-- <div>
            <a-button
              :icon="h(Icon, { icon: 'icon-park-outline:search', size: 14 })"
              type="primary"
              class="mr-4"
              :loading="okLoading"
              @click="handleSearch"
            >
              查询
            </a-button>
            <a-button
              :icon="h(Icon, { icon: 'icon-park-outline:refresh', size: 14 })"
              type="default"
              @click="handleReset"
            >
              重置
            </a-button>
          </div> -->
        </div>
        <div class="tool-container">
          <div class="left-box">
            <div class="tool-item">
              <div class="label">
                <span style="color: red">*</span>
                时间间隔：
              </div>
              <Select
                v-model:value="searchForm.interval"
                class="mr-4"
                style="width: 200px"
                @change="handleSearch"
              >
                <Select.Option v-for="item in intervalList" :key="item.value" :value="item.value">
                  {{ item.label }}
                </Select.Option>
              </Select>
            </div>
            <Checkbox
              v-model:checked="searchForm.lastYearChecked"
              :disabled="!baseChartData.length"
              @change="handleSearch"
            >
              同比
            </Checkbox>
            <Checkbox
              v-model:checked="searchForm.lastMonthChecked"
              :disabled="!baseChartData.length"
              @change="handleSearch"
            >
              环比
            </Checkbox>
          </div>
          <div class="right-box">
            <div class="btn" @click="handleSearch">
              <Icon icon="icon-park-outline:refresh" size="16" />
            </div>
            <div :class="['btn', { active: showType === 'curve' }]" @click="showType = 'curve'">
              <Icon icon="icon-park-outline:chart-line" size="16" />
            </div>
            <div :class="['btn', { active: showType === 'table' }]" @click="showType = 'table'">
              <Icon icon="icon-park-outline:table-file" size="16" />
            </div>
            <div class="btn" @click="handleExport">
              <Icon icon="icon-park-outline:upload" size="16" />
            </div>
          </div>
        </div>
        <div class="chart-container" :style="{ opacity: showType === 'curve' ? 1 : 0 }">
          <Chart
            v-if="baseChartData.length || lastMonthData.length || lastYearData.length"
            ref="chartRef"
            :baseData="baseChartData"
            :monthData="lastMonthData"
            :yearData="lastYearData"
          />
          <div
            class="data-empty"
            v-if="!baseChartData.length && !lastMonthData.length && !lastYearData.length"
          >
            <HEmpty />
          </div>
        </div>
        <div
          style="position: absolute; bottom: 0px; height: calc(100% - 124px); width: 100%"
          :style="{
            opacity: showType === 'table' ? 1 : 0,
            zIndex: showType === 'table' ? 1 : -1,
          }"
        >
          <BasicTable
            :columns="tableColumns"
            :dataSource="tableData"
            :loading="okLoading"
            :showIndexColumn="false"
            :pagination="pagination"
            @change="handleChangePage"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
  export default { name: 'DataManagementProductionCurve' };
</script>
<script lang="ts" setup name="ProductionCurve">
  import { onMounted, ref, reactive, nextTick, onUnmounted } from 'vue';
  import {
    RangePicker,
    Select,
    Checkbox,
    Tree,
    Tooltip,
    RadioGroup,
    RadioButton,
  } from 'ant-design-vue';
  import { RightOutlined, LeftOutlined } from '@ant-design/icons-vue';
  import {
    getGroupTreeWithIndicators,
    getDataCurve,
    dataCurvePageTable,
    dataCurveExport,
  } from '/@zhcz/api/config-center/scenes-group';
  import HEmpty from '/@/components/HEmpty';
  import dayjs from 'dayjs';
  import { timeList, intervalList } from './data';
  import { groupBy } from 'lodash-es';
  import Chart from './components/Chart.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicTable } from '/@/components/Table';
  import { downloadByData } from '/@/utils/file/download';
  import { useLoading } from '/@/components/Loading';
  import { Icon } from '/@/components/Icon';
  import { flatMapDeep } from 'lodash-es';

  // defineOptions({
  //   name: 'DataManagementProductionCurve',
  // });

  const [openWrapLoading, closeWrapLoading] = useLoading({
    props: {
      absolute: true,
    },
  });
  const { createMessage } = useMessage();
  const indicatorTreeData: any = ref([]);
  const checkedKeys = ref([]);
  const checkedIndicators: any = ref([]);
  const dateValue: any = ref([dayjs(), dayjs()]);
  const chartRef = ref(null);
  const baseChartData: any = ref([]);
  const lastMonthData: any = ref([]);
  const lastYearData: any = ref([]);
  const searchForm = reactive({
    type: 1,
    interval: 1,
    lastMonthChecked: false,
    lastYearChecked: false,
  });
  const okLoading = ref(false);
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const tableColumns = ref([]);
  const tableData = ref([]);
  const showType = ref('curve');
  const startX = ref(0);
  const startWidth = ref(0);
  const observer = ref<ResizeObserver | null>(null);
  const rightBoxRef = ref<HTMLElement | null>(null);
  const rightBoxWidth = ref(0);
  const showTree = ref(true);

  function toggleTree() {
    showTree.value = !showTree.value;
  }

  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }

  function handleExport() {
    if (!checkedIndicators.value.length) {
      createMessage.error('请选择指标');
      return;
    }
    openWrapLoading();
    const params = {
      interval: searchForm.interval,
      indexCodes: checkedIndicators.value.map((i) => i.indexCode).join(','),
      ...getDateParams(),
      tongBiFlag: searchForm.lastYearChecked ? '1' : '0',
      huanBiFlag: searchForm.lastMonthChecked ? '1' : '0',
    };
    dataCurveExport(params)
      .then((res) => {
        downloadByData(res.data, res.fileName);
        closeWrapLoading();
      })
      .catch(() => {
        closeWrapLoading();
      });
  }

  function handleChangePage(data) {
    pagination.current = data.current;
    pagination.pageSize = data.pageSize;
    mergeTableData();
  }

  const flattenTree = (tree) => {
    return flatMapDeep(tree, (node) => {
      const children = node.children ? flattenTree(node.children) : [];
      return [node, ...children];
    });
  };

  const setDefaultKeys = () => {
    const firstTree = indicatorTreeData.value[0]?.children || [];
    const flattenedArray = flattenTree(firstTree);
    const indexList = flattenedArray.filter((i) => i.indexCode);
    checkedKeys.value = indexList.map((i) => i.key);
    checkedIndicators.value = indexList.map((i) => ({
      indexCode: i.indexCode,
      groupCode: i.groupCode,
      resourceInterfaceId: i.resourceInterfaceId,
    }));
    if (indexList.length) {
      handleSearch();
    }
  };

  // function handleReset() {
  //   checkedKeys.value = [];
  //   checkedIndicators.value = [];
  //   searchForm.type = 1;
  //   searchForm.interval = 1;
  //   searchForm.lastMonthChecked = false;
  //   searchForm.lastYearChecked = false;
  //   dateValue.value = [dayjs(), dayjs()];
  //   baseChartData.value = [];
  //   lastMonthData.value = [];
  //   lastYearData.value = [];
  //   showType.value = 'curve';
  //   tableColumns.value = [];
  //   tableData.value = [];
  //   setDefaultKeys();
  // }

  // 切换环比
  async function toggleLastMonthChart() {
    if (searchForm.lastMonthChecked) {
      lastMonthData.value = [];
      let { startDateTime, endDateTime } = getDateParams();
      startDateTime = dayjs(startDateTime).subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss');
      endDateTime = dayjs(endDateTime).subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss');
      const data = await queryIndexData(startDateTime, endDateTime);
      lastMonthData.value = data;
    } else {
      lastMonthData.value = [];
    }
  }

  // 切换年同比
  async function toggleLastYearChart() {
    if (searchForm.lastYearChecked) {
      lastYearData.value = [];
      let { startDateTime, endDateTime } = getDateParams();
      startDateTime = dayjs(startDateTime).subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss');
      endDateTime = dayjs(endDateTime).subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss');
      const data = await queryIndexData(startDateTime, endDateTime);
      lastYearData.value = data;
    } else {
      lastYearData.value = [];
    }
  }

  async function handleSearch() {
    if (!checkedIndicators.value.length) {
      createMessage.error('请选择指标');
      return;
    }
    // tableColumns.value = [];
    // tableData.value = [];
    // baseChartData.value = [];
    const { startDateTime, endDateTime } = getDateParams();
    const data = await queryIndexData(startDateTime, endDateTime);
    await toggleLastMonthChart();
    await toggleLastYearChart();
    baseChartData.value = data;
    mergeTableData();
    nextTick(() => {
      chartRef.value?.renderEcharts();
    });
  }

  async function mergeTableData() {
    try {
      okLoading.value = true;
      const params = {
        pageNum: pagination.current,
        size: pagination.pageSize,
        interval: searchForm.interval,
        indexCodes: checkedIndicators.value.map((i) => i.indexCode).join(','),
        ...getDateParams(),
        tongBiFlag: searchForm.lastYearChecked ? '1' : '0',
        huanBiFlag: searchForm.lastMonthChecked ? '1' : '0',
      };
      const data = await dataCurvePageTable(params);
      tableColumns.value = data.columns.map((i) => {
        return {
          ...i,
          title: i.headerText,
          dataIndex: i.indexCode,
        };
      });
      tableData.value = data.data;
      pagination.total = data.total;
    } finally {
      okLoading.value = false;
    }
  }

  /**
   * 获取日期参数
   * @returns { beginDate: string, endDate: string }
   */
  function getDateParams() {
    let startDateTime, endDateTime;
    if (searchForm.type === -1) {
      startDateTime = dayjs(dateValue.value[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss');
      endDateTime = dayjs(dateValue.value[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    } else {
      startDateTime = dayjs().subtract(searchForm.type, 'hour').format('YYYY-MM-DD HH:mm:ss');
      endDateTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    }
    return {
      startDateTime,
      endDateTime,
    };
  }

  // 查询指标数据
  async function queryIndexData(startDateTime: string, endDateTime: string) {
    try {
      okLoading.value = true;
      const paramsGroup = groupBy(checkedIndicators.value, 'groupCode');
      const paramsArr = Object.keys(paramsGroup).map((key) => {
        const params = {
          groupCodes: key,
          startDateTime,
          endDateTime,
          indexCodes: paramsGroup[key].map((i) => i.indexCode).join(','),
          interval: searchForm.interval,
        };
        return params;
      });
      const res = await Promise.all(paramsArr.map((i) => getDataCurve(i)));

      return res.flat();
    } finally {
      okLoading.value = false;
    }
  }

  function handleSelectNode(_, e) {
    const indexList = e.checkedNodes.filter((i) => i.indexCode);
    if (indexList.length > 15) {
      checkedKeys.value = [];
      createMessage.error('最多选择15个指标');
      return;
    }
    checkedIndicators.value = indexList.map((i) => ({
      indexCode: i.indexCode,
      groupCode: i.groupCode,
      resourceInterfaceId: i.resourceInterfaceId,
    }));
    if (indexList.length) {
      handleSearch();
    }
  }

  function processTreeNodes(data) {
    data.forEach((node) => {
      node.title = node.groupName;
      node.key = node.groupCode;
      node.selectable = false;
      if (node.children && node.children.length > 0) {
        processTreeNodes(node.children);
      }
      if (node.indicators && node.indicators.length > 0) {
        node.children = node.children || [];
        node.children = node.children.concat(
          node.indicators.map((item) => ({
            title: item.name,
            key: node.groupCode + item.code,
            indexCode: item.code,
            groupCode: node.groupCode,
            resourceInterfaceId: node.defaultInterfaceId,
            selectable: false,
          })),
        );
      }
    });
  }

  // 查询指标树
  async function queryIndicatorTree() {
    const data = await getGroupTreeWithIndicators('sjgl_ssqx');
    processTreeNodes([data]);
    indicatorTreeData.value = data?.id
      ? [
          {
            ...data,
            title: data.groupName,
            key: data.groupCode,
            selectable: false,
          },
        ]
      : [];
    // 设置默认选中值
    setDefaultKeys();
  }

  function startDrag(e) {
    startX.value = e.clientX;
    const treeContent = document.getElementsByClassName('tree-content')[0] as HTMLDivElement;
    treeContent.style.transition = 'none';
    startWidth.value = treeContent.offsetWidth;
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);
  }

  function drag(e) {
    const moveX = e.clientX - startX.value;
    if (startWidth.value + moveX <= 150 || startWidth.value + moveX >= 500) {
      return;
    }
    const treeContent = document.getElementsByClassName('tree-content')[0] as HTMLDivElement;
    treeContent.style.width = startWidth.value + moveX + 'px';

    if (rightBoxRef.value) {
      rightBoxRef.value.style.width = `calc(100% - 12px - ${startWidth.value + moveX}px)`;
    }
    const resize = document.getElementsByClassName('resize')[0] as HTMLDivElement;
    resize.style.left = startWidth.value + moveX + 8 + 'px';
  }

  function stopDrag() {
    document.removeEventListener('mousemove', drag);
    document.removeEventListener('mouseup', stopDrag);
  }

  function handleResizeRight(entries: any) {
    for (const entry of entries) {
      const { width } = entry.contentRect;
      rightBoxWidth.value = width;
    }
  }

  function handleResizeWindow() {
    const treeContent = document.getElementsByClassName('tree-content')[0] as HTMLDivElement;
    if (window.innerWidth <= 1200) {
      const resize = document.getElementsByClassName('resize')[0] as HTMLDivElement;
      treeContent.style.transition = 'all 0.3s';
      treeContent.style.width = '248px';
      treeContent.style.left = '-248px';
      resize.style.left = '256px';
      showTree.value = false;
    } else {
      treeContent.style.left = '0';
    }
  }

  onMounted(async () => {
    queryIndicatorTree();
    observer.value = new ResizeObserver(handleResizeRight);
    observer.value.observe(rightBoxRef.value as HTMLElement);
    handleResizeWindow();
    window.addEventListener('resize', handleResizeWindow);
  });

  // 卸载组件时清除监听
  onUnmounted(() => {
    observer.value?.disconnect();
    window.removeEventListener('resize', handleResizeWindow);
  });
</script>

<style lang="less" scoped>
  .page {
    padding: 0 16px 16px;
    overflow: hidden;

    .content-container {
      display: flex;
      height: 100%;
    }
  }

  .tree-content {
    width: 248px;
    background: #fff;
    border-radius: 4px;
    // max-height: calc(100vh - 56px - 60px - 16px);
    position: relative;
    flex-shrink: 0;

    .tit {
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 1px solid #e9e9e9;
      display: flex;
      align-items: center;
      padding: 0 12px;
    }

    :deep(.ant-tree) {
      overflow-y: auto;
      height: calc(100% - 48px);
      padding: 12px;
      background: transparent;

      .ant-tree-treenode {
        height: 40px;
        align-items: center;
        padding: 0;

        .ant-tree-switcher {
          width: auto;
          min-width: 12px;
          display: flex;
          align-items: center;
        }

        .ant-tree-checkbox {
          margin-inline-end: unset;
          margin-block-start: unset;
          margin-left: 8px;
        }

        .ant-tree-node-content-wrapper {
          line-height: 40px;
          padding: 0 0 0 12px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .ant-tree-node-selected {
          background: none;
          color: var(--theme-color);
        }
      }
    }
  }

  .show-tree-content {
    width: 248px;
    left: 0 !important;
  }

  .resize {
    cursor: col-resize;
    position: absolute;
    top: 45%;
    left: 256px;
    background-color: #d6d6d6;
    border-radius: 999px;
    font-size: 32px;
    color: white;
    user-select: none;
  }

  .toggle-show {
    width: 24px;
    height: 32px;
    border-radius: 0 4px 4px 0;
    border: 1px solid #e9e9e9;
    justify-content: center;
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 45%;
    z-index: 999;
    display: none;
    transition: all 0.3s;

    .anticon {
      color: #333333;
      opacity: 0.65;
      margin-right: 0;
    }
  }

  .toggle-show-right {
    border-radius: 4px 0 0 4px;
    left: 220px;
  }

  .right-container {
    width: calc(100% - 248px - 12px);
    display: flex;
    flex-direction: column;
    margin-left: 12px;
    position: relative;

    .search-container {
      display: flex;
      justify-content: space-between;
      background: #fff;
      padding: 12px 16px;
      border-radius: 4px;

      .search-item {
        display: flex;
        align-items: center;

        .label,
        .ant-radio-group {
          flex-shrink: 0;
        }

        .ant-picker {
          width: 280px !important;
        }
      }
    }

    .tool-container {
      display: flex;
      justify-content: space-between;
      padding: 12px 16px;
      background: #fff;
      margin-top: 12px;
      border-radius: 4px 4px 0 0;

      .left-box {
        display: flex;
        align-items: center;

        .tool-item {
          display: flex;
          align-items: center;
        }
      }

      .right-box {
        display: flex;
        align-items: center;

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          border-radius: 4px;
          cursor: pointer;
          margin-left: 16px;
          color: #999;

          &:hover {
            color: var(--theme-color);
            background: #f8f8f8;
          }
        }

        .active {
          color: var(--theme-color);
        }
      }
    }

    .chart-container {
      flex: 1;
      background: #fff;
      padding: 12px 16px 16px;
      border-radius: 0 0 4px 4px;

      .ant-empty {
        position: relative;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    :deep(.vben-basic-table) {
      height: 100%;

      .ant-spin-nested-loading {
        height: 100%;

        .ant-spin-container {
          height: 100%;
        }
      }

      :deep(.ant-table) {
        height: 100%;
      }

      .ant-table-wrapper {
        margin: 0;
        padding: 16px 16px 0;
        height: 100%;

        .ant-pagination {
          padding-right: 16px;
        }
      }
    }
  }

  .ant-btn-default:hover {
    background: #f8f8f8;
    color: #333;
    border: 1px solid #e9e9e9;
  }

  @media screen and (max-width: 1200px) {
    .tree-content {
      position: absolute;
      left: 0;
      z-index: 999;
      height: 100%;
    }

    .resize {
      display: none;
    }

    .toggle-show {
      display: flex;
    }

    .right-container {
      margin-left: 0;
    }
  }

  .empty {
    height: calc(100% - 72px);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .data-empty {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>

<style lang="less" scoped>
  .vben-page-wrapper {
    padding: 0 16px;
    overflow: hidden;
  }
</style>
