<template>
  <div class="page-container">
    <div class="tree-container" :class="{ 'hide-tree-container': !isShowTree }">
      <div class="tit-content">
        <div class="tit">组织架构</div>
        <div class="add-btn" @click="handleOpenCatalogueModal(false)">
          <PlusOutlined />
          <span> 新增 </span>
        </div>
      </div>
      <div class="search-box">
        <Input v-model:value="searchValue" placeholder="请输入部门名称" @change="handleSearch" />
      </div>
      <div class="tree-content">
        <Tree
          v-if="treeData.length"
          :tree-data="treeData"
          :fieldNames="{ key: 'id', title: 'name' }"
          blockNode
          defaultExpandAll
          @select="handleSelectNode"
          v-model:selected-keys="selectedKeys"
        >
          <template #title="node">
            <div
              class="flex items-center justify-between"
              @mouseenter="handleHoverNode(node)"
              @mouseleave="hoverNode = null"
            >
              <div style="overflow: hidden; text-overflow: ellipsis">
                {{ node.name }}
              </div>
              <Dropdown overlayClassName="custom-dropdown">
                <div v-show="hoverNode && hoverNode.id === node.id">
                  <div class="flex">
                    <Icon size="28" color="var(--theme-color)" icon="icon-park-outline:more" />
                  </div>
                </div>
                <template #overlay>
                  <Menu @mouseenter="handleHoverNode(node)" @mouseleave="hoverNode = null">
                    <!-- <Menu.Item @click="handleOpenCatalogueModal(false)"> 添加下级 </Menu.Item> -->
                    <Menu.Item @click="handleOpenCatalogueModal(true)"> 编辑 </Menu.Item>
                    <Popconfirm
                      title="是否确认删除"
                      ok-text="确认"
                      cancel-text="取消"
                      @confirm="handleDeleteClassify(node.id)"
                    >
                      <Menu.Item> 删除 </Menu.Item>
                    </Popconfirm>
                  </Menu>
                </template>
              </Dropdown>
            </div>
          </template>
        </Tree>
        <HEmpty v-else class="empty" />
      </div>
    </div>
    <div
      class="toggle-tree"
      @click="handleChangeTreeShow"
      :style="{
        width: isShowTree ? '0' : '16px',
        borderRight: isShowTree ? 'none' : '1px solid #e9e9e9',
      }"
    >
      <div
        class="toggle-tree-btn"
        :style="{
          left: isShowTree ? '-16px' : '0',
          borderRadius: isShowTree ? '4px 0px 0px 4px' : '0px 4px 4px 0px',
          background: isShowTree ? '' : 'var(--theme-color)',
          borderColor: isShowTree ? '' : 'var(--theme-color)',
        }"
      >
        <LeftOutlined v-show="isShowTree" />
        <RightOutlined v-show="!isShowTree" style="color: #fff" />
      </div>
    </div>
    <div ref="tableBoxRef" class="table-box">
      <div class="card-box" style="flex: none">
        <div class="header-box">
          <div class="left-box">
            <span class="tit">内部文件</span>
            <div class="add-btn" @click="handleAddFile">
              <PlusOutlined />
              新增
            </div>
          </div>
          <div class="right-box" @click="handleOpenRemindModal">
            <BellOutlined />
            到期提醒
          </div>
        </div>
        <div class="files-box">
          <div v-for="item in fileList" :key="item.id" class="content-item">
            <div class="con-tit">
              <span>{{ item.fileName }}</span>
              <div class="action-box">
                <a-button type="link" @click="handleEditFile(item)">编辑</a-button>
                <a-button type="link" style="color: #ff522b" @click="handleDeleteFile(item)"
                  >删除</a-button
                >
              </div>
            </div>
            <div class="con-field-box">
              <div>
                <span>有效期：</span>
                <span>{{ item.effectiveDeadline }}</span>
              </div>
              <div>
                <span>发布日期：</span>
                <span>{{ item.effectiveDeadline }}</span>
              </div>
              <div>
                <span>发布人：</span>
                <span>{{ item.createName }}</span>
              </div>
              <div>
                <span>文件：</span>
                <div class="file-tag-box" @click="handleDownload(item)">
                  <component :is="{ template: getFileTypeIcon(item.fileRealName) }" />
                  <div class="tit">{{ getFileName(item.fileRealName) }}</div>
                  <div>{{ getFileType(item.fileRealName) }}</div>
                </div>
              </div>
            </div>
          </div>
          <HEmpty v-if="!fileList.length" class="empty w-full" />
        </div>
      </div>
      <div class="card-box" style="height: 1px">
        <div class="header-box">
          <div class="left-box">
            <span class="tit">成员列表</span>
            <div class="add-btn" @click="handleAddPeople">
              <PlusOutlined />
              新增
            </div>
          </div>
        </div>
        <div class="people-box" :style="{ height: peopleList.length ? 'auto' : '100%' }">
          <div v-for="item in peopleList" :key="item.id" class="people-item">
            <div class="people-content-box">
              <img :src="getEevReturnDomain(item.imgUrl)" />
              <div class="field-box">
                <div>
                  <span class="label">姓名：</span>
                  <span class="con">
                    {{ item.userName }}
                  </span>
                </div>
                <div>
                  <span class="label">号码：</span>
                  <span class="con">
                    {{ item.phone }}
                  </span>
                </div>
                <div>
                  <span class="label">部门：</span>
                  <span class="con">
                    {{ selectedNode.name }}
                  </span>
                </div>
                <div>
                  <span class="label">角色：</span>
                  <span class="con">
                    {{ item.roleName }}
                  </span>
                </div>
              </div>
            </div>
            <div class="action-box">
              <a-button danger @click="handleDeletePeople(item)"> 删除 </a-button>
              <a-button type="primary" ghost @click="handleEditPeople(item)"> 编辑 </a-button>
            </div>
          </div>
          <HEmpty v-if="!peopleList.length" class="empty w-full" />
        </div>
      </div>
    </div>
    <FileEditModal @success="queryFileList" @register="registerFileEditModal" />
    <PeopleEditModal @success="queryPeopleList" @register="registerPeopleEditModal" />
    <CatalogueModal @success="getDeptTreeData" @register="registerCatalogueModal" />
    <RemindModal @register="registerRemindModal" />
  </div>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { svgList } from './data';
  import { Tree, Input, Dropdown, Menu, Popconfirm } from 'ant-design-vue';
  import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
  import { useModal } from '/@/components/Modal';
  import FileEditModal from './components/FileEditModal.vue';
  import PeopleEditModal from './components/PeopleEditModal.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    getFilePageApi,
    batchDeleteFileApi,
    getUserPageApi,
    batchDeleteUserApi,
    getCatalogueTreeApi,
    getCatalogueByIdApi,
    deleteCatalogueApi,
  } from '/@zhcz/api/security-management/safety-management-agency';
  import { getEevReturnDomain } from '/@zhcz/utils/file/url';

  import CatalogueModal from './components/CatalogueModal.vue';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import { PlusOutlined, BellOutlined } from '@ant-design/icons-vue';
  import RemindModal from './components/RemindModal.vue';
  import { useRouter } from 'vue-router';
  import { downloadByUrl } from '/@/utils/file/download';

  const treeData = ref([]);
  const isShowTree = ref(true);
  const tableBoxRef = ref(null);
  const searchValue = ref('');
  const selectedKeys = ref([]);
  const hoverNode = ref();
  const fileList = ref([]);
  const peopleList = ref([]);
  const selectedNode = ref(null);

  const [registerFileEditModal, { openModal: openFileEditModal }] = useModal();
  const [registerPeopleEditModal, { openModal: openPeopleEditModal }] = useModal();
  const [registerCatalogueModal, { openModal: openCatalogueModal }] = useModal();
  const [registerRemindModal, { openModal: openRemindModal }] = useModal();

  const { createMessage, createConfirm } = useMessage();
  const { currentRoute } = useRouter();

  watch(
    () => currentRoute.value.query,
    async (v) => {
      if (v.detailParam) {
        const detailParam = JSON.parse(v.detailParam || '');
        selectedKeys.value = [detailParam.deptId];
        queryFileList();
        queryPeopleList();
        selectedNode.value = {
          id: detailParam.deptId,
          name: detailParam.deptName,
        };
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );

  function handleOpenRemindModal() {
    if (selectedKeys.value.length === 0) {
      createMessage.warning('请先选择部门！');
      return;
    }
    openRemindModal(true, {
      selectedNode: selectedNode.value,
    });
  }

  function handleEditPeople(data) {
    openPeopleEditModal(true, {
      isEdit: true,
      record: data,
      deptId: selectedKeys.value[0] || undefined,
      treeData: treeData.value,
    });
  }

  function queryPeopleList() {
    getUserPageApi({
      current: 1,
      size: 999,
      deptId: selectedKeys.value[0] || undefined,
    }).then((res) => {
      peopleList.value = res.records;
    });
  }

  function queryFileList() {
    getFilePageApi({
      current: 1,
      size: 10,
      deptId: selectedKeys.value[0] || undefined,
    }).then((res) => {
      fileList.value = res.records;
    });
  }

  async function handleDeleteClassify(id) {
    await deleteCatalogueApi(id);
    createMessage.success('操作成功');
    getDeptTreeData();
  }

  async function handleOpenCatalogueModal(isEdit) {
    let data = null;
    if (isEdit) {
      data = await getCatalogueByIdApi({ id: hoverNode.value.id });
    } else {
      data = hoverNode.value;
    }
    openCatalogueModal(true, {
      isEdit,
      nodeData: data,
      treeData: treeData.value,
    });
  }

  function handleDeletePeople(record) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '是否确认删除',
      onOk: async () => {
        await batchDeleteUserApi({ ids: [record.id] });
        createMessage.success('操作成功');
        queryPeopleList();
      },
    });
  }

  function handleEditFile(record) {
    openFileEditModal(true, {
      isEdit: true,
      record,
    });
  }

  function handleDownload(record) {
    try {
      downloadByUrl({
        url: getEevReturnDomain(record.fileUrl),
        fileName: record.fileRealName,
      });
    } catch (error) {
      createMessage.error('下载失败，请检查文件是否存在');
    }
  }

  function handleDeleteFile(record) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '是否确认删除',
      onOk: async () => {
        await batchDeleteFileApi({ ids: [record.id] });
        createMessage.success('操作成功');
        queryFileList();
      },
    });
  }

  function handleSelectNode(key, e) {
    peopleList.value = [];
    fileList.value = [];
    if (!key.length) {
      selectedKeys.value = [e.node.id];
    }
    selectedNode.value = e.node;
    queryFileList();
    queryPeopleList();
  }

  function handleAddPeople() {
    if (selectedKeys.value.length === 0) {
      createMessage.warning('请先选择部门！');
      return;
    }
    openPeopleEditModal(true, {
      isEdit: false,
      selectedNode: selectedNode.value,
      treeData: treeData.value,
    });
  }

  function handleSearch() {
    if (searchValue.value) {
      const searchTree = (tree) => {
        return tree.filter((item) => {
          const isMatch = item.name.includes(searchValue.value);
          if (item.children) {
            item.children = searchTree(item.children);
          }
          return isMatch || (item.children && item.children.length > 0);
        });
      };
      const filteredTree = searchTree(treeData.value);
      treeData.value = filteredTree.length
        ? filteredTree
        : [{ id: '', key: '-1', name: '所有部门', children: [] }];
    } else {
      getDeptTreeData();
    }
  }

  async function getDeptTreeData() {
    const data = await getCatalogueTreeApi();
    treeData.value = data;
    if (selectedNode.value) {
      selectedKeys.value = [selectedNode.value.id];
    } else {
      selectedKeys.value = [data[0].id];
      selectedNode.value = data[0];
    }
    queryFileList();
    queryPeopleList();
  }

  function getFileTypeIcon(fileName) {
    const fileType = fileName?.split('.').pop() || '';
    switch (fileType) {
      case 'pdf':
        return svgList.pdf;
      case 'doc':
        return svgList.doc;
      case 'txt':
        return svgList.txt;
      case 'csv':
        return svgList.csv;
      case 'excel':
        return svgList.excel;
    }
  }

  function getFileName(fileName) {
    const fileType = fileName?.split('.').pop() || '';
    return fileName?.replace(`.${fileType}`, '');
  }

  function getFileType(fileName) {
    const fileType = fileName?.split('.').pop() || '';
    return fileType ? '.' + fileType : '';
  }

  function handleAddFile() {
    if (selectedKeys.value.length === 0) {
      createMessage.warning('请先选择部门！');
      return;
    }
    if (fileList.value.length >= 3) {
      createMessage.warning('最多添加3个文件！');
      return;
    }
    openFileEditModal(true, {
      isEdit: false,
      deptId: selectedKeys.value[0] || undefined,
    });
  }

  function handleHoverNode(node) {
    hoverNode.value = node;
  }

  const handleChangeTreeShow = () => {
    isShowTree.value = !isShowTree.value;
    if (tableBoxRef.value) {
      tableBoxRef.value.style.width = isShowTree.value ? 'calc(100% - 248px)' : 'calc(100% - 16px)';
    }
  };

  onMounted(() => {
    getDeptTreeData();
  });
</script>

<style lang="less" scoped>
  .page-container {
    padding: 0 16px 16px;
    display: flex;
    // min-height: 816px;
    height: 100%;
    overflow: hidden;
    color: #333333;

    .tree-container {
      background: #fff;
      width: 248px;
      flex-shrink: 0;
      border-right: 1px solid #e9e9e9;
      transition: all 0.3s;
      overflow: hidden;

      .tit-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 12px;
        white-space: nowrap;
        height: 48px;
        line-height: 48px;
        border-bottom: 1px solid #e9e9e9;

        .tit {
          font-size: 16px;
          font-weight: 600;
        }

        .add-btn {
          color: var(--theme-color);
          cursor: pointer;
        }
      }
      .search-box {
        padding: 12px;
      }

      .tree-filter {
        padding: 12px 12px 0;

        :deep(.ant-select) {
          width: 100%;
        }
      }

      .tree-content {
        padding: 0 12px;
        overflow-y: auto;
        height: calc(100% - 60px);

        :deep(.ant-tree) {
          // background: #fcfcfc;

          .ant-tree-node-content-wrapper {
            overflow: hidden;

            &.ant-tree-node-selected {
              .ant-tree-title {
                color: var(--theme-color);
              }
            }
          }
        }
        .empty {
          width: 100%;
          height: 100%;
        }
      }
    }

    .hide-tree-container {
      width: 0;
      padding: 0;
      border-right: none;

      .tit-content,
      .tree-content {
        display: none;
      }
    }

    .toggle-tree {
      position: relative;
      width: 16px;
      height: 100%;
      // background: #fcfcfc;
      border-right: 1px solid #e9e9e9;
      cursor: pointer;

      .toggle-tree-btn {
        position: absolute;
        top: 50%;
        width: 16px;
        height: 32px;
        border-radius: 4px 0px 0px 4px;
        border: 1px solid #e9e9e9;
        transform: translateY(-50%);
        cursor: pointer;
        text-align: center;
        line-height: 30px;
        z-index: 999;
        background: white;
        transition: all 0.3s;

        .anticon {
          margin: 0;
        }
      }
    }

    .table-box {
      display: flex;
      flex-direction: column;
      background: #fff;
      position: relative;
      width: calc(100% - 248px);
      padding: 0 16px;

      .card-box {
        display: flex;
        flex-direction: column;
        flex: 1;
        .header-box {
          height: 48px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #d8d8d8;
          margin-bottom: 16px;
          flex-shrink: 0;
          .left-box {
            display: flex;
            align-items: center;
            .tit {
              font-weight: 600;
              font-size: 16px;
              margin-right: 12px;
            }
            .add-btn {
              cursor: pointer;
              color: var(--theme-color);
            }
          }
          .right-box {
            cursor: pointer;
            color: var(--theme-color);
          }
        }
        .content-item {
          padding: 0 16px;
          background: #f2f3f5;
          height: 110px;
          display: flex;
          flex-direction: column;
          margin-bottom: 12px;
          border-radius: 4px;
          .con-tit {
            height: 46px;
            line-height: 46px;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            justify-content: space-between;
            & > span {
              font-weight: 600;
            }
            .action-box {
              display: flex;
              align-items: center;
              gap: 16px;
              .ant-btn {
                padding: 0;
              }
            }
          }
          .con-field-box {
            display: flex;
            height: 100%;
            line-height: 64px;
            justify-content: space-between;
            & > div {
              width: 25%;
              display: flex;
              align-items: center;
              :deep(.file-tag-box) {
                display: flex;
                align-items: center;
                height: 28px;
                background: #fff;
                padding: 6px 8px;
                border-radius: 4px;
                color: #333;
                cursor: pointer;
                font-size: 12px;
                max-width: 170px;
                svg {
                  flex-shrink: 0;
                }
                .svg-path {
                  fill: var(--theme-color);
                }
                &:hover {
                  background: #0b62cb !important;
                  color: #fff;
                  .svg-path {
                    fill: #fff !important;
                  }
                }
                .tit {
                  max-width: 105px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }
        }
        .people-box {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          overflow-y: auto;
          padding-bottom: 16px;
          height: 100%;
          .people-item {
            width: calc(25% - 12px);
            display: flex;
            flex-direction: column;
            padding: 20px;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #d9d9d9;
            height: fit-content;
            .people-content-box {
              display: flex;
              margin-bottom: 20px;
              gap: 16px;
              img {
                width: 110px;
                height: 110px;
              }
              .field-box {
                line-height: 1;
                font-weight: 400;
                overflow: hidden;
                & > div {
                  display: flex;
                  margin-bottom: 16px;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  .label {
                    flex-shrink: 0;
                    color: #999999;
                  }
                  .con {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                }
              }
            }
            .action-box {
              display: flex;
              justify-content: flex-end;
              gap: 16px;
              .ant-btn {
                width: 72px;
              }
              .ant-btn-dangerous {
                color: #333 !important;
                border-color: #d8d8d8 !important;
                &:hover {
                  border-color: #ff522b !important;
                  color: #ff522b !important;
                }
              }
              .ant-btn-primary {
                color: #333 !important;
                border-color: #d8d8d8 !important;
                &:hover {
                  border: 1px solid var(--theme-color) !important;
                  color: var(--theme-color) !important;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
