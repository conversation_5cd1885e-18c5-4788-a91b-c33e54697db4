<template>
  <div style="width: 100%">
    <a-button type="primary" @click="emits('handleAddRow')" class="mb-2 mt-2">新增</a-button>
    <div style="max-height: 332px; overflow-y: auto">
      <FormItem class="table-container">
        <table width="100%">
          <!-- 表头 -->
          <thead class="thead">
            <tr>
              <th>指标值</th>
              <th>指标文字</th>
              <th>样式</th>
              <th width="40">操作</th>
            </tr>
          </thead>
          <!-- 表体 -->
          <tbody class="tbody-container">
            <tr v-for="(item, index) in styleInfo" :key="index" class="th-item">
              <!-- 指标值 -->
              <td>
                <FormItem name="style" :rules="styleItemRules(styleInfo[index]?.detailValue)">
                  <Input v-model:value="item.detailValue" placeholder="请输入" />
                </FormItem>
              </td>
              <!-- 指标文字 -->
              <td>
                <FormItem name="style" :rules="styleItemRules(styleInfo[index]?.detailLabel)">
                  <Input v-model:value="item.detailLabel" placeholder="请输入" />
                </FormItem>
              </td>
              <!-- 样式编辑 -->
              <td>
                <a-button @click="emits('handleEditStyle', index)">样式编辑</a-button>
              </td>
              <td>
                <div class="actions">
                  <a-button type="link" @click="emits('handleDeleteRow', index)">删除</a-button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </FormItem>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { Input, FormItem } from 'ant-design-vue';

  const emits = defineEmits(['handleAddRow', 'handleEditStyle', 'handleDeleteRow']);
  defineProps({
    styleInfo: {
      type: Array,
      default: () => [],
    },
  });

  const styleItemRules = (value) => {
    return {
      trigger: 'blur',
      validator: async (_, __) => {
        if (!value) {
          return Promise.reject('');
        }
        return Promise.resolve();
      },
    };
  };
</script>
<style lang="less" scoped>
  th,
  td {
    padding: 12px 8px;
    overflow-wrap: break-word;
  }

  .thead {
    background: #f1f4f7;
    border-radius: 8px 8px 0px 0px;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;

    th {
      color: #666;
      font-size: 14px;
      font-weight: 400;
      border-bottom: 1px solid #d8d8d8 !important;
    }
  }

  .tbody-container {
    max-height: 400px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }

    .th-item {
      font-size: 14px;
      font-weight: 400;
      color: #666;

      position: relative;

      td {
        border-bottom: 1px solid #f1f2f3 !important;
      }

      .th-drag {
        box-sizing: border-box;
        background: transparent;
      }
    }

    .actions {
      .ant-btn-link {
        padding: 0;
      }

      .ant-btn-link + .ant-btn-link {
        margin-left: 4px;
      }
    }
  }
</style>
