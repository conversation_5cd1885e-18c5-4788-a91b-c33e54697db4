<template>
  <div class="blc">
    <HlxbRankingPlusCard
      @ranking="ranking"
      v-bind="{ bottomList: fpgd.bottomList, clickFlags: clickFlags, empty, loading }"
      :title="'电量排行'"
    >
      <template #headerRight>
        <div class="header-right">
          <div>
            <Select
              v-model:value="typeType"
              style="width: 100px"
              placeholder="请选择"
              v-if="typeList.length"
            >
              <SelectOption v-for="item in typeList" :key="item.value" :value="item.value">
                {{ item.label }}
              </SelectOption>
            </Select>
          </div>
          <div>
            <Select v-model:value="dataType" style="width: 80px" v-if="dataList.length">
              <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
                {{ item.label }}
              </SelectOption>
            </Select>
          </div>
          <DatePicker
            v-if="dataList.find((item) => item.value === dataType)?.label === '月'"
            v-model:value="monthDate"
            picker="month"
            valueFormat="YYYY-MM"
            :allowClear="false"
            :disabledDate="disabledDateMonth"
          />
          <DatePicker
            v-if="dataList.find((item) => item.value === dataType)?.label === '日'"
            valueFormat="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model:value="dayDate"
            placeholder="请选择"
            :allowClear="false"
            :disabledDate="disabledDate"
          />
        </div>
      </template>
    </HlxbRankingPlusCard>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, ref, watch, reactive } from 'vue';
  // import { RankingPlusCard } from '/@zhcz/components/HLCardComponent';
  import { HlxbRankingPlusCard } from 'hlxb-ui';
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import {
    getSenceGroupTreeWithGroupCode,
    listSenceGroupByParent,
  } from '/@zhcz/api/cost-management';
  // import type { rankingListType } from '/@zhcz/components/HLCardComponent/src/dataType';
  import type { rankingListType } from 'hlxb-ui';

  const emit = defineEmits(['powerRingVal']);
  const empty = ref(false);
  const loading = ref(false);
  const setIndex = ref<null | number>(null);
  const colorList = [
    'rgba(46, 140, 255, 1)',
    'rgba(46, 196, 255, 1)',
    'rgba(129, 67, 255, 1)',
    'rgba(34, 205, 128, 1)',
    'rgba(118, 195, 31, 1)',
    'rgba(255, 140, 46, 1)',
  ];
  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);
  type OptionItem = { label: String; value: String };
  const typeList = ref<OptionItem[]>([]);
  const dataType = ref<any>(null);
  const dataList = ref<OptionItem[]>([
    // {
    //   label: '日',
    //   value: 'dhtj2_r',
    // },
    // {
    //   label: '月',
    //   value: 'dhtj2_y',
    // },
  ]);
  // 设备
  const typeType = ref('');
  const monthDate = ref(dayjs().subtract(1, 'month').format('YYYY-MM'));
  const dayDate = ref(dayjs().subtract(1, 'day').format('YYYY-MM-DD'));
  function disabledDate(current) {
    return current && current > dayjs().subtract(0, 'day').endOf('day');
  }

  function disabledDateMonth(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('day');
  }
  async function getIndicatorList() {
    try {
      const res = await getSenceGroupTreeWithGroupCode({
        groupCode: dataType.value,
        factoryId: 1,
      });
      if (res.length) {
        typeType.value = res.find((item) => item.groupCode)?.groupCode || '';
        typeList.value = res.map((item) => ({
          value: item.groupCode,
          label: item.groupName,
        }));
        getData();
      } else {
        empty.value = true;
      }
    } catch (err) {
      empty.value = true;
      console.log('err', err);
    }
  }
  async function getTimeList() {
    try {
      const res = await listSenceGroupByParent({
        groupCode: 'dhzl2_dlph',
        factoryId: 1,
        platformld: 1,
      });
      console.log('res.data', res);
      if (Object.keys(res).length) {
        dataType.value = Object.keys(res)[0];
        dataList.value = Object.keys(res).map((item) => ({
          value: item,
          label: res[item],
        }));
        getIndicatorList();
        empty.value = false;
      } else {
        empty.value = true;
      }
    } catch (err) {
      empty.value = true;
    }
  }
  function powerRing(val) {
    Object.keys(fpgd).forEach((key) => {
      fpgd[key] = val[key];
    });
  }
  const clickFlags = ref(false);
  const getData = async () => {
    try {
      const startDateTime =
        dataType.value === 'dhzl2_dlph_r'
          ? dayjs(dayDate.value).format('YYYY-MM-DD 00:00:00')
          : dayjs(monthDate.value).startOf('month').format('YYYY-MM-DD 00:00:00');
      const endDateTime =
        dataType.value === 'dhzl2_dlph_r'
          ? dayjs(dayDate.value).format('YYYY-MM-DD 23:59:59')
          : dayjs(monthDate.value).endOf('month').format('YYYY-MM-DD 23:59:59');
      const params = {
        startDateTime: startDateTime,
        endDateTime: endDateTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        resourceInterfaceId: '4',
        groupCode: typeType.value,
        jsConvert: true,
        paramsData: JSON.stringify(params),
      };
      loading.value = true;
      const { data } = await callResourceFunction(paramData);
      loading.value = false;
      console.log('电量ranking', data);

      if (data && data.length) {
        empty.value = false;
        let newData = data.map((item, index) => {
          return {
            indexName: item.indexName,
            indexCode: item.indexCode,
            unitName: item.unitName,
            value: item.value,
            // .map((i) => Number(i.value))
            // .reduce((prev, cur) => {
            //   return prev + cur;
            // }, 0),
            color: colorList[index % 8],
            ratio: 0,
            tag: item.tag,
          };
        });

        indexList.value = newData;
        if (
          dataList.value.find((item) => item.value === dataType.value)?.label === '日' &&
          'dhzl2_dlph_r_sbyd' === typeType.value
        ) {
          clickFlags.value = true;
        } else {
          clickFlags.value = false;
        }
        // console.log('clickFlags.value', clickFlags);
        powerRing({
          bottomList: indexList.value,
          value: fpgd.value,
          date: dayDate.value,
          clickFlags,
        });
      } else {
        empty.value = true;
        indexList.value = [];
        powerRing({
          bottomList: [],
          value: '',
          date: '',
          clickFlags,
        });
      }
    } catch (err) {
      empty.value = true;
      loading.value = false;
    }
    // console.log('电量排行', indexList.value);
  };
  const fpgd = reactive<{
    value: string;
    date: string;
    bottomList: rankingListType[];
    clickFlags: Boolean;
  }>({
    value: '',
    date: '',
    bottomList: [],
    clickFlags: false,
  });
  function ranking(val) {
    if (val) {
      fpgd.value = val;
      emit('powerRingVal', {
        value: val,
        date: dayDate.value,
      });
    }
  }

  watch(
    () => dayDate.value,
    async () => {
      if (dataType.value === 'dhzl2_dlph_r') {
        // pause();
        await getIndicatorList();
        // resume();
      }
    },
  );

  watch(
    () => monthDate.value,
    async (newVal) => {
      if (dataType.value === 'dhzl2_dlph_y') {
        console.log('newVal=>月', newVal);
        // pause();
        await getIndicatorList();
        // resume();
      }
    },
  );

  watch(
    () => dataType.value,
    async (newVal) => {
      console.log('newVal=>日期类型', newVal);
      monthDate.value = dayjs().subtract(1, 'month').format('YYYY-MM');
      dayDate.value = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
      // pause();
      await getIndicatorList();
      // resume();
    },
  );
  watch(
    () => typeType.value,
    async () => {
      setIndex.value = null;
      await getData();
    },
  );

  onMounted(async () => {
    getTimeList();
  });
</script>
<style lang="less" scoped>
  .blc {
    background-color: #fcfcfc;
    height: 100%;
    overflow: hidden;
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
    border-right: 1px solid #d8d8d8;

    .header-right {
      display: flex;
      gap: 0 16px;
    }
  }
</style>
