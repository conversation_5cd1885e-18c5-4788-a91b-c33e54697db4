<template>
  <div class="order w-full h-full overflow-hidden">
    <BasicTable @register="registerTable" class="table-transparent" v-show="!flowDetailVisible">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '详情',
                onClick: handleDetail.bind(null, record),
              },
            ]"
            stopButtonPropagation
          />
        </template>
        <template v-if="column.key === 'equipmentName'">
          <Tooltip>
            <template #title>{{ record.equipmentName }}</template>
            <span style="cursor: pointer; color: var(--theme-color)">
              {{ record.equipmentName }}
            </span>
          </Tooltip>
        </template>
      </template>
    </BasicTable>

    <!-- 流程申请，审核，详情页面 -->
    <FlowDetail
      v-model:visible="flowDetailVisible"
      :node-disabled="true"
      @success="handleSuccess"
      ref="flowDetail"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { columns, searchFormSchema } from './data';
  import { getEqWordOrderPage } from '/@zhcz/api/device-management';
  import FlowDetail from '/@/components/FlowTable/src/components/FlowDetail.vue';

  const emits = defineEmits(['setFixBottomShow']);

  defineOptions({
    name: 'DeviceManagementDeviceAssetsEquipmentLedgerNewModulesOrder',
  });

  const props = defineProps({
    equipmentId: {
      type: Number,
      default: () => null,
    },
  });

  const flowDetail = ref();
  const flowDetailVisible = ref(false);
  const router = useRouter();

  const [registerTable, { reload }] = useTable({
    columns,
    api: getEqWordOrderPage,
    beforeFetch: (params) => {
      console.log('params', params);
      return {
        ...params,
        eqId: props.equipmentId,
      };
    },
    fetchSetting: {
      pageField: 'pageIndex',
      sizeField: 'pageSize',
    },
    showIndexColumn: true,
    showTableSetting: true,
    useSearchForm: true,
    formConfig: {
      schemas: searchFormSchema,
      labelWidth: 100,
    },
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  // 详情
  const handleDetail = (record) => {
    flowDetailVisible.value = true;

    const _params = {
      record: {
        EntryID: record.entryId,
        moduleId: router.currentRoute.value.meta.moduleId,
        WorkItemID: record.itemId,
        modelShowType: 2,
      },
      moduleId: router.currentRoute.value.meta.moduleId,
    };

    if (flowDetail.value) {
      flowDetail.value?.openPage(true, _params);
    }
    emits('setFixBottomShow', false);
  };

  const handleSuccess = () => {
    flowDetailVisible.value = false;
    emits('setFixBottomShow', true);
    reload();
  };
</script>

<style lang="less" scoped>
  .order {
    :deep(.vben-basic-table) {
      padding: 0;

      .ant-table-title {
        padding-bottom: 0 !important;
      }

      .ant-form,
      .ant-table-wrapper {
        background: #fff !important;
      }
    }

    :deep(.detail-container) {
      .FlowDetail-box {
        height: 100% !important;
        padding-bottom: 0 !important;

        & > div {
          padding-left: 0;
          padding-right: 0;
        }

        .shadow-footer {
          left: 0;
          right: 0 !important;
        }
      }
    }
  }
</style>
