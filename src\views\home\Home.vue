<template>
  <div :class="[prefixCls, 'h-full', 'w-full']">
    <div class="w-full h-[fit-content] flex gap-12px overflow-x-hidden">
      <!-- 左侧可拖拽区域，绑定leftList列表，支持上下拖拽调整顺序，显示操作按钮 -->
      <Draggable
        group="left-draggable"
        class="drag-box-left flex flex-col"
        ghostClass="moving"
        :animation="180"
        :list="leftList"
        item-key="id"
        handle=".drag"
      >
        <template #item="{ element: item }">
          <div class="drag-box-item w-full">
            <component :is="componentMap[item.code]" />
            <div class="drag" v-if="showConfig">
              <div class="option">
                <div class="drag-icon mb-3">
                  <img :src="dragIcon" class="w-full h-full" />
                </div>
                <div>上下拖拽更换顺序</div>
              </div>
              <div class="action">
                <div
                  class="action-item"
                  :class="{
                    'action-item-active': item.showType === 1,
                  }"
                  @click.stop="handleShow(item)"
                >
                  <span>显示</span>
                </div>
                <div
                  class="action-item action-item-two"
                  :class="{
                    'action-item-hide': item.showType === 0,
                  }"
                  @click.stop="handleShow(item)"
                >
                  <span>隐藏</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </Draggable>
      <!-- 右侧可拖拽区域，绑定rightList列表，支持上下拖拽调整顺序，显示操作按钮 -->
      <Draggable
        group="right-draggable"
        class="drag-box-right flex flex-col"
        ghostClass="moving"
        :animation="180"
        :list="rightList"
        item-key="id"
        handle=".drag"
      >
        <template #item="{ element: item }">
          <div class="drag-box-item w-full">
            <component :is="componentMap[item.code]" />
            <div class="drag" v-if="showConfig">
              <div class="option">
                <div class="drag-icon mb-3">
                  <img :src="dragIcon" class="w-full h-full" />
                </div>
                <div>上下拖拽更换顺序</div>
              </div>
              <div class="action">
                <div
                  class="action-item"
                  :class="{
                    'action-item-active': item.showType === 1,
                  }"
                  @click.stop="handleShow(item)"
                >
                  <span>显示</span>
                </div>
                <div
                  class="action-item"
                  :class="{
                    'action-item-hide': item.showType === 0,
                  }"
                  @click.stop="handleShow(item)"
                >
                  <span>隐藏</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </Draggable>
    </div>
    <!-- 显示设置组件，用于配置显示选项，触发set和success事件 -->
    <ShowSet
      @set="handleConfig"
      @success="init"
      :data="configObj"
      :leftList="leftList"
      :rightList="rightList"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  import { useDesign } from '/@/hooks/web/useDesign';
  // import { useMessage } from '/@/hooks/web/useMessage';
  import { componentMap } from './getComponent';
  import { SPLIT_INDEX, DEF_CONFIG } from './enum';
  import ShowSet from './ShowSet.vue';
  import Draggable from 'vuedraggable';
  import dragIcon from './assets/images/drag.png';
  import { HomeListGetResultModel, HomeListItem } from '/@/api/admin/model/home';
  // import { Button } from 'ant-design-vue';

  import { getHomeDataApi } from '/@/api/admin/home';

  defineOptions({
    name: 'HomeHome',
  });

  const { prefixCls } = useDesign('home');
  const showConfig = ref(false);
  const configObj = ref<HomeListGetResultModel | null>(null);
  const leftList = ref<HomeListItem[]>([]);
  const rightList = ref<HomeListItem[]>([]);

  /**
   * 初始化函数，获取首页数据并分割左右列表。
   * 请求获取首页配置数据，将详情列表分割为左右两部分，
   * 左侧包含索引小于SPLIT_INDEX且显示类型为1的项，
   * 右侧包含索引大于等于SPLIT_INDEX且显示类型为1的项。
   *
   * @returns {Promise<void>} 无返回值。
   */
  async function init() {
    configObj.value = (await getHomeDataApi()) || DEF_CONFIG;
    leftList.value = configObj.value.detailList.filter(
      (item, idx) => idx < SPLIT_INDEX && item.showType === 1,
    );
    rightList.value = configObj.value.detailList.filter(
      (item, idx) => idx >= SPLIT_INDEX && item.showType === 1,
    );
  }

  init();

  /**
   * 处理配置开关及列表更新。
   * 当flag为true时，重新分割左右列表，包含所有项（无论显示类型）。
   *
   * @param {boolean} flag - 控制配置显示状态。
   * @returns {void}
   */
  function handleConfig(flag) {
    showConfig.value = flag;
    if (flag) {
      leftList.value = configObj.value!.detailList.filter((_, idx) => idx < SPLIT_INDEX);
      rightList.value = configObj.value!.detailList.filter((_, idx) => idx >= SPLIT_INDEX);
    }
  }

  /**
   * 切换指定项的显示状态。
   * 将item的showType属性在0和1之间切换。
   *
   * @param {HomeListItem} item - 需要切换显示状态的列表项。
   * @returns {void}
   */
  function handleShow(item) {
    item.showType = item.showType === 1 ? 0 : 1;
  }
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-home';

  .@{prefix-cls} {
    display: flex;
    margin-bottom: 16px;
    padding: 0 16px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .drag-box-left {
      flex: 6;
      gap: 12px;
      overflow-x: hidden;
    }

    .drag-box-right {
      position: relative;
      flex: 4;
      gap: 12px;
      overflow-x: hidden;
    }

    .drag-box-item {
      position: relative;

      .option {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s;
      }

      &:hover {
        .option {
          opacity: 1;
        }
      }
    }

    .drag {
      z-index: 8;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.56);
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 14px;
      flex-direction: column;

      &-icon {
        width: 34px;
        height: 28px;
      }

      .action {
        display: flex;
        position: absolute;
        right: 12px;
        top: 12px;

        background: @component-background;
        padding: 4px;
        border-radius: 4px;

        &-item {
          padding: 7px 12px;
          border-radius: 4px;
          color: @text-color-bold;
          transition: all 0.3s;
          font-weight: 400;

          &-active,
          &:hover {
            font-size: 14px;
            color: #ffffff;
            background: @theme-color;
          }

          &-hide {
            color: #ffffff;
            background: #666;
          }

          &-two {
            &:hover {
              background: #666;
            }
          }
        }
      }
    }
  }
</style>
