<template>
  <div class="item-content">
    <div class="infos">
      <!-- 左侧概览图片 -->
      <div class="img">
        <img :src="value.picUrl" v-if="value.picUrl" />
        <div class="images-empty" v-else>
          <Icon icon="icon-park-outline:pic" :size="30" color="#999" />
        </div>
      </div>
      <!-- 右侧概览信息 -->
      <div class="info">
        <template v-for="schema in schemas" :key="schema.field">
          <slot :name="schema.slot">
            <div :class="['info-item', `info-item-${schema.field}`]">
              <div class="label">{{ schema?.label }}：</div>
              <div class="value" :title="value[schema?.field]">{{ value[schema?.field] }}</div>
              <span v-if="schema?.addonAfter" class="addon-after">{{ schema?.addonAfter }}</span>
            </div>
          </slot>
        </template>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { Icon } from '/@/components/Icon';
  import { PropType } from 'vue';
  import { InfoCardSchema } from './hooks';

  defineProps({
    //值对象
    value: {
      type: Object,
      default: () => ({}),
    },
    // 信息卡片的配置规则
    schemas: {
      type: Array as PropType<InfoCardSchema[]>,
      default: () => [],
    },
  });
</script>
<style lang="less" scoped>
  .item-content {
    padding: 20px;
    overflow: hidden;

    .infos {
      display: flex;

      .img {
        width: 140px;
        min-width: 140px;
        height: 140px;
        overflow: hidden;
        background: #eeeff1;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          // max-width: 95%;
          // max-height: 95%;
          object-fit: cover;
        }

        .images-empty {
          height: 100%;
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #f2f3f5;
        }
      }

      .info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 4px;
        padding-left: 16px;
        width: calc(100% - 110px);

        .info-item {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
          line-height: 1;
          // margin-bottom: 16px;

          .label {
            color: #999999;
            white-space: nowrap;
          }

          .value {
            color: #333333;
            // flex: 1;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
        }
      }
    }
  }
</style>
