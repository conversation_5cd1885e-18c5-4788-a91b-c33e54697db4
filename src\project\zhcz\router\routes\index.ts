import type { AppRouteRecordRaw, AppRouteModule } from '/@/router/types';

import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';

import { mainOutRoutes } from './mainOut';

import {
  parentBasicRoutes, //  未经许可的基本路由
  ParentRootRoute, // 根路由
  parentRouteModuleList, // 公共菜单路由 系统管理
  treeList, // 将路由转为菜单所需要的结构
} from '/@/router/routes';
import { t } from '/@/hooks/web/useI18n';

// project  aoa import.meta.glob() 直接引入所有的模块 Vite 独有的功能
const modules: Record<string, any> = import.meta.glob('./modules/**/*.ts', { eager: true });

const routeModuleList: AppRouteModule[] = [];

// 加入到路由集合中
Object.keys(modules).forEach((key) => {
  const mod = modules[key].default || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  routeModuleList.push(...modList);
});

export const asyncRoutes = [PAGE_NOT_FOUND_ROUTE, ...routeModuleList, ...parentRouteModuleList];

export const RootRoute = ParentRootRoute;

export const LoginRoute: AppRouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: () => import('/@/views/sys/login/Login.vue'),
  meta: {
    title: t('routes.basic.login'),
  },
};

export const JDRoute = [
  {
    path: '/jd-table',
    name: 'JdTable',
    component: () => import('/@/project/jd/audit-table/index.vue'),
  },
  {
    path: '/jd-audit',
    name: 'JdAudit',
    component: () => import('/@/project/jd/audit-table/audit.vue'),
  },
];

export const routeMenuList = treeList([...routeModuleList, ...parentRouteModuleList]);

// Basic routing without permission
// 未经许可的基本路由
export const basicRoutes = [...parentBasicRoutes, LoginRoute, ...mainOutRoutes, ...JDRoute];
