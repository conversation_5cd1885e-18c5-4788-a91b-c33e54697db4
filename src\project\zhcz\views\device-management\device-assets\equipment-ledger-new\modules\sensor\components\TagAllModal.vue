<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    title="绑定标签"
    width="1200px"
    :minHeight="448"
    wrapClassName="tags-configuration-modal"
    :loading="okLoading"
  >
    <div class="content-container">
      <div>
        <div class="filter-container">
          <Input v-model:value="inputVal" placeholder="请输入" allowClear @change="reload">
            <template #prefix>
              <Icon icon="icon-park-outline:search" color="#d9d9d9" />
            </template>
          </Input>
          <Select
            v-model:value="selectedType"
            placeholder="请选择"
            allowClear
            :options="dataTypesOptions"
            @change="reload"
          />
          <Select
            v-model:value="selectedSource"
            placeholder="请选择"
            allowClear
            :options="dataSourcesOptions"
            @change="reload"
          />
          <Select
            v-model:value="selectedTimeType"
            placeholder="请选择"
            allowClear
            :options="timeTypesOptions"
            @change="reload"
          />
        </div>
        <div class="indicator-container">
          <BasicTable @register="registerTable" />
        </div>
      </div>
      <div>
        <div class="top-container">
          <div class="tips">
            <span>已选: </span>
            <span style="color: var(--theme-color)">{{ checkedList.length }}</span>
            <span>个标签</span>
          </div>
          <span class="clear-btn" @click="clearList">清空</span>
        </div>
        <div class="checked-header-container" v-if="checkedList.length">
          <div>序号</div>
          <div>资源标识</div>
          <div>资源名称</div>
          <div>操作</div>
        </div>
        <VScroll :itemHeight="36" :items="checkedList" :height="334" v-if="checkedList.length">
          <template #default="{ item, index }">
            <div class="checked-container">
              <div class="checked-item">
                <div class="th-drag"> {{ index + 1 }}</div>
                <div>
                  <Tooltip>
                    <template #title> {{ item.sourceUniqueKey }}</template>
                    {{ item.sourceUniqueKey }}
                  </Tooltip>
                </div>
                <div class="flex items-center">
                  <div class="edit-item">
                    <Tooltip>
                      <template #title> {{ item.displayName || '-' }}</template>
                      {{ item.displayName || '-' }}
                    </Tooltip>
                  </div>
                </div>
                <div class="delete-btn" @click="handleDeleteItem(item)">删除</div>
              </div>
            </div>
          </template>
        </VScroll>
        <div class="checked-empty" v-else> 请勾选左侧标签进行添加 </div>
      </div>
    </div>
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">确认</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup name="BindModal">
  import { ref, nextTick } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { Input, Tooltip, Select } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { Icon } from '/@/components/Icon';
  import { VScroll } from '/@/components/VirtualScroll/index';
  import { getIndicatorPageTable } from '/@zhcz/api/config-center/monitoring-points';
  import type { ResourceInfo } from '../../../typing';
  import { bindIndicatorColumns } from '../data';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import { DICT } from '/@/enums/dict';

  const props = defineProps({
    setInitCheckedList: {
      type: Boolean,
      default: false,
    },
  });
  const okLoading = ref(false);
  const checkedList = ref<ResourceInfo[]>([]);
  const selectNode = ref<any[]>([]);
  // 初始已选资源
  const initCheckedList = ref<ResourceInfo[]>([]);
  const dataTypesOptions: any = ref([]);
  const dataSourcesOptions: any = ref([]);
  const timeTypesOptions: any = ref([]);
  const inputVal = ref('');
  const selectedType = ref<string | number>(-1);
  const selectedSource = ref<string | null>(null);
  const selectedTimeType = ref<string | null>(null);

  const [registerTable, { reload, setSelectedRowKeys, clearSelectedRowKeys }] = useTable({
    columns: bindIndicatorColumns,
    api: getIndicatorPageTable,
    beforeFetch: (pageParams) => {
      return {
        ...pageParams,
        dataType: selectedType.value === -1 ? null : selectedType.value,
        sourceType: selectedSource.value,
        timeType: selectedTimeType.value,
        indicatorOrName: inputVal.value,
      };
    },
    fetchSetting: {
      pageField: 'page',
      sizeField: 'size',
      listField: 'records',
      totalField: 'total',
    },
    rowKey: 'indicatorCode',
    rowSelection: {
      type: 'checkbox',
      preserveSelectedRowKeys: true,
      onChange: (selectedRowKeys, selectedRows) => {
        const result: any = [];
        selectedRowKeys.forEach((key) => {
          const oldItem = initCheckedList.value.find((i) => i.sourceUniqueKey === key);
          if (oldItem) {
            result.push(oldItem);
          } else {
            const newItem = selectedRows.find((i) => i.indicatorCode === key);
            if (newItem) {
              newItem.sourceUniqueKey = newItem.indicatorCode;
              newItem.displayName = newItem.indicatorName;
              result.push(newItem);
            }
          }
        });
        checkedList.value = result;
      },
    },
    clickToRowSelect: true,
    showIndexColumn: false,
    useSearchForm: false,
    maxHeight: 286,
    inset: true,
  });

  const emit = defineEmits(['success', 'register']);

  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    selectNode.value = data.selectNode;
    initForm();
  });

  const handleDeleteItem = (item: any) => {
    const result = checkedList.value.filter((i) => i.sourceUniqueKey !== item.sourceUniqueKey);
    setSelectedRowKeys(result.map((item: any) => item.sourceUniqueKey));
    checkedList.value = result;
  };

  const clearList = () => {
    clearSelectedRowKeys();
    checkedList.value = [];
  };

  // 初始化表单数据
  const initForm = async () => {
    okLoading.value = true;
    try {
      // 全部资源
      getDictTypeListApi({ type: DICT.MONITORING_POINT_DATA_TYPE }).then((data) => {
        dataTypesOptions.value = data;
        dataTypesOptions.value.unshift({ label: '全部', value: -1 });
      });
      getDictTypeListApi({ type: DICT.MONITORING_POINT_DATA_SOURCE }).then((data) => {
        dataSourcesOptions.value = data.filter((item) => item.value !== '-1');
      });
      getDictTypeListApi({ type: DICT.MONITORING_POINT_DATA_TIME_TYPE }).then((data) => {
        timeTypesOptions.value = data;
      });

      // 已选资源
      checkedList.value = selectNode.value;
      if (props.setInitCheckedList) {
        initCheckedList.value = selectNode.value;
      }
      nextTick(() => {
        setSelectedRowKeys(selectNode.value.map((item: any) => item.sourceUniqueKey));
      });
      okLoading.value = false;
    } catch (error) {
      okLoading.value = false;
    }
  };

  async function handleSubmit() {
    emit('success', checkedList.value);
    handleCancel();
  }

  function handleCancel() {
    closeModal();
    inputVal.value = '';
    checkedList.value = [];
    initCheckedList.value = [];
  }
</script>

<style lang="less" scoped>
  .content-container {
    display: flex;
    border: 1px solid #d9d9d9;
    height: 448px;
    color: #333;
    border-radius: 4px;

    ::-webkit-scrollbar {
      height: 0 !important;
      width: 0 !important;
      background: transparent;
    }

    .filter-container {
      display: flex;
      gap: 0 16px;

      :deep(.ant-input-affix-wrapper) {
        width: 25%;
      }

      :deep(.ant-select) {
        width: 25%;
      }
    }

    & > div {
      width: 50%;
      padding: 16px;

      &:first-child {
        width: 650px;
      }

      &:last-child {
        border-left: 1px solid #e9e9e9;
        position: relative;
        width: 500px;
      }
    }

    .select-container {
      font-family: 'PingFang SC, PingFang SC';
      height: 36px;
      line-height: 36px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12px;
      font-weight: 400;
      background-color: #f1f4f7;
      border-radius: 4px;
      padding: 0 16px;

      & > div {
        flex-shrink: 0;

        &:nth-child(1) {
          width: 180px;
        }

        &:nth-child(2) {
          padding-left: 16px;
          flex: 1;
        }

        &:nth-child(3) {
          width: 53px;
        }
      }

      .checkAll-box {
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          margin-right: 8px;
        }

        img {
          cursor: pointer;
        }
      }
    }

    .empty {
      height: calc(100% - 32px);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .checked-empty {
      height: calc(100% - 32px);
      display: flex;
      justify-content: center;
      align-items: center;
      color: #999;
    }

    .indicator-container {
      :deep(.vben-basic-table) {
        .ant-table-wrapper {
          padding: 12px 0 0;
          margin: 0;

          .ant-table-thead {
            height: 36px;

            & > tr .ant-table-selection-column::before {
              display: none;
            }
          }

          .ant-table-row {
            height: 36px;
          }

          .ant-table-cell {
            padding: 0 !important;
          }
        }
      }
    }

    .checked-header-container {
      font-family: 'PingFang SC, PingFang SC';
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 400;
      height: 36px;
      line-height: 36px;
      background-color: #f1f4f7;
      border-radius: 4px;
      padding: 0 16px;
      margin-top: 12px;

      & > div {
        &:nth-child(1) {
          width: 40px;
        }

        &:nth-child(2) {
          width: 150px;
        }

        &:nth-child(3) {
          width: 230px;
        }

        &:nth-child(4) {
          width: 30px;
        }
      }
    }

    .checked-container {
      width: 100%;

      .checked-item {
        padding: 0 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 36px;
        line-height: 36px;
        width: 100%;
        border-radius: 4px 4px 4px 4px;

        &:hover {
          background: #f0f0f0;
        }

        & > * {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        & > div {
          &:nth-child(1) {
            width: 40px;
          }

          &:nth-child(2) {
            width: 150px;
          }

          &:nth-child(3) {
            width: 230px;
          }

          &:nth-child(4) {
            width: 30px;
          }
        }

        :deep(.ant-input-number) {
          min-width: 100%;
        }

        .edit-item {
          cursor: text;
          position: relative;
          width: 100%;
          display: flex;
          align-items: center;

          .name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            display: inline-block;
            line-height: 1;
            flex: 1;
          }

          :deep(span) {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            display: inline-block;
            line-height: 1;
            flex: 1;
          }

          .icon {
            display: none;
          }

          &:hover {
            // color: @theme-color;

            .icon {
              display: block;
            }
          }
        }

        .delete-btn {
          cursor: pointer;
          color: @theme-color;
        }
      }
    }

    .top-container {
      height: 32px;
      left: 0;
      width: 100%;
      line-height: 32px;
      display: flex;
      justify-content: space-between;

      .clear-btn {
        color: @theme-color;
        cursor: pointer;
      }
    }

    .preview-img {
      height: 30px;

      img {
        height: 100%;
      }
    }
  }
</style>
