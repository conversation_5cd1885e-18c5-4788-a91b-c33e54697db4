export const mockEnergyAllStatisticsData = [
  {
    value: null,
    preVal: null,
    indexName: '昨日用药量',
    indexPreName: '前日用药量',
    ratioName: '对比昨日',
    ratioVal: null,
    indexCode: '',
    unitName: 'kg',
  },
  {
    value: null,
    preVal: null,
    indexName: '近7日用药量',
    indexPreName: '前7日用药量',
    ratioName: '对比前7日',
    ratioVal: null,
    indexCode: '',
    unitName: 'kg',
  },
  {
    value: null,
    preVal: null,
    indexName: '上月用药量',
    indexPreName: '上上月用药量',
    ratioName: '对比上上月',
    ratioVal: null,
    indexCode: '',
    unitName: 'kg',
  },
  {
    value: null,
    preVal: null,
    indexName: '今年用药量',
    indexPreName: '去年用药量',
    ratioName: '对比去年',
    ratioVal: null,
    indexCode: '',
    unitName: 'kg',
  },
];

export const mockRingRatioData = [
  {
    indexCode: 'D_JY_M_M_SH_S_L_AVG',
    indexName: '石灰',
    unitName: 'kg',
    value: 0,
    valueSeven: 0,
  },
  {
    indexCode: 'D_JY_M_M_QYHN_S_L_AVG',
    indexName: '氢氧化钠',
    unitName: 'kg',
    value: 0,
    valueSeven: 0,
  },
  {
    indexCode: 'D_JY_M_M_EYHL_S_L_AVG',
    indexName: '二氧化氯',
    unitName: 'kg',
    value: 0,
    valueSeven: 0,
  },
  {
    indexCode: 'D_JY_M_M_GMSJ_S_L_AVG',
    indexName: '高锰酸钾',
    unitName: 'kg',
    value: 0,
    valueSeven: 0,
  },
  {
    indexCode: 'D_JY_M_M_YS_S_L_AVG',
    indexName: '盐酸',
    unitName: 'kg',
    value: 0,
    valueSeven: 0,
  },
];
