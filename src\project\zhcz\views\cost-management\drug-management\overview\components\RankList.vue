<template>
  <Card :bordered="false" dis-hover>
    <!-- :data-resource-code="dataType === 1 ? dayGroupCode : monthGroupCode" -->
    <template #title>
      <div data-index-name="RankingDrugConsumption" class="flex-center"
        >用药排行
        <div class="button_ai" v-if="indexList.length" @click="handleCreate()"
          ><img :src="aiImg" alt="" srcset="" /><span class="text">AI分析</span></div
        >
      </div>
    </template>
    <template #extra>
      <div class="flex extra-wrap">
        <div>
          <Select
            v-model:value="dataType"
            @change="getData"
            style="width: 80px"
            v-if="dataList.length"
          >
            <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
              {{ item.label }}
            </SelectOption>
          </Select>
        </div>

        <DatePicker
          v-if="dataList.find((item) => item.value === dataType)?.label === '月'"
          @change="getData"
          v-model:value="date"
          picker="month"
          valueFormat="YYYY-MM"
          :disabledDate="disabledMonthDate"
        />
        <DatePicker
          v-if="dataList.find((item) => item.value === dataType)?.label === '日'"
          @change="getData"
          valueFormat="YYYY-MM-DD"
          format="YYYY-MM-DD"
          v-model:value="date1"
          placeholder="请选择"
          :allowClear="false"
          :disabledDate="disabledDate"
        />
      </div>
    </template>
    <div class="item-list" v-loading="loading" v-if="indexList.length">
      <div :key="index" class="item" v-for="(item, index) in indexList">
        <div class="item-index">
          <div class="num-index">{{ index + 1 }}</div>
          <div class="label">{{ item.indexName }}</div>
        </div>
        <div class="item-content">
          <div class="value">{{ item.value }}</div>
          <div class="unit">{{ item.unitName }}</div>
        </div>
      </div>
    </div>
    <div v-else class="empty-box">
      <HEmpty />
    </div>
    <HAiDrawer @register="registerDrawer" :aiQuestion="aiDataCopy[0].aiQuestion" />
  </Card>
</template>

<script setup lang="ts">
  import { DatePicker, Card, Select, SelectOption } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { ref } from 'vue';
  // import { roundAndConvert } from '/@zhcz/utils/number';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import aiImg from '../AI.png';
  // import Icon from '/@/components/Icon/index';
  // import { aiData } from '../data';
  import { HAiDrawer } from '/@zhcz/components/HAiDrawer/index';
  import { useDrawer } from '/@/components/Drawer';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';

  // import { useMessage } from '/@/hooks/web/useMessage';
  const [registerDrawer, { openDrawer }] = useDrawer();
  function handleCreate() {
    openDrawer(true, {
      record: {},
      isUpdate: false,
    });
  }
  const aiDataCopy = ref([
    {
      aiQuestion:
        '自来水厂2025年2月份，用药量从高到低排行为：碱铝_kg，石灰_kg，次氯酸钠_kg，高锰酸钾_kg，PAM_kg。分析当前情况。',
      deepAnalysis:
        '好的，用户问用药量的原因，我需要详细分析。首先，我得回忆一下浊度的定义，素全面分析.\n可能需要进一步的信息来确定具体原因，但先列出这些可能性。\n帮助用户排查。',
      explain:
        '用药量通常由多种因素引起，具体原因需结合环境、人为活动和水处理流程综合分析。以下是常见原因分类：',
      resultTitle: '总览分析',
      resultDes: '',
      summaryTitle: '总结',
      summary:
        '若用药量持续超标，可能影响水质安全（如隐藏病原微生物），建议及时联系水务部门或环保机构介入调查。',
    },
  ]);
  // const props = defineProps({
  //   dayResourceInterfaceId: {
  //     type: String,
  //     default: '',
  //   },
  //   dayGroupCode: {
  //     type: String,
  //     default: '',
  //   },
  //   monthResourceInterfaceId: {
  //     type: String,
  //     default: '',
  //   },
  //   monthGroupCode: {
  //     type: String,
  //     default: '',
  //   },
  // });
  type OptionItem = {
    label: string;
    value: string;
  };
  const dataList = ref<OptionItem[]>([
    // {
    //   label: '日',
    //   value: 1,
    // },
    // {
    //   label: '月',
    //   value: 2,
    // },
  ]);
  const dataType = ref<any>(null);
  const date = ref(dayjs().subtract(1, 'month').format('YYYY-MM'));
  const date1 = ref(dayjs().subtract(1, 'day').format('YYYY-MM-DD'));

  const loading = ref(false);
  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);
  async function getTimeList() {
    const res = await listSenceGroupByParent({
      // dhzl2_dlph
      groupCode: 'yhzl2_yypx',
      factoryId: 1,
      platformld: 1,
    });
    // console.log('res.data', res);
    if (Object.keys(res).length) {
      dataType.value = Object.keys(res)[0];
      dataList.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      getData();
    }
  }
  function disabledDate(current) {
    return current && current > dayjs().subtract(0, 'day').endOf('day');
  }
  function disabledMonthDate(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('day');
  }

  const getData = async () => {
    const startDateTime =
      dataList.value.find((item) => item.value === dataType.value)?.label === '日'
        ? dayjs(date1.value).format('YYYY-MM-DD 00:00:00')
        : dayjs(date.value).startOf('month').format('YYYY-MM-DD 00:00:00');
    const endDateTime =
      dataList.value.find((item) => item.value === dataType.value)?.label === '日'
        ? dayjs(date1.value).format('YYYY-MM-DD 23:59:59')
        : dayjs(date.value).endOf('month').format('YYYY-MM-DD 23:59:59');
    const time =
      dataList.value.find((item) => item.value === dataType.value)?.label === '日'
        ? `${date1.value}日`
        : `${date.value}月`;
    const params = {
      startDateTime: startDateTime,
      endDateTime: endDateTime,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      resourceInterfaceId: '5',
      jsConvert: true,
      groupCode: dataType.value,
      paramsData: JSON.stringify(params),
    };

    const { data } = await callResourceFunction(paramData);
    // console.log('用药排行', data);
    if (data && data.length) {
      const newData = data.map((item) => {
        return {
          indexName: item.indexName,
          indexCode: item.indexCode,
          unitName: item.unitName,
          value: Number(item.total) ? Number(item.total) : '-',
          // item.data
          //   .map((i) => i.value)
          //   .reduce((prev, cur) => {
          //     return prev + cur;
          //   }, 0),
        };
      });
      const copyData = newData.map((item) => {
        return item.indexName + item.value + item.unitName + '\n';
      });
      aiDataCopy.value[0].aiQuestion = `自来水厂${time}，用药量从高到低排行为：${copyData.join(
        ',',
      )}。分析当前情况。`;
      indexList.value = BubbleSort(newData);
    }
  };

  function BubbleSort(ary) {
    for (var i = 0; i < ary.length - 1; i++) {
      for (var j = i + 1; j < ary.length; j++) {
        var current = ary[i];
        if (Number(current.value) < Number(ary[j].value)) {
          var tmp = ary[j];
          ary[j] = current;
          ary[i] = tmp;
        }
      }
    }
    return ary;
  }
  // watch(
  //   () => date.value,
  //   async () => {
  //     await getData();
  //   },
  // );

  // watch(
  //   () => date1.value,
  //   async () => {
  //     await getData();
  //   },
  // );

  // watch(
  //   () => dataType.value,
  //   async () => {
  //     await getData();
  //   },
  // );
  getTimeList();
  // watch(
  //   () => props.dayResourceInterfaceId,
  //   async () => {
  //     await getData();
  //   },
  // );
</script>

<style lang="less" scoped>
  .ant-card {
    height: 100%;

    :deep(.ant-card-head) {
      min-height: 48px;
      padding: 0 16px;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      border-bottom: 1px solid #e9e9e9;
    }

    :deep(.ant-card-body) {
      padding: 16px;
      height: calc(100% - 56px);
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px;
        height: 9px;
      }
    }
  }

  .extra-wrap {
    :deep(.ant-select) {
      margin-right: 16px;

      // .ant-select-selector {
      //   width: 80px;
      // }
    }
  }

  .flex-center {
    display: flex;
    align-items: center;
    justify-self: start;

    .button_ai {
      margin-left: 8px;
      padding: 5px 8px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      cursor: pointer;

      img {
        width: 16px;
      }

      .text {
        font-size: 14px;
        color: #0b62cb;
        line-height: 14px;
        padding-left: 4px;
      }

      &:hover {
        background: rgba(11, 98, 203, 0.12);
      }
    }
  }

  .item-list {
    width: 100%;
    // height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;

    .item {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 18px 16px;
      // cursor: pointer;
      border-radius: 4px 4px 4px 4px;
      background: #f2f3f5;
      color: #ffffff;

      .item-index {
        flex: 1;
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #333333;

        .num-index {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          background: #999;
          color: #ffffff;
          border-radius: 10px;
        }

        .label {
          padding-left: 8px;
          font-weight: 400;
          font-size: 14px;
          width: 120px;
          white-space: nowrap; /* 让文本不换行 */
          overflow: hidden; /* 超出部分隐藏 */
          text-overflow: ellipsis; /* 使用省略号代替超出部分 */
        }
      }

      .item-content {
        flex: 1;
        display: flex;
        align-items: baseline;
        justify-content: end;

        .value {
          font-size: 16px;
          font-weight: 600;
          color: #333333;
          display: inline-block;
          max-width: 120px;
          white-space: nowrap;
          overflow: hidden;
          // text-overflow: ellipsis;
          // .number-value {
          // }
        }

        .unit {
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          margin-left: 8px;
        }
      }

      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(3) {
        background: rgba(11, 98, 203, 0.08);

        .num-index {
          color: #ffffff;
          background: var(--theme-color);
        }
      }
    }
  }

  .empty-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #999;
  }
</style>
