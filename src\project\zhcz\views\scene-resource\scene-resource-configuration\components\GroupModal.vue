<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    width="456px"
  >
    <BasicForm @register="registerForm" />
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">确认</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup name="GroupModal">
  import { computed, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { addOrUpdateGroup } from '/@zhcz/api/config-center/scenes-group';
  import { BasicForm, useForm } from '/@/components/Form';
  import { groupSchemas } from '../data';

  const okLoading = ref(false);
  const isEdit = ref(false);
  const selectNode = ref();
  const resourceId = ref();

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    labelWidth: 84,
    baseColProps: {
      span: 24,
    },
    schemas: groupSchemas,
    showActionButtonGroup: false,
  });
  const title = computed(() => (isEdit.value ? '编辑分组' : '新增分组'));

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const [registerModal, { closeModal }] = useModalInner((data) => {
    console.log('===>', data);
    isEdit.value = data.isEdit;
    selectNode.value = data.selectNode;
    resourceId.value = data.resourceId;
    updateSchema([
      {
        field: 'parentName',
        label: selectNode.value.treeNodeType === 1 ? '父级场景' : '父级分组',
      },
      {
        field: 'groupCode',
        componentProps: {
          disabled: isEdit.value,
        },
      },
    ]);
    setFieldsValue({
      parentName: selectNode.value.name,
      requestHandle: data.selectNode.requestHandle,
    });
    if (isEdit.value) {
      updateSchema([
        {
          field: 'parentName',
          label: selectNode.value.parentNodeType === 1 ? '父级场景' : '父级分组',
        },
      ]);
      setFieldsValue({
        id: selectNode.value.id,
        parentName: selectNode.value.parentName,
        groupName: selectNode.value.name,
        groupCode: selectNode.value.groupCode,
        defaultInterfaceId: selectNode.value.defaultInterfaceId,
        groupPurpose: `${selectNode.value.groupPurpose}`,
        sort: selectNode.value.sort,
      });
    }
  });

  async function handleSubmit() {
    try {
      okLoading.value = true;
      const values = await validate();
      const data = {
        ...values,
        senceId: selectNode.value.scenceId,
      };
      if (isEdit.value) {
        data.parentId = selectNode.value.parentNodeType === 1 ? null : selectNode.value.parentId;
      } else {
        data.parentId = selectNode.value.treeNodeType === 1 ? null : selectNode.value.id;
      }
      await addOrUpdateGroup(data);
      const msg = isEdit.value ? '编辑成功' : '新增成功';
      createMessage.success(msg);
      handleCancel();
      emit('success');
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    closeModal();
    resetFields();
  }
</script>
