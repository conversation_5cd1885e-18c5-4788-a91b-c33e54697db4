<template>
  <Card :bordered="false" dis-hover title="故障类型">
    <template #extra>
      <div class="extra-wrap">
        <Select
          placeholder="请选择"
          @change="handleOneChange"
          v-model:value="formData.argOne"
          class="mr-16px"
          :dropdownMatchSelectWidth="false"
        >
          <SelectOption v-for="item in optionsOne" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>

        <DatePicker
          v-model:value="formData.dayDate"
          :picker="picker"
          :valueFormat="valueFormat"
          :allowClear="false"
          class="!w-[auto]"
          placeholder="请选择"
          :disabledDate="disabledDate"
          @change="handleChangeDayDate"
        />
      </div>
    </template>

    <div ref="wrapperRef" class="h-full w-full">
      <div ref="chartRef" class="h-full" v-show="!isEmpty"></div>
      <div v-show="isEmpty" class="empty-box"> <HEmpty /></div>
    </div>
  </Card>
</template>

<script setup>
  import { Card, Select, SelectOption, DatePicker } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { ref, reactive, onMounted, nextTick, computed } from 'vue';
  import { useResizeObserver } from '@vueuse/core';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { roundAndConvert } from '/@zhcz/utils/number';
  import { getDataApi } from '../mockData/index';

  const picker = computed(() => {
    let type = formData.argOne;
    return type || 'month';
  });
  function disabledDate(current) {
    return current && current > dayjs().subtract(1, picker.value).endOf('day');
  }
  const valueFormat = computed(() => {
    let map = {
      month: 'YYYY-MM',
      day: 'YYYY-MM-DD',
      year: 'YYYY',
    };
    return map[picker.value] || map.month;
  });

  let optionsOne = [
    { value: 'month', label: '月' },
    { value: 'year', label: '年' },
  ];
  let formData = reactive({
    dayDate: dayjs().format('YYYY-MM'),
    argOne: optionsOne[1].value,
  });

  const chartRef = ref(null);
  const wrapperRef = ref(null);
  // 会立即执行
  let chartWidth = ref(0);
  let chartHeight = ref(0);
  useResizeObserver(wrapperRef, (entries) => {
    const entry = entries[0];
    const { width, height } = entry.contentRect;
    chartWidth.value = width;
    chartHeight.value = height;
    renderChart();
  });

  defineProps({
    sourceData: { type: Array, default: () => [] },
  });

  // let getParams = () => {
  //   let paramsData = {
  //     startDateTime: dayjs(formData.dayDate).startOf(picker.value).format('YYYY-MM-DD 00:00:00'),
  //     endDateTime: dayjs(formData.dayDate).endOf(picker.value).format('YYYY-MM-DD 23:59:59'),
  //     indexCodes: '@￥Resource',
  //     tenantId: '@￥TenantId',
  //   };
  //   let params = {
  //     resourceInterfaceId: '3',
  //     groupCode: formData.argOne,
  //     paramsData: JSON.stringify(paramsData),
  //   };
  //   return params;
  // };

  let cacheData;
  async function getData() {
    // let params = getParams();
    // let { data } = await callResourceFunction(params);
    let data = await getDataApi(formData.argOne);
    cacheData = data;
    console.log('data', data);
    renderChart(data);
  }

  let isEmpty = ref(false);
  function checkDataIsValid() {
    if (!cacheData) return false;
    return true;
  }
  function renderChart() {
    // let data = cacheData;
    if (!checkDataIsValid()) {
      isEmpty.value = true;
      return;
    } else {
      isEmpty.value = false;
    }
    trueRender();
  }
  function trueRender() {
    // 都是假数据，计算逻辑仅供参考
    let data = cacheData;
    let color = [
      '#2E7BFF',
      '#2EC4FF',
      '#435CFF',
      '#8143FF',
      '#22CD80',
      '#76C31F',
      '#FF8C2E',
      '#FEC52D',
    ];
    let { total, list } = data;
    total = list.reduce((pre, cur) => {
      return pre + cur.value;
    }, 0);
    list.forEach((i) => {
      i.percentVal = !i.value ? 0 : ((i.value / total) * 100).toFixed(0);
    });
    let legendXPadding = 10;
    let itemGap = -10;
    let itemWidth = 8;
    // let top = 35;
    let legendItemWidth =
      (chartWidth.value -
        legendXPadding * 2 -
        (itemGap > 0 ? itemGap : 0) * 3 -
        (itemWidth + 6) * 4) /
      4;
    legendItemWidth = Math.floor(legendItemWidth);

    let legendHeight = list.length > 4 ? 100 : 55;
    let topCenter = (chartHeight.value - legendHeight) / 2;
    const option = {
      animation: false,
      color,
      title: [
        {
          text: `${total}`,
          left: `${50 - 1}%`,
          top: topCenter - 25,
          textAlign: 'center',
          textStyle: {
            fontSize: 26,
            fontWeight: 600,
            height: 50,
            color: '#333333',
          },
        },
        {
          text: `故障次数`,
          left: `${50 - 1}%`,
          top: topCenter + 10,
          textAlign: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 400,
            color: '#666666',
          },
        },
      ],
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 1)',

        formatter: (data) => {
          let { name } = data;
          let { percentVal, value, unitName } = data.data;

          // const seriesIndex = data.seriesIndex;

          return `${data.marker} <span>${name}</span> <span>${
            value === null ? '-' : roundAndConvert(Number(value), 0)
          }</span><span>${unitName}</span> <span>${percentVal}%</span>`;
        },
        backgroundColor: 'rgba(255, 255, 255, 1)',

        textStyle: {
          color: '#333',
          fontSize: 14,
          lineHeight: 28,
          height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
      },
      legend: {
        icon: 'circle',
        itemWidth,
        itemHeight: 8,
        bottom: 0,
        left: 'center',
        // right: 0,
        itemGap,
        textStyle: {
          fontSize: 14,
          color: '#333333',
          width: legendItemWidth,
          borderWidth: 1,
          borderColor: 'transparent',
          padding: [20, 0, 0, 0],
          rich: {
            title: {
              color: '#666666',
              fontSize: 14,
              fontWeight: 400,
              padding: [0, 0, 0, 0],
            },
            num: {
              padding: [4, 0, 0, 0],
              color: '#333',
              fontSize: 16,
              fontWeight: 600,
              align: 'left',
            },
          },
        },
        formatter: (name) => {
          console.log('name', name);
          let item = list.find((i) => i.indexName === name);
          if (!item) return 'not find name';
          let { percentVal } = item;
          return [`{title|${name}}`, `{num|${percentVal}%}`].join('\n');
        },
      },
      grid: {
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        containLabel: true,
      },

      series: {
        type: 'pie',
        // radius: ['50%', `${top * 2}%`],
        radius: ['65%', `90%`],
        bottom: legendHeight,
        top: 10,
        center: ['50%', `50%`],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emptyCircleStyle: {
          color: 'lightgray',
        },
        labelLine: {
          show: false,
        },
        data: list.map((i, index) => ({
          value: i.value !== null ? (i.value ? roundAndConvert(i.value, 0) : '0') : '-',
          name: i.indexName,
          originValue: i.value,
          percentVal: i.percentVal,
          unitName: i.unitName,
          itemStyle: {
            color: color[index % color.length],
          },
        })),
      },
    };
    // let instance = getInstance();
    setOptions(option);
  }

  function handleOneChange() {
    nextTick(() => {
      formData.dayDate = dayjs().format(valueFormat.value);
      getData();
    });
  }
  function handleChangeDayDate() {
    getData();
  }
  let setOptions;
  // let getInstance;
  // let echarts;
  onMounted(() => {
    ({ setOptions } = useECharts(chartRef));
    getData();
  });
</script>

<style lang="less" scoped>
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(11, 98, 203, 0.08);
    border-radius: 4px 4px 4px 4px;
    padding: 0 16px;

    .value {
      font-size: 16px;
      font-weight: 600;
    }
  }
</style>
