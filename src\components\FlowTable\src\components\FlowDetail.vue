<template>
  <div v-show="visible" class="detail-container w-full h-full absolute top-0 left-0 z-600">
    <BasicDetail
      v-bind="getBindValues"
      class="FlowDetail-box"
      @close="closePage"
      :style="setContentStyle"
      :customStyle="{
        right: '1rem',
      }"
      offsetWidth="2rem"
    >
      <div class="h-full content flex flex-col">
        <div class="text-base">
          <Tabs v-model:active-key="activeKey">
            <template v-for="item in flowProcessDesc.flowDataArr" :key="item.id">
              <TabPane :tab="item.name" />
            </template>
          </Tabs>
        </div>
        <ScrollContainer v-show="activeKey === 1" overflowYPadding>
          <div class="mt-2 mb-6">
            <h-empty class="mt-[17vh]" v-if="!flowProcessDesc.flowDataArr.length" />
            <SectionTitle v-if="flowDataRef.workItemName" :title="flowDataRef.workItemName" />
          </div>
          <div :style="getBoxWidth">
            <BasicForm @register="registerForm" />
          </div>
        </ScrollContainer>
        <div class="designer flex-1 px-4" v-if="flowDataRef.processXml && activeKey === 2">
          <Designer
            :readOnly="true"
            :flowWorkItemID="flowWorkItemID"
            :xml="flowDataRef.processXml"
            :finishedInfo="flowDataRef.finishedInfo"
            :node-disabled="nodeDisabled"
          />
        </div>

        <ApprovalInformation v-if="activeKey === 3" :list="flowProcessDesc.processDesc" />

        <template v-if="isShow && visible">
          <Attach v-show="activeKey === 4" :data="flowDataRef.attachContent" ref="attachRef" />
        </template>

        <MyOffice
          v-if="myOfficeInfo && flowDataRef.workItemDto.workItemID"
          :workItemDto="flowDataRef.workItemDto"
          :data="myOfficeInfo"
          @change="handleFile"
        />
      </div>
      <template #footer>
        <div
          class="h-full flow-btn-box flex items-center"
          v-if="footerBtns && footerBtns.length"
          :style="setFooterStyle"
        >
          <template v-for="(item, index) in footerBtns" :key="item.code">
            <Button
              size="large"
              class="flow-btn"
              :type="item.type || (currentBtn === index ? 'primary' : 'default')"
              @click="handerFn(item)"
            >
              {{ item.name }}
            </Button>
          </template>
        </div>
      </template>
    </BasicDetail>
    <SendFlowModel @register="registerModel" @success="handleSuccess" v-model:loading="loading" />

    <!-- 传阅，征询，转批 -->
    <ComFlowModel
      :formScheama="formScheama"
      @register="registerComFlowModel"
      @success="handleSuccessFn"
      v-model:loading="loading"
    />
    <ModelDesign
      v-if="visible && activeKey === 1 && btnData.dataModelId"
      :classify="2"
      :modelId="btnData.dataModelId"
      @success="initForm"
    />
    <!-- 转发 -->
    <SendToFlowModal @register="registerSendToModal" @success="handleSuccessSendTo" />
  </div>
</template>
<script lang="ts" setup>
  import {
    computed,
    ref,
    useAttrs,
    watch,
    unref,
    watchEffect,
    reactive,
    onUnmounted,
    toRaw,
    nextTick,
  } from 'vue';
  import { BasicDetail } from '/@/components/Detail';
  import { Tabs, Button } from 'ant-design-vue';
  // import { tabsData } from './flow-data';
  import { Designer } from '/@/components/BpmnChart';
  import ApprovalInformation from './ApprovalInformation/index.vue';
  import Attach from './attache/index.vue';
  import MyOffice from './MyOffice/index.vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { UseModalInit } from '/@/components/ModelTable/src/hooks/useComFormModel';
  import {
    createFlowProcessReturnApi,
    sendFlowProcessApi,
    // closeFlowProcessApi,
  } from '/@/api/flow/flow';
  import SendFlowModel from './SendFlowModel.vue';
  import SendToFlowModal from './SendToFlowModal.vue';
  import { useModal } from '/@/components/Modal';
  import ModelDesign from '/@/components/ModelTable/src/components/ShowModelDesign.vue';
  import { useFlowButton, flowApiFn } from './hooks/useFlowButton';
  import { useInitFlow } from './hooks/useInitFlow';
  import { cloneDeep } from 'lodash-es';
  import { deleteObjAttribute } from './helper';
  import { useFlowStoreWithout } from '/@/store/modules/flow';
  import SectionTitle from './SectionTitle.vue';
  import ComFlowModel from './ComFlowModel.vue';
  import { getBtnFormScheama } from './helper';
  import { getBtnFormScheama as getBtnFormScheamaParent } from '../helper';
  import { ScrollContainer } from '/@/components/Container';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { batchDelete } from '/@/api/flow/temp';
  import { useUserStore } from '/@/store/modules/user';
  import { LayoutPosition, FooterPosition } from './flow-data';
  // import { useMultipleTabWithOutStore } from '/@/store/modules/multipleTab';
  // import { AppNoCachePageList } from '/@/enums/appEnum';
  // import { useRoute } from 'vue-router';
  import { isFunction } from '/@/utils/is';
  import { getCSSValue } from '/@/utils';

  // const route = useRoute();

  // const multipleTabStore = useMultipleTabWithOutStore();

  const userInfo = useUserStore().getUserInfo;

  const TabPane = Tabs.TabPane;

  const props = defineProps({
    visible: Boolean,
    currentBtn: {
      type: Number,
      default: 0,
    },
    nodeDisabled: {
      type: Boolean,
      default: false,
    },
    handleFlowComFn: {
      type: Function as PropType<Fn>,
    },
    footerCallback: {
      type: Function as PropType<Fn>,
    },
  });

  const emit = defineEmits(['update:visible', 'success', 'change']);

  const loading = ref(false);
  const activeKey = ref(1);
  const btnData = ref<any>({});
  const visibleRef = ref(false);
  const flowStore = useFlowStoreWithout();
  const attachRef = ref<InstanceType<typeof Attach>>();

  const templateStyleDto = ref<{
    buttonLayout: number | null;
    layoutMode: number | null;
    detailWidth: null | string;
  }>({ buttonLayout: 1, layoutMode: 1, detailWidth: '100%' });

  const flowDataRef = reactive<any>({
    processXml: '',
    guid: '',
    workItemDto: {},
    workItemName: '',
    moduleId: '',
    finishedInfo: {},
    dataModelId: '',
    attachContent: {},
  });
  const flowCheckParams = ref({
    workItemID: '',
    entryID: '',
  });

  const footerBtns = computed(() => {
    if (props.footerCallback && isFunction(props.footerCallback)) {
      return props.footerCallback(flowDataRef.workItemDto.menus);
    }
    return (
      flowDataRef.workItemDto.menus || [
        {
          name: '返回',
          code: 'back',
          type: 'default',
        },
      ]
    );
  });

  const formScheama = ref();

  const flowProcessDesc = reactive({
    flowDataArr: [] as Indexable[],
    isCheck: false, // 是否流程详情
    isManage: false, // 是否流程版办理
    processDesc: [],
  });

  const modalInit = ref<Indexable>({});
  const attrs = useAttrs();

  const { createMessage } = useMessage();

  const getBindValues = computed(() => {
    const values = {
      ...attrs,
      visible: unref(visibleRef),
      loading: loading.value,
    };
    return values;
  });

  const setContentStyle = computed(() => ({
    width: templateStyleDto.value?.detailWidth || '100%',
    ...LayoutPosition[templateStyleDto.value?.layoutMode || 1],
  }));

  const setFooterStyle = computed(() => ({
    justifyContent: FooterPosition[templateStyleDto.value?.buttonLayout || 1],
  }));

  const myOfficeInfo = computed(() => {
    const item = flowProcessDesc.flowDataArr.find(
      (el) => activeKey.value === el.id && el.isDocument,
    );

    return item;
  });
  const handleFile = (data: Indexable) => {
    const item = flowProcessDesc.flowDataArr.find((el) => data.id === el.id);
    if (item) {
      item.name = data.fileName;
      item.fileId = data.fileId;
      item.url = data.url;
    }

    const ret = flowProcessDesc.flowDataArr
      .filter((el) => el.fileId)
      .map((el) => {
        return {
          id: el.fileId,
          name: el.name,
          url: el.url,
        };
      });

    flowDataRef.workItemDto.documentList = ret;
  };
  const flowWorkItemID = ref<any>('');
  const [registerForm, formActionType] = useForm({
    labelWidth: 110,
    baseColProps: { span: 23 },
    showActionButtonGroup: false,
  });

  const isShow = ref<boolean>(false);
  const formConfig = ref();

  const [registerModel, { openModal }] = useModal();

  const [registerComFlowModel, { openModal: openComFlowModal }] = useModal();

  const [registerSendToModal, { openModal: openSendToModal }] = useModal();

  watchEffect(() => {
    visibleRef.value = !!props.visible;
  });

  watch(
    () => unref(visibleRef),
    async (val) => {
      if (val) {
        emit('update:visible', val);
      } else {
        activeKey.value = flowProcessDesc.flowDataArr[0]?.id || 1;
        flowDataRef.workItemDto = {
          workItemDto: {},
        };
        flowDataRef.attachContent = {};
        flowDataRef.workItemName = '';
        // 关闭后初始化布局配置
        templateStyleDto.value = { buttonLayout: 1, layoutMode: 1, detailWidth: '100%' };
        // 上一次打开了流程详情，清除流程状态数据
        flowDataRef.finishedInfo = Object.keys(flowDataRef?.finishedInfo).length && {};
      }
    },
  );

  async function delFiles(arr: string[]) {
    await delAttachUploadFile(arr);
    flowStore.resetAttachFiles();
  }

  async function simulationButton(code: string) {
    const { getFieldsValue } = formActionType;

    const data = await useFlowButton(
      code,
      {
        flowDataRef: toRaw(flowDataRef),
        flowCheckParams: unref(flowCheckParams),
        flowProcessDesc: unref(flowProcessDesc),
        btnItem: btnData.value,
        modalInit: modalInit.value,
        delFiles,
        handleFlowComFn,
      },
      [openModal, emit, openComFlowModal, getFieldsValue, openSendToModal],
    );

    if (!!data) {
      flowDataRef.workItemDto = data;
    }
  }

  // 意见征询回复打开
  const handleFlowComFn = async () => {
    const btnItem = {
      ...btnData.value,
      isUpdate: true,
      eventType: 'flow-consult',
    };
    formScheama.value = getBtnFormScheamaParent(btnItem);

    openComFlowModal(true, btnItem);
  };

  // 打开流程页面
  async function openPage(visible = true, data): Promise<void> {
    visibleRef.value = visible;

    flowWorkItemID.value = data?.record?.WorkItemID ?? '';

    const params = cloneDeep(data.fieldsMap);
    // btnData.value =  cloneDeep(data);
    loading.value = true;
    try {
      await useInitFlow(data, {
        flowDataRef,
        flowCheckParams,
        flowProcessDesc,
        flowStore,
      });

      templateStyleDto.value = flowDataRef.workItemDto?.templateStyleDto ?? templateStyleDto.value;

      btnData.value = Object.assign({}, cloneDeep(data), {
        dataModelId: flowDataRef.dataModelId,
        params,
      });

      await nextTick();
      isShow.value = showAttach();
      await nextTick();
      isShow.value && attachRef.value && attachRef.value.initData();
      //  有表单才加载
      if (flowDataRef.workItemDto.formMap) {
        await initForm();
        flowStore.setCloseFun(simulationButton);
        simulationButton('open');
      }
    } catch (e) {
      throw e;
    } finally {
      activeKey.value = flowProcessDesc.flowDataArr[0]?.id || 1;
      loading.value = false;
    }
  }

  async function initForm() {
    // 表单模型开启了只读并且是详情才做表单详情解析
    const data =
      flowDataRef.workItemDto.formMap.modelFormDesignInfo.isReadonly && flowProcessDesc.isCheck
        ? {
            ...btnData.value,
            readonly: flowProcessDesc.isCheck,
            partReadonly: flowProcessDesc.isManage,
          }
        : { ...btnData.value };

    data.params = data.params ?? data.record ?? {};
    data.params.flowInfo = flowDataRef.workItemDto;
    modalInit.value = new UseModalInit(
      {
        btnData: { ...data },
        formDesignInfo: flowDataRef.workItemDto.formMap.modelFormDesignInfo,
        retFormDesignInfo: {
          ...data?.record_data,
          ...flowDataRef.workItemDto.formMap.resultMap,
        },
      },
      formActionType,
    );

    const { width, align } = await modalInit.value.initialize(
      flowDataRef.workItemDto?.frontScriptDto,
    );
    formConfig.value = {
      width,
      align,
    };
  }

  const getBoxWidth = computed(() => {
    const { width = '', align = 'left' } = unref(formConfig) || {};
    const ret: Indexable = {
      width: getCSSValue(width, '100%'),
    };
    if (align === 'center') {
      ret.marginLeft = 'auto';
      ret.marginRight = 'auto';
    } else if (align === 'right') {
      ret.marginLeft = 'auto';
    }
    return ret;
  });

  // 附件页签显示条件
  function showAttach() {
    return (
      ((flowDataRef.attachContent.annexPermission & 2) === 2 ||
        flowDataRef.attachContent?.annexDtoList?.length) ??
      false
    );
  }

  defineExpose({
    openPage,
  });

  function closePage() {
    emit('update:visible', false);
    emit('change', false);
  }

  //上传附件删除
  async function delAttachUploadFile(data: string[]) {
    await batchDelete({
      userId: userInfo.userId as string,
      updateResourcesFlag: false,
      removeKeyList: data,
    });
  }

  let sendLoading = false;
  // 按钮事件
  async function handerFn(item) {
    if (sendLoading) return;
    sendLoading = true;

    try {
      const { getFieldsValue } = formActionType;

      formScheama.value = getBtnFormScheama(item.code);

      // 流程详情内重置名称和type
      btnData.value.label = item.name;
      btnData.value.eventType = item.code;

      await useFlowButton(
        item.code,
        {
          flowDataRef: toRaw(flowDataRef),
          flowCheckParams: unref(flowCheckParams),
          flowProcessDesc: unref(flowProcessDesc),
          btnItem: btnData.value,
          modalInit: modalInit.value,
          delFiles,
          handleFlowComFn,
        },
        [openModal, emit, openComFlowModal, getFieldsValue, openSendToModal],
      );
    } catch (e: any) {
      sendLoading = false;
      e.errorFields && createMessage.error('表单校验失败：' + e.errorFields[0].errors);
      throw e;
    }

    sendLoading = false;
  }

  // 发送
  async function handleSuccess(values, successData) {
    //console.log('values233', values, flowDataRef.workItemDto);

    if (sendLoading) return;
    sendLoading = true;

    try {
      const { entryContent, choices, response, workItemName } = values;
      const { isSave, receiverLimitMap, afterSumbitFn } = successData;

      let receiverLimit;

      if (flowDataRef.workItemDto.receiverLimit.length) {
        receiverLimit = flowDataRef.workItemDto.receiverLimit.map((item) => {
          const selectedList = receiverLimitMap.get(`recipients${item.id}_${item.type}`);
          return { ...item, selectedList };
        });
      }

      if (workItemName) {
        flowDataRef.workItemName = workItemName;
      }
      const { guid, moduleId, workItemDto, workItemName: flowFileName } = toRaw(flowDataRef);

      const workItemDtoParam = cloneDeep(workItemDto);

      // 去除无用参数
      deleteObjAttribute(workItemDtoParam, ['menus', 'choices', 'recipients', 'receiverLimit']);

      if (workItemDtoParam.formMap) {
        deleteObjAttribute(workItemDtoParam.formMap, 'modelFormDesignInfo');
      }
      const isSaveParams = {
        guid,
        moduleId,
        workItemName: flowFileName,
        workItemDto: workItemDtoParam,
      };

      const isSendParams: Indexable = {
        ...isSaveParams,
        entryContent,
        choice: choices,
        response,
        receiverLimit,
      };
      if (workItemDtoParam.formMap && workItemDtoParam.formMap.resultMap) {
        isSendParams.item_id = workItemDtoParam.formMap.resultMap.item_id;
        isSendParams.id = workItemDtoParam.formMap.resultMap.id;
      }

      const params = !isSave ? isSendParams : isSaveParams;
      const finallyParams = handleAttachParams(cloneDeep(params?.workItemDto?.annexDtoList ?? []));

      if (attachRef.value) {
        const delAttachList = attachRef.value.getDelSoucre();
        params.workItemDto.annexDtoList = [...finallyParams, ...delAttachList];
      }

      const apiFn = !isSave ? sendFlowProcessApi : createFlowProcessReturnApi;
      try {
        await flowApiFn(params, apiFn, emit);
        const arr =
          flowStore.getFlowAttachFiles?.filter((i) => !i.new)?.map((i) => i.realfileName) ?? [];
        if (arr.length) {
          delFiles(arr);
        }

        afterSumbitFn && (await afterSumbitFn());
      } finally {
        loading.value = false;
      }
    } catch (err) {
      sendLoading = false;
    } finally {
      loading.value = false;
    }

    sendLoading = false;
  }

  // 处理附件信息入参
  const handleAttachParams = (attactParams: any[]) => {
    return attactParams.map((i) => ({
      documentName: i.documentName,
      documentId: i.changeType === 2 ? null : i.documentId,
      orignId: i.changeType === 2 ? (i.documentType === 8 ? null : i.documentId) : i.orignId,
      documentType: i.documentType,
      previewUrl: i.previewUrl,
      documentExt: i.documentExt,
      annexPermission: i.annexPermission,
      bucketName: i.bucketName,
      changeType: i.changeType,
      entryId: i.entryId,
      filePath: i.filePath,
      editorId: i?.editorId ?? null,
      editor: i?.editor ?? null,
      editorDate: i?.editorDate ?? null,
      creatorId: i?.creatorId ?? null,
      creator: i?.creator ?? null,
      createDate: i?.createDate ?? null,
      realDocumentName: i.realDocumentName,
    }));
  };

  // 转发
  const handleSuccessSendTo = async (data) => {
    const { guid, afterSumbitFn } = data;
    const { response, workItemName } = data.record;
    const { moduleId } = toRaw(flowDataRef);
    if (sendLoading) return;
    sendLoading = true;
    try {
      // 去除无用参数
      deleteObjAttribute(data.record, ['menus', 'choices', 'recipients', 'receiverLimit']);
      const isSendParams: Indexable = {
        guid,
        moduleId,
        workItemName: workItemName,
        workItemDto: data.record,

        choice: 0,
        response,
        receiverLimit: [],
      };

      try {
        loading.value = true;
        await flowApiFn(isSendParams, sendFlowProcessApi, emit);
        const arr =
          flowStore.getFlowAttachFiles?.filter((i) => !i.new)?.map((i) => i.realfileName) ?? [];
        if (arr.length) {
          delFiles(arr);
        }
        afterSumbitFn && (await afterSumbitFn());
      } finally {
        loading.value = false;
      }
    } catch (err) {
      sendLoading = false;
    }
    sendLoading = false;
  };

  function handleSuccessFn() {
    emit('update:visible', false);
    emit('change', false);
    emit('success');
  }

  // watch(
  //   () => multipleTabStore.getCachedTabList,
  //   (v) => {
  //     // 处理流程详情页面在页签关闭后还是开启状态
  //     const cachedTabList = v;
  //     const moduleId = route?.meta?.moduleId;
  //     if (!cachedTabList.includes(AppNoCachePageList[0] + moduleId) && props.visible) {
  //       if (flowCheckParams.value.workItemID && flowStore.getFlowReleaseParams) {
  //         simulationButton('close');
  //       }
  //       emit('update:visible', false);
  //     }
  //   },
  // );

  // 刷新切换关闭流程
  onUnmounted(async () => {
    if (props.visible && flowCheckParams.value.workItemID && flowStore.getFlowReleaseParams) {
      // const delList =
      //   flowStore.getFlowAttachFiles?.filter((i) => i.new)?.map((i) => i.realfileName) ?? [];
      // if (delList.length) {
      //   delFiles(delList);
      // }
      // await closeFlowProcessApi({ ...flowCheckParams.value, guid: flowDataRef.guid });
      // flowStore.setLockFlag(true);
      simulationButton('close');
    }
  });
</script>
<style lang="less" scoped>
  // @import '../style/footer.less';
  .flow-btn {
    min-width: 120px;
    height: 40px;
    font-size: 16px;

    & + .flow-btn {
      margin-left: 24px;
    }
  }

  .djs-palette {
    display: none;
  }

  .detail-container {
    background: #eeeff1;
  }

  ::v-deep(.ant-tabs-nav-wrap) .ant-tabs-tab {
    font-size: 16px !important;
  }

  ::v-deep(.full-loading) {
    background-color: unset !important;
  }
</style>
