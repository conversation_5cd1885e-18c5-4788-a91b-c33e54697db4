<template>
  <div class="h-full px-4">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <div>
          <a-button
            type="primary"
            :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
            @click="handleCreate"
          >
            新建
          </a-button>
          <a-button
            :icon="h(Icon, { icon: 'icon-park-outline:download' })"
            @click="handleImport"
            class="ml-4"
          >
            导入
          </a-button>
          <!-- <a-button @click="reload()"> 刷新 </a-button> -->
          <ButtomImport @register="rgImport" @before-upload="handleImportJson" />
        </div>
      </template>
      <template #emptyText>
        <div>暂无数据</div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'bindSourceUniqueId'">
          {{ factoryList.find((item) => item.id === record.bindSourceUniqueId)?.name || '--' }}
        </template>
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '配置',
                onClick: handleConfig.bind(null, record),
              },
              {
                label: '预览',
                onClick: handlePreview.bind(null, record),
              },
              {
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
            ]"
            :dropDownActions="[
              {
                label: '删除',
                onClick: handleDelete.bind(null, record),
              },
              {
                label: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                label: '导出',
                onClick: handleExport.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <CreateModal @register="registerModal" @success="reload" />
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, h } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { ButtomImport, useButtomImport } from '/@/components/ImportButton';
  import CreateModal from './CreateModal.vue';
  import { columns, searchFormSchema } from './items.data';
  import {
    getFlowDataPageApi,
    exportEquipmentFlowApi,
    importEquipmentFlowApi,
    deleteEquipmentFlowApi,
    copyEquipmentFlowApi,
  } from '/@/api/process';
  import { openWindow } from '/@/utils';
  import QueryString from 'qs';
  import { getToken } from '/@/utils/auth';
  import { TENANTID_KEY } from '/@/enums/cacheEnum';
  import { createLocalStorage } from '/@/utils/cache';
  import { downloadByUrl, setEncoded } from '/@/utils/file/download';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  import { getFactoryId } from '/@zhcz/utils/factory';
  import { getFactoryListApi } from '/@/api/process';

  const { createMessage, createConfirm } = useMessage();

  const userStore = useUserStore();
  const userInfo = computed(() => userStore.getUserInfo);
  const bindSourceFrom = computed(() => userInfo.value.bindSourceFrom);

  const factoryList = ref<Recordable[]>([]);

  async function getFactoryList() {
    const res = await getFactoryListApi();
    factoryList.value = res;
  }
  getFactoryList();

  const [registerTable, { reload }] = useTable({
    columns,
    api: getFlowDataPageApi,
    // showIndexColumn: false,
    formConfig: {
      labelWidth: 80,
      schemas: searchFormSchema,
    },
    useSearchForm: true,
    bordered: false,
    searchInfo: computed(() => {
      const factoryId = getFactoryId();
      return {
        bindSourceFrom: bindSourceFrom.value,
        bindSourceUniqueIds: factoryId ? [factoryId] : [],
      };
    }),
    actionColumn: {
      width: 186,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });
  const [registerModal, { openModal }] = useModal();

  function handleCreate() {
    openModal(true, {
      type: '新建',
      form: {
        id: '',
        recentVersion: '',
        flowName: '',
        bindSourceUniqueId: '',
        sortNumber: '',
      },
    });
  }

  async function handleExport(record) {
    const data = await exportEquipmentFlowApi(record.id);
    const json = JSON.stringify(data);
    const { href, filename } = setEncoded('JSON', record.flowName, json);

    downloadByUrl({ url: href, fileName: filename });
  }

  const [rgImport, { importFile }] = useButtomImport();
  async function handleImport() {
    await importFile();
  }

  async function handleImportJson(jsonStr) {
    const canvasInfo = JSON.parse(jsonStr);

    await importEquipmentFlowApi(canvasInfo);
    createMessage.success('导入成功');
  }

  function handleEdit(record) {
    openModal(true, {
      type: '编辑',
      form: record,
    });
  }

  async function handleCopy(record) {
    await copyEquipmentFlowApi(record.id);
    createMessage.success('复制成功');
    reload();
  }

  const ls = createLocalStorage();
  const tenantId = ls.get(TENANTID_KEY) || '';
  async function handleConfig(record) {
    let url = `${location.origin}/#/process-editor`;
    url = `${url}${QueryString.stringify(
      {
        flowId: record.id,
        bindSourceUniqueId: record.bindSourceUniqueId,
        bindSourceFrom: record.bindSourceFrom,
        flowName: record.flowName,
        token: getToken(),
        tenantId,
        isIframe: true,
        factoryId: record.bindSourceUniqueId,
      },
      { addQueryPrefix: true },
    )}`;
    openWindow(url, { target: '_blank' });
  }

  async function handlePreview(record) {
    let url = `${location.origin}/#/process-preview`;
    url =
      url +
      QueryString.stringify(
        {
          flowId: record.id,
          token: getToken(),
          tenantId,
          isIframe: true,
          factoryId: record.bindSourceUniqueId,
        },
        { addQueryPrefix: true },
      );
    openWindow(url, { target: '_blank' });
  }

  async function handleDelete(record) {
    createConfirm({
      title: '提示',
      iconType: 'warning',
      content: '是否确认删除',
      onOk: async () => {
        await deleteEquipmentFlowApi(record.id);
        createMessage.success('删除成功');
        reload();
      },
    });
  }
</script>

<style scoped lang="less">
  :deep(.ant-table-tbody) {
    > tr.ant-table-row-selected > td {
      background-color: @theme-color-5p;
    }
  }

  :deep(.vben-basic-table .ant-table-wrapper) {
    padding-left: 16px;
    padding-top: 16px;
  }

  :deep(.vben-basic-table .ant-table-wrapper .ant-table-title) {
    padding-bottom: 12px !important;
  }

  :deep(.vben-basic-table-form-container) {
    padding: 0;
  }
</style>
