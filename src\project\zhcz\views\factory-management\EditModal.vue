<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    width="45%"
  >
    <div class="container">
      <BasicForm ref="editModalFormRef" @register="registerForm">
        <template #id="{ model, field }">
          <InputNumber
            v-model:value="model[field]"
            :min="0"
            :precision="0"
            :defaultValue="0"
            :disabled="isEdit ? true : false"
            placeholder="请输入"
          />
        </template>
      </BasicForm>
    </div>
    <template #footer>
      <a-button class="default-btn" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">保存</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup name="EditModal">
  import { ref, unref, reactive } from 'vue';
  import { InputNumber } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { createFactory, updateFactory, getFactoryDetail } from '/@zhcz/api/patrol';
  import { schemas, editModalFormRef } from './data';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const title = ref('');
  const isEdit = ref(false);
  let EditData = reactive({
    id: null,
  });
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    if (data.isEdit) {
      title.value = '编辑水厂';
      await getPlatformld(data.id);
    } else {
      title.value = '新增水厂';
    }
    isEdit.value = data.isEdit;

    setFieldsValue(data);
    EditData.id = data.id;
  });

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 116,
    schemas: schemas,
    showActionButtonGroup: false,
  });

  const handleCancel = () => {
    resetFields();
    closeModal();
  };

  const handleSubmit = async () => {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      const params = values;
      const editParams = Object.assign(EditData, values);
      unref(isEdit) ? await updateFactory(editParams) : await createFactory(params);
      await handleCancel();
      createMessage.success('操作成功');
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  };

  const getPlatformld = async (id) => {
    const res = await getFactoryDetail(id);
    if (res) {
      setFieldsValue({
        platformId: res.platformId,
      });
    }
  };
</script>
<style lang="less" scoped></style>
