<template>
  <Card :bordered="false" dis-hover>
    <!-- :data-resource-code="dateType === 'dhtj2_r' ? dayGroupCode : monthGroupCode" -->
    <template #title>
      <div data-index-name="ElectricityConsumptionMonthly">电耗统计</div>
    </template>
    <template #extra>
      <div class="flex extra-wrap">
        <div>
          <Select
            v-model:value="indicator"
            style="width: 100px"
            placeholder="请选择"
            @change="handleChangeIndicator"
            v-if="indicatorList.length"
          >
            <SelectOption v-for="item in indicatorList" :key="item.value" :value="item.value">
              {{ item.label }}
            </SelectOption>
          </Select>
        </div>
        <div>
          <Select v-model:value="dateType" style="width: 80px" v-if="dataList.length">
            <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
              {{ item.label }}
            </SelectOption>
          </Select>
        </div>
        <DatePicker
          v-if="dataList.find((item) => item.value === dateType)?.label === '月'"
          style="width: 200px"
          v-model:value="monthDate"
          picker="month"
          valueFormat="YYYY-MM"
          :disabledDate="disabledDateMonth"
        />
        <DatePicker
          v-if="dataList.find((item) => item.value === dateType)?.label === '日'"
          valueFormat="YYYY-MM-DD"
          format="YYYY-MM-DD"
          v-model:value="dayDate"
          placeholder="请选择"
          style="width: 200px"
          :allowClear="false"
          :disabledDate="disabledDate"
        />
      </div>
    </template>
    <template v-if="indicatorList.length && indexList.length">
      <div class="total-list">
        <div class="item" v-for="(item, index) in totalList" :key="index">
          <div class="content">
            <div class="name">{{ item.indexName }}</div>
            <div class="value">
              <div class="number-value" v-if="item.value === null" style="font-weight: 400">-</div>
              <Tooltip @mouseenter="showTooltip" v-else>
                <template #title>{{ item.value }}{{ item.unitName }}</template>
                <div class="number-value">{{ item.value }}</div>
              </Tooltip>
              <span class="unit" v-if="item.value !== null">{{ item.unitName }}</span>
            </div>
          </div>
        </div>
      </div>
      <div
        style="height: calc(100% - 62px); min-height: 120px; margin-top: 6px"
        v-loading="loading"
      >
        <div ref="chartRef" style="height: 100%"></div>
      </div>
    </template>
    <div v-else class="empty-box">
      <HEmpty />
    </div>
  </Card>
</template>
<script setup lang="ts">
  import { Card, DatePicker, Tooltip, Select, SelectOption } from 'ant-design-vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { ref, watch, onMounted } from 'vue';
  import dayjs from 'dayjs';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import {
    getSenceGroupTreeWithGroupCode,
    listSenceGroupByParent,
  } from '/@zhcz/api/cost-management';
  // import { useIntervalFn } from '@vueuse/core';
  // import { useUserStore } from '/@/store/modules/user';
  import icon1 from '/@zhcz/views/cost-management/assets/images/energy-ratio_1.png';
  import icon2 from '/@zhcz/views/cost-management/assets/images/energy-ratio_2.png';
  import HEmpty from '/@/components/HEmpty/index.vue';

  type OptionItem = { label: String; value: String };
  const indicatorList = ref<OptionItem[]>([]);
  const indicator = ref('');
  const dateType = ref<any>(null);
  const dataList = ref<OptionItem[]>([
    // {
    //   label: '日',
    //   value: 'dhtj2_r',
    // },
    // {
    //   label: '月',
    //   value: 'dhtj2_y',
    // },
  ]);
  function showTooltip(e) {
    if (e.target.clientWidth >= e.target.scrollWidth) {
      e.target.style.pointerEvents = 'none';
    }
  }
  const handleChangeIndicator = async () => {
    // pause();
    await getData();
    // resume();
  };
  const monthDate = ref(dayjs().subtract(0, 'month').format('YYYY-MM'));
  const dayDate = ref(dayjs().format('YYYY-MM-DD'));
  const chartRef = ref<any>(null);

  const { setOptions } = useECharts(chartRef);
  const colorBox = ['rgba(34,205,128,0.8)', 'rgba(46,140,255,0.8)', 'rgba(255,140,46,0.8)'];
  const colorBox2 = ['rgba(34,205,128,', 'rgba(46,140,255,', 'rgba(255,140,46,'];
  function handleSetVisitChart() {
    const option = {
      color: [...colorBox],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: '#fff',
        textStyle: {
          color: '#333',
          fontSize: 14,
          lineHeight: 28,
          height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
        formatter: (params) => {
          // console.log(params, 'params');
          const htmlStrBefor = `<div style="color: #999;">${params[0].name}</div>`;
          let htmlStrAfter = params
            .map((item) => {
              return `${item.marker} <span style="display: inline-block; width: 60px;">${
                item.seriesName
              }</span><span style="display: inline-block; width: 150px; font-weight: bold; text-align: right;">${
                item.value ? item.value : '-'
              }&nbsp;${indexList.value.length ? indexList.value[0].unitName : ''}</span>
            `;
            })
            .join('<br/>');
          return htmlStrBefor + htmlStrAfter;
        },
      },
      legend: {
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 24,
        color: [...colorBox],
        data: indexList.value.length ? indexList.value.map((i) => i.indexName) : [],
        textStyle: {
          fontSize: 14,
          color: '#333333',
        },
      },
      grid: {
        top: '25%',
        left: '0',
        right: '0',
        bottom: '1%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          data: indexList.value[0].XAxis,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#666666',
              fontSize: 14,
            },
          },
          axisPointer: {
            type: 'shadow',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name:
            indicatorList.value.find((item) => item.value === indicator.value)?.label === '电耗'
              ? '单位（kW·h）'
              : '',
          // offset: -5,
          nameTextStyle: {
            color: '#333333',
            fontSize: 14,
            align: 'left',
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#666666',
              fontSize: 14,
            },
          },
        },
      ],
      series: indexList.value.map((item, index) => {
        return {
          name: item.indexName, // 总用电
          type: 'line',
          symbol: 'none',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: `${colorBox2[index]}0.08)`, // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: `${colorBox2[index]}0)`, // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },
          color: [colorBox[index]],
          data: item.data,
        };
      }),
      animation: false,
    };
    setOptions(option as any);
  }
  type DataList = {
    indexName: string;
    indexCode: string;
    unitName: string;
    data: any[];
    XAxis: string[];
  };
  const loading = ref(false);
  const indexList = ref<DataList[]>([]);
  const totalList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      icon: string;
      value: number | null;
    }[]
  >([]);

  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }
  function disabledDateMonth(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('month');
  }
  async function getIndicatorList() {
    const res = await getSenceGroupTreeWithGroupCode({
      groupCode: dateType.value,
      factoryId: 1,
    });
    if (res.length) {
      indicator.value = res.find((item) => item.groupCode)?.groupCode || '';
      indicatorList.value = res.map((item) => ({
        value: item.groupCode,
        label: item.groupName,
      }));
      getData();
    }
  }
  async function getTimeList() {
    const res = await listSenceGroupByParent({
      // dhzl2_dlph
      groupCode: 'dhzl2_dhtj',
      factoryId: 1,
      platformld: 1,
    });
    console.log('res.data', res);
    if (Object.keys(res).length) {
      dateType.value = Object.keys(res)[0];
      dataList.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      getIndicatorList();
    }
  }
  const getData = async () => {
    const startDateTime =
      dataList.value.find((item) => item.value === dateType.value)?.label === '日'
        ? dayjs(dayDate.value).format('YYYY-MM-DD 00:00:00')
        : dayjs(monthDate.value).startOf('month').format('YYYY-MM-DD 00:00:00');
    const endDateTime =
      dataList.value.find((item) => item.value === dateType.value)?.label === '日'
        ? dayjs(dayDate.value).format('YYYY-MM-DD 23:59:59')
        : dayjs(monthDate.value).endOf('month').format('YYYY-MM-DD 23:59:59');
    const params = {
      startDateTime: startDateTime,
      endDateTime: endDateTime,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      resourceInterfaceId: '5',
      jsConvert: true,
      groupCode: indicator.value,
      paramsData: JSON.stringify(params),
    };

    const { data } = await callResourceFunction(paramData);
    // console.log('电耗统计', data);
    if (data && data.length) {
      interface arrItem {
        collectDateTime?: string;
        value?: number;
      }
      let XAxisData: string[] = [];
      if (dataList.value.find((item) => item.value === dateType.value)?.label === '日') {
        let x = 0;
        while (x < 24) {
          if (x < 10) {
            XAxisData.push(`0${x}:00`);
          } else {
            XAxisData.push(`${x}:00`);
          }
          x++;
        }
      } else if (dataList.value.find((item) => item.value === dateType.value)?.label === '月') {
        let x = 1;
        let maxD;
        if (
          dayjs(monthDate.value).endOf('month').format('DD') >
          dayjs(monthDate.value).subtract(1, 'month').endOf('month').format('DD')
        ) {
          maxD = dayjs(monthDate.value).endOf('month').format('DD');
        } else {
          maxD = dayjs(monthDate.value).subtract(1, 'month').endOf('month').format('DD');
        }
        maxD = Number(maxD) + 1;
        while (x < maxD) {
          if (x < 10) {
            XAxisData.push(`0${x}`);
          } else {
            XAxisData.push(`${x}`);
          }
          x++;
        }
      }
      const newData: any = [];
      const XAxis = XAxisData.map((i) => {
        return dataList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? i
          : `${i}日`;
      });
      if (data.length) {
        data.forEach((item) => {
          newData.push({
            indexName: item.indexName,
            indexCode: item.indexCode,
            unitName: item.unitName,
            data: addXais(XAxisData, item.data),
            XAxis: XAxis,
          });
        });
      }
      function addXais(data: string[], data2: []) {
        return data.map((item) => {
          const copyArr: arrItem[] = data2.filter((i: arrItem) => {
            if (dataList.value.find((item) => item.value === dateType.value)?.label === '日') {
              return item === dayjs(i.collectDateTime).format('HH:mm');
            } else if (
              dataList.value.find((item) => item.value === dateType.value)?.label === '月'
            ) {
              return item === dayjs(i.collectDateTime).format('DD');
            }
          });
          return copyArr.length > 0 ? copyArr[0]?.value : '';
        });
      }
      totalList.value = data.map((item, index) => {
        return {
          indexName: `${item.indexName}`,
          indexCode: item.indexCode,
          unitName: item.unitName,
          icon: index % 2 === 0 ? icon1 : icon2,
          value: item.total
            ? item.total
            : // item.data.length
              //   ? Number(
              //       item.data
              //         .map((i) => i.value)
              //         .reduce((prev, cur) => {
              //           return prev + cur;
              //         }, 0),

              //     ).toFixed(2)
              null,
        };
      });
      indexList.value = newData;
    }

    handleSetVisitChart();
  };

  // const { pause, resume } = useIntervalFn(getIndicatorList, 60 * 1000);
  // const userStore = useUserStore();
  // const token = computed(() => userStore.getToken);
  // watch(
  //   () => token.value,
  //   (newVal) => {
  //     if (!newVal) {
  //       pause();
  //     }
  //   },
  // );

  watch(
    () => dayDate.value,
    async (newVal) => {
      console.log('newValArr=>日', newVal);
      // pause();
      await getIndicatorList();
      // resume();
    },
  );

  watch(
    () => monthDate.value,
    async (newVal) => {
      console.log('newVal=>月', newVal);
      // pause();
      await getIndicatorList();
      // resume();
    },
  );

  watch(
    () => dateType.value,
    async (newVal) => {
      console.log('newVal=>日期类型', newVal);
      monthDate.value = dayjs().subtract(0, 'month').format('YYYY-MM');
      dayDate.value = dayjs().format('YYYY-MM-DD');
      // pause();
      await getIndicatorList();
      // resume();
    },
  );

  onMounted(() => {
    // resume();
    getTimeList();
  });

  // onActivated(() => {
  //   resume();
  // });

  // onUnmounted(() => {
  //   pause();
  // });

  // onDeactivated(() => {
  //   pause();
  // });
</script>
<style lang="less" scoped>
  .ant-card {
    height: 100%;

    :deep(.ant-card-head) {
      padding: 0 16px;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      min-height: 48px;
      border-bottom: 1px solid #e9e9e9;
    }

    :deep(.ant-card-body) {
      border-top: 1px solid #e9e9e9;
      padding: 16px;
      height: calc(100% - 48px);
      overflow: auto;
    }
  }

  .extra-wrap {
    :deep(.ant-select) {
      margin-right: 16px;

      // .ant-select-selector {
      //   width: 80px;
      // }
    }
  }

  .total-list {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    // height: 56px;
    gap: 17px;

    .item {
      height: 100%;
      flex: 1;
      // max-width: calc(33.3% - 8.5px);
      background: rgba(11, 98, 203, 0.08);
      border-radius: 4px 4px 4px 4px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 8px;

      .icon {
        width: 40px;
        height: 40px;

        // img {
        //   width: 100%;
        // }
      }

      .content {
        display: flex;
        flex: 1;
        /* margin-left: 16px; */
        padding: 20px 16px;
        justify-content: space-between;
        align-items: center;

        .name {
          font-weight: 500;
          font-size: 14px;
          color: #333333;
          line-height: 15px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .value {
          font-size: 1rem;

          font-weight: 600;
          color: #333333;
          display: flex;
          align-items: flex-end;
          line-height: 1;

          .number-value {
            font-family: D-DIN-PRO;
            display: inline-block;
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .unit {
            margin-left: 8px;
            display: inline-block;
            font-size: 14px;
            font-weight: 400;
            color: #666;
          }
        }
      }
    }
  }

  .empty-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #999;
  }
</style>
