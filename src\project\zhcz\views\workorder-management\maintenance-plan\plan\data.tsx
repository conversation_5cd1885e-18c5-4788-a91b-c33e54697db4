import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Form/index';
import dayjs, { Dayjs } from 'dayjs';
import { h } from 'vue';
import { HChooseUser } from '/@zhcz/components/HChooseUser';

export const columns: BasicColumn[] = [
  {
    title: '计划名称',
    dataIndex: 'planName',
  },
  {
    title: '设备类别',
    dataIndex: 'eqTypeName',
  },
  {
    title: '保养类别',
    dataIndex: 'maintenanceTypeName',
  },
  {
    title: '是否启用',
    dataIndex: 'isEnabled',
  },
  // {
  //   title: '维护保养项目',
  //   dataIndex: 'maintenanceProjectNameList',
  // },
  {
    title: '执行计划',
    dataIndex: 'executionDescription',
    width: 300,
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'eqType',
    label: '设备类别',
    component: 'Input',
    slot: 'eqType',
    colProps: { span: 6 },
    labelWidth: 68,
  },
  {
    field: 'maintenanceTypeId',
    label: '保养类别',
    component: 'Input',
    slot: 'type',
    colProps: { span: 6 },
  },
];
export const schemas: FormSchema[] = [
  {
    field: 'blank1',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    render: () => {
      return h(
        'div',
        {
          style: {
            borderBottom: '1px solid #eeeff1',
            paddingBottom: '6px',
            display: 'flex',
            alignItems: 'center',
          },
        },
        [
          h(
            'span',
            {
              style: {
                width: '4px',
                height: '14px',
                background: 'var(--theme-color)',
                display: 'inline-block',
              },
            },
            '',
          ),
          h(
            'span',
            { style: { paddingLeft: '8px', fontSize: '16px', fontWeight: 600 } },
            '维保计划',
          ),
        ],
      );
    },
  },
  {
    field: 'planFactoryId',
    component: 'Select',
    label: '单位',
    required: true,
    slot: 'planFactoryId',
    colProps: {
      span: 12,
    },
  },

  {
    field: 'maintenanceTypeId',
    component: 'Select',
    label: '保养类别',
    required: true,
    slot: 'name',
    colProps: {
      span: 12,
    },
  },

  {
    field: 'type',
    component: 'ApiSelect',
    label: '保养类型',
    required: true,
    slot: 'type',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'planName',
    component: 'Input',
    label: '维保名称',
    slot: 'planName',
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    field: 'eqType',
    component: 'Select',
    label: '设备类别',
    slot: 'eqType',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'eqpList',
    component: 'Input',
    label: '保养设备',
    slot: 'project-table',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'blank2',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    render: () => {
      return h(
        'div',
        {
          style: {
            borderBottom: '1px solid #eeeff1',
            paddingBottom: '6px',
            display: 'flex',
            alignItems: 'center',
          },
        },
        [
          h(
            'span',
            {
              style: {
                width: '4px',
                height: '14px',
                background: 'var(--theme-color)',
                display: 'inline-block',
              },
            },
            '',
          ),
          h(
            'span',
            { style: { paddingLeft: '8px', fontSize: '16px', fontWeight: 600 } },
            '基本信息',
          ),
        ],
      );
    },
  },
  {
    field: 'dispatchUserIds',
    component: 'HChooseUser',
    label: '派单工人',
    required: true,
    itemProps: {
      autoLink: false,
    },
    render: (data) => {
      const { model, field } = data;

      return h(HChooseUser, {
        placeholder: '请选择派单工人',
        value: model[field],
        'onUpdate:value': (newValue) => {
          // Listen for the `update:modelValue` event
          model[field] = newValue; // Update the parent model
        },
        disabled: false,
      });
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'responsibilityUserIds',
    component: 'HChooseUser',
    label: '工作责任人',
    required: true,
    colProps: {
      span: 12,
    },
    itemProps: {
      autoLink: false,
    },
    render: (data) => {
      const { model, field } = data;

      return h(HChooseUser, {
        placeholder: '请选择工作责任人',
        value: model[field],
        'onUpdate:value': (newValue) => {
          // Listen for the `update:modelValue` event
          model[field] = newValue; // Update the parent model
        },
        disabled: false,
      });
    },
  },
  {
    field: 'executorUserIds',
    component: 'HChooseUser',
    label: '执行人',
    required: true,
    colProps: {
      span: 12,
    },
    itemProps: {
      autoLink: false,
    },
    render: (data) => {
      const { model, field } = data;

      return h(HChooseUser, {
        placeholder: '请选择执行人',
        value: model[field],
        'onUpdate:value': (newValue) => {
          // Listen for the `update:modelValue` event
          model[field] = newValue; // Update the parent model
        },
        disabled: false,
      });
    },
  },
  {
    field: 'acceptanceUserIds',
    component: 'HChooseUser',
    label: '验收人',
    required: true,
    colProps: {
      span: 12,
    },
    itemProps: {
      autoLink: false,
    },
    render: (data) => {
      const { model, field } = data;

      return h(HChooseUser, {
        placeholder: '请选择验收人',
        value: model[field],
        'onUpdate:value': (newValue) => {
          // Listen for the `update:modelValue` event
          model[field] = newValue; // Update the parent model
        },
        disabled: false,
      });
    },
  },
  {
    field: 'scheduledTimes',
    component: 'InputNumber',
    label: '计划工时',
    required: true,
    componentProps: {
      min: 0,
      precision: 0,
      addonAfter: '小时',
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '工单描述',
    required: true,
    colProps: {
      span: 24,
    },
  },
  // {
  //   field: 'reason',
  //   component: 'InputTextArea',
  //   label: '原因分析',
  //   colProps: {
  //     span: 24,
  //   },
  // },

  {
    field: 'blank4',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    render: () => {
      return h(
        'div',
        {
          style: {
            borderBottom: '1px solid #eeeff1',
            paddingBottom: '6px',
            display: 'flex',
            alignItems: 'center',
          },
        },
        [
          h(
            'span',
            {
              style: {
                width: '4px',
                height: '14px',
                background: 'var(--theme-color)',
                display: 'inline-block',
              },
            },
            '',
          ),
          h(
            'span',
            { style: { paddingLeft: '8px', fontSize: '16px', fontWeight: 600 } },
            '设置周期',
          ),
        ],
      );
    },
  },
  {
    field: 'planStartTime',
    component: 'DatePicker',
    label: '开始日期',
    required: true,
    slot: 'planStartTime',
    componentProps: {
      picker: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD 00:00:00',
      allowClear: true,
      showTime: false,
      disabledDate: (current: Dayjs) => current && current < dayjs().startOf('day'),
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'planEndTime',
    component: 'DatePicker',
    label: '结束日期',
    slot: 'planEndTime',
    componentProps: {
      picker: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD 23:59:59',
      allowClear: true,
      showTime: false,
      disabledDate: (current: Dayjs) => current && current < dayjs().startOf('day'),
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'cronExpression',
    component: 'InputTextArea',
    label: '生成计划',
    required: true,
    slot: 'cronExpression',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'executionDescription',
    component: 'InputTextArea',
    label: '计划描述',
    required: true,
    componentProps: {
      disabled: true,
      placeholder: '由cron表达式生成',
      autosize: {
        minRows: 3,
      },
    },
    colProps: {
      span: 24,
    },
  },
];

export const projectColumns: BasicColumn[] = [
  {
    title: '保养设备',
    dataIndex: 'name',
    ellipsis: false,
  },
  {
    title: '上级设备',
    dataIndex: 'parentEquipmentName',
  },
  {
    title: '设备地址',
    dataIndex: 'processLocation',
  },
];
