<template>
  <div class="flex flex-col h-full mx-4 pb-4 bg-#ffffff">
    <div class="search-form">
      <BasicForm @register="registerForm" @submit="handleSubmit()" @reset="handleReset">
        <template #date-slot>
          <div class="flex items-center">
            <RadioGroup
              v-model:value="quickCheckDay"
              :options="dayOptions"
              optionType="button"
              @change="onRadioChange"
            />
            <RangePicker
              style="width: 320px !important; margin-left: 16px"
              :value="searchForm.rangeDate"
              :allow-clear="false"
              :disabled-date="disabledDate"
              @change="onChange"
              @open-change="onOpenChange"
              @calendar-change="onCalendarChange"
            />
            <!-- <div class="flex items-center gap-1">
              <img class="w-16px" src="../assets/images/abnormal.png" alt="" />
              <span style="color: #999999">不超过七天</span>
            </div> -->
            <BasicTitle class="basic-title ml-12px" helpMessage="自定义日期可选范围不超过7天">
              说明
            </BasicTitle>
          </div>
        </template>
        <template #data-set-slot>
          <div class="tag-container">
            <Tag
              v-for="item in dataset"
              :key="item.id"
              :class="{ dataset__active: item.id === activeDataset?.id }"
              closable
              @click="handleActiveDataset(item)"
              @close="handleDeleteDataset(item)"
            >
              {{ item.name }}
            </Tag>
            <a-button
              :disabled="checkedList.length === 0"
              type="second"
              :icon="h(Icon, { icon: 'icon-park-outline:save' })"
              @click="openDatasetModal"
            >
              保存
            </a-button>
          </div>
        </template>
        <template #data-index-slot>
          <div class="tag-container indictor">
            <Tag
              v-for="item in checkedList"
              :key="item.code"
              closable
              @close="handleDeleteIndictor(item)"
            >
              {{ item.name }}
            </Tag>
            <a-button
              type="second"
              :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
              @click="openIndexModal"
            >
              {{ checkedList.length ? '继续添加' : '添加指标' }}
            </a-button>
            <a-button
              :disabled="checkedList.length === 0"
              type="second"
              style="min-width: 72px"
              :icon="h(Icon, { icon: 'icon-park-outline:clear' })"
              @click="handleClear"
            >
              清空
            </a-button>
          </div>
        </template>
      </BasicForm>
    </div>
    <LineChart :data="indexList" :loading="indexLoading" @chart-move="handleChartMove" />
    <TabTable
      ref="tabTableRef"
      :index-data="indexList"
      :event-data="eventList"
      :loading="eventLoading"
      v-model:activeTabKey="activeTabKey"
      @reload="reloadPage"
    />
    <ChoseIndicatorModal
      :api="getMonitorIndexApi"
      :queryParams="queryParams"
      v-model:checkedList="checkedList"
      @register="registerModal"
      @confirm="activeDataset = undefined"
    />
    <CreateDatasetModal
      :checkedList="checkedList"
      @register="registerDatasetModal"
      @success="handleAddDataSet"
    />
  </div>
</template>
<script lang="ts">
  export default { name: 'DataManagementRealTimeMonitoring' };
</script>
<script setup lang="ts">
  import { ref, computed, reactive, h } from 'vue';
  import { RangePicker, RadioGroup, Tag } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { cloneDeep } from 'lodash-es';
  import dayjs, { Dayjs } from 'dayjs';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useModal } from '/@/components/Modal';
  import { ChoseIndicatorModal } from '/@/components/ChoseIndicatorModal';
  import CreateDatasetModal from './CreateDatasetModal.vue';
  import LineChart from './LineChart.vue';
  import TabTable from './TabTable.vue';
  import { searchFormSchema, dayOptions } from './data';
  import {
    getMonitorIndexApi,
    getMonitorIndexListApi,
    getEventListApi,
    getDatasetListApi,
    deleteDatasetListApi,
  } from '/@zhcz/api/data-management';
  import { useDomain } from '/@/locales/useDomain';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { indicatorType } from '/@zhcz/enums/indicator';
  import type {
    IIndicators,
    QueryIndexList,
    IMonitorIndexList,
    IEventList,
    IDatasetList,
  } from '/@zhcz/api/data-management/model';
  import { useEmitt } from '/@/hooks/web/useEmitt';
  import { BasicTitle } from '/@/components/Basic/index';

  defineOptions({ name: 'DataManagementRealTimeMonitoring' });

  type RangeValue = [Dayjs, Dayjs];

  const { getTenantId } = useDomain();
  const { createMessage } = useMessage();

  const [registerForm] = useForm({
    schemas: searchFormSchema,
    showActionButtonGroup: true,
    labelWidth: 68,
    actionColOptions: {
      span: 4,
      style: {
        textAlign: 'right',
      },
    },
  });

  const dataset = ref<IDatasetList[]>([]);
  const activeDataset = ref<IDatasetList>();
  async function getDataSetData() {
    try {
      const data = await getDatasetListApi({
        timeType: 0,
        type: 2,
      });
      dataset.value = data;
      activeDataset.value = dataset.value[0];
      const firstCheckedList = JSON.parse(dataset.value[0]?.content || '[]');
      checkedList.value = firstCheckedList.map((item) => ({
        name: item.indexName,
        code: item.indexCode,
        unit: '',
        tag: '',
      }));
    } catch {
      dataset.value = [];
      activeDataset.value = undefined;
      checkedList.value = [];
    }
  }
  getDataSetData().then(() => {
    handleSubmit(true);
  });

  const [registerDatasetModal, { openModal: openDatasetModalFn }] = useModal();

  function openDatasetModal() {
    // 不能添加更多数据集
    if (dataset.value.length >= 5) {
      createMessage.info('最多只能添加6个数据集');
      return;
    }
    openDatasetModalFn(true, {
      timeType: 0,
      indecatorCodes: checkedList.value,
    });
  }

  function handleAddDataSet() {
    getDataSetData();
  }

  function handleActiveDataset(record: IDatasetList) {
    if (activeDataset.value === record) {
      activeDataset.value = undefined;
      checkedList.value = [];
    } else {
      activeDataset.value = record;
      const firstCheckedList = JSON.parse(record.content || '[]');
      checkedList.value = firstCheckedList.map((item) => ({
        name: item.indexName,
        code: item.indexCode,
        unit: '',
        tag: '',
      }));
    }
  }

  async function handleDeleteDataset(record: IDatasetList) {
    await deleteDatasetListApi(record.id);
    createMessage.success('删除成功');
    getDataSetData();
  }

  const [registerModal, { openModal }] = useModal();
  const queryParams = computed(() => ({
    tenantId: getTenantId.value,
    timeType: indicatorType.实时数据,
  }));
  async function openIndexModal() {
    try {
      openModal(true, []);
    } catch (error) {
      openModal(true, []);
    }
  }

  function handleClear() {
    checkedList.value = [];
    activeDataset.value = undefined;
  }

  const checkedList = ref<IIndicators[]>([]);
  function handleDeleteIndictor(record: IIndicators) {
    checkedList.value = checkedList.value.filter((item) => item.code !== record.code);
    activeDataset.value = undefined;
  }

  const searchForm = reactive<{
    rangeDate: [Dayjs, Dayjs];
  }>({
    rangeDate: [dayjs(), dayjs()],
  });
  const quickCheckDay = ref('今天');
  const hackValue = ref<RangeValue>();
  console.log(' dayjs()', dayjs().endOf('day'));

  function disabledDate(current: Dayjs) {
    if (!searchForm.rangeDate || (searchForm.rangeDate as any).length === 0) {
      return false;
    }

    const tooLate = searchForm.rangeDate[0] && current.diff(searchForm.rangeDate[0], 'days') > 7;
    const tooEarly = searchForm.rangeDate[1] && searchForm.rangeDate[1].diff(current, 'days') > 7;
    return tooEarly || tooLate;
  }

  function onOpenChange(open: boolean) {
    if (open) {
      searchForm.rangeDate = [] as any;
      hackValue.value = [] as any;
    } else {
      hackValue.value = undefined;
    }
  }

  function onChange(val: RangeValue) {
    searchForm.rangeDate = val;
    if (val[0] && val[1]) {
      const endDateTime = val[1];
      const startDateTime = val[0];
      const diff = endDateTime.diff(startDateTime, 'day');
      if (diff >= 1) {
        quickCheckDay.value = '自定义';
      } else {
        const today = dayjs().startOf('day');
        const yesterday = dayjs().subtract(1, 'days');
        const beforeYesterday = dayjs().subtract(2, 'days');
        if (startDateTime.isSame(today, 'day')) {
          quickCheckDay.value = '今天';
        } else if (startDateTime.isSame(yesterday, 'day')) {
          quickCheckDay.value = '昨天';
        } else if (startDateTime.isSame(beforeYesterday, 'day')) {
          quickCheckDay.value = '前天';
        }
      }
    }
  }

  function onCalendarChange(val: RangeValue) {
    searchForm.rangeDate = val;
    if (val[0] && val[1]) {
      const endDateTime = val[1];
      const startDateTime = val[0];
      const diff = endDateTime.diff(startDateTime, 'day');
      if (diff >= 1) {
        quickCheckDay.value = '自定义';
      } else {
        const today = dayjs().startOf('day');
        const yesterday = dayjs().subtract(1, 'days');
        const beforeYesterday = dayjs().subtract(2, 'days');
        if (startDateTime.isSame(today, 'day')) {
          quickCheckDay.value = '今天';
        } else if (startDateTime.isSame(yesterday, 'day')) {
          quickCheckDay.value = '昨天';
        } else if (startDateTime.isSame(beforeYesterday, 'day')) {
          quickCheckDay.value = '前天';
        }
      }
    }
  }

  function onRadioChange(e: any) {
    const val = e.target.value;

    switch (val) {
      case '前天':
        searchForm.rangeDate = [dayjs().subtract(2, 'days'), dayjs().subtract(2, 'days')];
        break;
      case '昨天':
        searchForm.rangeDate = [dayjs().subtract(1, 'days'), dayjs().subtract(1, 'days')];
        break;
      case '今天':
        searchForm.rangeDate = [dayjs(), dayjs()];
        break;
      case '自定义':
        searchForm.rangeDate = [dayjs(), dayjs()];
        break;
      default:
        break;
    }
  }

  function validate(isFirst?: boolean) {
    if (!searchForm.rangeDate.every(Boolean)) {
      createMessage.info('请选择时间区间');
      return false;
    }

    if (!checkedList.value.length) {
      if (!isFirst) createMessage.info('请选择数据指标');
      return false;
    }

    return true;
  }

  function getQueryParams() {
    const { rangeDate } = searchForm;
    const params: QueryIndexList = {
      tenantId: getTenantId.value,
      startDateTime: dayjs(`${dayjs(rangeDate[0]).format('YYYY-MM-DD 00:00:00')}`).valueOf(),
      endDateTime: dayjs(`${dayjs(rangeDate[1]).format('YYYY-MM-DD 23:59:59')}`).valueOf(),
      indecatorCodes: checkedList.value.map((item) => ({
        indexCode: item.code,
        indexName: item.name,
        unit: item.unit,
      })),
      resourceInterfaceId: 3,
    };

    return params;
  }

  const { emitter } = useEmitt();
  const rawIndexList = ref<IMonitorIndexList[]>([]);
  const indexList = ref<IMonitorIndexList[]>([]);
  const eventList = ref<IEventList[]>([]);

  const indexLoading = ref(false);
  const eventLoading = ref(false);
  async function handleSubmit(isFirst?: boolean) {
    const bol = validate(isFirst);

    async function getMonitorIndexListData() {
      let data: IMonitorIndexList[] = [];
      try {
        indexLoading.value = true;
        const params = getQueryParams();
        data = await getMonitorIndexListApi(params);
      } finally {
        rawIndexList.value = data ?? [];
        indexList.value = data ?? [];
        emitter.emit('monitor-index-update');
        indexLoading.value = false;
      }
    }

    async function getEventListData() {
      let data: IEventList[] = [];
      try {
        eventLoading.value = true;
        const params = getQueryParams();
        data = await getEventListApi(params);
      } finally {
        eventList.value = data ?? [];
        emitter.emit('warning-event-update');
        eventLoading.value = false;
      }
    }

    if (bol) {
      getMonitorIndexListData();
      getEventListData();
    }
  }

  function handleReset() {
    searchForm.rangeDate = [dayjs(), dayjs()];
    quickCheckDay.value = '今天';
    if (dataset.value.length) {
      handleActiveDataset(dataset.value[0]);
    }

    handleSubmit();
  }

  async function reloadPage() {
    try {
      const params = getQueryParams();
      const data = await getEventListApi(params);
      eventList.value = data;
    } finally {
    }
  }

  const activeTabKey = ref('数据列表');
  type TabTableType = InstanceType<typeof TabTable>;
  const tabTableRef = ref<TabTableType>();
  function handleChartMove(val: string) {
    activeTabKey.value = '数据列表';

    const infoArr = val.split('-');
    const indexName = infoArr[0];
    const collectTime = infoArr[1];
    const list = cloneDeep(rawIndexList.value);
    const arr = list.filter((i) => i.indexName === indexName);
    const index = arr[0]?.data.findIndex(
      (i) => dayjs(i.collectTime).format('YYYY/MM/DD HH:mm:ss') === collectTime,
    );

    let data = arr[0]?.data ?? [];
    if (index > 10) {
      data = arr[0]?.data?.slice(index - 10, index + 10) || [];
    } else {
      data = arr[0]?.data?.slice(0, 20) || [];
    }
    arr[0].data = data;

    // const newIndexData = arr
    //   .map((item) => {
    //     const result = item.data.map((j) => ({
    //       ...j,
    //       indexName: item.indexName,
    //       unit: item.unit,
    //       id: `${item.indexName}-${dayjs(j.collectTime).format('YYYY/MM/DD HH:mm:ss')}`,
    //     }));
    //     return result;
    //   })
    //   .flat();
    // tabTableRef.value?.setTableData(newIndexData);
    // nextTick(() => {
    //   tabTableRef.value?.scrollTo('bottom');
    //   setTimeout(() => {
    //     tabTableRef.value?.scrollTo(val);
    //   }, 200);
    // });
  }
</script>

<style lang="less" scoped>
  .search-form {
    flex-shrink: 0;
    padding: 16px 16px 0 16px;
    background-color: #ffffff;
    border-radius: 4px 4px 0 0;
    overflow: hidden;

    :deep(.basic-title) {
      font-size: 14px;
      color: #333;
    }

    :deep(.ant-row) {
      .ant-col {
        .ant-form-item {
          margin-bottom: 16px !important;
        }
      }
    }

    :deep(.ant-form) {
      .ant-radio-group {
        .ant-radio-button-wrapper {
          &:not(.ant-radio-button-wrapper-checked) span:not(.ant-radio-button) {
            color: #333;
          }
        }
      }

      .tag-container {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 16px;

        &.indictor .ant-tag {
          cursor: default;

          .ant-tag-close-icon {
            cursor: pointer;
          }
        }

        .ant-tag {
          height: 32px;
          padding: 0 12px;
          // color: #333;
          color: @theme-color;
          cursor: pointer;
          background-color: @theme-color-5p;
          margin-inline-end: 0;
          display: flex;
          align-items: center;
          max-width: 100%;
          border: @theme-color-12p 1px solid;
          border-radius: 4px;
          font-size: 14px;

          &.dataset__active {
            border-color: @theme-color;
          }

          .text {
            font-size: 14px;
          }

          .ant-tag-close-icon {
            font-size: 12px;
            color: @theme-color;
            margin-left: 12px;
          }
        }
      }
    }
  }

  .vben-basic-table {
    :deep(.ant-table-wrapper) {
      margin: 0;
    }
  }
</style>
