import { FormSchema } from '/@/components/Table';
import {
  searchEquipmentTypeOptionsApi,
  searchEquipmentModelOptionsApi,
} from '/@zoology-wzb/api/device';
import { functionalCategoryOptiton, salesStatusOptiton, factoryOptiton, isViewPage } from '../data';
import { ref, computed } from 'vue';

export const formRef1 = ref(); // 第1、第2列表单实例
//使用单位下拉选项（新增、编辑页只显示开始状态的数据）
const factoryOptitonFilterByIsView = computed(() => {
  const res = isViewPage.value
    ? factoryOptiton.value
    : factoryOptiton?.value?.filter((item) => item.isActive == 1);
  return res;
});
const equipmentModeOptions = ref([]);
const validateCode = (_, value) => {
  if (!value) {
    return Promise.reject();
  }
  const varReg = /^[\u0000-\u00FF]+$/;
  if (!varReg.test(value)) {
    return Promise.reject('不能输入含有中文字符');
  }
  return Promise.resolve();
};
/* 第1、第2列表单 */
export const formSchemas: FormSchema[] = [
  {
    field: 'equipmentName',
    component: 'Input',
    label: '设备名称',
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'equipmentCode',
    component: 'Input',
    label: '设备编码',
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
    },
  },
  {
    label: '设备类型',
    field: 'equipmentTypeId',
    component: 'ApiSelect',
    componentProps: {
      api: async () => {
        const res = await searchEquipmentTypeOptionsApi({ equipmentTypeName: '' });
        if (!isViewPage.value && res?.length) {
          res.forEach((item) => {
            item.disabled = item.equipmentTypeStatusKey == '0';
          });
        }
        return res;
      },
      labelField: 'equipmentTypeName',
      valueField: 'id',
      showSearch: true,
      filterOption: (input, option) => {
        return option.label.indexOf(input) >= 0;
      },
      onChange: (val) => {
        formRef1.value.setFieldsValue({
          equipmentModelId: '',
        });
        searchEquipmentModelOptionsApi({
          equipmentTypeId: val,
        }).then((res) => {
          equipmentModeOptions.value = res;
        });
      },
      popupClassName: 'sludge-device-device-ledger-edit-page-equipmentTypeId-select',
    },
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'equipmentModelId',
    component: 'ApiSelect',
    label: '设备型号',
    required: true,
    componentProps: {
      labelField: 'equipmentModelName',
      valueField: 'id',
      // showSearch: true,
      // filterOption: (input, option) => {
      //   return option.label.indexOf(input) >= 0;
      // },
      options: equipmentModeOptions,
    },
    colProps: { span: 12 },
  },
  {
    field: 'usingOrganizationId',
    component: 'ApiSelect',
    label: '使用单位',
    required: true,
    componentProps: {
      labelField: 'name',
      valueField: 'id',
      options: factoryOptitonFilterByIsView,
      // showSearch: true,
      // filterOption: (input, option) => {
      //   return option.label.indexOf(input) >= 0;
      // },
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'salesStatusKey',
    label: '销售状态',
    component: 'ApiSelect',
    componentProps: {
      options: salesStatusOptiton,
    },
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    field: 'productionDate',
    label: '生产日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: false,
      disabledDate: (current) => {
        return current && current > Date.now();
      },
      style: {
        width: '100%',
      },
    },
    colProps: { span: 12 },
  },
  {
    field: 'salesDate',
    label: '销售日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: false,
      disabledDate: (current) => {
        return current && current > Date.now();
      },
      style: {
        width: '100%',
      },
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'power',
    component: 'InputNumber',
    label: '设备功率',
    componentProps: {
      precision: 0,
      addonAfter: 'kW',
    },
    colProps: { span: 12 },
  },
  {
    field: 'price',
    label: '设备售价',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 2,
      addonAfter: '元',
    },
    colProps: { span: 12 },
  },
  {
    slot: 'tagList',
    field: 'tagList',
    label: '设备标签',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    field: 'thumbnailUrlList',
    component: 'HUpload',
    label: '图片',
    componentProps: {
      maxNumber: 4,
      maxSize: 2,
      title: '上传',
    },
    slot: 'thumbnailUrlList',
    colProps: {
      span: 12,
    },
  },
];
// 第2列表单
export const formSchemas2: FormSchema[] = [
  {
    field: 'equipmentExternalId',
    component: 'Input',
    label: '设备标识',
    required: true,
    colProps: { span: 24 },
    rules: [
      {
        required: true,
        trigger: 'blur',
      },
      {
        validator: validateCode,
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'functionalCategoryKey',
    label: '功能类别',
    component: 'ApiSelect',
    componentProps: {
      options: functionalCategoryOptiton,
    },
    required: true,
    colProps: { span: 24 },
  },
  {
    field: 'material',
    component: 'Input',
    label: '设备材质',
    colProps: { span: 24 },
  },
  {
    field: 'dimensions',
    component: 'Input',
    label: '设备尺寸',
    colProps: { span: 24 },
  },
  {
    field: 'usableYears',
    component: 'InputNumber',
    label: '可使用年限',
    componentProps: {
      min: 0,
      precision: 0,
      addonAfter: '年',
    },
    colProps: { span: 24 },
  },
  // {
  //   field: 'sort',
  //   component: 'InputNumber',
  //   label: '排序',
  //   componentProps: {
  //     min: 1,
  //     precision: 0,
  //   },
  //   colProps: { span: 24 },
  // },
  // {
  //   field: 'isImportant',
  //   component: 'Select',
  //   label: '重要资产',
  //   colProps: {
  //     span: 24,
  //   },
  //   componentProps: {
  //     options: [
  //       {
  //         label: '是',
  //         value: true,
  //       },
  //       {
  //         label: '否',
  //         value: false,
  //       },
  //     ],
  //   },
  //   defaultValue: false,
  // },
];
/* 最底下一行表单 */
export const formSchemas3: FormSchema[] = [
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
  },
];
