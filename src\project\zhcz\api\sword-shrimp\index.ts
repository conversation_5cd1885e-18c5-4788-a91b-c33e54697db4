// 剑水蚤
import { defZhczHttp } from '/@/utils/http/axios';

enum Api {
  GetLastOne = '/realTimeData/getLastOne', // 过滤池实时数据
  // /realTimeData/chartData?filterKeys=1-1&dataType=1&startTime=2025-06-27 14:35:01&endTime=2025-06-27 14:35:01
  RealTimeDataChartData = '/realTimeData/chartData', // 曲线

  RealTimeDataGetFilterTree = '/realTimeData/getFilterTree', // 获取滤池树形结构

  // /realTimeData/export?filterKeys=1-1&dataType=1&startTime=2025-06-27 00:00:00&endTime=2025-06-27 23:59:59
  RealTimeDataExport = '/realTimeData/export', // 导出剑水蚤实时数据
}

export const getLastOne = (data) =>
  defZhczHttp.get<any>({
    url: Api.GetLastOne,
    params: data,
  });

export const realTimeDataChartData = (data) =>
  defZhczHttp.post<any>({
    url: Api.RealTimeDataChartData,
    params: data,
  });

export const realTimeDataGetFilterTree = (data) =>
  defZhczHttp.get<any>({
    url: Api.RealTimeDataGetFilterTree,
    params: data,
  });

export const realTimeDataExport = (data) =>
  defZhczHttp.post<any>(
    {
      url: Api.RealTimeDataExport,
      data,
      responseType: 'blob',
    },
    { isTransformResponse: false },
  );

// export const updateMaintenancePlan = (data) =>
//   defZhczHttp.post<any>({
//     url: Api.UpdateMaintenancePlan,
//     data,
//   });

// export const getMaintenancePlanDetail = (data: { id: number }) =>
//   defZhczHttp.get<any>({
//     url: Api.GetMaintenancePlanDetail,
//     params: data,
//   });

// export const multipleDeleteMaintenancePlan = (data: { ids: number[] }) =>
//   defZhczHttp.post<any>({
//     url: Api.MultipleDeleteMaintenancePlan,
//     data,
//   });
