<!-- 左侧刻度栏 -->
<template>
  <div class="early-warning-scale">
    <div class="left-scale">
      <div class="child-bg"></div>
      <div
        class="child-slider"
        :style="{
          bottom: `${scaleData * 2.34}px`,
          border: `3px solid ${scaleColor[Math.floor(scaleData / 30)]}`,
        }"
      ></div>
    </div>
    <div class="right-scale">
      <div class="right-scale__item" v-for="value in scaleNum" :key="value">{{
        value || '--'
      }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  // This component is currently empty and does not contain any logic or data.
  // It can be used as a placeholder or for future development.
  import { ref } from 'vue';
  const scaleData = ref(10); // Placeholder for scale data, can be populated later
  const scaleColor = ['#24d988', '#ffdd00', '#fd9432', '#fd3232'];
  const scaleNum = ['100+', '100', '60', '30', '0']; // Scale numbers
</script>

<style lang="less" scoped>
  .early-warning-scale {
    // Add styles for the early-warning-scale component here
    // width: 76px;
    height: 252px;
    background: rgba(239, 249, 251, 0.64);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #ffffff;
    padding: 10px 8px 8px 8px;
    display: flex;

    .left-scale {
      width: 16px;
      position: relative;

      .child-bg {
        width: 12px;
        height: 234px;
        margin: 0 2px;
        background: linear-gradient(180deg, #fd3232 0%, #fd9432 37%, #ffdd00 71%, #24d988 100%);
        border-radius: 36px 36px 36px 36px;
        border: 1px solid #ffffff;
      }

      .child-slider {
        position: absolute;
        width: 16px;
        height: 16px;
        background: #ffffff;
        border-radius: 36px 36px 36px 36px;
        // border: 3px solid #fd9432;
      }
    }

    .right-scale {
      margin-left: 8px;
      // margin-right: 8px;
      display: flex;
      flex-direction: column;

      .right-scale__item {
        flex: 1;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #000000;
        line-height: 18px;
        text-align: left;
        display: flex;
        align-items: end;

        &:first-child {
          flex: 0;
        }

        // &:last-child {
        //   flex: 0;
        // }
      }
    }
  }
</style>
