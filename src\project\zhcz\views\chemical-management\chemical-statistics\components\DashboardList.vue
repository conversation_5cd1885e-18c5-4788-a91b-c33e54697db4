<template>
  <div
    class="grid gap-y-16px text-[#333] leading-16px min-h-[100%]"
    :style="{ 'grid-template-rows': `repeat(${list.length}, minmax(0, 1fr));` }"
  >
    <div
      v-for="item in list"
      :key="item.indexName"
      class="min-h-63px p-[0px_0px_0px_0px] bg-[rgba(11,98,203,0.08)] rounded-4px justify-items-start items-center grid gap-y-16px grid-cols-[1fr_1fr_1fr_1fr] item"
    >
      <div class="justify-self-start">
        <div class="!text-left font-semibold !mb-0">{{ item.indexName }}</div>
        <!-- <div class="!text-left">
          <span class="text-[16px] font-semibold">{{ item.lastValue }}</span>
          <span class="text-[#666] ml-4px">{{ item.unitName }}</span>
        </div> -->
      </div>
      <!-- <div class="w-1px h-48px h-full bg-[#BEBEBE] justify-self-end !p-0px"> </div> -->
      <!-- <div class=""> </div> -->

      <div class="">
        <div>化验次数</div>
        <div>
          <span class="text-[16px] font-semibold">{{ item.total }}</span>
          <!-- <span class="text-[#666] ml-4px">次</span> -->
        </div>
      </div>

      <div class="">
        <div>达标次数</div>
        <div>
          <span class="text-[16px] font-semibold">{{ item.successCount }}</span>
          <!-- <span class="text-[#666] ml-4px">次</span> -->
        </div>
      </div>

      <div class="">
        <div class="">
          <!-- <Icon
            class="mr-5px"
            color="var(--theme-color)"
            icon="icon-park-outline:cycle-one"
            size="12"
          /> -->
          月达标率
        </div>
        <!-- <div class="ml-17px"> -->
        <div class="">
          <span class="text-[16px] font-semibold">{{ item.successPercent }}</span>
          <span class="text-[#666] ml-4px">%</span>
          <!-- <img
            class="w-12px h-12px align-baseline ml-4px"
            v-if="item.preStatus"
            :src="item.preStatus > 0 ? up : down"
          /> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { defineProps } from 'vue';
  // import { Icon } from '/@/components/Icon';
  // import up from '../assets/img/up.png';
  // import down from '../assets/img/down.png';

  defineProps({
    list: { type: Array, required: true },
  });

  // onMounted(getData);
</script>

<style lang="less" scoped>
  .text_ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .item {
    & > div {
      padding-left: 16px;
      white-space: nowrap;

      div:first-child {
        margin-bottom: 8px;
      }
    }
  }
</style>
