<template>
  <div class="index-list">
    <template v-if="!loading">
      <template v-if="data.length">
        <div
          class="item"
          :class="getItemBindClass(index)"
          v-for="(item, index) in props.data"
          :key="index"
        >
          <div class="item-top">
            <img :src="getUrl(index)" alt="" srcset="" />
            <span>{{ item.name }}</span>
          </div>
          <div class="item-bottom">{{ item.val }} </div>
        </div>
      </template>
      <span v-else>-</span>
    </template>
    <template v-else>
      <div style="background: unset"></div>
    </template>
  </div>
</template>

<script lang="ts" setup>
  import warn1 from './assets/images/warn1.png';
  import warn2 from './assets/images/warn2.png';
  import warn3 from './assets/images/warn3.png';
  const urlBox = [warn1, warn2, warn3];
  const bgBox = ['warn1', 'warn2', 'warn3'];
  function getUrl(index) {
    return urlBox[index];
  }
  function getItemBindClass(index) {
    return bgBox[index];
  }
  interface ListType {
    val: number | null | undefined;
    unit: string;
    name: string;
  }
  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
    data: {
      type: Array as PropType<ListType[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });
</script>

<style lang="less" scoped>
  .index-list {
    padding: 0 0 16px 0;
    display: flex;
    gap: 0 16px;
    justify-content: space-between;

    .item {
      flex: 1;
      padding: 10px 12px;
      font-size: 14px;
      color: #ffffff;
      line-height: 14px;
      text-align: left;

      .item-top {
        display: flex;
        align-items: center;

        & > img {
          width: 18px;
          margin-right: 8px;
        }
      }

      .item-bottom {
        font-weight: 700;
        font-size: 16px;
        margin-top: 8px;
        margin-left: 26px;
      }
    }

    .warn1 {
      background: rgba(229, 0, 0, 0.24);
      border-radius: 4px 4px 4px 4px;
      border: 1px solid rgba(255, 38, 38, 0.32);
    }

    .warn2 {
      background: rgba(255, 133, 19, 0.32);
      border-radius: 4px 4px 4px 4px;
      border: 1px solid rgba(255, 133, 19, 0.32);
    }

    .warn3 {
      background: rgba(229, 183, 0, 0.24);
      border-radius: 4px 4px 4px 4px;
      border: 1px solid rgba(229, 183, 0, 0.32);
    }
  }
</style>
