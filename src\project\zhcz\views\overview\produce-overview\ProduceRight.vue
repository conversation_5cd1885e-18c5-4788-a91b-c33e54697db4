<template>
  <div class="produce-overview-right">
    <BigBoxContainer>
      <!-- 药剂数据，变更为出水水质折线图，标题为出水水质：PH值、COD、SS -->
      <BoxContainer style="height: calc(50% - 4px)">
        <template #header>
          <BoxHeader :title="title1">
            <template #right>
              <DatePicker
                v-model:value="date"
                picker="month"
                class="date-picker big-screen-date-picker"
                popupClassName="big-screen-date-picker-dropdown"
                placeholder="选择日期"
                :allowClear="false"
                :showToday="false"
                style="width: 120px !important"
                :disabledDate="disabledDate"
                @change="handleDateChange"
              />
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div class="container sludge-data">
            <!-- <template v-if="biEventTableLoad">
              <div style="background: unset"></div>
            </template> -->
            <template v-if="biEventTableLoad">
              <cardTop :data="biEventNumData" />
              <tableBottom :data="biEventTableData" className="auto_overy" />
            </template>
            <template v-else>
              <DataEmpty />
            </template>
          </div>
        </template>
      </BoxContainer>
      <!-- 历史数据，变更为生产报警统计图表，近3天，近一周，近一月。报警次数柱状图 -->
      <BoxContainer style="height: calc(50% - 4px)">
        <template #header>
          <BoxHeader :title="title2">
            <template #right>
              <DatePicker
                v-model:value="date2"
                picker="month"
                class="date-picker big-screen-date-picker"
                popupClassName="big-screen-date-picker-dropdown"
                placeholder="选择日期"
                :allowClear="false"
                :showToday="false"
                style="width: 120px !important"
                :disabledDate="disabledDate"
                @change="handleDateChange2"
              />
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div class="container">
            <!-- <template v-if="state.waterLoad">
              <div style="background: unset"></div>
            </template> -->
            <ProduceDataChart
              v-if="state.waterLoad"
              :data="state.waterData"
              :psHeight="100"
              :tooltipWidth="50"
              :time-type="waterValue"
            />
            <DataEmpty v-else />
          </div>
        </template>
      </BoxContainer>
    </BigBoxContainer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, reactive, computed } from 'vue';
  import BigBoxContainer from '../components/box-container/BigBoxContainer.vue';
  import BoxContainer from '../components/box-container/index.vue';
  import BoxHeader from '../components/box-container/BoxHeader.vue';
  import ProduceDataChart from '../components/echarts/ProduceDataChart.vue';
  import cardTop from './components/pdWarningAnalysis/card.vue';
  import tableBottom from './components/pdWarningAnalysis/table.vue';
  import { useIndexList, useTitleList } from '../hooks';
  import { mockWaterQualityData } from './data';
  import { DatePicker } from 'ant-design-vue';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { getBiEventNum, getBiEvent } from '/@zhcz/api/event-center';
  import dayjs from 'dayjs';
  import { flatMap } from 'lodash-es';
  import DataEmpty from '../components/data-empty/index.vue';

  const { indexList } = useIndexList();
  const { titleList } = useTitleList();
  // const sludgeTime = ref('1');
  const waterValue = ref('7');

  const state = reactive({
    sludgeData: [],
    waterData: [],
    waterLoad: true,
  });

  const title1 = computed(() => {
    return '生产报警统计';
    // return titleList.value.title_RT;
  });
  const title2 = computed(() => {
    return titleList.value.title_RB1;
  });
  function disabledDate(current) {
    // 禁止选择当月以后的日期
    return current && current > dayjs().subtract(0, 'month');
  }
  function handleDateChange() {
    getSludgeData();
  }
  function handleDateChange2() {
    getWaterData();
  }
  const date = ref(dayjs().subtract(0, 'month'));
  const date2 = ref(dayjs().subtract(0, 'month'));
  const biEventNumData = ref<{ name: string; unit: string; val: number | null | undefined }[]>([]);
  const biEventTableData = ref<any[]>([]);
  const biEventTableLoad = ref<Boolean>(true);
  const getSludgeData = async () => {
    const params = {
      startTime: dayjs(date.value).startOf('month').format('YYYY-MM-DD 00:00:00'),
      endTime: dayjs(date.value).endOf('month').format('YYYY-MM-DD 23:59:59'),
      factoryId: '1',
    };
    // 查询大屏报警事件列表
    // 查询大屏报警事件统计
    const data = await getBiEvent(params);
    const result = await getBiEventNum(params);
    biEventNumData.value = result;
    biEventTableData.value = data;
    if (data.length) {
      biEventTableLoad.value = true;
    } else {
      biEventTableLoad.value = false;
    }
  };
  const waterDataFlag = ref(false);
  const getWaterData = async () => {
    try {
      // const collDate = Number(waterValue.value) - 1;
      const tempParams = indexList.value.index_RB[0];
      // const endDataTime = dayjs().subtract(1, 'd').format('YYYY-MM-DD 23:59:59');
      const lastDayOfLastMonth = dayjs(date2.value).endOf('month').format('YYYY-MM-DD HH:mm:ss');
      const firstDayOfLastMonth = dayjs(date2.value).startOf('month').format('YYYY-MM-DD HH:mm:ss');
      const params = {
        startDateTime: firstDayOfLastMonth,
        endDateTime: lastDayOfLastMonth,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
        type: '3',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };
      const { data } = await callResourceFunction(paramData);

      if (data && data.length) {
        state.waterLoad = true;
        waterDataFlag.value = true;
        const newData = JSON.parse(JSON.stringify(mockWaterQualityData));
        newData.title = titleList.value.title_RB1;
        newData.chartOptions.xAxis.data = data[0].data.map((item) =>
          dayjs(item.collectDateTime).format('MM-DD'),
        );

        newData.chartOptions.series = data.map((item, index) => {
          const oldItem = mockWaterQualityData.chartOptions.series[index];
          let t_data = mockWaterQualityData.chartOptions.series[index]?.data;
          if (item.data.length) {
            t_data = flatMap(item.data, 'value');
          }
          // console.log('t_data', t_data);
          return {
            ...oldItem,
            name: item.indexName,
            data: t_data,
            unitName: item.unitName,
          };
        });
        state.waterData = newData;
        // console.log('state.waterData', state.waterData);
      } else {
        waterDataFlag.value = false;
        state.waterLoad = false;
        // state.waterData = mockWaterQualityData;
      }
    } catch (_) {
      waterDataFlag.value = false;
      state.waterLoad = false;
    }
  };

  // const handleSludgeChange = () => {
  //   getSludgeData();
  // };

  // const handleWaterChange = () => {
  //   getWaterData();
  // };

  const getData = () => {
    getSludgeData();
    getWaterData();
  };

  watch(
    () => indexList.value,
    () => {
      getData();
    },
  );
</script>

<style lang="less" scoped>
  .produce-overview-right {
    width: 100%;
    height: 100%;

    .container {
      position: relative;
      height: 100%;
      padding: 1rem 0rem 0 0rem;
    }

    .sludge-data {
      display: flex;
      overflow-y: auto;
      flex-direction: column;

      @media screen and (min-width: 1921px) {
        .consumption-item {
          justify-content: center;
        }
      }

      .auto_overy {
        flex: 1;
      }

      &::-webkit-scrollbar {
        width: 0px;
      }
    }

    :deep(.ant-select-single) {
      .ant-select-selector {
        .ant-select-selection-item {
          color: #fff;
        }
      }
    }

    :deep(.ant-picker) {
      .ant-picker-input > input {
        color: #fff;
      }
    }
  }
</style>
