import { iconTextMap } from '../data';
import model4 from '../assets/images/model-produce4.png';
import dayjs from 'dayjs';
import consumptionBlueImg from '../assets/images/consumption-blue.png';
import consumptionYellowLightImg from '../assets/images/consumption-yellow-light.png';
import consumptionYellowDarkImg from '../assets/images/consumption-yellow-dark.png';
import consumptionGreenImg from '../assets/images/consumption-green.png';
export const otherIconData = [
  consumptionBlueImg,
  consumptionYellowLightImg,
  consumptionYellowDarkImg,
  consumptionGreenImg,
  consumptionGreenImg,
  consumptionBlueImg,
];
export const mockConsumptionData = [
  {
    indexName: '石灰单耗',
    value: 0,
    unitName: 'mg/L',
    src: consumptionBlueImg,
  },
  {
    indexName: '碱铝单耗',
    value: 0,
    unitName: 'mg/L',
    src: consumptionYellowLightImg,
  },
  {
    indexName: '次氯酸钠单耗',
    value: 0,
    unitName: 'mg/L',
    src: consumptionYellowDarkImg,
  },
  {
    indexName: '原料帆单耗',
    value: 0,
    unitName: 'mg/L',
    src: consumptionGreenImg,
  },
  {
    indexName: '氢氧化钠单耗',
    value: 0,
    unitName: 'mg/L',
    src: consumptionGreenImg,
  },
  {
    indexName: '活性炭单耗',
    value: 0,
    unitName: 'mg/L',
    src: consumptionBlueImg,
  },
];
export const symbolBlue =
  'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAAXNSR0IArs4c6QAAAKBJREFUKFOFj70OAVEUhGduIxESvUap2G2VConH4A20OtylES1PwBsoSRQq/So0Om9gV6K5Iyfx1+10k/lyZg4BIPZqilqQ6JiXcKA4TD0vtBBOR4mzTNgYUCF6pEYIbDNKwpbkLh1zZeFHUaIBoC7jacjynPXrHHcAJwNIthpetarTrRiwCoD784RLSe59Ifwq/kY+SlgbUH6i/x1Z9OYLuSNZrTekIJkAAAAASUVORK5CYII=';

export const symbolGreen =
  'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAAXNSR0IArs4c6QAAAKFJREFUKFNjZGBgYJDaP1+DhYWli/E/oxOI/5/x/74/f/6UPXNMvMEIkmRlYTv0n+l/M9Ov34tBCv6xscYy/mOs/f3nlx2j3OHFmxgZmXY9tImeApKEAbmjS3IZ/v13ZZQ/tOQL88+v0vdc0z/+////FEgBIyOjmcL++QL/mVmfEFYAsoKBiXH3I+uYyVitQHbkZ3a2RSBFvD9/xcEdScibAHmHYqeIC964AAAAAElFTkSuQmCC';

export const symbolYellow =
  'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAAXNSR0IArs4c6QAAAJxJREFUKFNjZGBgYPh/TFfj/7//XQwMjE4gPgPD/32MTIxljFaXbzCCJf8zHGJk+N/M8OvLYrA8G0/sfwbGWkZGBjvGf0d0NjEyMe5itLo8BaIbasZRndz//xlcGf8d0f3C+P2rNKPrvY////8/BZJmZGQ0+79fQeA/K+8TYhTobGJkZNjNaH1lMlYrUBzJ/m0RWNFPrji4Iwl5EwCu/FtDhtHa3QAAAABJRU5ErkJggg==';

export const mockProduceDataList = [
  {
    indexName: '高锰酸钾单耗',
    value: 0,
    unitName: 'kg/km³',
  },
  {
    indexName: '次氯酸钠单耗',
    value: 0,
    unitName: 'kg/km³',
  },
  {
    indexName: '氢氧化钠单耗',
    value: 0,
    unitName: 'kg/km³',
  },
];
export const mockDrugDataList = [
  {
    indexName: '高锰酸钾单耗',
    unitName: 'kg/km³',
    value: 0,
    percentage: '0%',
  },
  {
    indexName: '次氯酸钠单耗',
    unitName: 'kg/km³',
    value: 0,
    percentage: '0%',
  },
];

function radomNumber(min, max) {
  return Number.parseInt(Math.floor(Math.random() * (max - min + 1) + min));
}

export const mockDrugCenterData = [
  {
    indexName: iconTextMap.get('drug1'),
    value: radomNumber(500, 1000),
    unitName: 'kg',
  },
  {
    indexName: iconTextMap.get('drug2'),
    value: radomNumber(500, 1000),
    unitName: 'kg',
  },
  {
    indexName: iconTextMap.get('drug3'),
    value: radomNumber(500, 1000),
    unitName: 'kg',
  },
];

export const drugCenterEmptyData = [
  {
    indexName: iconTextMap.get('drug1'),
    value: '',
    unitName: 'kg',
  },
  {
    indexName: iconTextMap.get('drug2'),
    value: '',
    unitName: 'kg',
  },
  {
    indexName: iconTextMap.get('drug3'),
    value: '',
    unitName: 'kg',
  },
];

export const mockIndexData1 = [
  {
    indexName: '高锰酸钾单耗',
    value: 5038,
    unitName: 'kg',
  },
  {},
  {
    indexName: '次氯酸钠单耗',
    value: 741,
    unitName: 'kg',
  },
  {
    indexName: '次氯酸钠单耗',
    value: 56,
    unitName: 'kg/km³',
  },
  {},
  {
    indexName: '次氯酸钠单耗',
    value: 9,
    unitName: 'kg/km³',
  },
];

export const mockModelData = [
  {
    src: model4,
    text: '加药间',
    index: mockIndexData1,
  },
];
// 生成近半年的月份数据
const getMon = (n) => {
  return Number(dayjs().subtract(n, 'month').format('MM')) + '月';
};

export const mockDrugCostData = {
  title: '',
  chartOptions: {
    xAxis: {
      data: [getMon(6), getMon(5), getMon(4), getMon(3), getMon(2), getMon(1)],
    },
    series: [
      {
        name: '高锰酸钾',
        color: 'rgba(45, 130, 254, 1)',
        areaColor: ['rgba(45, 130, 254, 1)', 'rgba(45, 130, 254, 0)'],
        data: [
          [getMon(6), 0],
          [getMon(5), 0],
          [getMon(4), 0],
          [getMon(3), 0],
          [getMon(2), 0],
          [getMon(1), 0],
        ],
        unitName: 'kg',
        symbol: symbolBlue,
      },
      {
        name: '次氯酸纳',
        color: 'rgba(31, 195, 164, 1)',
        areaColor: ['rgba(31, 195, 164, 0.3)', 'rgba(31, 195, 164, 0)'],
        data: [
          [getMon(6), 0],
          [getMon(5), 0],
          [getMon(4), 0],
          [getMon(3), 0],
          [getMon(2), 0],
          [getMon(1), 0],
        ],
        unitName: 'kg',
        symbol: symbolGreen,
      },
      {
        name: '氢氧化钠',
        color: 'rgba(254, 197, 45, 1)',
        areaColor: ['rgba(254, 197, 45, 0.3)', 'rgba(254, 197, 45, 0)'],
        data: [
          [getMon(6), 0],
          [getMon(5), 0],
          [getMon(4), 0],
          [getMon(3), 0],
          [getMon(2), 0],
          [getMon(1), 0],
        ],
        unitName: 'kg',
        symbol: symbolYellow,
      },
      {
        name: '石灰',
        color: 'rgba(254, 145, 45, 1)',
        areaColor: ['rgba(254, 145, 45, 0.3)', 'rgba(254, 145, 45, 0)'],
        data: [
          [getMon(6), 0],
          [getMon(5), 0],
          [getMon(4), 0],
          [getMon(3), 0],
          [getMon(2), 0],
          [getMon(1), 0],
        ],
        unitName: 'kg',
        symbol: symbolBlue,
      },
    ],
  },
};
function getChartXAxisData(number) {
  return dayjs().subtract(number, 'day').format('MM-DD');
}
export const mockChartData = {
  title: '近七天数据曲线',
  chartOptions: {
    xAxis: {
      data: [
        getChartXAxisData(7),
        getChartXAxisData(6),
        getChartXAxisData(5),
        getChartXAxisData(4),
        getChartXAxisData(3),
        getChartXAxisData(2),
        getChartXAxisData(1),
      ],
    },
    yAxis: {
      nameTextStyle: {
        padding: [0, 42, 0, 42],
      },
    },
    series: [
      {
        name: '石灰',
        color: 'rgba(45, 130, 254, 1)',
        data: [],
        unitName: 'kg/km³',
        areaColor: ['rgba(45, 130, 254, 0.3)', 'rgba(45, 130, 254, 0)'],
      },
      {
        name: '碱铝',
        color: 'rgba(31, 195, 164, 1)',
        data: [],
        unitName: 'kg/km³',
        areaColor: ['rgba(31, 195, 164, 0.3)', 'rgba(31, 195, 164, 0)'],
      },
      {
        name: '次氯酸钠',
        color: 'rgba(254, 197, 45, 1)',
        data: [],
        unitName: 'kg/km³',
        areaColor: ['rgba(254, 197, 45, 0.3)', 'rgba(254, 197, 45, 0)'],
      },
      {
        name: '原料帆',
        color: 'rgba(254, 145, 45, 1)',
        data: [],
        unitName: 'kg/km³',
        areaColor: ['rgba(254, 145, 45, 0.3)', 'rgba(254, 145, 45, 0)'],
      },
      {
        name: '氢氧化钠',
        color: 'rgba(255, 255, 255, 0.56)',
        data: [],
        unitName: 'kg/km³',
        areaColor: ['rgba(254, 197, 45, 0.3)', 'rgba(254, 197, 45, 0)'],
      },
      {
        name: '活性炭',
        color: 'rgba(255, 255, 255, 0.56)',
        data: [],
        unitName: 'kg/km³',
        areaColor: ['rgba(254, 145, 45, 0.3)', 'rgba(254, 145, 45, 0)'],
      },
    ],
  },
};
