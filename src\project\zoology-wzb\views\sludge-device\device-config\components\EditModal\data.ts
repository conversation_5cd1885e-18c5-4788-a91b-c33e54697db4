import { FormSchema } from '/@/components/Table';
import { getDictTypeListApi } from '/@/api/admin/dict';
import { ref } from 'vue';

export const editModalFormRef = ref();
export const editModalSchemas: FormSchema[] = [
  {
    field: 'configName',
    component: 'Input',
    label: '业务名称',
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    field: 'dataTypeKey',
    label: '数据类型',
    required: true,
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: 'data_type' },
      allowClear: false,
      onChange: (val) => {
        editModalFormRef.value?.updateSchema([
          {
            field: 'styleConfigList',
            show: val == '1',
          },
          {
            field: 'normalStyle',
            show: val == '2',
            required: val == '2',
          },
          {
            field: 'alertStyle',
            show: val == '2',
            required: val == '2',
          },
        ]);
      },
    },
    defaultValue: '1',
    colProps: { span: 12 },
  },
  {
    field: 'associatedMetricList',
    label: '关联指标',
    component: 'Select',
    required: true,
    colProps: { span: 24 },
    slot: 'indicators',
  },
  {
    field: 'styleConfigList',
    label: '样式配置',
    component: 'Input',
    slot: 'style',
    rules: [
      {
        trigger: ['change', 'blur'],
        validator: async (_rule, arr) => {
          if (editModalFormRef.value.formModel.dataTypeKey != '1') return Promise.resolve();
          const isFilled = arr?.every?.((item) => Object.values(item)?.every((v) => v));
          if (!isFilled || !arr?.length) {
            return Promise.reject('请输入样式配置');
          }
          return Promise.resolve();
        },
      },
    ],
    colProps: {
      span: 24,
    },
    componentProps: {
      defaultValue: [],
    },
  },
  {
    field: 'normalStyle',
    label: '阈值内样式',
    component: 'Select',
    required: true,
    colProps: { span: 12 },
    slot: 'normalStyle',
  },
  {
    field: 'alertStyle',
    label: '阈值外样式',
    component: 'Select',
    required: true,
    colProps: { span: 12 },
    slot: 'alertStyle',
  },
];
