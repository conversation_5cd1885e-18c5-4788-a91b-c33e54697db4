<template>
  <Card :bordered="false" dis-hover>
    <template #title>
      <div>峰平谷用电</div>
    </template>
    <template #extra>
      <div class="flex extra-wrap">
        <Select
          v-model:value="indicator"
          @change="handleChangeIndicator"
          v-if="indicatorList.length"
        >
          <SelectOption v-for="item in indicatorList" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
        <DatePicker
          valueFormat="YYYY-MM-DD"
          format="YYYY-MM-DD"
          v-model:value="date"
          placeholder="请选择"
          style="width: 200px"
          :allowClear="false"
          :disabledDate="disabledDate"
        />
      </div>
    </template>
    <div class="echart-container" v-loading="loading" v-if="indexList.length">
      <div class="total-list">
        <div class="item" v-for="(item, index) in pieIndexList" :key="index">
          <!-- <div class="icon"><img :src="item.icon" /></div> -->
          <div class="content">
            <div :class="['name', `beforecor_${index}`]">
              {{ item.indexName }}
            </div>
            <div class="value">
              <div class="number-value" v-if="item.value === null" style="font-weight: 400">-</div>
              <Tooltip @mouseenter="showTooltip" v-else>
                <template #title>{{ item.value }}{{ item.unitName }}</template>
                <div class="number-value">{{ item.value ? item.value : '' }}</div>
              </Tooltip>
              <!-- <span class="unitLevel">{{ getLevel(Number(item.value)) }}</span> -->
              <span class="unit" v-if="item.value !== null">{{ item.unitName }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="pie"><PeakFlatValleyPieEchart :data="pieIndexList" /></div> -->
      <div class="bar" style="height: calc(100% - 54px); min-height: 112px; margin-top: 6px">
        <PeakFlatValleyBarEchart :data="indexList" :priceList="priceList" />
      </div>
    </div>
    <div v-else class="empty-box">
      <HEmpty />
    </div>
  </Card>
</template>
<script setup lang="ts">
  import { Card, DatePicker, Select, SelectOption, Tooltip } from 'ant-design-vue';
  import { ref, watch, onMounted, reactive } from 'vue';
  import dayjs from 'dayjs';
  import { getFpgIndicatorCodeList, getFpgDataList } from '/@zhcz/api/scenes-group';
  // import { useIntervalFn } from '@vueuse/core';
  // import { useUserStore } from '/@/store/modules/user';
  import PeakFlatValleyBarEchart from './PeakFlatValleyBarEchart.vue';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  // import { roundAndConvert } from '/@zhcz/utils/number';

  import HEmpty from '/@/components/HEmpty/index.vue';

  const props = defineProps({
    itemVal: {
      type: Object,
      default: () => {},
    },
  });
  function showTooltip(e) {
    if (e.target.clientWidth >= e.target.scrollWidth) {
      e.target.style.pointerEvents = 'none';
    }
  }
  watch(
    () => props.itemVal,
    (val) => {
      if (val) {
        // console.log('val', val);
        // indicator.value = val;
        if (val.date && val.value) {
          date.value = val.date;
          if (indicatorList.value.find((item) => item.tag === val.value)?.value) {
            indicator.value = indicatorList.value.find((item) => item.tag === val.value)?.value;
          }
          getData();
        }
      }
    },
  );
  const indicatorList = ref<{ value: string; label: string; unit: string; tag: string }[]>([]);
  const indicator = ref('');
  const date = ref(dayjs().format('YYYY-MM-DD'));
  const loading = ref(false);
  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      data: any[];
    }[]
  >([]);
  const pieIndexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);
  const priceList = ref<{ label: string; value: number; remarks: number }[]>([]);
  const getprice = async () => {
    try {
      const data = await getDictTypeListApi({
        type: 'dhdj',
      });
      if (data.length) {
        priceList.value = data.map((item) => {
          const { label, value, remarks } = item;
          if (Number(label) < 10) {
            return {
              value: Number(value),
              label: `0${label}:00`,
              remarks: Number(remarks),
            };
          } else {
            return {
              value: Number(value),
              label: `${label}:00`,
              remarks: Number(remarks),
            };
          }
        });
      }
      // console.log('priceList.value', priceList.value);
    } catch (err) {}
  };
  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }

  const getIndicatorList = async () => {
    const data = await getFpgIndicatorCodeList();
    if (data && data.length) {
      indicatorList.value = data.map((item) => {
        return {
          label: item.name,
          value: item.code,
          unit: item.unit,
          tag: item.tag,
        };
      });
      indicator.value = indicatorList.value[0].value;
    }
  };
  const dJata = reactive<any>({
    data: {},
  });
  const chartsData = ref<any>([]);
  const getData = async () => {
    try {
      loading.value = true;
      if (!indicator.value) return;
      const params = {
        startDateTime: dayjs(date.value).startOf('day').valueOf(),
        endDateTime: dayjs(date.value).endOf('day').valueOf(),
        timeType: 2,
        indecatorCodeList: [
          {
            indexCode: indicator.value,
            indexName: indicatorList.value.find((i) => i.value === indicator.value)?.label,
            unit: indicatorList.value.find((i) => i.value === indicator.value)?.unit,
          },
        ],
      };
      const result = await getFpgDataList(params);
      const data = priceList.value.map((item) => {
        const row = result.data.find((i) => {
          return item.label === dayjs(i.provideTime).format('HH:mm');
        });
        const { codeRecord, provideTime, stage } = row;
        return {
          codeRecord,
          provideTime,
          remarks: item.remarks, // 不查字典
          stage, // 接口直接返回
          value: codeRecord.length ? codeRecord[0].val : 0,
        };
      });
      chartsData.value = data;
      console.log('峰平谷电耗', data);
      if (data && data.length) {
        const newData: {
          indexName: string;
          indexCode: string;
          unitName: string;
          data: any[];
        }[] = [];
        const getVal = (arr) => {
          const data = arr
            .map((item) => {
              return item.val === '' || item.val === null ? null : Number(item.val);
            })
            .reduce((prev, cur) => {
              return prev + cur;
            });
          return data !== null ? data.toFixed(2) : null;
        };
        // 峰
        const fD = data.filter((i) => i.stage === 1);
        if (fD.length) {
          const fData = {
            indexName: '峰',
            indexCode: fD[0].codeRecord[0]?.indicatorCode,
            unitName: fD[0].codeRecord[0]?.unit,
            data: data.map((item) => {
              return {
                collectDateTime: item.provideTime.slice(11, 16),
                value:
                  item.stage === 1
                    ? item.codeRecord && item.codeRecord.length
                      ? getVal(item.codeRecord)
                      : null
                    : null,
              };
            }),
          };
          newData.push(fData);
        }
        // 峰尖
        const fJ = data.filter((i) => i.stage === 2);
        if (fJ.length) {
          const fData = {
            indexName: '峰尖',
            indexCode: fJ[0].codeRecord[0]?.indicatorCode,
            unitName: fJ[0].codeRecord[0]?.unit,
            data: data.map((item) => {
              return {
                collectDateTime: item.provideTime.slice(11, 16),
                value:
                  item.stage === 2
                    ? item.codeRecord && item.codeRecord.length
                      ? getVal(item.codeRecord)
                      : null
                    : null,
              };
            }),
          };
          newData.push(fData);
        }

        // 平
        const pD = data.filter((i) => i.stage === 3);
        if (pD.length) {
          const pData = {
            indexName: '平',
            indexCode: pD[0].codeRecord[0]?.indicatorCode,
            unitName: pD[0].codeRecord[0]?.unit,
            data: data.map((item) => {
              return {
                collectDateTime: item.provideTime.slice(11, 16),
                value:
                  item.stage === 3
                    ? item.codeRecord && item.codeRecord.length
                      ? getVal(item.codeRecord)
                      : null
                    : null,
              };
            }),
          };
          newData.push(pData);
        }

        // 谷
        const gD = data.filter((i) => i.stage === 4);
        if (gD.length) {
          const gData = {
            indexName: '谷',
            indexCode: gD[0].codeRecord[0]?.indicatorCode,
            unitName: gD[0].codeRecord[0]?.unit,
            data: data.map((item) => {
              return {
                collectDateTime: item.provideTime.slice(11, 16),
                value:
                  item.stage === 4
                    ? item.codeRecord && item.codeRecord.length
                      ? getVal(item.codeRecord)
                      : null
                    : null,
              };
            }),
          };
          newData.push(gData);
        }
        // 谷底
        const gd = data.filter((i) => i.stage === 5);
        if (gd.length) {
          const gData = {
            indexName: '谷底',
            indexCode: gd[0].codeRecord[0]?.indicatorCode,
            unitName: gd[0].codeRecord[0]?.unit,
            data: data.map((item) => {
              return {
                collectDateTime: item.provideTime.slice(11, 16),
                value:
                  item.stage === 5
                    ? item.codeRecord && item.codeRecord.length
                      ? getVal(item.codeRecord)
                      : null
                    : null,
              };
            }),
          };
          newData.push(gData);
        }
        // 电价
        if (data) {
          dJata.data = {
            indexName: '电价',
            indexCode: 'dj',
            unitName: '元',
            data: priceList.value.map((item) => {
              // const row = data.find((i) => {
              //   return item.label === dayjs(i.provideTime).format('HH:mm');
              // });
              // const { codeRecord } = row;
              return {
                collectDateTime: item.label,
                value: item.value ? item.value : null,
                // ? (Number(codeRecord[0].val) * item.value).toFixed(2)
                // : null,
              };
            }),
          };
          // console.log('总电价', dJata.data);
          newData.push(dJata.data);
        }
        // console.log('indexList.value', newData);
        indexList.value = newData;
        pieIndexList.value = newData.map((item) => {
          let value;
          // const listArr = item.data.map((val) => val.value).filter((i) => i && i !== 0);
          if (item.indexName === '峰' || item.indexName === '平' || item.indexName === '谷') {
            if (item.indexName === '峰') {
              value = Number(result.fengzhi) ? Number(result.fengzhi) : '-';
            } else if (item.indexName === '平') {
              value = result.pingzhi || '-';
              value = Number(result.pingzhi) ? Number(result.pingzhi) : '-';
            } else if (item.indexName === '谷') {
              value = Number(result.guzhi) ? Number(result.guzhi) : '-';
            }
            // 取平均值
            // if (listArr.length) {
            //   value = roundAndConvert(
            //     listArr.reduce((a, b) => {
            //       return Number(a) + Number(b);
            //     }, 0) / listArr.length,
            //     2,
            //   );
            // }
          } else if (item.indexName === '峰尖') {
            // 取极大值
            // if (listArr.length) {
            //   value = Math.max.apply(null, listArr);
            // }
            value = Number(result.fengjian) ? Number(result.fengjian) : '-';
          } else if (item.indexName === '谷底') {
            // 取极小值
            // if (listArr.length) {
            //   value = Math.min.apply(null, listArr);
            // }
            value = Number(result.gudizhi) ? Number(result.gudizhi) : '-';
          } else if (item.indexName === '电价') {
            // 取当前值
            value =
              item.data.find((val) => val.collectDateTime.slice(0, 2) === dayjs().format('HH'))
                ?.value || '-';
          }
          return {
            indexName: item.indexName === '电价' ? '当前电价' : item.indexName,
            indexCode: item.indexCode,
            unitName: item.unitName,
            value: value ? value : '-',
          };
        });
      }
    } finally {
      loading.value = false;
    }
  };

  // const { pause, resume } = useIntervalFn(getData, 60 * 1000);

  const handleChangeIndicator = async () => {
    // pause();
    await getData();
    // resume();
  };

  watch(
    () => date.value,
    async () => {
      // pause();
      await getData();
      // resume();
    },
  );

  onMounted(async () => {
    await getprice();
    await getIndicatorList();
    await getData();
  });

  // const userStore = useUserStore();
  // const token = computed(() => userStore.getToken);
  // watch(
  //   () => token.value,
  //   (newVal) => {
  //     if (!newVal) {
  //       pause();
  //     }
  //   },
  // );

  // onMounted(() => {
  //   resume();
  // });

  // onActivated(() => {
  //   resume();
  // });

  // onUnmounted(() => {
  //   pause();
  // });

  // onDeactivated(() => {
  //   pause();
  // });
</script>
<style lang="less" scoped>
  .ant-card {
    height: 100%;

    :deep(.ant-card-head) {
      padding: 0 16px;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      min-height: 48px;
      border-bottom: 1px solid #e9e9e9;
    }

    :deep(.ant-card-body) {
      padding: 16px;
      height: calc(100% - 48px);
      overflow: auto;
    }
  }

  .extra-wrap {
    :deep(.ant-select) {
      margin-right: 16px;

      .ant-select-selector {
        width: 157px;
      }
    }
  }

  .echart-container {
    display: flex;
    height: 100%;
    flex-direction: column;

    .total-list {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      // height: 56px;
      gap: 17px;

      .item {
        height: 100%;
        flex: 1;
        background: rgba(11, 98, 203, 0.08);
        border-radius: 4px 4px 4px 4px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 8px;

        .icon {
          width: 40px;
          height: 40px;

          // img {
          //   width: 100%;
          // }
        }

        .content {
          display: flex;
          flex-direction: column;
          flex: 1;
          padding: 16px;
          justify-content: space-between;
          align-items: start;
          gap: 8px 0;

          .name {
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 15px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &::before {
              content: '';
              display: inline-block;
              width: 6px;
              height: 6px;
              border-radius: 3px;
              margin-right: 8px;
              vertical-align: middle;
            }
          }

          .beforecor_0::before {
            background: rgba(46, 196, 255, 0.8);
          }

          .beforecor_1::before {
            background: rgba(67, 92, 255, 0.8);
          }

          .beforecor_2::before {
            background: rgba(34, 205, 128, 0.8);
          }

          .beforecor_3::before {
            background: rgba(255, 140, 46, 0.8);
          }

          .beforecor_4::before {
            background: rgba(237, 210, 38, 0.8);
          }

          .beforecor_5::before {
            background: rgba(255, 82, 43, 0.8);
          }

          .value {
            padding-left: 14px;
            font-size: 1rem;

            font-weight: 600;
            color: #333333;
            display: flex;
            align-items: flex-end;
            line-height: 1;

            .number-value {
              font-family: D-DIN-PRO;
              display: inline-block;
              max-width: 120px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .unit {
              margin-left: 8px;
              display: inline-block;
              font-size: 14px;
              font-weight: 400;
              color: #666;
            }
          }
        }
      }
    }

    .bar {
      flex: 1;
      // height: 100%;
    }
  }

  .empty-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #999;
  }
</style>
