# HLCardComponent 组件库文档

## 组件库概述

HLCardComponent 是一个卡片组件库，提供了各种类型的卡片组件用于数据展示。组件库包含基础组件、图表组件和组合卡片组件，适用于各种数据展示场景。

## 文件结构

```
/src/project/aoa/components/HLCardComponent/
├── index.tsx                    # 组件库入口文件
├── src/
│   ├── basicComponents/         # 基础组件
│   │   ├── Ranking.vue          # 排名组件
│   │   ├── HorizontaSmallSquare.vue # 水平小方块组件
│   │   ├── EnergySummary.vue    # 能耗统计组件
│   │   ├── DrugSummary.vue      # 药耗统计组件
│   │   ├── Loading.vue          # 加载组件
│   │   ├── Empty.vue            # 空状态组件
│   │   └── RankingSimple.vue    # 简单排名组件
│   ├── charts/                  # 图表基础组件
│   │   ├── bar/                 # 柱状图
│   │   │   └── BarEcharts.vue
│   │   ├── line/                # 折线图
│   │   │   └── LineEcharts.vue
│   │   └── pie/                 # 饼图
│   │       └── PieEcharts.vue
│   └── combinationCards/        # 组合卡片组件
│       ├── index.ts             # 组合卡片入口文件
│       ├── chartsType/          # 图表类卡片
│       │   ├── BarSimpleCard.vue  # 简单柱状图卡片
│       │   ├── BarPlusCard.vue    # 增强柱状图卡片
│       │   ├── LineSimpleCard.vue # 简单折线图卡片
│       │   ├── LinePlusCard.vue   # 增强折线图卡片
│       │   ├── PieSimpleCard.vue  # 简单饼图卡片
│       │   └── PiePlusCard.vue    # 增强饼图卡片
│       ├── rankingType/         # 排名类卡片
│       │   ├── RankingSimpleCard.vue # 简单排名卡片
│       │   └── RankingPlusCard.vue   # 增强排名卡片
│       └── summaryType/         # 总结类卡片
│           └── SummarySimpleCard.vue  # 简单总结卡片
```

## 组件类型

### 基础组件

- `Ranking`: 排名组件
- `HorizontaSmallSquare`: 水平小方块组件
- `EnergySummary`: 能耗统计组件
- `DrugSummary`: 药耗统计组件
- `Loading`: 加载状态组件
- `Empty`: 空状态组件
- `RankingSimple`: 简单排名组件

### 基础图表组件

- `BarEcharts`: 柱状图组件
- `LineEcharts`: 折线图组件
- `PieEcharts`: 饼图组件

### 组合卡片组件

组合卡片组件是基于基础组件和基础图表组件封装的更高级别组件，分为三类：

1. **图表类卡片**:

   - `PieSimpleCard`: 简单饼图卡片
   - `PiePlusCard`: 增强饼图卡片
   - `LineSimpleCard`: 简单折线图卡片
   - `LinePlusCard`: 增强折线图卡片
   - `BarSimpleCard`: 简单柱状图卡片
   - `BarPlusCard`: 增强柱状图卡片

2. **排名类卡片**:

   - `RankingSimpleCard`: 简单排名卡片
   - `RankingPlusCard`: 增强排名卡片

3. **总结类卡片**:
   - `SummarySimpleCard`: 简单总结卡片

## 使用方式

### 直接引入组件

可以从组件库中直接引入所需组件：

```typescript
import { BarSimpleCard, LineSimpleCard, PieSimpleCard } from '/@/project/aoa/components/HLCardComponent';

// 然后在Vue组件中使用
<template>
  <BarSimpleCard
    :bottomList="dataList"
    :empty="isEmpty"
    :loading="isLoading"
    :themeColor="theme"
  />
</template>
```

### 使用 combinationCards

组件库提供了动态加载组件的方法，通过 `combinationCards/index.ts` 中的 `loadComponents` 函数可以动态加载所有组合卡片组件：

```typescript
import { loadComponents } from '/@/project/aoa/components/HLCardComponent/src/combinationCards';

// 异步加载所有组件
loadComponents().then((components) => {
  // components 对象包含所有动态加载的组件
  // 可以通过组件名称访问特定组件
  const BarSimpleCard = components['BarSimpleCard'];
  const LineSimpleCard = components['LineSimpleCard'];
  // ...
});
```

### 组件属性

以 `BarSimpleCard` 为例，组合卡片组件通常支持以下属性：

- `bottomList`: 数据列表，用于图表渲染
- `empty`: 布尔值，表示数据是否为空
- `loading`: 布尔值，是否显示加载状态
- `themeColor`: 主题颜色，可选值包括 'light'、'Dark' 和 'screenColor'

所有组合卡片组件都基于 `HLCard` 组件构建，并支持通过插槽进行内容自定义。

## 主题支持

组件库支持以下主题：

- `light`: 亮色主题，适合白天环境
- `Dark`: 暗色主题，适合夜间环境
- `screenColor`: 透明背景主题，适合在大屏展示

## 注意事项

1. 组合卡片组件需要依赖 HLCard 组件
2. 使用组件时需要提供相应的数据格式，特别是图表类组件的数据结构
3. 所有组件支持主题切换，通过 themeColor 属性控制
