<template>
  <BasicModal
    :title="`${titleName}资源`"
    v-bind="$attrs"
    @cancel="handleCancel"
    @register="register"
    width="35%"
    wrapClassName="img-resource-manager-modal"
  >
    <BasicForm @register="registerForm">
      <template #mediumType="{ model, field }">
        <!-- :disabled="titleName === '编辑'" width: 34.125rem -->
        <Select v-model:value="model[field]" @change="changeType">
          <SelectOption :value="item.value" :key="index" v-for="(item, index) in options"
            >{{ item.label }}
          </SelectOption>
        </Select>
      </template>
      <template #url="{ model, field }">
        <div style="display: flex; align-items: center">
          <div style="width: auto">
            <HUpload
              v-model:value="model[field]"
              :accept="
                typeVal === '1'
                  ? ['.png', '.jpeg', '.jpg']
                  : typeVal === '2'
                  ? ['.mp4', '.flv', '.webm']
                  : ''
              "
              :multiple="false"
              :maxNumber="1"
              :maxSize="typeVal === '1' ? 5 : 50"
              :api="uploadFile"
              :showHover="false"
            />
          </div>
          <div style="width: 100%; margin-left: 6px">
            <span type="warning" style="width: 100%; color: #666">
              {{
                `图片支持格式：${['.png', '.jpeg', '.jpg'].join(',')}；视频支持格式：${[
                  '.mp4',
                  '.flv',
                  '.webm',
                ].join(',')}。`
              }}
            </span>
          </div>
        </div>
      </template>
    </BasicForm>
    <template #footer>
      <a-button class="default-btn" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">确认</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formSchema } from './data';
  import { Select, SelectOption } from 'ant-design-vue';
  import { addDsImg, updateDsImg } from '/@zhcz/api/config-center/scenes-group';
  import { HUpload } from '/@/components/Upload';
  import { uploadFile } from '/@zhcz/api/config-center/scenes-group';
  import { ref } from 'vue';

  const typeVal = ref('1');
  async function changeType(val) {
    // console.log('val', val);
    typeVal.value = val;
    const values = await validate();
    setFieldsValue({
      name: values.name,
      mediumType: typeVal.value || '1',
      url: [],
    });
  }
  const options = [
    {
      label: '图片',
      value: '1',
    },
    {
      label: '视频',
      value: '2',
    },
  ];
  const { createMessage } = useMessage();
  const titleName = ref('新增');
  const emit = defineEmits(['success', 'register']);
  const detailInfo = ref<any>({});
  const [register, { closeModal }] = useModalInner((data) => {
    // console.log('data', data.form.mediumType);
    if (data.form?.name) {
      titleName.value = '编辑';
      setFieldsValue({
        name: data.form.name,
        mediumType: data.form.mediumType || '1',
        url: [{ url: data.form.url }],
      });
    } else {
      titleName.value = '新增';
    }
    detailInfo.value = data.form;
  });

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    schemas: formSchema,
    labelWidth: 78,
    showActionButtonGroup: false,
  });
  const okLoading = ref(false);
  async function handleSubmit() {
    const values = await validate();
    okLoading.value = true;
    try {
      if (detailInfo.value?.id) {
        await updateDsImg({
          id: detailInfo.value.id,
          ...values,
          url: values.url[0].url,
        });
      } else {
        await addDsImg({
          ...values,
          url: values.url[0].url,
        });
      }
    } finally {
      okLoading.value = false;
    }
    createMessage.success('操作成功');
    emit('success');
    handleCancel();
  }

  function handleCancel() {
    detailInfo.value = {};
    resetFields();
    closeModal();
  }
</script>

<style lang="less">
  .img-resource-manager-modal {
    .basic-upload {
      padding-top: 0;
    }

    .upload-list-inline1 {
      margin-bottom: 0;
    }

    .preview-content .preview-box {
      margin-bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;

      .preview-icon {
        width: 90%;
        height: 90%;
      }
    }
  }
</style>
