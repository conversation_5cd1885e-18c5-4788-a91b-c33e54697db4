<template>
  <div style="width: 100%">
    <a-button type="primary" @click="emits('handleAddModel')" class="mb-2 mt-2">新增</a-button>
    <div style="max-height: 332px; overflow-y: auto">
      <FormItem class="table-container">
        <table width="100%">
          <thead class="thead">
            <tr>
              <th>型号名称</th>
              <th>排序</th>
              <th width="40">操作</th>
            </tr>
          </thead>
          <tbody class="tbody-container">
            <tr v-for="(item, index) in styleInfo" :key="index" class="th-item">
              <td>
                <FormItem
                  name="style"
                  :rules="styleItemRules(styleInfo[index]?.equipmentModelName)"
                >
                  <Input v-model:value="item.equipmentModelName" placeholder="请输入型号名称" />
                </FormItem>
              </td>
              <td>
                <FormItem
                  name="style"
                  :rules="styleItemRules(styleInfo[index]?.equipmentModelSort)"
                >
                  <InputNumber
                    v-model:value="item.equipmentModelSort"
                    placeholder="请输入排序"
                    :min="0"
                  />
                </FormItem>
              </td>
              <td>
                <div class="actions">
                  <a-button type="link" @click="emits('handleDeleteModel', index)">删除</a-button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </FormItem>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { Input, InputNumber, FormItem } from 'ant-design-vue';

  const emits = defineEmits(['handleAddModel', 'handleEditStyle', 'handleDeleteModel']);
  defineProps({
    styleInfo: {
      type: Array,
      default: () => [],
    },
  });
  const styleItemRules = (value) => {
    return {
      trigger: 'blur',
      validator: async (_, __) => {
        if (!value) {
          return Promise.reject('');
        }
        return Promise.resolve();
      },
    };
  };
</script>
<style lang="less" scoped>
  th,
  td {
    padding: 12px 8px;
    overflow-wrap: break-word;
  }

  .thead {
    background: #f1f4f7;
    border-radius: 8px 8px 0px 0px;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;

    th {
      color: #666;
      font-size: 14px;
      font-weight: 400;
      border-bottom: 1px solid #d8d8d8 !important;
    }
  }

  .tbody-container {
    max-height: 400px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }

    .th-item {
      font-size: 14px;
      font-weight: 400;
      color: #666;

      position: relative;

      td {
        border-bottom: 1px solid #f1f2f3 !important;
      }

      .th-drag {
        box-sizing: border-box;
        background: transparent;
      }
    }

    .actions {
      .ant-btn-link {
        padding: 0;
      }

      .ant-btn-link + .ant-btn-link {
        margin-left: 4px;
      }
    }
  }
</style>
