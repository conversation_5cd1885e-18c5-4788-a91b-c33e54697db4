<template>
  <div class="device-operation-analysis">
    <div class="layout-container">
      <!-- 左侧设备树 -->
      <div class="left-sidebar">
        <DeviceTree @select="handleDeviceSelect" />
      </div>

      <!-- 右侧图表区域 -->
      <div class="right-content">
        <!-- 上方图表 - 设备运行时长 -->
        <div class="chart-container top-chart">
          <OperatingTimeChart :deviceId="selectedDeviceId" :deviceName="selectedDeviceName" />
        </div>

        <!-- 下方图表 - 预留空间 -->
        <div class="chart-container bottom-chart">
          <div class="placeholder-chart">
            <div class="placeholder-content">
              <Icon icon="ant-design:bar-chart-outlined" size="48" color="#ccc" />
              <p>图表功能开发中...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="DeviceOperationAnalysis">
  import { ref } from 'vue';
  import { Icon } from '/@/components/Icon';
  import DeviceTree from '../fault-record/components/DeviceTree.vue';
  import OperatingTimeChart from './components/OperatingTimeChart.vue';

  // 响应式数据
  const selectedDeviceId = ref<string>('');
  const selectedDeviceName = ref<string>('');

  // 处理设备选择
  const handleDeviceSelect = (node: any, selectedKeys: string[]) => {
    console.log('选择的设备:', node, selectedKeys);
    selectedDeviceId.value = node?.id || '';
    selectedDeviceName.value = node?.name || '';
  };
</script>

<style lang="less" scoped>
  .device-operation-analysis {
    height: 100%;
    padding: 16px;
    background: #f5f5f5;

    .layout-container {
      display: flex;
      height: 100%;
      gap: 16px;

      .left-sidebar {
        flex-shrink: 0;
        height: 100%;
      }

      .right-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 16px;
        height: 100%;

        .chart-container {
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          &.top-chart {
            flex: 1;
            min-height: 400px;
          }

          &.bottom-chart {
            flex: 1;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;

            .placeholder-chart {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;

              .placeholder-content {
                text-align: center;
                color: #999;

                p {
                  margin-top: 16px;
                  font-size: 16px;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
