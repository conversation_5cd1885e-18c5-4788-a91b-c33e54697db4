import { defZhczHttp } from '/@/utils/http/axios';

enum Api {
  GetResouceDataBySceneCode = '/api/GroupRelationShip/Resorce/GetResouceDataBySceneCode',
  GetProductHomeIndexCodeValues = '/eq/ProductIndexValue/GetProductHomeIndexCodeValues',
  GetProductCraftIndexCodeLastValues = '/api/v1/SIndustryPdata/ProductIndexValue/GetProductCraftIndexCodeLastValues',
  GetWorkOrderStatistics = '/api/eq/equipmentWorkOrder/getWorkOrderStatistics',
  WorkOrderMaintenanceStatistics = '/api/eq/equipmentWorkOrder/workOrderMaintenanceStatistics',
  WorkOrderDayStatisticsRale = '/api/eq/equipmentWorkOrder/workOrderDayStatisticsRale',
  WorkOrderStatisticsRale = '/api/eq/equipmentWorkOrder/workOrderStatisticsRale',
  WorkOrderCountStatistics = '/api/eq/equipmentWorkOrder/workOrderCountStatistics',
  GetAllWorkGroup = '/api/admin/workgroup/getAll',
  QueryAllShiftInfo = '/api/eq/schedulingPlan/queryAllShiftInfo',
  GetHealthAverageScore = '/api/eq/equipmenthealth/getScoreByMonth',
  GET_USER_CURRENT_FACTROY_ID = '/factory/factory/getUserCurrentFactroyId',
  GetResourceInfosLast = '/data-sence/displayResourceInfo/last',

  // 设备总览
  GetGdInfo = '/eq/bi/getGdInfo',
  GetRepairStatistics = '/eq/bi/getRepairStatistics',
  GetMaintenanceAndRepair = '/eq/bi/getMaintenanceAndRepair',
  GetImportEqHealthInfo = '/eq/bi/getImportEqHealthInfo',
  GetKpi = '/eq/bi/getKpi',
  GetBiOperatingStatistic = '/eq/bi/operatingStatistic',
  GetBiNumStatistic = '/eq/bi/eqNumStatistic',
  GetBiRepairRank = '/eq/bi/repairRank',
}

// 获取设备健康厂平均分
export const getHealthAverageScore = (data) =>
  defZhczHttp.post<any>(
    {
      url: Api.GetHealthAverageScore,
      data,
    },
    { isTransformResponse: false },
  );

export const queryAllShiftInfoApi = (data) =>
  defZhczHttp.get<any>(
    {
      url: Api.QueryAllShiftInfo,
      data,
    },
    { isTransformResponse: false },
  );

// 获取资源数据
export const getResouceDataBySceneCodeApi = (code) =>
  defZhczHttp.get<any>({
    url: Api.GetResouceDataBySceneCode,
    params: {
      SeneceCode: code,
    },
  });

// 获取指标值
export const getProductIndexCodeValuesApi = (data, groupCode = '') =>
  defZhczHttp.post<any>(
    {
      url: `${Api.GetProductHomeIndexCodeValues}?groupCode=${groupCode}`,
      data,
    },
    { isTransformResponse: false },
  );

// 获取指标值-最后一条
export const getProductCraftIndexCodeLastValuesApi = (data, groupCode = '') =>
  defZhczHttp.post<any>(
    {
      url: `${Api.GetProductCraftIndexCodeLastValues}?groupCode=${groupCode}`,
      data,
    },
    { isTransformResponse: false },
  );

// 设备总览-故障统计-右上
export const getWorkOrderStatisticsApi = (factoryId) =>
  defZhczHttp.post<any>(
    {
      url: Api.GetWorkOrderStatistics,
      data: { factoryId },
    },
    { isTransformResponse: false },
  );

// 设备总览-设备维保成本-右下
export const getWorkOrderMaintenanceStatisticsApi = (factoryId) =>
  defZhczHttp.post<any>(
    {
      url: Api.WorkOrderMaintenanceStatistics,
      data: { factoryId },
    },
    { isTransformResponse: false },
  );

// 设备总览-关键api-卡片-左下
export const getWorkOrderDayStatisticsRaleApi = (data) =>
  defZhczHttp.post<any>(
    {
      url: Api.WorkOrderDayStatisticsRale,
      data,
    },
    { isTransformResponse: false },
  );

// 设备总览-关键api-图表-左下
export const getWorkOrderStatisticsRaleApi = (data) =>
  defZhczHttp.post<any>(
    {
      url: Api.WorkOrderStatisticsRale,
      data,
    },
    { isTransformResponse: false },
  );

// 设备总览-卡片-左上
export const getWorkOrderCountStatisticsApi = (factoryId) =>
  defZhczHttp.post<any>(
    {
      url: Api.WorkOrderCountStatistics,
      data: { factoryId },
    },
    { isTransformResponse: false },
  );

// 获取所有班组
export const getAllWorkGroupApi = (params) =>
  defZhczHttp.get<any>(
    {
      url: Api.GetAllWorkGroup,
      params,
    },
    { isTransformResponse: false },
  );

export const getFactoryIdApi = () =>
  defZhczHttp.get({
    url: Api.GET_USER_CURRENT_FACTROY_ID,
  });

// 获取分组数据
export const getResourceInfosLast = (data: {
  factoryId: string;
  startDateTime: string;
  endDateTime: string;
  indexCodes: string;
  tenantId: string;
}) =>
  defZhczHttp.post<any>(
    {
      url: Api.GetResourceInfosLast,
      data,
    },
    { isTransformResponse: false },
  );

/**
 * 设备总览
 */

// 工单
export const getGdInfo = () =>
  defZhczHttp.get({
    url: Api.GetGdInfo,
  });

// 故障统计（近30天）
export const getRepairStatistics = (data: { type: number }) =>
  defZhczHttp.post({
    url: Api.GetRepairStatistics,
    data,
  });

// 维修维保（近半年）
export const getMaintenanceAndRepair = () =>
  defZhczHttp.get({
    url: Api.GetMaintenanceAndRepair,
  });

// 重点设备健康指数
export const getImportEqHealthInfo = () =>
  defZhczHttp.get({
    url: Api.GetImportEqHealthInfo,
  });

// 关键KPI
export const getKpi = (data: { endTime: string }) =>
  defZhczHttp.post({
    url: Api.GetKpi,
    data,
  });

// 设备运行情况
export const getBiOperatingStatistic = (data) =>
  defZhczHttp.post({
    url: Api.GetBiOperatingStatistic,
    data,
  });

export const getBiNumStatistic = () =>
  defZhczHttp.post({
    url: Api.GetBiNumStatistic,
  });

export const getBiRepairRank = (data) =>
  defZhczHttp.post({
    url: Api.GetBiRepairRank,
    data,
  });
