import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Form/index';
import { FACTORY } from '/@zhcz/enums/factory';
import { getDictTypeListApi } from '/@/api/admin/dict';
// import { getPlatformInfoPage } from '/@zhcz/api/config-center/scenes-group';
import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import { getDeptTreeByPage } from '/@/api/admin/dept';
import { verify } from '/@/utils/validator';
import { searchLocationList } from '/@zhcz/api/patrol';

export const editModalFormRef = ref(); //编辑弹框表单实例
const cityOptions = ref([]); //市下拉选项
export const columns: BasicColumn[] = [
  {
    title: '厂名',
    dataIndex: 'name',
  },
  {
    title: '厂ID',
    dataIndex: 'id',
  },
  {
    title: '厂编码',
    dataIndex: 'code',
  },

  // {
  //   title: '对接竹云编码',
  //   dataIndex: 'externalOrgCode',
  // },
  // {
  //   title: '业务编码',
  //   dataIndex: 'businessCode',
  // },
  // {
  //   title: '安全帽分组',
  //   dataIndex: 'rdGroupId',
  // },
  // {
  //   title: '业务类型',
  //   dataIndex: 'bizType',
  // },
  // {
  //   title: '外部属性',
  //   dataIndex: 'extraProperties',
  // },
  {
    title: '排序',
    dataIndex: 'sort',
  },
  // {
  //   title: '创建人',
  //   dataIndex: 'createdBy',
  // },
  // {
  //   title: '创建时间',
  //   dataIndex: 'creationTime',
  //   width: 180,
  // },
  {
    title: '是否启用',
    dataIndex: 'isActive',
    customRender: ({ record }) => {
      const status = record.isActive;
      const enable = ~~status === 1;
      const color = enable ? 'green' : 'red';
      const text = enable ? '启用' : '停用';
      return h(Tag, { color: color }, () => text);
    },
  },
  // {
  //   title: '修改人',
  //   dataIndex: 'lastModifiedBy',
  // },
  // {
  //   title: '修改时间',
  //   dataIndex: 'lastModificationTime',
  //   width: 180,
  // },
  {
    title: '水厂组织',
    dataIndex: 'deptId',
    width: 300,
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '水厂名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  // {
  //   field: 'name',
  //   label: '水厂类型',
  //   component: 'ApiSelect',
  //   colProps: { span: 5 },
  // },
];

const checkNoChinese = async (_rule, value: string) => {
  if (!value) {
    return Promise.reject('请输入水厂编码');
  }
  if (!verify.noChinese.value.test(value)) {
    return Promise.reject(verify.noChinese.label);
  }
};

export const schemas: FormSchema[] = [
  {
    field: 'name',
    component: 'Input',
    label: '水厂名称',
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    field: 'id',
    component: 'InputNumber',
    label: '水厂ID',
    // labelWidth: 84,
    required: true,
    slot: 'id',
    componentProps: {
      defaultValue: 1,
      min: 1,
      precision: 0,
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'type',
    component: 'ApiSelect',
    label: '水厂类型',
    required: true,
    componentProps: {
      api: getDictTypeListApi,
      params: { type: FACTORY.FACTORY_TYPE },
    },
    colProps: {
      span: 12,
    },
    ifShow: false,
  },
  {
    field: 'code',
    component: 'Input',
    label: '水厂编码',
    required: true,
    colProps: {
      span: 12,
    },
    rules: [{ required: true, validator: checkNoChinese, trigger: 'blur' }],
  },
  // {
  //   field: 'businessCode',
  //   component: 'Input',
  //   label: '业务编码',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'platformId',
  //   component: 'ApiSelect',
  //   label: '平台',
  //   labelWidth: 84,
  //   required: true,
  //   componentProps: {
  //     api: getPlatformInfoPage,
  //     mode: 'multiple',
  //     params: {
  //       current: 1,
  //       size: 999,
  //     },
  //     resultField: 'records',
  //     labelField: 'displayName',
  //     valueField: 'id',
  //     showArrow: true,
  //   },
  //   colProps: { span: 12 },
  // },
  {
    field: 'deptId',
    component: 'ApiTreeSelect',
    required: true,
    label: '水厂组织',
    // labelWidth: 84,
    componentProps: {
      multiple: false,
      api: getDeptTreeByPage,
      labelField: 'name',
      valueField: 'id',
      getPopupContainer: () => document.body,
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'isActive',
    component: 'RadioGroup',
    required: true,
    label: '水厂状态',
    colProps: {
      span: 12,
    },
    componentProps: {
      options: [
        {
          label: '启用',
          value: 1,
        },
        {
          label: '停用',
          value: 0,
        },
      ],
    },
    defaultValue: 1,
  },
  // {
  //   field: 'externalOrgCode',
  //   component: 'Input',
  //   label: '竹云组织编码',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'threeDimensionalUrl',
  //   component: 'Input',
  //   label: '三维地址',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'rdGroupId',
  //   component: 'Input',
  //   label: '安全帽分组',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  {
    field: 'sort',
    component: 'InputNumber',
    label: '排序',
    // labelWidth: 84,
    componentProps: {
      min: 1,
      precision: 0,
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'provinceId',
    component: 'ApiSelect',
    label: '省（区、市）',
    componentProps: {
      api: searchLocationList,
      params: {
        parentId: 0,
      },
      labelField: 'locationName',
      valueField: 'id',
      onChange: async (val) => {
        if (editModalFormRef.value?.formModel) editModalFormRef.value.formModel.cityId = null;
        const res = await searchLocationList({
          parentId: val || -1,
        });
        cityOptions.value = res;
      },
    },
    colProps: { span: 12 },
  },
  {
    field: 'cityId',
    component: 'ApiSelect',
    label: '市（地、州）',
    componentProps: {
      labelField: 'locationName',
      valueField: 'id',
      options: cityOptions,
    },
    colProps: { span: 12 },
  },
];
