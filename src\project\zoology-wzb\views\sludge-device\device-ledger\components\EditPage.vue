<template>
  <div class="h-full relative sludge-device-device-ledger-edit-page">
    <div class="page-wrapper" :class="{ 'is-view-page': isViewPage }">
      <Tabs v-model:activeKey="activeKey" class="h-full" destroyInactiveTabPane>
        <TabPane key="1" :tab="isViewPage ? '设备详情' : equipmentId ? '编辑设备' : '新增设备'">
          <div class="device-edit">
            <div class="info">
              <Row class="info-row">
                <!-- 第1、第2列表单 -->
                <Col :span="16" class="form1-class" id="form1-id">
                  <BasicForm ref="formRef1" @register="registerForm" :disabled="isViewPage">
                    <template #thumbnailUrlList="{ model, field }">
                      <div
                        class="images-container"
                        :style="{ maxWidth: form1Width ? `calc(${form1Width} - 120px)` : '' }"
                      >
                        <HUpload
                          v-model:value="model[field]"
                          :accept="['.png', '.jpeg', '.jpg']"
                          multiple
                          :maxNumber="3"
                          :disabled="isViewPage"
                        />
                      </div>
                    </template>
                    <template #tagList="{ model, field }">
                      <Select
                        mode="multiple"
                        v-model:value="model[field]"
                        class="tag-select"
                        showArrow
                        max-tag-count="responsive"
                        placeholder="请选择"
                        :options="tagOptions"
                        @change="handleChangeCamera"
                      >
                        <template #dropdownRender="{ menuNode: menu }">
                          <v-nodes :vnodes="menu" />
                          <div class="flex" style="padding-top: 8px">
                            <Input allowClear placeholder="请输入" v-model:value="newTagName" />
                            <a-button
                              class="ml-2"
                              :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
                              @click="handleAddTag"
                              type="primary"
                              >新增</a-button
                            >
                          </div>
                        </template>
                      </Select>
                    </template>
                  </BasicForm>
                </Col>
                <!-- 第2列表单 -->
                <Col :span="8">
                  <BasicForm @register="registerForm2" :disabled="isViewPage" />
                </Col>
              </Row>
              <!-- 最底下一行表单 -->
              <BasicForm @register="registerForm3" :disabled="isViewPage" />
            </div>
          </div>
        </TabPane>
      </Tabs>
    </div>
    <div class="fix-bottom">
      <div class="back-btn">
        <a-button v-if="!isViewPage" type="primary" @click="handleSubmit" :loading="loading"
          >保存</a-button
        >
        <a-button class="ml-4" @click="back">{{ isViewPage ? '返回' : '取消' }}</a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { Select, Tabs, TabPane, Row, Col, Input } from 'ant-design-vue';
  import { BasicForm, useForm } from '/@/components/Form';
  import { HUpload } from '/@/components/Upload';
  import { Icon } from '/@/components/Icon';
  import { formSchemas, formSchemas2, formSchemas3, formRef1 } from './data';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    saveEquipmentInfoApi,
    searchEquipmentDeatilApi,
    searchEquipmentTagsApi,
    saveEquipmentTagsApi,
  } from '/@zoology-wzb/api/device';
  import { ref, defineComponent, h, onMounted, nextTick } from 'vue';

  const VNodes = defineComponent({
    props: {
      vnodes: {
        type: Object,
        required: true,
      },
    },
    render() {
      return this.vnodes;
    },
  });
  const emits = defineEmits(['back', 'success']);

  const props = defineProps({
    equipmentId: {
      type: Number,
      default: () => null,
    },
    isViewPage: {
      type: Boolean,
      default: false,
    },
  });

  const activeKey = ref('1');
  const loading = ref(false);

  const { createMessage } = useMessage();

  const [registerForm, { resetFields, clearValidate, validate, setFieldsValue }] = useForm({
    schemas: formSchemas,
    showActionButtonGroup: false,
    labelWidth: 120,
  });

  const [
    registerForm2,
    {
      setFieldsValue: setFieldsValue2,
      resetFields: resetFields2,
      clearValidate: clearValidate2,
      validate: validate2,
    },
  ] = useForm({
    schemas: formSchemas2,
    showActionButtonGroup: false,
    labelWidth: 120,
  });

  const [
    registerForm3,
    {
      setFieldsValue: setFieldsValue3,
      resetFields: resetFields3,
      clearValidate: clearValidate3,
      validate: validate3,
    },
  ] = useForm({
    schemas: formSchemas3,
    showActionButtonGroup: false,
    labelWidth: 120,
  });

  const newTagName = ref(''); //新增标签名
  /* 添加标签 */
  const handleAddTag = async () => {
    if (tagOptions.value.find((v) => v.label === newTagName.value)) {
      createMessage.warn('标签已存在');
      return;
    }
    await saveEquipmentTagsApi(newTagName.value);
    newTagName.value = '';
    await getTagList();
  };
  /* 校验并提交表单 */
  const handleSubmit = async () => {
    try {
      const values = await validate();
      const values2 = await validate2();
      const values3 = await validate3();
      const { thumbnailUrlList } = values;
      loading.value = true;
      const params = {
        ...values,
        ...values2,
        ...values3,
        id: props.equipmentId || undefined,
        thumbnailUrlList: thumbnailUrlList?.length ? thumbnailUrlList.map((v) => v.url) : [],
      };
      if (!params.equipmentCode) params.equipmentCode = `WNSBZNGJ-${new Date().getTime()}`;
      await saveEquipmentInfoApi(params);
      handleCancel();
      createMessage.success('操作成功');
      emits('success');
    } catch (e) {
      await validate2();
    } finally {
      loading.value = false;
    }
  };
  /* 重置表单数据 */
  const handleCancel = () => {
    resetFields();
    resetFields2();
    resetFields3();
    clearValidate();
    clearValidate2();
    clearValidate3();
  };
  /* 获取详情数据 */
  const getDetail = async (id) => {
    const res = await searchEquipmentDeatilApi(id);
    const thumbnailUrlList = res.thumbnailUrlList?.map?.((v) => ({ fileName: v, url: v })) || [];
    setFieldsValue({
      ...res,
      thumbnailUrlList,
    });

    setFieldsValue2({
      ...res,
      deviceTags: res.cameraInfoRespList?.map((i) => i.id),
    });
    setFieldsValue3({ ...res });
    clearValidate();
    clearValidate2();
    clearValidate3();
  };
  const tagOptions = ref([]); // 标签下拉选项
  /** 查询设备标签 */
  const getTagList = async () => {
    try {
      const res = await searchEquipmentTagsApi();
      tagOptions.value = res.map((v) => {
        return {
          value: v,
          label: v,
        };
      });
    } catch (e) {}
  };
  /* 初始化 */
  const init = async () => {
    getTagList();
    if (props.equipmentId) {
      getDetail(props.equipmentId);
    } else {
      nextTick(() => {
        setFieldsValue({
          equipmentCode: `WNSBZNGJ-${new Date().getTime()}`,
        });
      });
    }
  };
  init();
  /* 返回 */
  const back = () => {
    emits('back');
  };
  /* 获取到表单1宽度 */
  const form1Width = ref();
  onMounted(() => {
    const dom = document.getElementById('form1-id');
    if (dom) form1Width.value = getComputedStyle(dom).getPropertyValue('width');
  });
</script>

<style lang="less" scoped>
  .sludge-device-device-ledger-edit-page {
    .page-wrapper {
      height: calc(100% - 56px);

      &.is-view-page {
        :deep(.ant-form-item-required) {
          &::before {
            display: none;
          }
        }

        :deep(.ant-upload-drag) {
          display: none;
        }
      }

      :deep(.ant-tabs) {
        .ant-tabs-nav {
          margin: 0;

          .ant-tabs-nav-wrap {
            background: #fff;
            padding: 0 16px;
            border-bottom: 1px solid #d8d8d8;
          }
        }

        .ant-tabs-content {
          height: 100%;
        }
      }
    }

    .fix-bottom {
      position: absolute;
      bottom: -16px;
      left: 0;
      width: 100%;
      height: 72px;
      background: #ffffff;
      box-shadow: 0px -4px 4px 0px rgba(0, 0, 0, 0.08);
      border-radius: 0px 0px 4px 4px;
      display: flex;
      align-items: center;

      .back-btn {
        padding-left: 16px;

        :deep(.ant-btn) {
          width: 120px;
          height: 40px;
        }
      }
    }

    :deep(.ant-select-disabled) {
      .ant-select-selector {
        background: #f2f3f5;
      }
    }
  }

  .device-edit {
    position: relative;
    height: 100%;
    background: #fff;
    display: flex;
    flex-direction: column;

    .info {
      padding: 16px;
      overflow-y: auto;
      position: relative;
      height: 100%;
      width: 100%;

      .images-container {
        height: 88px;
        width: max-content;
      }

      .info-row {
        :deep(.ant-form-item) {
          margin-bottom: 24px;
        }
      }
    }
  }

  .tag-select {
    :deep(&.ant-select-disabled) {
      .ant-select-selector {
        cursor: pointer;
        background: #f2f3f5 !important;
      }
    }
  }

  .form1-class {
    :deep(.ant-form-item-control-input-content) {
      z-index: 99;
    }
  }
</style>
<style lang="less">
  .sludge-device-device-ledger-edit-page-equipmentTypeId-select {
    .ant-select-item-option-disabled {
      background: #f0f0f0 !important;

      .ant-select-item-option-content {
        color: gray !important;
      }
    }
  }
</style>
