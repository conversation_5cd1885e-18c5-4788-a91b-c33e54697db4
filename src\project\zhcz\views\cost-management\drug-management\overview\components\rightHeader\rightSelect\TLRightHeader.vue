<template>
  <div class="flex-center">
    <div class="left">
      <div class="button_ai" v-if="!isEmptyData" @click="handleCreate()"
        ><img :src="aiImg" alt="" srcset="" /><span class="text">AI分析</span></div
      >
    </div>
    <div class="right">
      <div>
        <Select
          v-if="dataList.length"
          v-model:value="dataType"
          placeholder="请选择"
          style="width: 100px"
          @change="handleChangeIndicator"
        >
          <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
      </div>
      <div>
        <Select
          style="width: 80px"
          v-model:value="dateType"
          @change="getIndicatorList"
          v-if="dateList.length"
        >
          <SelectOption v-for="item in dateList" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
      </div>

      <DatePicker
        @change="getData"
        v-if="dateList.find((item) => item.value === dateType)?.label === '日'"
        valueFormat="YYYY-MM-DD"
        format="YYYY-MM-DD"
        v-model:value="date1"
        placeholder="请选择"
        style="width: 200px"
        :allowClear="false"
        :disabledDate="disabledDate"
      />
      <DatePicker
        @change="getData"
        v-if="dateList.find((item) => item.value === dateType)?.label === '月'"
        style="width: 200px"
        v-model:value="date"
        picker="month"
        valueFormat="YYYY-MM"
        :disabledDate="disabledMonthDate"
      />
    </div>
  </div>

  <HAiDrawer @register="registerDrawer" :aiQuestion="aiDataCopy[0].aiQuestion" />
</template>
<script setup lang="ts">
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  // import { useECharts } from '/@/hooks/web/useECharts';
  import { ref, nextTick } from 'vue';
  import dayjs from 'dayjs';
  // import { mockRingRatioData } from '../data';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import aiImg from '../../../AI.png';
  import { HAiDrawer } from '/@zhcz/components/HAiDrawer/index';
  import { useDrawer } from '/@/components/Drawer';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';

  const [registerDrawer, { openDrawer }] = useDrawer();
  function handleCreate() {
    openDrawer(true, {
      record: {},
      isUpdate: false,
    });
  }
  const aiDataCopy = ref([
    {
      aiQuestion:
        '自来水厂，次氯酸钠在2025年03月25日的趋势图中显示，今日单耗为：2点_kg/kt，4点_kg/kt，6点_kg/kt，8点_kg/kt，10点_kg/kt，12点_kg/kt，14点_kg/kt…………；昨日单耗为2点_kg/kt，4点_kg/kt，6点_kg/kt，8点_kg/kt，10点_kg/kt，12点_kg/kt，14点_kg/kt…………；根据以上内容结合原水水质、天气等情况进行情况分析。',
      deepAnalysis:
        '好的，用户问自来水厂单耗趋势等的原因，我需要详细分析。首先，我得回忆一下浊度的定义，素全面分析.\n可能需要进一步的信息来确定具体原因，但先列出这些可能性。\n帮助用户排查。',
      explain:
        '自来水厂单耗趋势等通常由多种因素引起，具体原因需结合环境、人为活动和水处理流程综合分析。以下是常见原因分类：',
      resultTitle: '总览分析',
      resultDes: '',
      summaryTitle: '总结',
      summary:
        '若药耗持续超标，可能影响水质安全（如隐藏病原微生物），建议及时联系水务部门或环保机构介入调查。',
    },
  ]);
  // });

  const date1 = ref(dayjs().format('YYYY-MM-DD'));
  const date = ref(dayjs().subtract(0, 'month').format('YYYY-MM'));
  // const chartRef = ref<any>(null);
  const dataType = ref<any>(null);
  const dateType = ref<any>(null);
  const dateList = ref<OptionItem[]>([
    // {
    //   label: '日',
    //   value: 'yhzl2_qst_r',
    // },
    // {
    //   label: '月',
    //   value: 'yhzl2_qst_y',
    // },
  ]);
  type OptionItem = {
    label: string;
    value: string;
  };
  const dataList = ref<OptionItem[]>([]);
  // [
  //   {
  //     label: '石灰',
  //     value: 1,
  //     month: 'Z_YJSH_S_D_SUM',
  //     day: 'Z_YJSH_S',
  //   },
  //   {
  //     label: '碱铝',
  //     value: 2,
  //     month: 'Z_YJJL_S_D_SUM',
  //     day: 'Z_YJJL_S',
  //   },
  //   {
  //     label: '次氯酸钠',
  //     value: 3,
  //     month: 'Z_YJCLSN_S_D_SUM',
  //     day: 'Z_YJCLSN_S',
  //   },
  // ];
  async function getIndicatorList() {
    let startDateTime = dayjs(date1.value).format('YYYY-MM-DD 00:00:00');
    let endDateTime = dayjs(date1.value).format('YYYY-MM-DD 23:59:59');
    let paramsPre = {
      startDateTime:
        dateList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? startDateTime
          : dayjs(date.value).subtract(0, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'),
      endDateTime:
        dateList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? endDateTime
          : dayjs(date.value).subtract(0, 'month').endOf('month').format('YYYY-MM-DD 23:59:59'),
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    let paramDataPre = {
      resourceInterfaceId: '3',
      groupCode: dateType.value,
      paramsData: JSON.stringify(paramsPre),
    };
    const res = await callResourceFunction(paramDataPre);
    if (res.data.length) {
      dataType.value = res.data.find((item) => item.indexCode)?.indexCode || '';
      dataList.value = res.data.map((item) => ({
        value: item.indexCode,
        label: item.indexName,
      }));
      getData();
    }
  }
  async function getTimeList() {
    const res = await listSenceGroupByParent({
      // dhzl2_dlph
      groupCode: 'yhzl2_qst',
      factoryId: 1,
      platformld: 1,
    });
    // console.log('res.data', res);
    if (Object.keys(res).length) {
      dateType.value = Object.keys(res)[0];
      dateList.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      getIndicatorList();
    }
  }
  function handleSetVisitChart() {
    // console.log('indexList.value[0]', indexList.value[0]);
    const yName = dataList.value.find((i) => dataType.value === i.value)?.label;
    const time =
      dateList.value.find((item) => item.value === dateType.value)?.label === '日'
        ? `${date1.value}日`
        : `${date.value}月`;
    const toDayAr = indexList.value[0].XAxis?.map((item, index) => {
      return dateList.value.find((item) => item.value === dateType.value)?.label === '日'
        ? `${item.substring(0, 2)}点`
        : item + indexList.value[0].data[index] + 'kg/kt' + '\n';
    });
    const toDayBr = indexList.value[0].XAxis?.map((item, index) => {
      return item + indexList.value[1].data[index] + 'kg/kt' + '\n';
    });
    const toDayA =
      dateList.value.find((item) => item.value === dateType.value)?.label === '日'
        ? '今日单耗'
        : '当月单耗';
    const toDayB =
      dateList.value.find((item) => item.value === dateType.value)?.label === '日'
        ? '昨日单耗'
        : '上月单耗';
    aiDataCopy.value[0].aiQuestion = `自来水厂，${yName}在${time}的趋势图中显示，${toDayA}为：${toDayAr?.join(
      ',',
    )}；${toDayB}为${toDayBr?.join(',')}；根据以上内容结合原水水质、天气等情况进行情况分析。`;
    // const { setOptions, echarts } = useECharts(chartRef);
    // try {
    //   const option = {
    //     color: ['rgba(46,123,255,0.8)', 'rgba(255,140,46,0.8)'],
    //     tooltip: {
    //       trigger: 'axis',
    //       axisPointer: {
    //         type: 'shadow',
    //       },
    //       backgroundColor: '#fff',
    //       textStyle: {
    //         color: '#333',
    //         fontSize: 14,
    //         lineHeight: 28,
    //         height: 28,
    //         fontWeight: 400,
    //       },
    //       borderColor: 'transparent',
    //       formatter: (params) => {
    //         return `<div style="color: #999;">${params[0].name}</div>
    //         ${params[0].marker} <span>${
    //           params[0].seriesName
    //         }</span><span style="display: inline-block; width: 150px; font-weight: bold; text-align: right;">${
    //           params[0].value ? params[0].value : '-'
    //         }&nbsp;${indexList.value[0]?.unitName}</span>
    //         <br/>
    //         ${params[1].marker} <span>${
    //           params[1].seriesName
    //         }</span><span style="display: inline-block; width: 150px; font-weight: bold; text-align: right;">${
    //           params[1].value ? params[1].value : '-'
    //         }&nbsp;${indexList.value[0]?.unitName}</span>`;
    //       },
    //     },
    //     legend: {
    //       icon: 'circle',
    //       itemWidth: 8,
    //       itemHeight: 8,
    //       itemGap: 24,
    //       // color: ['rgba(46,123,255,0.8)', 'rgba(255,140,46,0.8)'],
    //       data:
    //         dateList.value.find((item) => item.value === dateType.value)?.label === '日'
    //           ? ['今日单耗', '昨日单耗']
    //           : ['当月单耗', '上月单耗'],
    //       textStyle: {
    //         fontSize: 14,
    //         color: '#999',
    //       },
    //     },
    //     grid: {
    //       top: '15%',
    //       left: '0',
    //       right: '0',
    //       bottom: '1%',
    //       containLabel: true,
    //     },
    //     xAxis: [
    //       {
    //         type: 'category',
    //         boundaryGap: true,
    //         data: indexList.value[0].XAxis,
    //         axisLine: {
    //           show: true,
    //           lineStyle: {
    //             color: '#E9E9E9',
    //             type: 'solid',
    //           },
    //         },
    //         axisTick: {
    //           show: false,
    //         },
    //         axisLabel: {
    //           textStyle: {
    //             color: '#999999',
    //             fontSize: 14,
    //           },
    //         },
    //         axisPointer: {
    //           type: 'shadow',
    //         },
    //       },
    //     ],
    //     yAxis: [
    //       {
    //         type: 'value',
    //         name: indexList.value[0].unitName ? `单位（${indexList.value[0].unitName}/kt）` : '',
    //         nameTextStyle: {
    //           color: '#999',
    //           fontSize: 14,
    //           align: 'left',
    //         },
    //         axisLine: {
    //           show: false,
    //           lineStyle: {
    //             color: '#E9E9E9',
    //             type: 'solid',
    //           },
    //         },
    //         splitLine: {
    //           show: true,
    //           lineStyle: {
    //             color: '#E9E9E9',
    //             type: 'dashed',
    //           },
    //         },
    //         axisTick: {
    //           show: false,
    //         },
    //         axisLabel: {
    //           show: true,
    //           textStyle: {
    //             color: '#999999',
    //             fontSize: 14,
    //           },
    //         },
    //       },
    //     ],
    //     series: [
    //       {
    //         name:
    //           dateList.value.find((item) => item.value === dateType.value)?.label === '日'
    //             ? '今日单耗'
    //             : '当月单耗',
    //         type: 'line',
    //         symbol: 'none',
    //         areaStyle: {
    //           color: {
    //             type: 'linear',
    //             x: 0,
    //             y: 0,
    //             x2: 0,
    //             y2: 1,
    //             colorStops: [
    //               {
    //                 offset: 0,
    //                 color: 'rgba(11, 98, 203,0.16)', // 0% 处的颜色
    //               },
    //               {
    //                 offset: 1,
    //                 color: 'rgba(11, 98, 203,0.04)', // 100% 处的颜色
    //               },
    //             ],
    //             global: false, // 缺省为 false
    //           },
    //         },
    //         // barWidth: 24,
    //         label: {
    //           show: true,
    //           position: 'top',
    //           // formatter(params) {
    //           //   const up = '{triangle_up|▲}';
    //           //   const down = '{triangle_down|▼}';
    //           //   const item = indexList.value.find((i) => i.indexName === params.name);
    //           //   let numberValue = 0;
    //           //   if (item) {
    //           //     numberValue = item.valueSeven;
    //           //     // const res = sumPercent(item.value, params.value);
    //           //   } else {
    //           //     numberValue = 0;
    //           //   }
    //           //   if (numberValue > Number(params.value)) {
    //           //     const result = sumPercent(params.value, numberValue);
    //           //     return `${down} {value|${roundAndConvertCheckNullAndUnDef(Number(result), 2)}%}`;
    //           //   } else if (numberValue < Number(params.value)) {
    //           //     const result = sumPercent(params.value, numberValue);
    //           //     return `${up} {value|${roundAndConvertCheckNullAndUnDef(Number(result), 2)}%}`;
    //           //   } else {
    //           //     return '';
    //           //   }
    //           // },
    //           rich: {
    //             triangle_up: {
    //               fontSize: 10,
    //               color: '#FF522B',
    //               lineHeight: 10,
    //               borderColor: '#FF522B',
    //             },
    //             triangle_down: {
    //               fontSize: 10,
    //               color: '#4DB803',
    //               lineHeight: 10,
    //               borderColor: '#4DB803',
    //             },
    //             value: {
    //               fontSize: 12,
    //               color: '#333333',
    //               fontWeight: 500,
    //             },
    //           },
    //         },
    //         itemStyle: {
    //           color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //             {
    //               offset: 0,
    //               color: '#2B63A1',
    //             },
    //             {
    //               offset: 1,
    //               color: '#669CD9',
    //             },
    //           ]),
    //           barBorderRadius: [2, 2, 0, 0],
    //         },
    //         data: indexList.value[0].data,
    //       },
    //       {
    //         name:
    //           dateList.value.find((item) => item.value === dateType.value)?.label === '日'
    //             ? '昨日单耗'
    //             : '上月单耗',
    //         type: 'line',
    //         symbol: 'none',
    //         areaStyle: {
    //           color: {
    //             type: 'linear',
    //             x: 0,
    //             y: 0,
    //             x2: 0,
    //             y2: 1,
    //             colorStops: [
    //               {
    //                 offset: 0,
    //                 color: 'rgba(255,140,46,0.16)', // 0% 处的颜色
    //               },
    //               {
    //                 offset: 1,
    //                 color: 'rgba(255,140,46,0.04)', // 100% 处的颜色
    //               },
    //             ],
    //             global: false, // 缺省为 false
    //           },
    //         },
    //         // barWidth: 24,
    //         // type: 'line',
    //         // smooth: true,
    //         // showSymbol: false,
    //         // lineStyle: {
    //         //   color: '#FC7C22',
    //         // },
    //         itemStyle: {
    //           color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //             {
    //               offset: 0,
    //               color: '#FC7C22',
    //             },
    //             {
    //               offset: 1,
    //               color: '#FC7C22',
    //             },
    //           ]),
    //           barBorderRadius: [2, 2, 0, 0],
    //         },
    //         data: indexList.value[1].data,
    //       },
    //     ],
    //     animation: false,
    //   };
    //   // console.log('option', option, chartRef.value);
    //   // setOptions(option as any);
    // } catch (error) {
    //   console.log('error', error);
    // }
  }

  // const loading = ref(false);
  const indexList = ref<any[]>([]);

  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }
  function disabledMonthDate(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('day');
  }

  const isEmptyData = ref(true);
  const emit = defineEmits(['setLineData']);
  const getData = async () => {
    // 当日、当月
    let startDateTime = dayjs(date1.value).format('YYYY-MM-DD 00:00:00');
    let endDateTime = dayjs(date1.value).format('YYYY-MM-DD 23:59:59');
    let paramsPre = {
      startDateTime:
        dateList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? startDateTime
          : dayjs(date.value).subtract(0, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'),
      endDateTime:
        dateList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? endDateTime
          : dayjs(date.value).subtract(0, 'month').endOf('month').format('YYYY-MM-DD 23:59:59'),
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    let paramDataPre = {
      resourceInterfaceId: '3',
      groupCode:
        dateList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? 'yhzl2_qst_r'
          : 'yhzl2_qst_y',
      paramsData: JSON.stringify(paramsPre),
    };
    const preData = await callResourceFunction(paramDataPre);
    const dataOne = preData.data.find((item: any) => item.indexCode === dataType.value);

    // 前一日、前一月
    startDateTime = dayjs(date1.value).subtract(1, 'day').format('YYYY-MM-DD 00:00:00');
    endDateTime = dayjs(date1.value).subtract(1, 'day').format('YYYY-MM-DD 23:59:59');
    paramsPre = {
      startDateTime:
        dateList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? startDateTime
          : dayjs(date.value).subtract(1, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'),
      endDateTime:
        dateList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? endDateTime
          : dayjs(date.value).subtract(1, 'month').endOf('month').format('YYYY-MM-DD 23:59:59'),
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    paramDataPre = {
      resourceInterfaceId: '3',
      groupCode:
        dateList.value.find((item) => item.value === dateType.value)?.label === '日'
          ? 'yhzl2_qst_r'
          : 'yhzl2_qst_y',
      paramsData: JSON.stringify(paramsPre),
    };
    const preDataBefore = await callResourceFunction(paramDataPre);
    const dataTwo = preDataBefore.data.find((item: any) => item.indexCode === dataType.value);

    // console.log('单耗趋势data', data);
    let XAxisData: string[] = [];
    if (dateList.value.find((item) => item.value === dateType.value)?.label === '日') {
      let x = 0;
      while (x < 24) {
        if (x < 10) {
          XAxisData.push(`0${x}:00`);
        } else {
          XAxisData.push(`${x}:00`);
        }
        x++;
      }
    } else if (dateList.value.find((item) => item.value === dateType.value)?.label === '月') {
      let x = 1;
      let maxD;
      if (
        dayjs(date.value).endOf('month').format('DD') >
        dayjs(date.value).subtract(1, 'month').endOf('month').format('DD')
      ) {
        maxD = dayjs(date.value).endOf('month').format('DD');
      } else {
        maxD = dayjs(date.value).subtract(1, 'month').endOf('month').format('DD');
      }
      maxD = Number(maxD) + 1;
      while (x < maxD) {
        if (x < 10) {
          XAxisData.push(`0${x}`);
        } else {
          XAxisData.push(`${x}`);
        }
        x++;
      }
    }
    interface arrItem {
      collectDateTime?: string;
      value?: number;
    }
    const copyDataArr = [addXais(XAxisData, dataOne.data), addXais(XAxisData, dataTwo.data)];
    function addXais(data: string[], data2: []) {
      return data.map((item) => {
        const copyArr: arrItem[] = data2.filter((i: arrItem) => {
          if (dateList.value.find((item) => item.value === dateType.value)?.label === '日') {
            return item === dayjs(i.collectDateTime).format('HH:mm');
          } else if (dateList.value.find((item) => item.value === dateType.value)?.label === '月') {
            return item === dayjs(i.collectDateTime).format('DD');
          }
        });
        return copyArr.length > 0 ? copyArr[0]?.value : '';
      });
    }
    const XAxis = XAxisData.map((i) => {
      return dateList.value.find((item) => item.value === dateType.value)?.label === '日'
        ? i
        : `${i}日`;
    });
    indexList.value = [];
    [dataOne, dataTwo].forEach((value, index) => {
      const { indexCode, indexName, unitName } = value;
      indexList.value.push({
        unitName,
        indexName,
        indexCode,
        data: copyDataArr[index],
        XAxis,
      });
    });
    console.log('indexList.value', XAxisData, indexList.value);
    if (indexList.value[0].data?.length) {
      isEmptyData.value = false;
      emit('setLineData', { dataList: indexList.value });
      await nextTick();
      handleSetVisitChart();
    } else {
      emit('setLineData', { dataList: [] });
      isEmptyData.value = true;
    }
  };

  const handleChangeIndicator = () => {
    getData();
  };
  // watch(
  //   () => dateType.value,
  //   async () => {
  //     await nextTick();
  //     getIndicatorList();
  //   },
  // );

  // watch(
  //   () => props.solidResourceInterfaceId,
  //   async (val) => {
  //     await nextTick();
  //     if (val) {
  //       await getIndicatorList();
  //     } else {
  //       indexList.value = mockRingRatioData;
  //       handleSetVisitChart();
  //     }
  //   },
  //   { immediate: true },
  // );
  getTimeList();
</script>
<style lang="less" scoped>
  .extra-wrap {
    :deep(.ant-select) {
      margin-right: 16px;

      // .ant-select-selector {
      //   width: 80px;
      // }
    }
  }

  .flex-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    justify-self: start;

    .right {
      display: flex;
      gap: 0 16px;
    }

    .button_ai {
      margin-left: 8px;
      padding: 5px 8px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      cursor: pointer;

      img {
        width: 16px;
      }

      .text {
        font-size: 14px;
        color: #0b62cb;
        line-height: 14px;
        padding-left: 4px;
        font-weight: 600;
      }

      &:hover {
        background: rgba(11, 98, 203, 0.12);
      }
    }
  }

  .empty {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
