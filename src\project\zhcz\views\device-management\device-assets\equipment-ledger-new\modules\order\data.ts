import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { mock } from 'mockjs';
import { random } from 'lodash-es';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: '工单类型',
    dataIndex: 'orderType',
    width: 120,
  },
  {
    title: '工单编号',
    dataIndex: 'orderNumber',
  },
  {
    title: '工单名称',
    dataIndex: 'name',
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
  },
  {
    title: '工单描述',
    dataIndex: 'remark',
  },
  {
    title: '设备地址',
    dataIndex: 'unitName',
  },
  {
    title: '工单状态',
    dataIndex: 'itemStatus',
    width: 120,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '工单名称',
    labelWidth: 67,
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'orderNumber',
    label: '工单编号',
    component: 'Input',
    colProps: { span: 6 },
  },
];

const statusList = ['工单审核', '工单完成'];
const typeList = ['维修工单', '维保工单'];

export const mockTableData = Array.from({ length: 10 }, (_, index) => {
  return {
    id: `${index + 1}`,
    type: typeList[random(0, 1)],
    no: mock('@id'),
    name: `${mock('@cword(4)')}${typeList[random(0, 1)]}`,
    createDate: dayjs().subtract(random(0, 6), 'day').format('YYYY-MM-DD HH:mm:ss'),
    descriptions: mock('@csentence'),
    address: mock('@csentence'),
    status: statusList[random(0, 1)],
  };
});
