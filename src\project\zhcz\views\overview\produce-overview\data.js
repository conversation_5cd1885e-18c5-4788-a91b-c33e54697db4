import dayjs from 'dayjs';
import { iconTextMap } from '../data';
import consumptionBlueImg from '../assets/images/consumption-blue.png';
import consumptionYellowLightImg from '../assets/images/consumption-yellow-light.png';
import consumptionYellowDarkImg from '../assets/images/consumption-yellow-dark.png';
import consumptionGreenImg from '../assets/images/consumption-green.png';
import model1 from '../assets/images/model-produce1.png';
import model2 from '../assets/images/model-produce2.png';

export const statusData = ['正常', '信号中断', '异常'];

export const timeList = [
  {
    value: '1',
    label: '昨日',
  },
  {
    value: '3',
    label: '近3天',
  },
  {
    value: '7',
    label: '近7天',
  },
  {
    value: '15',
    label: '近15天',
  },
  {
    value: '30',
    label: '近30天',
  },
];
export const timeList3 = [
  {
    value: 'day',
    label: '日',
  },
  {
    value: 'month',
    label: '月',
  },
  {
    value: 'year',
    label: '年',
  },
];
export const timeList2 = [
  {
    value: '3',
    label: '近3天',
  },
  {
    value: '7',
    label: '近7天',
  },
  {
    value: '15',
    label: '近15天',
  },
  {
    value: '30',
    label: '近30天',
  },
];

// function radomNumber(min, max) {
//   return Number.parseInt(Math.floor(Math.random() * (max - min + 1) + min));
// }

export const sludgeIconData = [
  consumptionBlueImg,
  consumptionYellowLightImg,
  consumptionYellowDarkImg,
  consumptionGreenImg,
];

export const mockDataList = [
  {
    indexName: '进水累计量',
    value: 0,
    unitName: 'm³',
  },
  {
    indexName: '出水累计量',
    value: 0,
    unitName: 'm³',
  },
];

function getChartXAxisData(number) {
  return dayjs().subtract(number, 'day').format('MM-DD');
}

export const mockChartData = {
  title: '生产数据曲线',
  chartOptions: {
    xAxis: {
      data: [
        // getChartXAxisData(7),
        // getChartXAxisData(6),
        // getChartXAxisData(5),
        // getChartXAxisData(4),
        // getChartXAxisData(3),
        // getChartXAxisData(2),
        // getChartXAxisData(1),
      ],
    },
    series: [
      {
        name: '',
        color: '#2D82FE',
        data: [],
        unitName: 'm³',
        areaColor: ['rgba(45, 130, 254, 0.3)', 'transparent'],
      },
      {
        name: '',
        color: '#1FC3A4',
        data: [],
        unitName: 'm³',
        areaColor: ['rgba(31, 195, 164, 0.3)', 'transparent'],
      },
      {
        name: '',
        color: '#FEC52D',
        data: [],
        unitName: 'kW',
        areaColor: ['rgba(254, 197, 45, 0.3)', 'transparent'],
      },
    ],
  },
};

export const mockWaterQualityData = {
  title: '近3天数据曲线',
  chartOptions: {
    xAxis: {
      data: [getChartXAxisData(3), getChartXAxisData(2), getChartXAxisData(1)],
    },
    series: [
      {
        name: 'pH',
        color: 'rgba(45, 130, 254, 1)',
        data: [0, 0, 0],
        unitName: '',
        areaColor: ['rgba(5, 130, 254, 0.3)', 'transparent'],
      },
      {
        name: 'COD',
        color: 'rgba(31, 195, 164, 1)',
        data: [0, 0, 0],
        unitName: '',
        areaColor: ['rgba(31, 195, 164, 0.3)', 'transparent'],
      },
      {
        name: 'SS',
        color: 'rgba(254, 197, 45, 1)',
        data: [0, 0, 0],
        unitName: '',
        data: [0, 0, 0],
        areaColor: ['rgba(254, 197, 45, 0.3)', 'transparent'],
      },
    ],
  },
};

export const mockSludgeData = [
  {
    indexName: 'PAC',
    value: 0,
    unitName: 'kg',
    src: consumptionBlueImg,
  },
  {
    indexName: '次氯酸钠',
    value: 0,
    unitName: 'kg',
    src: consumptionYellowLightImg,
  },
  {
    indexName: '氢氧化钠',
    value: 0,
    unitName: 'kg',
    src: consumptionYellowDarkImg,
  },
  {
    indexName: '石灰',
    value: 0,
    unitName: 'kg',
    src: consumptionGreenImg,
  },
];

export const mockRawWaterData = [
  {
    indexName: '氨氮',
    value: 0,
    unitName: 'mg/L',
  },
  {
    indexName: 'C',
    value: 0,
    unitName: 'mg/L',
  },
  {
    indexName: 'PH',
    value: 0,
    unitName: '',
  },
  {
    indexName: '总磷',
    value: 0,
    unitName: 'mg/L',
  },
  {
    indexName: '浊度',
    value: 0,
    unitName: 'NTU',
  },
];

export const mockFactoryWaterData = [
  {
    indexName: '氨氮',
    value: 0,
    unitName: 'mg/L',
  },
  {
    indexName: 'C',
    value: 0,
    unitName: 'mg/L',
  },
  {
    indexName: 'PH',
    value: 0,
    unitName: '',
  },
  {
    indexName: '总磷',
    value: 0,
    unitName: 'mg/L',
  },
  {
    indexName: '浊度',
    value: 0,
    unitName: 'NTU',
  },
];

export const mockProduceCenterData = [
  {
    indexName: iconTextMap.get('water1'),
    value: 0,
    unitName: 'm³',
  },
  {
    indexName: iconTextMap.get('water2'),
    value: 0,
    unitName: 'm³',
  },
  {
    indexName: iconTextMap.get('water3'),
    value: 0,
    unitName: 'm³',
  },
];

export const produceCenterEmptyData = [
  {
    indexName: iconTextMap.get('water1'),
    value: '',
    unitName: 'm³',
  },
  {
    indexName: iconTextMap.get('water2'),
    value: '',
    unitName: 'm³',
  },
  {
    indexName: iconTextMap.get('water3'),
    value: '',
    unitName: 'm³',
  },
];

export const mockIndexData1 = [
  {
    indexName: '栅道清理量',
    value: 0,
    unitName: 't',
  },
  {
    indexName: '1#泵启动液位',
    value: 0,
    unitName: 'm',
  },
  {
    indexName: '2#泵启动液位',
    value: 0,
    unitName: 'm',
  },
  {
    indexName: '3#泵启动液位',
    value: 0,
    unitName: 'm',
  },
];
export const mockIndexData2 = [
  {
    indexName: '鼓风机瞬时风量',
    value: 0,
    unitName: 'm³/h',
  },
  {
    indexName: '鼓风机累计风量',
    value: 0,
    unitName: 'm³/h',
  },
  {
    indexName: '鼓风机实测压力',
    value: 0,
    unitName: 'kPa',
  },
  {
    indexName: '鼓风机设定压力',
    value: 0,
    unitName: 'kPa',
  },
];

export const mockModelData = [
  {
    src: model1,
    text: '粗格栅',
    index: mockIndexData1,
  },
  {
    src: model2,
    text: '鼓风机房',
    index: mockIndexData2,
  },
];
