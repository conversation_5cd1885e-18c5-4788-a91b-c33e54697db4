<template>
  <Card :bordered="false" dis-hover>
    <template #title>
      <div>全局风险统计</div>
    </template>
    <div
      ref="tableWrapperRef"
      style="padding-top: 1px; width: 100%; height: 100%; overflow: hidden"
    >
      <BasicTable ref="tableRef" @register="registerTable">
        <template #bodyCell="{ column }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  label: '查看',
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
  </Card>
</template>

<script setup lang="ts">
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import { Card } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';

  const tableWrapperRef = ref();
  const defaultTop = ref(0);
  const scrollTimer = ref(null);
  const maxHeight = ref(0);
  const bodyDom = ref();

  const [registerTable] = useTable({
    columns: [
      {
        title: '内容',
        dataIndex: 'title',
      },
      {
        title: '类型',
        dataIndex: 'code',
      },
    ],
    showIndexColumn: true,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: false,
    },
    pagination: false,
    dataSource: [
      {
        title: '高空作业风险作业书',
        code: '人员安全',
      },
      {
        title: '老旧设备漏电风险源',
        code: '设备安全',
      },
      {
        title: '危险化学品存放整改',
        code: '系统安全',
      },
      {
        title: '水质安全隐患整改',
        code: '水质安全',
      },
      {
        title: '园区安全隐患整改',
        code: '园区安全',
      },
      {
        title: '网络安全隐患整改',
        code: '网络安全',
      },
      {
        title: '人员安全隐患整改',
        code: '人员安全',
      },
      {
        title: '生产安全隐患整改',
        code: '生产安全',
      },
      {
        title: '消防安全隐患整改',
        code: '消防安全',
      },
      {
        title: '消防安全隐患整改',
        code: '消防安全',
      },
      {
        title: '消防安全隐患整改',
        code: '消防安全',
      },
      {
        title: '消防安全隐患整改',
        code: '消防安全',
      },
      {
        title: '消防安全隐患整改',
        code: '消防安全',
      },
      {
        title: '消防安全隐患整改',
        code: '消防安全',
      },
      {
        title: '消防安全隐患整改',
        code: '消防安全',
      },
      {
        title: '消防安全隐患整改',
        code: '消防安全',
      },
      {
        title: '消防安全隐患整改',
        code: '消防安全',
      },
      {
        title: '消防安全隐患整改',
        code: '消防安全',
      },
    ],
  });

  function beginScroll() {
    scrollTimer.value = setInterval(() => {
      bodyDom.value?.scrollTo({
        top: defaultTop.value,
      });

      defaultTop.value = defaultTop.value + 1;
      if (defaultTop.value > maxHeight.value) {
        defaultTop.value = 0;
      }
    }, 60);
  }

  onMounted(() => {
    setTimeout(() => {
      bodyDom.value = tableWrapperRef.value?.querySelector('.ant-table-body');
      const tableDom = tableWrapperRef.value?.querySelector('.ant-table-body table');
      maxHeight.value = tableDom.offsetHeight - tableWrapperRef.value.offsetHeight + 40;
      bodyDom.value?.addEventListener('mouseover', () => {
        clearInterval(scrollTimer.value);
      });
      bodyDom.value?.addEventListener('mouseout', () => {
        defaultTop.value = bodyDom.value.scrollTop;
        beginScroll();
      });
      beginScroll();
    }, 1000);
  });

  onBeforeUnmount(() => {
    clearInterval(scrollTimer.value);
    bodyDom.value?.removeEventListener('mouseover', () => {
      clearInterval(scrollTimer.value);
    });
    bodyDom.value?.removeEventListener('mouseout', () => {
      defaultTop.value = bodyDom.value.scrollTop;
      beginScroll();
    });
  });
</script>
