<template>
  <a-form
    :label-col="{ span: 8 }"
    :wrapper-col="{ span: 16 }"
    :colon="false"
    labelAlign="left"
    autocomplete="off"
  >
    <template v-for="(formItem, formIndex) in formList" :key="formIndex">
      <a-form-item label="套件名称">
        <a-input v-model:value="formItem.data.kitDisplayName" />
      </a-form-item>
      <a-form-item label="是否显示">
        <a-radio-group
          v-model:value="formItem.data.displayMode"
          :options="displayOptions"
          @change="onDisplayChange"
        />
      </a-form-item>
      <a-row>
        <a-col :span="24" v-show="typeof formItem.data.frameType === 'number'">
          <a-form-item label="外框方位">
            <a-select v-model:value="formItem.data.framePlacement" @change="onPlacementChange">
              <a-select-option
                v-for="(item, placementIndex) in placementOptions"
                :key="placementIndex"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24" v-show="typeof formItem.data.frameType === 'number'">
          <a-form-item label="外框横坐标">
            <a-input-number style="width: 100%" v-model:value="formItem.data.positionX" />
          </a-form-item>
        </a-col>
        <a-col :span="24" v-show="typeof formItem.data.frameType === 'number'">
          <a-form-item label="外框纵坐标">
            <a-input-number style="width: 100%" v-model:value="formItem.data.positionY" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="图表宽">
            <a-input-number
              style="width: 100%"
              v-model:value="formItem.data.businessData.width"
              :min="0"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="图表高">
            <a-input-number
              style="width: 100%"
              v-model:value="formItem.data.businessData.height"
              :min="0"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="图例颜色">
            <color-picker
              style="width: 100%"
              v-model:value="formItem.data.businessData.echarts.option.legend.textStyle.color"
              :input="true"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="x轴线颜色">
            <color-picker
              style="width: 100%"
              v-model:value="
                formItem.data.businessData.echarts.option.xAxis.axisLine.lineStyle.color
              "
              :input="true"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="x轴label颜色">
            <color-picker
              style="width: 100%"
              v-model:value="
                formItem.data.businessData.echarts.option.xAxis.axisLabel.textStyle.color
              "
              :input="true"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="y轴标题颜色">
            <color-picker
              style="width: 100%"
              v-model:value="formItem.data.businessData.echarts.option.yAxis[0].nameTextStyle.color"
              :input="true"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="y轴线颜色">
            <color-picker
              style="width: 100%"
              v-model:value="
                formItem.data.businessData.echarts.option.yAxis[0].axisLine.lineStyle.color
              "
              :input="true"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="y轴分割线颜色">
            <color-picker
              style="width: 100%"
              v-model:value="
                formItem.data.businessData.echarts.option.yAxis[0].splitLine.lineStyle.color
              "
              :input="true"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="y轴label颜色">
            <color-picker
              style="width: 100%"
              v-model:value="
                formItem.data.businessData.echarts.option.yAxis[0].axisLabel.textStyle.color
              "
              :input="true"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="联动表单项">
            <a-select v-model:value="formItem.data.businessData.formItemType">
              <a-select-option
                v-for="item in formItemOptions"
                :key="item.label"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="指标接口">
            <a-select v-model:value="formItem.data.businessData.resourceInterfaceId">
              <a-select-option
                v-for="(item, interfaceIndex) in interfaceOptions"
                :key="interfaceIndex"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="数据集">
            <TreeSelect
              v-model:value="formItem.data.businessData.dataset"
              show-search
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              placeholder="请选择指标"
              allow-clear
              tree-default-expand-all
              :tree-data="dataset"
              tree-node-filter-prop="label"
              @change="onTreeSelectChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="指标">
            <div
              style=""
              class="tag"
              v-for="item in formItem.data.businessData.indexCodes"
              :key="item.originName"
            >
              <span class="text" :title="item.displayName">{{ item.displayName }}</span>

              <span>
                <Icon class="icon" style="margin-right: 4px" icon="icon-park-outline:add"></Icon>
                <Icon
                  class="icon"
                  icon="icon-park-outline:close"
                  @click="() => handleClose(item)"
                ></Icon>
              </span>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </template>
  </a-form>
</template>

<script>
  import {
    Form,
    FormItem,
    InputNumber,
    Select,
    SelectOption,
    RadioGroup,
    Row,
    Col,
    TreeSelect,
    Tag,
  } from 'ant-design-vue';

  const AForm = Form;
  const AFormItem = FormItem;
  const AInputNumber = InputNumber;
  const ASelect = Select;
  const ASelectOption = SelectOption;
  const ARadioGroup = RadioGroup;
  const ARow = Row;
  const ACol = Col;
  const ATag = Tag;
  import { Icon } from '/@/components/Icon';

  export default {};
</script>

<script setup>
  import { ref, onMounted } from 'vue';
  import { ColorPicker } from '/@/components/ColorPicker';

  import { updatePositionNoBorder, updatePositionHasBorder } from '/@process-editor/core/kits/data';
  import { displayOptions, placementOptions, formItemOptions } from '../kit.data';
  import { updatePropData } from '../utils';
  import {
    getKitDataSetTreeApi,
    getPlatformListApi,
    getResourceInterfacePage,
  } from '/@process-editor/api/index';

  const props = defineProps({
    formList: {
      type: Array,
      default: () => [
        {
          data: {},
        },
      ],
    },
    data: {
      type: Object,
      default: () => {},
    },
    index: {
      type: Number,
      default: 0,
    },
    activePen: {
      type: Object,
      default: () => {},
    },
  });
  const UPDATE_DATA = 'update:data';
  const emits = defineEmits(['change-view-mode', 'update:data']);

  onMounted(() => {
    meta2d.on('editPen', () => {
      onPlacementChange(props.data.framePlacement);
    });
  });

  const platformId = ref('');
  async function getPlatformList() {
    const data = await getPlatformListApi();
    return data;
  }

  const interfaceOptions = ref([]);
  async function getInterfaceList() {
    // current=1&size=10&resourceIndexId=1&factoryId=2024&orgId=waterBusiness&appId=station&renterId=2024
    const params = {
      current: 1,
      pageSize: 999,
      resourceIndexId: 1,
    };
    const data = await getResourceInterfacePage(params);
    interfaceOptions.value = (data.records || []).map((i) => ({
      label: i.interfaceName,
      value: i.id,
    }));
    console.log('interfaceOptions', interfaceOptions.value);
  }
  getInterfaceList();

  const dataset = ref('');
  async function getDatasetData() {
    const params = {
      platformId: platformId.value,
      groupPurpose: 1,
    };
    const data = await getKitDataSetTreeApi(params);
    return data;
  }

  async function initData() {
    const platformList = await getPlatformList();
    platformId.value = platformList[0]?.id || '';
    const _dataset_ = await getDatasetData();
    dataset.value = _dataset_;
  }
  initData();

  function getNode(dataset, value) {
    function treeFind(tree, func) {
      for (const data of tree) {
        if (func(data)) return data;
        if (data.children) {
          const res = treeFind(data.children, func);
          if (res) return res;
        }
      }
      return null;
    }
    const node = treeFind(dataset, (item) => item.value === value);

    return node;
  }
  function onTreeSelectChange(value) {
    const node = getNode(dataset.value, value);
    console.log('node', node);
    updateFormDataByProp((data) => {
      data.businessData.indexCodes = node?.indexList || [];
      data.businessData.groupId = node?.id || '';
    });
  }

  function handleClose(item) {
    updateFormDataByProp((data) => {
      const result = data.businessData.indexCodes.filter((i) => i.originName !== item.originName);
      data.businessData.indexCodes = result;
    });
  }

  function updateFormDataByProp(cb) {
    const params = [props.data, emits, UPDATE_DATA];
    updatePropData(params, cb);
  }

  function onDisplayChange() {
    emits('change-view-mode');
  }

  function noBorderPlacement(e) {
    updateFormDataByProp((data) => {
      updatePositionNoBorder(e, data, props.activePen);
    });
  }

  function borderPlacement(e) {
    updateFormDataByProp((data) => {
      updatePositionHasBorder(e, data, props.activePen);
    });
  }

  function onPlacementChange(e) {
    const type = props.data.frameType;
    switch (type) {
      case 0:
        noBorderPlacement(e);
        break;
      case 1:
        borderPlacement(e);
        break;
      default:
        break;
    }
  }
</script>

<style lang="less" scoped>
  .tip-box {
    padding-top: 4px;
    color: #646a73;
    font-size: 12px;
  }

  .tag {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin: 0 0 8px 0;
    line-height: 28px;
    background-color: #66666633;
    padding: 0 12px;
    border-radius: 4px;

    .text {
      width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    :deep(.icon) {
      cursor: pointer;
    }
  }
</style>
