<template>
  <div class="flex extra-wrap">
    <div>
      <Select
        v-model:value="typeType"
        style="width: 100px"
        placeholder="请选择"
        v-if="typeList.length"
      >
        <SelectOption v-for="item in typeList" :key="item.value" :value="item.value">
          {{ item.label }}
        </SelectOption>
      </Select>
    </div>
    <div>
      <Select v-model:value="dataType" style="width: 80px" v-if="dataList.length">
        <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
          {{ item.label }}
        </SelectOption>
      </Select>
    </div>
    <DatePicker
      v-if="dataList.find((item) => item.value === dataType)?.label === '月'"
      v-model:value="monthDate"
      picker="month"
      valueFormat="YYYY-MM"
      :allowClear="false"
      :disabledDate="disabledDateMonth"
    />
    <DatePicker
      v-if="dataList.find((item) => item.value === dataType)?.label === '日'"
      valueFormat="YYYY-MM-DD"
      format="YYYY-MM-DD"
      v-model:value="dayDate"
      placeholder="请选择"
      :allowClear="false"
      :disabledDate="disabledDate"
    />
  </div>
</template>

<script setup lang="ts">
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import { ref, watch } from 'vue';
  import dayjs from 'dayjs';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import {
    getSenceGroupTreeWithGroupCode,
    listSenceGroupByParent,
  } from '/@zhcz/api/cost-management';

  const emit = defineEmits(['powerRing']);
  const setIndex = ref<null | number>(null);
  const itemVal = ref<any>('');
  const colorList = [
    'rgba(46, 140, 255, 1)',
    'rgba(46, 196, 255, 1)',
    'rgba(129, 67, 255, 1)',
    'rgba(34, 205, 128, 1)',
    'rgba(118, 195, 31, 1)',
    'rgba(255, 140, 46, 1)',
  ];
  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);
  type OptionItem = { label: String; value: String };
  const typeList = ref<OptionItem[]>([]);
  const dataType = ref<any>(null);
  const dataList = ref<OptionItem[]>([
    // {
    //   label: '日',
    //   value: 'dhtj2_r',
    // },
    // {
    //   label: '月',
    //   value: 'dhtj2_y',
    // },
  ]);
  // 设备
  const typeType = ref('');
  const monthDate = ref(dayjs().subtract(1, 'month').format('YYYY-MM'));
  const dayDate = ref(dayjs().subtract(1, 'day').format('YYYY-MM-DD'));
  function disabledDate(current) {
    return current && current > dayjs().subtract(0, 'day').endOf('day');
  }

  function disabledDateMonth(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('day');
  }
  async function getIndicatorList() {
    const res = await getSenceGroupTreeWithGroupCode({
      groupCode: dataType.value,
      factoryId: 1,
    });
    if (res.length) {
      typeType.value = res.find((item) => item.groupCode)?.groupCode || '';
      typeList.value = res.map((item) => ({
        value: item.groupCode,
        label: item.groupName,
      }));
      getData();
    }
  }
  async function getTimeList() {
    const res = await listSenceGroupByParent({
      groupCode: 'dhzl2_dlph',
      factoryId: 1,
      platformld: 1,
    });
    console.log('res.data', res);
    if (Object.keys(res).length) {
      dataType.value = Object.keys(res)[0];
      dataList.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      getIndicatorList();
    }
  }
  const getData = async () => {
    const startDateTime =
      dataType.value === 'dhzl2_dlph_r'
        ? dayjs(dayDate.value).format('YYYY-MM-DD 00:00:00')
        : dayjs(monthDate.value).startOf('month').format('YYYY-MM-DD 00:00:00');
    const endDateTime =
      dataType.value === 'dhzl2_dlph_r'
        ? dayjs(dayDate.value).format('YYYY-MM-DD 23:59:59')
        : dayjs(monthDate.value).endOf('month').format('YYYY-MM-DD 23:59:59');
    const params = {
      startDateTime: startDateTime,
      endDateTime: endDateTime,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      resourceInterfaceId: '4',
      groupCode: typeType.value,
      jsConvert: true,
      paramsData: JSON.stringify(params),
    };

    const { data } = await callResourceFunction(paramData);
    console.log('电量ranking', data);
    let clickFlags = false;
    if (data && data.length) {
      let newData = data.map((item, index) => {
        return {
          indexName: item.indexName,
          indexCode: item.indexCode,
          unitName: item.unitName,
          value: item.value,
          // .map((i) => Number(i.value))
          // .reduce((prev, cur) => {
          //   return prev + cur;
          // }, 0),
          color: colorList[index % 8],
          ratio: 0,
          tag: item.tag,
        };
      });

      indexList.value = newData;
      if (
        dataList.value.find((item) => item.value === dataType.value)?.label === '日' &&
        'dhzl2_dlph_r_sbyd' === typeType.value
      ) {
        clickFlags = true;
      }
      console.log('clickFlags.value', clickFlags);
      emit('powerRing', {
        bottomList: indexList.value,
        value: itemVal.value,
        date: dayDate.value,
        clickFlags,
      });
    } else {
      indexList.value = [];
      emit('powerRing', {
        bottomList: [],
        value: '',
        date: '',
        clickFlags,
      });
    }
    // console.log('电量排行', indexList.value);
  };

  watch(
    () => dayDate.value,
    async () => {
      if (dataType.value === 'dhzl2_dlph_r') {
        // pause();
        await getIndicatorList();
        // resume();
      }
    },
  );

  watch(
    () => monthDate.value,
    async (newVal) => {
      if (dataType.value === 'dhzl2_dlph_y') {
        console.log('newVal=>月', newVal);
        // pause();
        await getIndicatorList();
        // resume();
      }
    },
  );

  watch(
    () => dataType.value,
    async (newVal) => {
      console.log('newVal=>日期类型', newVal);
      monthDate.value = dayjs().subtract(1, 'month').format('YYYY-MM');
      dayDate.value = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
      // pause();
      await getIndicatorList();
      // resume();
    },
  );
  watch(
    () => typeType.value,
    async () => {
      // monthDate.value = dayjs().subtract(1, 'month').format('YYYY-MM');
      // dayDate.value = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
      // pause();
      setIndex.value = null;
      await getData();
      // resume();
    },
  );

  getTimeList();
</script>

<style lang="less" scoped>
  .extra-wrap {
    :deep(.ant-select) {
      margin-right: 16px;

      // .ant-select-selector {
      //   width: 80px;
      // }
    }
  }

  .item-list {
    width: 100%;
    // height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;

    .activeItem {
      position: relative;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid @theme-color-88p !important;

      .img_box {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }

    .hoverItem {
      cursor: pointer;

      &:hover {
        border: 1px solid @theme-color-88p;
      }
    }

    .item {
      width: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
      // justify-content: flex-start;
      padding: 16px;
      // cursor: pointer;
      border: 1px solid #d9d9d9;
      border-radius: 4px 4px 4px 4px;
      // background: #f2f3f5;
      color: #ffffff;

      .top-title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .bar-box {
        width: 100%;
        margin-top: 12px;
        height: 12px;
        background: #eeeff1;
        border-radius: 4px 4px 4px 4px;

        .bar-conter {
          height: 12px;
          background: #999999;
          border-radius: 4px 4px 4px 4px;
        }

        .abar-conter {
          height: 12px;
          background: @theme-color-88p;
          border-radius: 4px 4px 4px 4px;
        }
      }

      .abar-box {
        background: rgba(11, 98, 203, 0.16);
      }

      .item-index {
        flex: 1;
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #333333;

        .num-index {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          // background: #999;
          color: #ffffff;
          // border-radius: 10px;
        }

        .label {
          padding-left: 8px;
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          width: 120px;
          white-space: nowrap; /* 让文本不换行 */
          overflow: hidden; /* 超出部分隐藏 */
          text-overflow: ellipsis; /* 使用省略号代替超出部分 */
        }
      }

      .item-content {
        flex: 1;
        display: flex;
        align-items: baseline;
        justify-content: end;

        .value {
          font-size: 16px;
          font-weight: 600;
          color: #333333;
          display: inline-block;
          max-width: 120px;
          white-space: nowrap;
          overflow: hidden;
          // text-overflow: ellipsis;
          // .number-value {
          // }
        }

        .unit {
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          margin-left: 8px;
        }
      }

      .num-index {
        color: #ffffff;
        background: url('../assets/images/item_fo.png') no-repeat center;
        background-size: 100% 100%;
      }

      &:nth-child(1) {
        .num-index {
          // color: #ffffff;
          // background: var(--theme-color);
          background: url('../assets/images/item_o.png') no-repeat center;
          background-size: 100% 100%;
        }
      }

      &:nth-child(2) {
        .num-index {
          color: #ffffff;
          // background: var(--theme-color);
          background: url('../assets/images/item_tw.png') no-repeat center;
          background-size: 100% 100%;
        }
      }

      &:nth-child(3) {
        .num-index {
          color: #ffffff;
          // background: var(--theme-color);
          background: url('../assets/images/item_th.png') no-repeat center;
          background-size: 100% 100%;
        }
      }
    }
  }

  .empty-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #999;
  }
</style>
