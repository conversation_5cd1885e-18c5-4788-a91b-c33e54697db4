<template>
  <div class="tlc">
    <HlxbSummarySimpleCard
      :title="'节能统计'"
      v-bind="{ bottomList: summaryData, empty: empty, loading }"
    />
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  // import {
  //   // components,
  //   // loadComponents,
  //   SummarySimpleCard,
  // } from '/@zhcz/components/HLCardComponent';
  import {
    // components,
    // loadComponents,
    HlxbSummarySimpleCard,
  } from 'hlxb-ui';
  import { zydtjData } from '/@zhcz/api/cost-management';
  // import type { summaryListType } from '/@zhcz/components/HLCardComponent/src/dataType';
  import type { summaryListType } from 'hlxb-ui';
  const empty = ref(false);
  const loading = ref(false);
  const summaryData = ref<summaryListType[]>([]);
  const getSummaryData = async () => {
    try {
      loading.value = true;
      const res = await zydtjData({ factoryId: 1 });
      loading.value = false;
      console.log('res', res);
      if (res instanceof Array && res.length) {
        empty.value = false;
        summaryData.value = res;
      } else {
        empty.value = true;
      }
    } catch (err) {
      loading.value = false;
      empty.value = true;
      console.log(err);
    }
  };
  onMounted(async () => {
    // 初始化
    // 确保组件加载完成
    // await loadComponents();
    // summarySimpleCard.value = components.summarySimpleCard;
    getSummaryData();
  });
</script>
<style lang="less" scoped>
  .tlc {
    background-color: #fcfcfc;
    height: 100%;
    overflow: hidden;
    border-radius: 4px;
  }
</style>
