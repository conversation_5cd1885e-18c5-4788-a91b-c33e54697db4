<template>
  <div class="device-detail-wrapper h-full relative" :class="`tab-${activeKey}`">
    <div class="page-wrapper">
      <Tabs v-model:activeKey="activeKey" class="h-full" destroyInactiveTabPane>
        <TabPane key="1" tab="设备详情">
          <Info :activeKey="activeKey" :equipmentId="equipmentId" />
        </TabPane>
        <TabPane key="2" tab="设备分析">
          <Analysis :activeKey="activeKey" :equipmentId="equipmentId" />
        </TabPane>
        <TabPane key="3" tab="传感器信息">
          <Sensor
            :activeKey="activeKey"
            :equipmentId="equipmentId"
            :equipmentName="equipmentName"
          />
        </TabPane>
        <TabPane key="4" tab="工单记录">
          <Order
            :activeKey="activeKey"
            :equipmentId="equipmentId"
            @setFixBottomShow="setFixBottomShow"
          />
        </TabPane>
        <TabPane key="5" tab="生命周期档案">
          <Profile :activeKey="activeKey" :equipmentId="equipmentId" />
        </TabPane>
        <!-- <TabPane key="1" tab="设备详情" v-if="checkAuth('DEVICE-DETAILS')">
          <Info :activeKey="activeKey" :equipmentId="equipmentId" />
        </TabPane>
        <TabPane key="2" tab="设备分析" v-if="checkAuth('DEVICE-ANALYZE')">
          <Analysis :activeKey="activeKey" :equipmentId="equipmentId" />
        </TabPane>
        <TabPane key="3" tab="传感器信息" v-if="checkAuth('DEVICE-SENSOR')">
          <Sensor
            :activeKey="activeKey"
            :equipmentId="equipmentId"
            :equipmentName="equipmentName"
          />
        </TabPane>
        <TabPane key="4" tab="工单记录" v-if="checkAuth('DEVICE-WORKSHEET')">
          <Order :activeKey="activeKey" :equipmentId="equipmentId" />
        </TabPane>
        <TabPane key="5" tab="生命周期档案" v-if="checkAuth('DEVICE-ARCHIVES')">
          <Profile :activeKey="activeKey" :equipmentId="equipmentId" />
        </TabPane> -->
      </Tabs>
    </div>
    <div class="fix-bottom" v-if="showFixBottom">
      <div class="back-btn">
        <a-button @click="back">返回</a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="">
  import { Tabs, TabPane } from 'ant-design-vue';
  import { ref, watch } from 'vue';
  import Info from './info/index.vue';
  import Analysis from './analysis/index.vue';
  import Sensor from './sensor/index.vue';
  import Order from './order/index.vue';
  import Profile from './profile/index.vue';

  defineProps({
    equipmentId: {
      type: Number,
      default: () => null,
    },
    equipmentName: {
      type: String,
      default: () => '',
    },
  });

  const emits = defineEmits(['back']);

  const activeKey = ref('1');
  const showFixBottom = ref(true);

  // 返回
  const back = () => {
    emits('back');
  };

  const setFixBottomShow = (show: boolean) => {
    showFixBottom.value = show;
  };

  watch(
    () => activeKey.value,
    () => {
      showFixBottom.value = true;
    },
    {
      deep: true,
    },
  );
</script>
<style lang="less" scoped>
  .device-detail-wrapper {
    .page-wrapper {
      height: calc(100% - 56px);

      :deep(.ant-tabs) {
        .ant-tabs-nav {
          margin: 0;

          .ant-tabs-nav-wrap {
            background: #fff;
            padding: 0 16px;
            border-bottom: 1px solid #d8d8d8;
          }
        }

        .ant-tabs-content {
          height: 100%;
        }
      }
    }

    &.tab-2 {
      .page-wrapper {
        :deep(.ant-tabs) {
          .ant-tabs-nav {
            .ant-tabs-nav-wrap {
              border-bottom: none;
            }
          }
        }
      }
    }

    .fix-bottom {
      position: absolute;
      bottom: -16px;
      left: 0;
      width: 100%;
      height: 72px;
      background: #ffffff;
      box-shadow: 0px -4px 4px 0px rgba(0, 0, 0, 0.08);
      border-radius: 0px 0px 4px 4px;
      display: flex;
      align-items: center;

      .back-btn {
        padding-left: 16px;

        :deep(.ant-btn) {
          width: 120px;
          height: 40px;
        }
      }
    }
  }
</style>
