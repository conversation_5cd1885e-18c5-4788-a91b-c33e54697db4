<template>
  <div class="scene-resource-page">
    <div class="tree-container" :class="{ 'hide-tree-container': !isShowTree }">
      <div class="tit-content">
        <div class="tit">场景配置</div>
        <div class="add-btn" @click="handleOpenScenesModal(false)"> <PlusOutlined />新增场景 </div>
      </div>
      <!-- <div class="tree-filter">
        <Select
          v-model:value="factoryId"
          :getPopupContainer="(target) => target.parentNode"
          @change="changeFactory"
        >
          <SelectOption
            v-for="item in factoryInfoList"
            :key="item.factoryId"
            :value="item.factoryId"
            >{{ item.factoryName }}</SelectOption
          >
        </Select>
      </div> -->
      <div class="tree-content">
        <Tree
          v-if="treeData.length"
          v-model:expandedKeys="expandedKeys"
          :tree-data="treeData"
          :fieldNames="treeFieldNames"
          blockNode
          @select="handleSelectNode"
        >
          <template #title="node">
            <div
              class="flex items-center justify-between"
              @mouseenter="handleHoverNode(node)"
              @mouseleave="hoverNode = null"
            >
              <div style="overflow: hidden; text-overflow: ellipsis">
                <Tooltip>
                  <template #title>{{ node.name }}</template>
                  {{ node.name }}
                </Tooltip>
              </div>
              <Dropdown overlayClassName="custom-dropdown">
                <div v-show="hoverNode && hoverNode.id === node.id">
                  <div class="flex">
                    <Icon size="28" color="var(--theme-color)" icon="icon-park-outline:more" />
                  </div>
                </div>
                <template #overlay>
                  <Menu @mouseenter="handleHoverNode(node)" @mouseleave="hoverNode = null">
                    <Menu.Item
                      v-if="node.treeNodeType !== 1"
                      @click="handleEditLock(node.id, node.editLock)"
                    >
                      {{ node.editLock === 1 ? '关闭锁定' : '锁定编辑' }}
                    </Menu.Item>
                    <Menu.Item
                      v-if="node.editLock !== 1 || node.treeNodeType === 1"
                      @click="handleAddClassify(node.treeNodeType, node.id)"
                    >
                      添加下级分组
                    </Menu.Item>
                    <Menu.Item v-if="node.treeNodeType === 1" @click="handleOpenScenesModal(true)">
                      编辑场景
                    </Menu.Item>
                    <Menu.Item
                      v-if="node.treeNodeType !== 1 && node.editLock !== 1"
                      @click="handleChangeClassify"
                    >
                      编辑分组
                    </Menu.Item>
                    <Popconfirm
                      v-if="node.editLock !== 1 || node.treeNodeType === 1"
                      title="是否确认删除"
                      ok-text="确认"
                      cancel-text="取消"
                      @confirm="handleDeleteClassify(node.id, node.treeNodeType)"
                    >
                      <Menu.Item>
                        {{ node.treeNodeType === 1 ? '删除场景' : '删除分组' }}
                      </Menu.Item>
                    </Popconfirm>
                  </Menu>
                </template>
              </Dropdown>
            </div>
          </template>
        </Tree>
        <HEmpty v-else class="empty" />
      </div>
    </div>
    <div
      class="toggle-tree"
      @click="handleChangeTreeShow"
      :style="{
        width: isShowTree ? '0' : '24px',
        borderRight: isShowTree ? 'none' : '1px solid #e9e9e9',
      }"
    >
      <div
        class="toggle-tree-btn"
        :style="{
          left: isShowTree ? '-24px' : '0',
          borderRadius: isShowTree ? '4px 0px 0px 4px' : '0px 4px 4px 0px',
          background: isShowTree ? '' : 'var(--theme-color)',
          borderColor: isShowTree ? '' : 'var(--theme-color)',
        }"
      >
        <LeftOutlined v-show="isShowTree" />
        <RightOutlined v-show="!isShowTree" style="color: #fff" />
      </div>
    </div>

    <div ref="tableBoxRef" class="table-box">
      <Tabs @change="handleChangeResourceIndex" :activeKey="tabActiveKey as number">
        <TabPane v-for="item in resourceIndexData" :key="item.intValue" :tab="item.label" />
      </Tabs>
      <BasicTable
        @register="registerTable"
        @selection-change="handleChangeSelection"
        :class="{ hideHeader: isHideHeader }"
      >
        <template #tableTitle>
          <a-button
            type="primary"
            :style="
              selectNode && selectNode.treeNodeType !== 1 && selectNode?.editLock !== 1
                ? ''
                : 'opacity: 0.5;'
            "
            :disabled="!selectNode || selectNode.treeNodeType === 1 || selectNode?.editLock === 1"
            :icon="h(Icon, { icon: 'icon-park-outline:link-one' })"
            @click="handleBind"
          >
            绑定资源
          </a-button>
          <a-button
            class="ml-4"
            :style="isDisabledBatch || selectNode?.editLock === 1 ? 'opacity: 0.5;' : ''"
            :disabled="isDisabledBatch || selectNode?.editLock === 1"
            :icon="h(Icon, { icon: 'icon-park-outline:unlink' })"
            @click="handleBatchDelete"
            type="primary"
            ghost
            danger
          >
            批量解绑
          </a-button>
          <a-button
            v-if="tabActiveKey === RESOURCE_TYPE.API"
            class="ml-4"
            :disabled="!selectNode || selectNode.treeNodeType === 1 || getDataSource().length === 0"
            :icon="h(Icon, { icon: 'icon-park-outline:preview-open' })"
            @click="handlePreview"
          >
            预览
          </a-button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  label: '解绑',
                  onClick: ($event) => $event.stopPropagation(),
                  disabled: selectNode?.editLock === 1,
                  popConfirm: {
                    title: '是否确认解绑此场景和资源？',
                    placement: 'left',
                    confirm: handleDelete.bind(null, record),
                  },
                },
              ]"
            />
          </template>
          <template v-if="column.key === 'sort'">
            <HolderOutlined />
          </template>
          <template v-if="column.key === 'src'">
            <!-- <Image
              class="preview-img"
              :width="80"
              :src="getEevReturnDomain(record.sourceUniqueKey)"
              @click="openDetail(record)"
            /> -->
            <img
              :src="
                record.mediumType === '2' ? videoDemo : getEevReturnDomain(record.sourceUniqueKey)
              "
              style="width: 40px; height: 40px; object-fit: contain; cursor: pointer"
              @click="openDetail(record)"
            />
          </template>
          <template v-if="column.key === 'openLimit'">
            <span>{{ record.openLimit ? '是' : '否' }}</span>
          </template>
        </template>
      </BasicTable>
      <BindModal @success="reload" @register="registerBindModal" />
      <BindImageModal @success="reload" @register="registerBindImageModal" />
      <GroupModal @success="querySenceGroupTree" @register="registerGroupModal" />
      <ScenesModal @success="querySenceGroupTree" @register="registerScenesModal" />
      <PreviewModal @register="registerPreviewModal" />
    </div>
  </div>
</template>

<script lang="ts" setup name="SceneResourceConfiguration">
  import { ref, onMounted, h } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import BindModal from './components/BindModal.vue';
  import GroupModal from './components/GroupModal.vue';
  import ScenesModal from './components/ScenesModal.vue';
  import PreviewModal from './components/PreviewModal.vue';
  import BindImageModal from './components/BindImageModal.vue';
  import { columns, searchFormSchema } from './data';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getEevReturnDomain } from '/@/utils/url';
  import { Tree, Dropdown, Menu, Popconfirm, Tabs, TabPane, Tooltip } from 'ant-design-vue';
  import {
    getSenceGroupTree,
    deleteSences,
    deleteGroups,
    deleteDisplayResourceInfos,
    getResourceInfoPages,
    updateEditLock,
    batchUpdateSort,
  } from '/@zhcz/api/config-center/scenes-group';
  import { useLoading } from '/@/components/Loading';
  import { LeftOutlined, RightOutlined, PlusOutlined, HolderOutlined } from '@ant-design/icons-vue';
  import { ResourceIndex } from './typing';
  import Icon from '/@/components/Icon';
  // import { stubFalse } from 'lodash-es';
  import HEmpty from '/@/components/HEmpty/index.vue';
  // import { getDictTypeListApi } from '/@/api/admin/dict';
  import { RESOURCE_TYPE } from '/@zhcz/enums/sceneResource';
  import { createImgPreview } from '/@zhcz/components/Preview/index';
  import videoDemo from '../img-resource-manager/videoDemo.png';

  defineOptions({
    name: 'SceneResourceSceneResourceConfiguration',
  });

  const [openFullLoading, closeFullLoading] = useLoading({});
  const treeFieldNames = {
    title: 'name',
    key: 'id',
    children: 'children',
  };
  const selectNode = ref();
  const treeData = ref([]);
  const expandedKeys = ref([]);
  const isShowTree = ref(true);
  const tableBoxRef = ref<null | HTMLElement>(null);
  // 新版场景资源分组更新冲突，暂时定死
  const resourceIndexData: any = ref([
    {
      label: '指标',
      id: '1',
      intValue: RESOURCE_TYPE.API,
    },
    {
      label: '资源',
      id: '1768492693508411394',
      intValue: RESOURCE_TYPE.DATABASE,
    },
  ]);
  const resourceIndexInfo: any = ref(undefined);
  const isDisabledBatch = ref(true);
  const isHideHeader = ref(true);
  const hoverNode = ref();
  const platformId = ref();
  const sourceObj = ref<ResourceIndex | undefined>(undefined);
  const targetObj = ref<ResourceIndex | undefined>(undefined);
  const tabActiveKey = ref<null | number>(null);
  const openDetail = (row) => {
    try {
      const { sourceUniqueKey, mediumType } = row;
      const fileType = sourceUniqueKey.split('.').pop();
      if (mediumType === '2') {
        if (['mp4', 'flv', 'webm'].includes(fileType)) {
          createImgPreview({
            imageList: [getEevReturnDomain(sourceUniqueKey)],
            defaultWidth: 700,
            rememberState: true,
          });
          return;
        }
      } else if (mediumType === '1') {
        if (['png', 'jpeg', 'jpg'].includes(fileType)) {
          createImgPreview({
            imageList: [getEevReturnDomain(sourceUniqueKey)],
            defaultWidth: 700,
            rememberState: true,
          });
          return;
        }
      }
      // previewFile(record.fileDTOS[0]);
      // openPreviewModal(true);
    } catch (error) {
      createMessage.error('预览失败，请检查文件是否存在');
    }
  };
  // const userStore = useUserStore();
  // const userInfo = computed(() => userStore.getUserInfo);
  // const factoryInfoList = computed(() => userInfo.value.factoryInfoList);
  // const factoryId = computed(
  //   () =>
  //     factoryInfoList.value?.find((item) => item.isUsed == '1')?.factoryId ||
  //     factoryInfoList.value[0].factoryId,
  // );

  const customRow = (record, index) => {
    return {
      style: {
        cursor: 'grab',
      },
      // 鼠标移入
      onMouseenter: (ev) => {
        ev.target.draggable = true;
      },
      // 开始拖拽
      onDragstart: (ev) => {
        // 阻止冒泡
        ev.stopPropagation();
        // 得到源目标数据
        sourceObj.value = record;
      },
      // 拖动元素经过的元素
      onDragover: (ev) => {
        // 阻止默认行为
        ev.preventDefault();

        Array.from(document.getElementsByClassName('ant-table-tbody')).forEach((parent) => {
          Array.from(parent.getElementsByTagName('tr')).forEach((item, i) => {
            if (i === index) {
              Array.from(item.getElementsByTagName('td')).forEach((tdItem) => {
                tdItem.style.borderBottom = '2px solid #1677ff';
              });
            } else {
              Array.from(item.getElementsByTagName('td')).forEach((tdItem) => {
                tdItem.style.borderBottom = 'none';
              });
            }
          });
        });
      },
      // 拖动元素离开的元素
      onDrop: async (ev) => {
        try {
          openFullLoading();
          // 阻止冒泡
          ev.stopPropagation();
          // 得到目标数据
          targetObj.value = record;

          const tableData = getDataSource();
          const source = tableData.findIndex((item) => item.id == sourceObj.value!.id);
          const target = tableData.findIndex((item) => item.id == targetObj.value!.id);
          tableData.splice(source, 1);
          tableData.splice(target, 0, sourceObj.value!);
          const params = tableData.map((item, index) => {
            return {
              id: item.id,
              sort: index,
              displayName: item.displayName,
            };
          });

          try {
            await batchUpdateSort({ data: params });
            createMessage.success('操作成功');
          } catch (_) {
            tableData.splice(target, 1);
            tableData.splice(source, 0, sourceObj.value!);
          } finally {
            closeFullLoading();
            //松开鼠标后，清除底部拖拽样式
            removeStyle();
          }
        } finally {
          closeFullLoading();
        }
      },
      onMouseleave: (ev) => {
        //鼠标移出拖拽范围禁止拖拽并清除拖拽效果
        ev.target.draggable = false;
        removeStyle();
      },
    };
  };

  //移除拖拽样式方法
  const removeStyle = () => {
    Array.from(document.getElementsByClassName('ant-table-tbody')).forEach((parent) => {
      Array.from(parent.getElementsByTagName('tr')).forEach((item) => {
        Array.from(item.getElementsByTagName('td')).forEach((tdItem) => {
          tdItem.style.borderBottom = 'none';
        });
      });
    });
  };

  const [
    registerTable,
    { reload, setTableData, getSelectRows, getDataSource, getColumns, setColumns },
  ] = useTable({
    columns,
    rowSelection: {
      type: 'checkbox',
    },
    clickToRowSelect: false,
    api: getResourceInfoPages,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'size',
      listField: 'records',
      totalField: 'total',
    },
    immediate: false,
    showIndexColumn: false,
    formConfig: {
      labelWidth: 80,
      schemas: searchFormSchema,
    },
    beforeFetch: (pageParams) => {
      return {
        ...pageParams,
        resourceId: resourceIndexInfo.value?.id,
        groupId: selectNode.value.id,
        sort: 1,
      };
    },
    afterFetch: (data) => {
      isHideHeader.value = data.length === 0;
    },
    useSearchForm: true,
    actionColumn: {
      width: 80,
      title: '标签列表',
      dataIndex: 'action',
      fixed: 'right',
    },
    customRow,
  });
  const [registerBindModal, { openModal: openBindModal }] = useModal();
  const [registerGroupModal, { openModal: openGroupModal }] = useModal();
  const [registerScenesModal, { openModal: openScenesModal }] = useModal();
  const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();
  const [registerBindImageModal, { openModal: openBindImageModal }] = useModal();

  // async function queryPlatformId() {
  //   const data = await getCurrentPlatformId();
  //   platformId.value = data.id;
  // }

  const handleHoverNode = (node) => {
    hoverNode.value = node;
  };

  // function handlePreviewIndex() {
  //   dataCurveRef.value.openCurve();
  // }

  const handlePreview = () => {
    // const tableData = getDataSource();
    // if (tableData.length === 0) {
    //   createMessage.warning('请绑定资源');
    //   return;
    // }
    if (!selectNode.value || selectNode.value.treeNodeType === 1) {
      return;
    }
    openPreviewModal(true, {
      selectNode: selectNode.value,
      resourceId: resourceIndexInfo.value?.id,
    });
  };

  const handleChangeSelection = (data) => {
    isDisabledBatch.value = data.rows.length === 0;
  };

  const updateColumns = (resourceIndexId: number) => {
    const cols = getColumns();
    const unitNameItem = cols.filter((item) => item.dataIndex === 'unitName')[0];
    const srcItem = cols.filter((item) => item.dataIndex === 'src')[0];
    if (resourceIndexId === 1) {
      unitNameItem.ifShow = false;
      srcItem.ifShow = true;
    } else {
      unitNameItem.ifShow = true;
      srcItem.ifShow = false;
    }
    setColumns(getColumns());
  };

  // 资源索引tab切换
  const handleChangeResourceIndex = (value: number) => {
    resourceIndexInfo.value = resourceIndexData.value.find((item) => item.intValue === value);
    tabActiveKey.value = value;
    updateColumns(value);
    if (!selectNode.value) {
      return;
    }
    reload();
  };

  const handleOpenScenesModal = (isEdit: boolean) => {
    openScenesModal(true, {
      isEdit,
      selectNode: hoverNode.value,
      platformId: platformId.value,
    });
  };

  const handleChangeTreeShow = () => {
    isShowTree.value = !isShowTree.value;
    if (tableBoxRef.value) {
      tableBoxRef.value.style.width = isShowTree.value ? 'calc(100% - 248px)' : 'calc(100% - 24px)';
    }
  };

  const handleDeleteClassify = async (id: number, type: number) => {
    if (hoverNode.value?.children.length > 0) {
      createMessage.warning('存在子分组，不允许删除');
      return;
    }
    const postUrl = type === 1 ? deleteSences : deleteGroups;
    await postUrl(id);
    selectNode.value = undefined;
    hoverNode.value = undefined;
    createMessage.success('操作成功');
    querySenceGroupTree();
  };

  const handleChangeClassify = () => {
    openGroupModal(true, {
      isEdit: true,
      selectNode: hoverNode.value,
    });
  };

  const handleAddClassify = (type, parentId) => {
    openGroupModal(true, {
      isEdit: false,
      parentId: type === 1 ? null : parentId,
      selectNode: hoverNode.value,
    });
  };

  async function handleSelectNode(selectedKeys, e) {
    resourceIndexInfo.value = resourceIndexData.value[0];
    // tabActiveKey.value = resourceIndexData.value.length ? resourceIndexData.value[0].value : null;
    tabActiveKey.value = resourceIndexInfo.value?.intValue ?? null;
    const resourceIndexId = resourceIndexData.value.length
      ? resourceIndexData.value[0].intValue
      : RESOURCE_TYPE.API;
    updateColumns(resourceIndexId);
    if (!selectedKeys[0]) {
      selectNode.value = undefined;
      setTableData([]);
      return;
    }
    selectNode.value = e.node;
    if (e.node.treeNodeType === 1) {
      setTableData([]);
      return;
    }
    // await queryResourceIndex();
    reload();
  }

  function handleBind() {
    if (
      !selectNode.value ||
      selectNode.value.treeNodeType === 1 ||
      selectNode.value?.editLock === 1
    ) {
      return;
    }
    if (tabActiveKey.value === RESOURCE_TYPE.DATABASE) {
      openBindImageModal(true, {
        isEdit: false,
        selectNode: selectNode.value,
        resourceIndexInfo: resourceIndexInfo.value,
      });
    } else {
      openBindModal(true, {
        isEdit: false,
        selectNode: selectNode.value,
        resourceIndexInfo: resourceIndexInfo.value,
      });
    }
  }

  const processTreeNodes = (
    treeNodes,
    parentId = null,
    parentName = null,
    parentNodeType = null,
    rootId = null,
  ) => {
    const processedNodes: any = [];

    for (const node of treeNodes) {
      const processedNode = {
        ...node,
        parentId: parentId,
        parentName: parentName,
        parentNodeType: parentNodeType,
        scenceId: rootId || node.id,
        children: null,
      };
      if (node.children && node.children.length > 0) {
        processedNode.children = processTreeNodes(
          node.children,
          node.id,
          node.name,
          node.treeNodeType,
          rootId || node.id,
        );
      }

      processedNodes.push(processedNode);
    }

    return processedNodes;
  };

  const flattenTreeData = (treeData) => {
    const flattenData: any = [];
    const flatten = (data) => {
      data.forEach((item) => {
        flattenData.push(item);
        if (item.children && item.children.length > 0) {
          flatten(item.children);
        }
      });
    };
    flatten(treeData);
    return flattenData;
  };

  // async function queryResourceIndex() {
  //   await getDictTypeListApi({ type: DICT.RESOURCE_TYPE }).then((data) => {
  //     resourceIndexData.value = data;
  //     resourceIndexInfo.value = data[0];
  //     tabActiveKey.value = data[0].intValue;
  //   });
  //   // const data = await getResourceIndexPage();
  //   // resourceIndexData.value = data.records;
  //   // console.log('resourceIndexData', resourceIndexData.value);
  //   // resourceIndexInfo.value = data.records[0];
  //   // tabActiveKey.value = data.records[0].id;
  // }

  const querySenceGroupTree = async () => {
    openFullLoading();
    const data = await getSenceGroupTree();
    treeData.value = processTreeNodes(data);
    // 平铺数据
    const flattenData = flattenTreeData(treeData.value);
    // 更新选中节点
    if (selectNode.value) {
      const node = flattenData.find((item) => item.id === selectNode.value.id);
      if (node) {
        selectNode.value = node;
      }
    }
    closeFullLoading();
  };

  const handleBatchDelete = async () => {
    if (isDisabledBatch.value || selectNode.value?.editLock === 1) {
      return;
    }
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: `是否确认批量解绑？`,
      onOk: async () => {
        const ids = getSelectRows().map((item) => item.id);
        await deleteDisplayResourceInfos({ ids });
        createMessage.success('操作成功');
        isDisabledBatch.value = true;
        reload();
      },
    });
  };

  const { createMessage, createConfirm } = useMessage();
  async function handleDelete(record: Recordable, $event) {
    $event.stopPropagation();
    await deleteDisplayResourceInfos({ ids: [record.id] });
    createMessage.success('操作成功');
    reload();
  }

  async function handleEditLock(groupId, lock) {
    let editLock = lock === 0 ? 1 : 0;
    await updateEditLock(groupId, editLock);
    createMessage.success('操作成功');
    await querySenceGroupTree();
    await reload();
  }

  // const getPlatformInfo = async () => {
  //   await getPlatformInfoPage();
  // };

  onMounted(() => {
    querySenceGroupTree();
    // queryPlatformId();
  });
</script>

<style lang="less" scoped>
  .scene-resource-page {
    padding: 0 16px 16px;
    display: flex;
    // min-height: 816px;
    height: 100%;
    overflow: hidden;

    .tree-container {
      background: #fff;
      width: 248px;
      flex-shrink: 0;
      border-right: 1px solid #e9e9e9;
      transition: all 0.3s;
      overflow: hidden;

      .tit-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        padding: 0 12px;
        border-bottom: 1px solid #e9e9e9;
        white-space: nowrap;

        .tit {
          font-size: 16px;
          font-weight: 600;
        }

        .add-btn {
          color: @theme-color;
          cursor: pointer;

          .anticon-plus {
            margin-right: 4px;
          }
        }
      }

      .tree-filter {
        padding: 12px 12px 0;

        :deep(.ant-select) {
          width: 100%;
        }
      }

      .tree-content {
        padding: 12px;
        overflow-y: auto;
        height: calc(100% - 60px);

        :deep(.ant-tree) {
          background: #fff;

          .ant-tree-node-content-wrapper {
            overflow: hidden;

            &.ant-tree-node-selected {
              .ant-tree-title {
                color: var(--theme-color);
              }
            }
          }
        }

        .empty {
          height: 100%;
        }
      }
    }

    .hide-tree-container {
      width: 0;
      padding: 0;
      border-right: none;

      .tit-content,
      .tree-content {
        display: none;
      }
    }

    .toggle-tree {
      position: relative;
      width: 24px;
      height: 100%;
      background: #fff;
      border-right: 1px solid #e9e9e9;
      cursor: pointer;

      .toggle-tree-btn {
        position: absolute;
        top: 50%;
        width: 24px;
        height: 32px;
        border-radius: 4px 0px 0px 4px;
        border: 1px solid #e9e9e9;
        transform: translateY(-50%);
        cursor: pointer;
        text-align: center;
        line-height: 30px;
        z-index: 999;
        background: white;
        transition: all 0.3s;

        .anticon {
          margin: 0;
        }
      }
    }

    .table-box {
      background: #fff;
      position: relative;
      flex: 1;

      :deep(.ant-tabs-nav) {
        margin: 0;
        padding-left: 24px;

        &:before {
          border-bottom-color: #e9e9e9;
        }
      }
    }
  }

  :deep(.vben-basic-table) {
    .ant-form {
      background-color: #fff;
      border-bottom: 8px solid #eeeff1;
      border-radius: 4px 4px 0 0;
    }

    .ant-table-wrapper {
      background: #fff;
      padding: 16px;

      .ant-table {
        .ant-table-expanded-row-fixed {
          background: #fff;
        }
      }
    }
  }

  :deep(.vben-basic-table-form-container) {
    padding: 0;
    height: calc(100% - 48px);
  }

  .preview-img {
    height: 30px;
  }
</style>
