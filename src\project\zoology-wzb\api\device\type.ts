export type faultDeviceTreeType = Array<deviceTreeItem>;
export type deviceTreeItem = {
  id: number | string;
  name: string;
  type: number;
  sort?: number;
  children?: Array<deviceTreeItem>;
};

export interface faultDeviceDetailType {
  id: number; // 设备id
  name: string; // 设备名称
  faultDate: string; // 故障时间
  finishedDate: string; // 完成时间
  faultPhenomenon: string; // 故障现象
  faultReasonValue: number; // 故障原因值，
}

export type faultDeviceListType = {
  total: number;
  page: number;
  pageSize: number;
  list: Array<faultDeviceDetailType>;
};

export interface addOrEditFaultDeviceType extends faultDeviceDetailType {
  type: 'add' | 'edit';
  emergencyLevelValue: ''; // 紧急程度
  imageUrl?: ''; // 图片
}

export type FalutRatioType = Array<{
  name: string; // 原因名称
  value: number; // 次数
  percent: number;
}>;

export type faultDeviceRankType = Array<{
  name: string; // 设备名称
  count: string; // 故障次数
}>;

export type faultDeviceRecordType = Array<{
  date: Array<string>; // 日期
  data: Array<number>; // 数据
  name: string;
}>;

export type equipmentOperatingTimType = Array<{
  date: Array<string>; // 日期
  data: Array<number>; // 数据
  name: string;
}>;
