<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="isKyk ? '查看排班' : '排班计划'"
    width="1372px"
    :loading="okLoading"
  >
    <div class="schedule-warp">
      <div class="schedule-container" v-if="!isKyk">
        <div class="flex items-center justify-between mb-3">
          <div class="title">排班信息</div>
          <!-- <div class="oper-btn">
            <a-button
              class="add-btn"
              type="ghost"
              :icon="h(Icon, { icon: 'icon-park-outline:refresh' })"
              @click="confirmSchedule"
            >
              更新
            </a-button>
          </div> -->
        </div>
        <BasicForm @register="registerForm">
          <template #startTimes="{ model, field }">
            <RangePicker
              popupClassName="schedule-picker-popup"
              v-model:value="model[field]"
              :disabledDate="disabledBeginDate"
              :showToday="false"
              :showTime="false"
              @change="handleChangeFeild"
            >
              <template #dateRender="{ current }">
                <div class="ant-picker-cell-inner" :style="getCurrentStyle(current)">
                  {{ current.date() }}
                </div>
              </template>
            </RangePicker>
          </template>
        </BasicForm>
        <div class="flex items-center justify-end mt-3">
          <a-button
            class="add-btn w-82"
            type="ghost"
            :icon="h(Icon, { icon: 'icon-park-outline:doc-detail' })"
            @click="confirmSchedule"
          >
            生成明细
          </a-button>
        </div>
      </div>
      <div class="schedule-info" :style="{ width: isKyk ? '100%' : 'calc(100% - 468px)' }">
        <div v-if="isKyk">
          <Form layout="inline">
            <FormItem label="日期范围" :rules="[{ required: true }]">
              <RangePicker
                popupClassName="schedule-picker-popup"
                v-model:value="kykDate"
                :showToday="false"
                :allowClear="false"
                :showTime="false"
              >
                <template #dateRender="{ current }">
                  <div class="ant-picker-cell-inner" :style="getCurrentStyle(current)">
                    {{ current.date() }}
                  </div>
                </template>
              </RangePicker>
            </FormItem>
            <FormItem>
              <a-button
                style="width: 4.5rem"
                class="add-btn-n"
                type="ghost"
                @click="handleChangeFeild"
                >查询</a-button
              >
            </FormItem>
          </Form>
        </div>
        <div class="flex items-center justify-between mb-1">
          <div class="title"> 排班明细 </div>
          <div class="oper-container" v-if="!isKyk">
            <a-button
              danger
              :class="['delete-noon-btn', '!mr-0']"
              :icon="h(Icon, { icon: 'icon-park-outline:clear' })"
              @click="clearData"
            >
              {{ '清除' }}
            </a-button>
          </div>
        </div>

        <div class="content-container">
          <div class="fixed-header">
            <div class="w-12 text-center flex justify-between">
              <Checkbox
                v-if="!isKyk"
                class="ml-4"
                :indeterminate="indeterminate"
                @change="handleChangeAllCheck"
                v-model:checked="checkAll"
              />
              <div v-if="!isKyk" class="split ml-3">|</div>
            </div>
            <div class="w-15 ml-4"> 日期 </div>
            <div class="item person">人员</div>
            <div
              class="item"
              :class="{
                'item-one': tableColLen1,
                'item-second': tableColLen2,
              }"
              v-show="isShowMorn || isKyk"
            >
              <Checkbox v-if="!isKyk" class="mr-1" v-model:checked="checkMorning" />
              早班
              <!-- <span class="clear-icon" @click="clearMorning">
                <Icon icon="icon-park-outline:clear" />
              </span> -->
            </div>
            <div
              class="item"
              :class="{
                'item-one': tableColLen1,
                'item-second': tableColLen2,
              }"
              v-show="isShowNoon || isKyk"
            >
              <Checkbox v-if="!isKyk" class="mr-1" v-model:checked="checkNoon" />
              中班
              <!-- <span class="clear-icon">
                <Icon icon="icon-park-outline:clear" @click="clearNoon" />
              </span> -->
            </div>
            <div
              class="item"
              :class="{
                'item-one': tableColLen1,
                'item-second': tableColLen2,
              }"
              v-show="isShowNight || isKyk"
            >
              <Checkbox v-if="!isKyk" class="mr-1" v-model:checked="checkNight" />
              晚班
              <!-- <span class="clear-icon"> v-click-outside
                <Icon icon="icon-park-outline:clear" @click="clearNight" />
              </span> -->
            </div>
          </div>
          <div class="scheduling-details-container">
            <div v-for="(item, index) in scheduleData" :key="index" class="detail-item">
              <div class="w-12 text-center">
                <Checkbox
                  v-if="!isKyk"
                  v-model:checked="item.checked"
                  @change="handleChangeCheck"
                />
              </div>
              <div class="w-15 ml-4">{{ dayjs(item.scheduleTime).format('MM-DD') || '-' }}</div>
              <div class="person">
                <div class="item leader">班长</div>
                <div class="item">组员</div>
              </div>
              <div
                v-if="!item.isEdit || isKyk"
                @click="handleChangeEdit(index)"
                class="person-items"
              >
                <!-- 早 -->
                <div
                  class="schedule-item"
                  :class="{
                    'schedule-item-second': tableColLen2,
                    'schedule-item-one': tableColLen1,
                  }"
                  v-show="isShowMorn || isKyk"
                >
                  <div class="item leader">
                    <div class="lead-name" :style="{ opacity: item.morningLeader.length }">
                      {{ getNameCon(item.morningLeader) }}
                      <!-- <span class="tag morning">班长</span> -->
                    </div>
                  </div>
                  <div class="item">
                    <!-- <div class="name"> {{ getNameCon(item.morningPerson) }} </div> -->
                    <tooltip @mouseenter="showTooltip">
                      <template #title>
                        {{ getNameCon(item.morningPerson) }}
                      </template>
                      <div class="name"> {{ getNameCon(item.morningPerson) }} </div>
                    </tooltip>
                  </div>
                </div>
                <!-- 中 -->
                <div
                  class="schedule-item"
                  :class="{
                    'schedule-item-second': tableColLen2,
                    'schedule-item-one': tableColLen1,
                  }"
                  v-show="isShowNoon || isKyk"
                >
                  <div class="item leader">
                    <div class="lead-name" :style="{ opacity: item.noonLeader.length }">
                      {{ getNameCon(item.noonLeader) }}
                      <!-- <span class="tag noon">班长</span> -->
                    </div>
                  </div>
                  <div class="item">
                    <tooltip @mouseenter="showTooltip">
                      <template #title>
                        {{ getNameCon(item.noonPerson) }}
                      </template>
                      <div class="name"> {{ getNameCon(item.noonPerson) }} </div>
                    </tooltip>
                    <!-- <div class="name"> {{ getNameCon(item.noonPerson) }} </div> -->
                  </div>
                </div>
                <!-- 晚 -->
                <div
                  class="schedule-item"
                  :class="{
                    'schedule-item-second': tableColLen2,
                    'schedule-item-one': tableColLen1,
                  }"
                  :style="{ borderRight: isShowNoon ? 'none' : '1px solid #f5f6f7' }"
                  v-show="isShowNight || isKyk"
                >
                  <div class="item leader">
                    <div class="lead-name" :style="{ opacity: item.nightLeader.length }">
                      {{ getNameCon(item.nightLeader) }}
                      <!-- <span class="tag night">班长</span> -->
                    </div>
                  </div>
                  <div class="item">
                    <tooltip @mouseenter="showTooltip">
                      <template #title>
                        {{ getNameCon(item.nightPerson) }}
                      </template>
                      <div class="name"> {{ getNameCon(item.nightPerson) }} </div>
                    </tooltip>
                  </div>
                </div>
              </div>
              <div v-else class="person-items">
                <!-- 早 -->
                <div
                  class="schedule-item"
                  :class="{
                    'schedule-item-second': tableColLen2,
                    'schedule-item-one': tableColLen1,
                  }"
                  v-show="isShowMorn"
                >
                  <div class="item leader">
                    <HChooseUser
                      v-model:value="item.morningLeader"
                      :maxSelect="1"
                      class="w-full"
                      placeholder="请选择早班班长"
                    />
                  </div>
                  <div class="item">
                    <HChooseUser
                      v-model:value="item.morningPerson"
                      class="w-full"
                      placeholder="请选择早班组员"
                      :maxTagCount="maxTagCount"
                    />
                  </div>
                </div>
                <!-- 中 -->
                <div
                  class="schedule-item"
                  :class="{
                    'schedule-item-second': tableColLen2,
                    'schedule-item-one': tableColLen1,
                  }"
                  v-show="isShowNoon"
                >
                  <div class="item leader">
                    <HChooseUser
                      v-model:value="item.noonLeader"
                      :maxSelect="1"
                      class="w-full"
                      placeholder="请选择中班班长"
                    />
                  </div>
                  <div class="item">
                    <HChooseUser
                      v-model:value="item.noonPerson"
                      class="w-full"
                      placeholder="请选择中班组员"
                      :maxTagCount="schedulePlan.length <= 2 ? 3 : 1"
                    />
                  </div>
                </div>

                <!-- 晚 -->
                <div
                  class="schedule-item"
                  :class="{
                    'schedule-item-second': tableColLen2,
                    'schedule-item-one': tableColLen1,
                  }"
                  :style="{ borderRight: isShowNoon ? 'none' : '1px solid #f5f6f7' }"
                  v-show="isShowNight"
                >
                  <div class="item leader">
                    <HChooseUser
                      v-model:value="item.nightLeader"
                      :maxSelect="1"
                      class="w-full"
                      placeholder="请选择晚班班长"
                    />
                  </div>
                  <div class="item">
                    <HChooseUser
                      v-model:value="item.nightPerson"
                      class="w-full"
                      placeholder="请选择晚班组员"
                      :maxTagCount="schedulePlan.length <= 2 ? 2 : 1"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">{{ isKyk ? '返回' : '取消' }}</a-button>
      <a-button v-if="!isKyk" class="add-btn-n" type="ghost" @click="handleSubmit(0)"
        >保存</a-button
      >
      <a-button v-if="!isKyk" type="primary" @click="handleSubmit(1)">立即生效</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive, h, CSSProperties, computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { Checkbox, RangePicker, Form, FormItem, Tooltip } from 'ant-design-vue';
  //   import vClickOutside from '/@/directives/clickOutside';
  // v-click-outside="handleCancelEdit"
  import dayjs, { Dayjs } from 'dayjs';
  import { HChooseUser } from '/@/components/HChooseUser';
  import {
    createPlant,
    queryScheduleCycle,
    // getGainPlanDay
  } from '/@zhcz/api/scheduling';
  import { formSchemas } from './form.data';
  import { getScheduleGroupListApi } from '/@zhcz/api/scheduling';
  import { FACTORY_KEY } from '/@/enums/cacheEnum';
  import { createLocalStorage } from '/@/utils/cache';
  import { useUserStore } from '/@/store/modules/user';

  // import { HChooseUser } from '/@zhcz/components/HChooseUser';
  const ls = createLocalStorage();
  const userStore = useUserStore();

  const okLoading = ref(false);
  const toDay = dayjs();
  const formState = reactive({
    id: '',
    name: '',
    type: 1,
    startTimes: [toDay, toDay.add(1, 'day')],
    planEndTime: toDay as any,
    startTime: toDay as any,
    cycle: 1,
    isEffective: 0,
    remark: '',
    schedulePlan: [] as any[],
    morn: [],
    noon: [],
    night: [],
  });

  function showTooltip(e) {
    if (e.target.clientWidth >= e.target.scrollWidth) {
      e.target.style.pointerEvents = 'none';
    }
  }
  const schedulePlan = ref<any[]>([1, 2, 3]);

  const scheduleData = ref<Indexable[]>([]);
  const dataNextDay = ref();
  const grouPptions = ref<any[]>([]);

  const emit = defineEmits(['success', 'register']);

  const { createMessage, createConfirm } = useMessage();
  const [registerForm, { setFieldsValue, updateSchema, validate, resetFields, getFieldsValue }] =
    useForm({
      schemas: formSchemas,
      showActionButtonGroup: false,
      labelWidth: 90,
      baseColProps: {
        span: 24,
      },
      rowProps: {
        gutter: 24,
      },
    });
  const factoryId = computed(() => ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId);
  const isKyk = ref(false);
  const kykDate = ref([toDay, toDay.add(1, 'day')]);
  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    okLoading.value = true;
    isKyk.value = data?.isKyk;
    if (isKyk.value) {
      kykDate.value = [toDay, toDay.add(1, 'day')];
    }
    Object.assign(formState, data.baseForm);
    try {
      await resetFields();
      const params: Indexable = { type: data.baseForm.type, schedulePlanId: data.baseForm.id };
      // await getPlanData(params);
      params.startTime = dayjs(formState.startTimes[0]).format('YYYY-MM-DD');
      params.endTime = dayjs(formState.startTimes[1]).format('YYYY-MM-DD');
      await getScheduleCycleData(params);
      const options = await getScheduleGroupListApi(factoryId.value);
      grouPptions.value = options;
      updateSchema([
        // {
        //   field: 'startTimes',
        //   label: '日期范围',
        //   component: 'RangePicker',
        //   required: true,
        //   componentProps: {
        //     showTime: false,
        //     showToday: false,
        //     disabledDate: disabledBeginDate,
        //   },
        // },
        {
          field: 'schedulePlan',
          label: '班次选择',
          required: false,
        },
        { field: 'morn', componentProps: { options } },
        { field: 'noon', componentProps: { options } },
        { field: 'night', componentProps: { options } },
      ]);

      await setFieldsValue(formState);
      updateSchema({
        field: 'schedulePlan',
        label: '班次选择',
        required: false,
      });
    } catch (e) {
      throw e;
    } finally {
      okLoading.value = false;
    }
  });

  // 获取已排班最后一天
  // const getPlanData = async (params: Indexable) => {
  //   const date = await getGainPlanDay(params);

  //   dataNextDay.value = date;
  //   const firstDate = date ? dayjs(date).add(1, 'day') : dayjs();
  //   formState.startTimes[0] = firstDate;

  //   formState.startTimes[1] = firstDate.add(formState.cycle - (date ? 1 : 0), 'day');
  // };

  const sortScheduleDataDesc = (data) => {
    data.sort((a, b) => {
      const timeA = dayjs(a.scheduleTime).valueOf(); // Convert to timestamp
      const timeB = dayjs(b.scheduleTime).valueOf();
      return timeA - timeB; // Descending order
    });
  };
  // 获取已排班周期数据
  const getScheduleCycleData = async (params: Indexable) => {
    const res = await queryScheduleCycle(params);
    if (res?.scheduleDayInfoList.length) {
      scheduleData.value = [];

      const newData = Array.from({ length: res.scheduleDayInfoList.length }, (_, i) => {
        return {
          isEdit: true,
          morningPerson: [],
          morningLeader: [],
          noonPerson: [],
          noonLeader: [],
          nightPerson: [],
          nightLeader: [],
          sort: i + 1,
        };
      });
      scheduleData.value = newData;

      if (
        res.scheduleDayInfoList[0].schedulePersonInfoList &&
        res.scheduleDayInfoList[0].schedulePersonInfoList.length
      ) {
        sortScheduleDataDesc(res.scheduleDayInfoList);

        res.scheduleDayInfoList.map((item, index) => {
          scheduleData.value[index].isEdit = false;
          scheduleData.value[index].scheduleTime =
            item.scheduleTime ?? dayjs(formState.startTimes[0]).add(index, 'day');
          if (item.schedulePlanId) {
            formState.id = item.schedulePlanId;
          }
          scheduleData.value[index].schedulePlanId = formState.id;

          item.schedulePersonInfoList.forEach((info) => {
            const { classType, leaderId, leaderName, scheduleMemberList, detailId } = info;
            const persons = scheduleMemberList
              ? scheduleMemberList.filter((person) => person.memberName !== '')
              : [];
            if (classType === 1) {
              schedulePlan.value[0] = 1;
              scheduleData.value[index].classType1Id = detailId;
              scheduleData.value[index].morningPerson = persons.map((person) => ({
                userId: person.memberId,
                name: person.memberName,
              }));
              scheduleData.value[index].morningLeader = leaderId
                ? [
                    {
                      userId: leaderId,
                      name: leaderName,
                    },
                  ]
                : [];
            } else if (classType === 2) {
              schedulePlan.value[1] = 2;
              scheduleData.value[index].classType2Id = detailId;
              scheduleData.value[index].noonPerson = persons.map((person) => ({
                userId: person.memberId,
                name: person.memberName,
              }));
              scheduleData.value[index].noonLeader = leaderId
                ? [
                    {
                      userId: leaderId,
                      name: leaderName,
                    },
                  ]
                : [];
            } else if (classType === 3) {
              schedulePlan.value[2] = 3;
              scheduleData.value[index].classType3Id = detailId;
              scheduleData.value[index].nightPerson = persons.map((person) => ({
                userId: person.memberId,
                name: person.memberName,
              }));
              scheduleData.value[index].nightLeader = leaderId
                ? [
                    {
                      userId: leaderId,
                      name: leaderName,
                    },
                  ]
                : [];
            }
          });
        });
      }
    } else {
      const newData = Array.from({ length: formState.cycle }, (_, i) => {
        const sort = i + 1;
        return {
          scheduleTime: dayjs(params.startTime),
          isEdit: true,
          morningPerson: [],
          morningLeader: [],
          noonPerson: [],
          noonLeader: [],
          nightPerson: [],
          nightLeader: [],
          sort,
        };
      });

      scheduleData.value = newData;
    }
  };

  const handleChangeEdit = (index: number) => {
    scheduleData.value.forEach((item, i) => (item.isEdit = i === index));
  };

  const disabledBeginDate = (current: Dayjs) => {
    return current && current < dayjs().startOf('day');
  };

  const handleSubmit = async (isEffective = 0) => {
    createConfirm({
      iconType: 'info',
      title: '提示',
      content: isEffective === 1 ? '是否确认立即生效？' : '是否确认保存？',
      onOk: async () => {
        const ret = await validate();
        try {
          if (!scheduleData.value.length) {
            createMessage.error('请先添加排班信息');
            return;
          }
          okLoading.value = true;
          const cycle = scheduleData.value.length; // 频率

          const data: any = {
            id: formState.id,
            type: formState.type,
            startTime: dayjs(ret.startTimes[0]).format('YYYY-MM-DD'),
            planEndTime: dayjs(ret.startTimes[1]).format('YYYY-MM-DD'),
            remark: ret.remark,
            cycle,
            isEffective,
            scheduleDetailDtoList: [],
          };
          if (ret.schedulePlan) {
            data.schedulePlanGroupInfo = {
              mornFlag: ret.schedulePlan[0],
              noonFlag: ret.schedulePlan[1],
              nightFlag: ret.schedulePlan[2],
            };
            if (ret.schedulePlan[0]) {
              data.schedulePlanGroupInfo.morn = ret.morn || [];
            }
            if (ret.schedulePlan[1]) {
              data.schedulePlanGroupInfo.noon = ret.noon || [];
            }
            if (ret.schedulePlan[2]) {
              data.schedulePlanGroupInfo.night = ret.night || [];
            }
          }

          scheduleData.value.forEach((item, index) => {
            if (isShowMorn.value && (item.morningPerson.length || item.morningLeader.length)) {
              data.scheduleDetailDtoList.push({
                id: item.id || null,
                leader: item.morningLeader[0]?.userId || '',
                member: item.morningPerson.map((item: any) => item.userId).join(','),
                classType: 1,
                typeName: '早班',
                scheduleTime: dayjs(item.scheduleTime).format('YYYY-MM-DD'),
                sort: index + 1,
                schedulePlanId: item.schedulePlanId,
              });
            }
            if (isShowNoon.value && (item.noonPerson.length || item.noonLeader.length)) {
              data.scheduleDetailDtoList.push({
                id: item.id || null,
                leader: item.noonLeader[0]?.userId || '',
                member: item.noonPerson.map((item: any) => item.userId).join(','),
                classType: 2,
                typeName: '中班',
                scheduleTime: dayjs(item.scheduleTime).format('YYYY-MM-DD'),
                sort: index + 1,
                schedulePlanId: item.schedulePlanId,
              });
            }
            if (isShowNight.value && (item.nightPerson.length || item.nightLeader.length)) {
              data.scheduleDetailDtoList.push({
                id: item.id || null,
                leader: item.nightLeader[0]?.userId || '',
                member: item.nightPerson.map((item: any) => item.userId).join(','),
                classType: 3,
                typeName: '晚班',
                scheduleTime: dayjs(item.scheduleTime).format('YYYY-MM-DD'),
                sort: index + 1,
                schedulePlanId: item.schedulePlanId,
              });
            }
          });
          await createPlant(data);
          createMessage.success('新增成功');
          handleCancel();
          emit('success');
        } finally {
          okLoading.value = false;
        }
      },
    });
  };

  const handleCancel = () => {
    scheduleData.value = [];
    formState.id = '';
    formState.type = 0;
    formState.remark = '';
    formState.cycle = 1;
    formState.isEffective = 0;
    schedulePlan.value = [];
    updateSchema({
      field: 'schedulePlan',
      label: '班次选择',
      required: false,
    });
    closeModal();
  };

  const getNameCon = (arr) => {
    return arr.map((item) => item.name).join('、');
  };

  const checkMorning = ref(false);
  const checkNoon = ref(false);
  const checkNight = ref(false);
  const checkAll = ref(false);
  const indeterminate = ref(false);
  const isShowMorn = computed(() => {
    return schedulePlan.value.includes(1);
  });
  const isShowNoon = computed(() => {
    return schedulePlan.value.includes(2);
  });
  const isShowNight = computed(() => {
    return schedulePlan.value.includes(3);
  });
  const tableColLen2 = computed(() => {
    return schedulePlan.value.length == 2;
  });
  const tableColLen1 = computed(() => {
    return schedulePlan.value?.length == 1;
  });
  const maxTagCount = computed(() => {
    const len = schedulePlan.value.length;
    if (len == 1) return 6;
    return len <= 2 ? 2 : 1;
  });
  function handleChangeFeild(_, value) {
    if (isKyk.value) {
      formState.startTime = dayjs(kykDate.value[0]).format('YYYY-MM-DD');
      formState.planEndTime = dayjs(kykDate.value[1]).format('YYYY-MM-DD');
    } else {
      formState.startTime = value[0];
      formState.planEndTime = value[1];

      formState.cycle = dayjs(value[1]).diff(dayjs(value[0]), 'day') + 1;
    }

    const params = {
      type: formState.type,
      schedulePlanId: formState.id,
      startTime: formState.startTime,
      endTime: formState.planEndTime,
    };
    getScheduleCycleData(params);
  }
  const isCheck = computed(() => {
    return (
      checkAll.value ||
      indeterminate.value ||
      checkMorning.value ||
      checkNoon.value ||
      checkNight.value
    );
  });
  function handleChangeAllCheck() {
    indeterminate.value = false;

    scheduleData.value.forEach((item) => {
      item.checked = checkAll.value;
    });
  }

  // const handleCopy = async () => {
  //   const ret = await getFieldsValue();
  //   const copys = cloneDeep(scheduleData.value.filter((item) => item.checked));
  //   const copyLength = copys.length;
  //   const length = scheduleData.value.length;
  //   if (copyLength === 0) {
  //     createMessage.warning('请先选择要复制的数据');
  //     return;
  //   }
  //   //formState.cycle
  //   // 1. 复制的数据+已更新的数据 不得大于当前周期
  //   if (copyLength + length > formState.cycle || length === formState.cycle) {
  //     createMessage.warning('复制数据不能大于当前循环周期');
  //     return;
  //   }
  //   copys.forEach((item, idx) => {
  //     item.checked = false;
  //     item.sort = length + idx + 1;
  //   });
  //   scheduleData.value.push(...copys);
  // };

  function handleChangeCheck() {
    const length = scheduleData.value.length;
    const checkedLength = scheduleData.value.filter((item) => item.checked).length;
    checkAll.value = length === checkedLength;

    const checked = scheduleData.value.find((item) => item.checked);
    indeterminate.value = !checkAll.value && !!checked;
  }

  // 清空所有
  const clearData = () => {
    if (!isCheck.value) return;
    createConfirm({
      iconType: 'info',
      title: '提示',
      content: '是否确认清除所选数据？',
      onOk: () => {
        if (checkAll.value) {
          scheduleData.value.forEach((item) => {
            item.morningLeader = [];
            item.morningPerson = [];
            item.noonLeader = [];
            item.noonPerson = [];
            item.nightLeader = [];
            item.nightPerson = [];
          });
          checkAll.value = false;
          handleChangeAllCheck();
          return;
        }
        if (checkMorning.value) {
          clearMorning();
          checkMorning.value = false;
        }
        if (checkNoon.value) {
          clearNoon();
          checkNoon.value = false;
        }
        if (checkNight.value) {
          clearNight();
          checkNight.value = false;
        }
        const checkedList = scheduleData.value.filter((item) => item.checked);

        if (checkedList.length) {
          checkedList.forEach((item) => {
            item.morningLeader = [];
            item.morningPerson = [];
            item.noonLeader = [];
            item.noonPerson = [];
            item.nightLeader = [];
            item.nightPerson = [];
            item.checked = false;
          });
          indeterminate.value = false;
        }
      },
    });
  };
  const clearMorning = () => {
    scheduleData.value.forEach((item) => {
      item.morningLeader = [];
      item.morningPerson = [];
    });
  };

  const clearNoon = () => {
    scheduleData.value.forEach((item) => {
      item.noonLeader = [];
      item.noonPerson = [];
    });
  };

  const clearNight = () => {
    scheduleData.value.forEach((item) => {
      item.nightLeader = [];
      item.nightPerson = [];
    });
  };

  const setScheduleData = async (diff: number) => {
    const ret = await getFieldsValue();
    // 创建映射表加速查找
    const grouPptionsMap = new Map(grouPptions.value.map((item) => [item.id, item])) || [];

    const morn =
      ret.morn?.map((id) => grouPptionsMap.get(id)).filter((item) => item !== undefined) || []; // 过滤未找到的项

    const noon =
      ret.noon?.map((id) => grouPptionsMap.get(id)).filter((item) => item !== undefined) || [];

    const night =
      ret.night?.map((id) => grouPptionsMap.get(id)).filter((item) => item !== undefined) || [];

    const startTime = dayjs(ret.startTimes[0]);
    const diffNewData = Array.from({ length: diff }, (_, i) => {
      const morning = morn[i % (morn?.length || 1)] || {};
      const nooning = noon[i % (noon?.length || 1)] || {};
      const nighting = night[i % (night?.length || 1)] || {};
      return {
        isEdit: false,
        morningPerson: morning.option?.groupMember || morning?.groupMember || [],
        morningLeader: morning.option?.groupLeader || morning?.groupLeader || [],
        noonPerson: nooning.option?.groupMember || nooning?.groupMember || [],
        noonLeader: nooning.option?.groupLeader || nooning?.groupLeader || [],
        nightPerson: nighting.option?.groupMember || nighting?.groupMember || [],
        nightLeader: nighting.option?.groupLeader || nighting?.groupLeader || [],
        classType1Id: null,
        classType2Id: null,
        classType3Id: null,
        sort: i + 1,
        scheduleTime: startTime.add(i, 'day'),
        schedulePlanId: formState.id,
      };
    });
    scheduleData.value = diffNewData;
  };

  const confirmSchedule = async () => {
    createConfirm({
      iconType: 'info',
      title: '提示',
      content: '是否确认生成明细？',
      onOk: async () => {
        const ret = getFieldsValue();
        if (!ret.schedulePlan || !ret.schedulePlan.length)
          return createMessage.error('请先选择班次');
        schedulePlan.value = ret.schedulePlan || [];
        setScheduleData(formState.cycle);
      },
    });
  };

  const getCurrentStyle = (current: Dayjs) => {
    const style: CSSProperties = {};
    const today = dayjs().startOf('day');

    if (dataNextDay.value && dayjs(dataNextDay.value).isValid()) {
      const nextDay = dayjs(dataNextDay.value).startOf('day'); // Ensure same time
      const currentDate = current.startOf('day');

      if (
        (currentDate.isAfter(today) || currentDate.isSame(today)) &&
        (currentDate.isBefore(nextDay) || currentDate.isSame(nextDay))
      ) {
        style.color = 'var(--theme-color)';
      }
    }

    return style;
  };
</script>

<style lang="less" scoped>
  .schedule-warp {
    display: flex;
    height: 532px;
    border-radius: 4px;

    .split {
      color: #c9c9c9;
      font-weight: 300;
    }
  }

  .schedule-container {
    padding: 16px 24px 24px;
    max-width: 468px;
    border: 1px solid @border-color-base;
    margin-right: 16px;
    border-radius: 4px;

    .cycle-wrap {
      :deep(.ant-form-item-control-input-content) {
        display: flex;
        align-items: center;

        .confirm-schedule-btn {
          margin-left: 12px;
        }
      }
    }
  }

  .schedule-info {
    width: calc(100% - 468px);
    padding: 6px 24px 24px;
    border-radius: 4px;
    border: 1px solid @border-color-base;
  }

  .title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
  }

  .oper-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    & > button {
      margin: 10px 16px 6px 0;
      // border-radius: 2px;

      &.confirm-schedule-btn {
        margin-left: 16px;
        // border-radius: 4px;
      }
    }

    .delete-noon-btn {
      background: @danger-color-8p;
      border-color: @danger-color;
      color: @danger-color;
    }
  }

  .add-btn {
    height: 36px;
    border-color: @theme-color;
    background: @theme-color-8p;
    color: @theme-color;

    &:hover {
      background: @theme-color-16p;
    }
  }

  .add-btn-n {
    border-color: @theme-color;
    color: @theme-color;

    &:hover {
      background: @theme-color-8p;
    }
  }

  .content-container {
    .fixed-header {
      height: 48px;
      background: #f1f4f7;
      border-bottom: 1px solid #d8d8d8;
      border-radius: 4px 4px 0px 0px;
      line-height: 48px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .item {
        width: calc((100% - 160px) / 3);
        text-align: left;
        padding-left: 16px;

        &-second {
          width: calc((100% - 160px) / 2);
        }

        &-one {
          width: calc((100% - 160px));
        }

        & + .item {
          border-left: 1px solid #f5f6f7;
        }

        &.sort {
          width: 80px;
        }

        &.person {
          width: 60px;
          height: 48px;
          padding-left: 0;
        }
      }

      .clear-icon {
        padding: 4px;
        cursor: pointer;
        margin-left: 32px;

        &:hover {
          color: @danger-color;
        }
      }
    }

    .scheduling-details-container {
      overflow-y: scroll;
      border-bottom: 1px solid #f5f6f7;
      border-top: none;
      border-radius: 0 0 4px 4px;
      max-height: 401px;

      &::-webkit-scrollbar {
        // height: 0;
        width: 0;
        // background: transparent;
      }

      .detail-item {
        display: flex;
        align-items: center;
        // border-bottom: 1px solid #f5f6f7;
        // border-left: 1px solid #f5f6f7;
        &:hover {
          background-color: @table-row-hover-bg;
        }

        & + .detail-item {
          border-top: 1px solid @table-td-border;
        }

        .sort {
          padding: 0 16px;
          width: 50px;
          height: 48px;
          line-height: 48px;
          font-size: 16px;
        }

        .person {
          width: 60px;

          .item {
            padding-right: 16px;
            color: #666;
            line-height: 36px;

            &.leader {
              color: #333;
              font-weight: 500;
            }
          }
        }

        .person-items {
          display: flex;
          align-items: center;
          width: calc(100% - 185px);

          cursor: pointer;

          .schedule-item {
            height: 92px;
            border-left: 1px solid #f5f6f7;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 33.33%;

            &-second {
              width: 50%;
            }

            &-one {
              width: 100%;
            }

            .item {
              display: flex;
              align-items: center;
              padding: 0 16px;
              height: 40px;
              line-height: 40px;
              width: 100%;

              .lead-name {
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                margin: 8px 0;
                text-align: center;

                .tag {
                  width: 40px;
                  height: 20px;
                  line-height: 20px;
                  text-align: center;
                  margin-left: 8px;
                  display: inline-block;
                  border-radius: 20px;
                  color: white;
                  font-weight: 400;
                  font-family: PingFang SC-Regular, PingFang SC;
                  font-size: 12px;
                }

                .morning {
                  background: linear-gradient(90deg, #86c724 0%, #4db803 100%);
                }

                .noon {
                  background: linear-gradient(90deg, #81a7ff 0%, #3860ff 100%);
                }

                .night {
                  background: linear-gradient(90deg, #ffa05d 3%, #fc7c22 100%);
                }
              }

              .name {
                font-weight: 400;
                color: #666666;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
</style>
<style>
  .schedule-picker-popup {
    .ant-picker-cell-range-end .ant-picker-cell-inner,
    .ant-picker-cell-range-start .ant-picker-cell-inner,
    .ant-picker-cell-selected .ant-picker-cell-inner {
      color: #fff !important;
    }
  }
</style>
