import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Form/index';

import { getDeptTreeByPage } from '/@/api/admin/dept';

export const columns: BasicColumn[] = [];
export const searchFormSchema: FormSchema[] = [
  // {
  //   field: 'eqType',
  //   label: '设备类别',
  //   component: 'Input',
  //   slot: 'eqType',
  //   colProps: { span: 6 },
  //   labelWidth: 68,
  // },
  // {
  //   field: 'maintenanceTypeId',
  //   label: '保养类别',
  //   component: 'Input',
  //   slot: 'type',
  //   colProps: { span: 6 },
  // },
];
export const schemas: FormSchema[] = [
  {
    field: 'recordTime',
    component: 'DatePicker',
    label: '时间',
    required: true,
    // slot: 'recordTime',
    componentProps: {
      showTime: true,
      picker: 'date',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      allowClear: true,
      disabled: false,
      // disabledDate: (current: Dayjs) => current && current < dayjs().startOf('day'),
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'planType',
    component: 'Select',
    label: '排班班次',
    componentProps: {
      options: [
        { label: '值班排班', value: '1' },
        { label: '化验排班', value: '2' },
      ],
      allowClear: false,
    },
    required: true,
    defaultValue: '1',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'recordType',
    component: 'ApiSelect',
    label: '分类',
    componentProps: {
      multiple: false,
      api: getDeptTreeByPage,
      // labelField: 'name',
      // valueField: 'id',
      getPopupContainer: () => document.body,
      onChange(data) {
        // if (formActionType) {
        //   formActionType.setFieldsValue({
        //     layerId: undefined,
        //   });
        // }
        // if (data != 'general') getLayerBycodeOptions(data);
        console.log('%c data-------->>>>>>', data);
      },
    },
    colProps: {
      span: 24,
    },
    slot: 'recordType',
  },
  {
    field: 'recordContent',
    component: 'InputTextArea',
    label: '内容',
    required: true,
    colProps: {
      span: 24,
    },
    componentProps: {
      maxlength: 200,
    },
    slot: 'recordContent',
  },
  {
    field: 'dutyId',
    component: 'HChooseUser',
    label: '值班人',
    required: true,
    colProps: {
      span: 24,
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
  },
  // {
  //   field: 'dutyId',
  //   component: 'HChooseUser',
  //   label: '提交人',
  //   required: true,
  //   colProps: {
  //     span: 24,
  //   },
  // },
  // {
  //   field: 'planFactoryId',
  //   component: 'Select',
  //   label: '单位',
  //   required: true,
  //   slot: 'planFactoryId',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'planName',
  //   component: 'Input',
  //   label: '维保名称',
  //   slot: 'planName',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'maintenanceTypeId',
  //   component: 'Select',
  //   label: '保养类别',
  //   required: true,
  //   slot: 'name',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'type',
  //   component: 'ApiSelect',
  //   label: '保养类型',
  //   required: true,
  //   slot: 'type',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'eqType',
  //   component: 'Select',
  //   label: '设备类别',
  //   slot: 'eqType',
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'blank2',
  //   component: 'Input',
  //   label: '',
  //   colProps: {
  //     span: 24,
  //   },
  //   render: () => {
  //     return h(
  //       'div',
  //       {
  //         style: {
  //           borderBottom: '1px solid #eeeff1',
  //           paddingBottom: '6px',
  //           display: 'flex',
  //           alignItems: 'center',
  //         },
  //       },
  //       [
  //         h(
  //           'span',
  //           {
  //             style: {
  //               width: '4px',
  //               height: '14px',
  //               background: 'var(--theme-color)',
  //               display: 'inline-block',
  //             },
  //           },
  //           '',
  //         ),
  //         h(
  //           'span',
  //           { style: { paddingLeft: '8px', fontSize: '16px', fontWeight: 600 } },
  //           '基本信息',
  //         ),
  //       ],
  //     );
  //   },
  // },
  // {
  //   field: 'dispatchUserIds',
  //   component: 'HChooseUser',
  //   label: '派单工人',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'dutyId',
  //   component: 'HChooseUser',
  //   label: '工作责任人',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'executorUserIds',
  //   component: 'HChooseUser',
  //   label: '执行人',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'acceptanceUserIds',
  //   component: 'HChooseUser',
  //   label: '验收人',
  //   required: true,
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'scheduledTimes',
  //   component: 'InputNumber',
  //   label: '计划工时(小时)',
  //   required: true,
  //   componentProps: {
  //     min: 0,
  //     precision: 0,
  //   },
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'remark',
  //   component: 'InputTextArea',
  //   label: '工单描述',
  //   required: true,
  //   colProps: {
  //     span: 24,
  //   },
  // },
  // {
  //   field: 'reason',
  //   component: 'InputTextArea',
  //   label: '原因分析',
  //   colProps: {
  //     span: 24,
  //   },
  // },
  // {
  //   field: 'project',
  //   component: 'Input',
  //   label: '保养项目',
  //   slot: 'project-table',
  //   colProps: {
  //     span: 24,
  //   },
  // },
  // {
  //   field: 'blank4',
  //   component: 'Input',
  //   label: '',
  //   colProps: {
  //     span: 24,
  //   },
  //   render: () => {
  //     return h(
  //       'div',
  //       {
  //         style: {
  //           borderBottom: '1px solid #eeeff1',
  //           paddingBottom: '6px',
  //           display: 'flex',
  //           alignItems: 'center',
  //         },
  //       },
  //       [
  //         h(
  //           'span',
  //           {
  //             style: {
  //               width: '4px',
  //               height: '14px',
  //               background: 'var(--theme-color)',
  //               display: 'inline-block',
  //             },
  //           },
  //           '',
  //         ),
  //         h(
  //           'span',
  //           { style: { paddingLeft: '8px', fontSize: '16px', fontWeight: 600 } },
  //           '设置周期',
  //         ),
  //       ],
  //     );
  //   },
  // },

  // {
  //   field: 'planEndTime',
  //   component: 'DatePicker',
  //   label: '结束日期',
  //   slot: 'planEndTime',
  //   componentProps: {
  //     picker: 'date',
  //     format: 'YYYY-MM-DD',
  //     valueFormat: 'YYYY-MM-DD 00:00:00',
  //     allowClear: true,
  //     showTime: false,
  //     disabledDate: (current: Dayjs) => current && current < dayjs().startOf('day'),
  //   },
  //   colProps: {
  //     span: 12,
  //   },
  // },
  // {
  //   field: 'cronExpression',
  //   component: 'InputTextArea',
  //   label: '表达式',
  //   required: true,
  //   slot: 'cronExpression',
  //   colProps: {
  //     span: 24,
  //   },
  // },
  // {
  //   field: 'executionDescription',
  //   component: 'InputTextArea',
  //   label: '计划描述',
  //   required: true,
  //   componentProps: {
  //     disabled: true,
  //     placeholder: '由cron表达式生成',
  //   },
  //   colProps: {
  //     span: 24,
  //   },
  // },
];

export const projectColumns: BasicColumn[] = [
  {
    title: '保养项目',
    dataIndex: 'name',
    ellipsis: false,
  },
];
