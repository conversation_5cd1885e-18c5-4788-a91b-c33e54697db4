<template>
  <div class="overview-box h-full overflow-y overflow-x" style="overflow-y: auto">
    <div class="content">
      <Row :gutter="12" style="height: calc(50% - 6px)">
        <Col :span="16" style="height: 100%">
          <TLComponent />
        </Col>
        <Col :span="8" style="height: 100%">
          <TRComponent />
        </Col>
      </Row>
      <Row :gutter="12" style="height: calc(50% - 6px); margin-top: 12px">
        <Col :span="16" style="height: 100%">
          <BLComponent @powerRingVal="ranking" />
        </Col>
        <Col :span="8" style="height: 100%">
          <BRComponent :itemVal="itemVal" />
        </Col>
      </Row>
    </div>
  </div>
</template>

<script setup lang="ts" name="ElectricityManagementOverview">
  import { onMounted, ref } from 'vue';
  import { Row, Col } from 'ant-design-vue';
  import TLComponent from './components/TLComponent.vue';
  import TRComponent from './components/TRComponent.vue';
  import BLComponent from './components/BLComponent.vue';
  import BRComponent from './components/BRComponent.vue';
  import { getSenceGroupTree } from '/@zhcz/api/scenes-group';

  const indexCodeDataList = ref<{ resourceInterfaceId: string; groupCode: string }[]>([]);

  const initIndexCOdeDataList = () => {
    const initData = Array.from({ length: 10 }, () => {
      return {
        resourceInterfaceId: '',
        groupCode: '',
      };
    });
    indexCodeDataList.value = initData;
  };
  initIndexCOdeDataList();

  // 查找温度监测下面配置的选项
  const EnvironmentOptions = ref([]);

  function flattenChildren(nodes) {
    if (!Array.isArray(nodes)) return [];
    const result: any[] = [];
    nodes.forEach((node) => {
      if (!node || typeof node !== 'object') return;
      result.push(node);
      if (Array.isArray(node.children)) {
        result.push(...flattenChildren(node.children));
      }
    });

    return result;
  }

  async function getSenceGroupTreeFn() {
    let data = await getSenceGroupTree();
    data = data || [];
    const flattenData = flattenChildren(data);
    EnvironmentOptions.value =
      flattenData.find((i: any) => i.groupCode === 'nhzl_hjjc')?.children || [];
    console.log('EnvironmentOptions.value', EnvironmentOptions.value);
  }

  let itemVal = ref<{
    value: string;
    date: string;
  }>({
    value: '',
    date: '',
  });
  function ranking(val) {
    // console.log('电量排行回参2', val);
    itemVal.value = val;
  }
  onMounted(async () => {
    getSenceGroupTreeFn();
  });
</script>
<script lang="ts">
  export default { name: 'CostManagementElectricityManagementOverview' };
</script>
<style lang="less" scoped>
  .overview-box {
    padding: 0 16px 16px;
    height: 100%;
    overflow: auto;

    .content {
      height: 100%;

      :deep(.ant-col) {
        height: 100%;
      }

      .content-item {
        background-color: #fcfcfc;
        height: 100%;

        overflow: hidden;

        :deep(.ant-card) {
          box-shadow: none;
        }
      }

      .content-item-t {
        border-radius: 4px;
      }

      .content-item-c {
        border-bottom-right-radius: 4px;
        border-top-right-radius: 4px;
      }

      .content-item-b {
        border-bottom-left-radius: 4px;
        border-top-left-radius: 4px;
        border-right: 1px solid #d8d8d8;
      }
    }
  }
</style>
