<template>
  <div
    class="absolute z-10 right-0"
    :class="{
      'left-0 px-4  bottom-4': isSet,
      'bottom-0': !isSet,
    }"
    v-if="isShow"
  >
    <div v-if="!isSet" class="set-icon" title="设置" @click="handleSet">
      <Icon icon="ant-design:setting-filled" color="" />
      <span>设置</span>
    </div>
    <div class="w-full flow-btn-box flex items-center" v-else>
      <Button size="large" class="flow-btn" type="primary" @click="submit()"> 保存 </Button>
      <Button size="large" class="flow-btn" @click="back()"> 返回 </Button>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted } from 'vue';
  import { useUserStore } from '/@/store/modules/user';
  import { Button } from 'ant-design-vue';

  import { Icon } from '/@/components/Icon';
  import { ParamsKeyEnum } from '/@/enums/appEnum';
  import { getParamKeyApi } from '/@/api/admin/param';
  import { HomeListGetResultModel, HomeListItem } from '/@/api/admin/model/home';
  import { saveHomeConfigApi } from '/@/api/admin/home';
  import { useMessage } from '/@/hooks/web/useMessage';

  export default defineComponent({
    name: 'SaveHomeConfig',
    components: {
      Icon,
      Button,
    },
    props: {
      data: { type: Object as PropType<HomeListGetResultModel>, requerd: true },
      leftList: { type: Array as PropType<HomeListItem[]>, requerd: true },
      rightList: { type: Array as PropType<HomeListItem[]>, requerd: true },
    },
    emits: ['set', 'success'],
    setup(props, { emit }) {
      const isShow = ref(false);
      const isSet = ref(false);
      const userStore = useUserStore();
      const { createMessage } = useMessage();
      async function getShow() {
        const ret = await getParamKeyApi(ParamsKeyEnum.DESIGN);

        if (ret) {
          const roles = userStore.getRoleList;
          for (const val of roles) {
            if (ret.includes(val)) {
              isShow.value = true;
              return;
            }
          }
        }
      }

      onMounted(() => {
        getShow();
      });

      async function submit() {
        try {
          props.data!.detailList = [...props.leftList!, ...props.rightList!];
          await saveHomeConfigApi(props.data!);
          back();
          createMessage.success('保存成功');
        } catch (error: any) {
          createMessage.success(error.msg || '保存失败');
          throw error;
        }
      }
      function back() {
        emit('success');
        isSet.value = false;
        emit('set', false);
      }
      function handleSet() {
        isSet.value = !isSet.value;
        emit('set', isSet.value);
      }
      return {
        isShow,
        isSet,
        submit,
        back,
        handleSet,
      };
    },
  });
</script>
<style lang="less" scoped>
  .set-icon {
    cursor: pointer;
    padding: 6px 12px 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 14px;
    text-align: center;
    background: @theme-color;
    border-radius: 22px 0px 0px 0px;
    color: white;
  }

  .flow-btn-box {
    padding: 16px;
    background: @component-background;
    box-shadow: 0px -4px 16px 0px rgba(0, 0, 0, 0.12);
    border-radius: 0px 0px 4px 4px;
  }

  .flow-btn {
    min-width: 120px;
    height: 40px;
    font-size: 16px;

    & + .flow-btn {
      margin-left: 24px;
    }
  }
</style>
