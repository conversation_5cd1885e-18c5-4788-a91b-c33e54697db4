<template>
  <div class="data-monitor">
    <HLCard title="数据监测" :styleData="{ borderRadius: '4px' }">
      <template #defaultBody>
        <TableSimple
          :dataSource="list"
          :columns="tableColumns"
          :showIndexColumn="false"
          :pagination="false"
        />
      </template>
    </HLCard>
  </div>
</template>
<script lang="ts" setup>
  import { HLCard } from '/@/project/zhcz/components/HLCard';
  import { TableSimple } from '/@zhcz/components/HLCardTable';
  import { ref, h } from 'vue';

  const emits = defineEmits(['onDetail']);
  defineProps({
    list: {
      type: Array,
      default: () => [],
    },
    configGroupInfo: {
      type: Array,
      default: () => [],
    },
  });

  const tableColumns = ref([
    {
      title: '名称',
      dataIndex: 'indexName',
    },
    {
      title: '数据值',
      dataIndex: 'value',
      customRender: ({ record }) => {
        let style = record.alertStyle;
        if (
          record.maxVal &&
          record.minVal &&
          record.value <= record.maxVal &&
          record.value >= record.minVal
        ) {
          style = record.normalStyle;
        }
        if (typeof style != 'object') style = {};
        style.cursor = 'pointer';
        return h(
          'span',
          {
            style,
            onClick() {
              emits('onDetail', record);
            },
          },
          record.value,
        );
      },
    },
    {
      title: '单位',
      dataIndex: 'unitName',
    },
  ]);
</script>
<style lang="less" scoped>
  .data-monitor {
    width: 544px;
    // max-height: 288px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
  }
</style>
