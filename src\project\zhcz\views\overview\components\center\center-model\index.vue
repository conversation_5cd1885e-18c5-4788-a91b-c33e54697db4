<template>
  <div class="center-model">
    <div class="index-text">
      <CenterText :text="dataList[currentIndex].text" />
    </div>
    <div class="model-outer">
      <div :class="getBindPrevClass" @click="handlePrev"></div>
      <div class="center-model" ref="centerModelRef">
        <Swiper
          class="swiper"
          :options="swiperOptions"
          @slide-change="handleSideChange"
          @swiper="onSwiper"
        >
          <SwiperSlide class="swiper-no-swiping" v-for="(item, index) in dataList" :key="index">
            <div class="bg">
              <template v-if="item.mediumType === '1'">
                <img
                  :src="item.src"
                  alt=""
                  class="img"
                  style="max-width: 100%; object-fit: contain; max-height: 100%"
                />
              </template>
              <!-- <template v-if="item.mediumType === '2'"> -->
              <template v-else>
                <div
                  class="video_box"
                  style="max-width: 100%; max-height: 100%; object-fit: contain"
                  :id="item.id"
                ></div>
              </template>
            </div>
          </SwiperSlide>
        </Swiper>
      </div>
      <div :class="getBindNextClass" @click="handleNext"></div>
    </div>
    <div class="index-container" :style="getIndexStyle">
      <template :key="index" v-for="(item, index) in getIndexArr">
        <div class="ciitem">
          <CenterIndex v-if="item[0]" :data="item[0]" />
          <CenterIndex v-if="item[1]" :data="item[1]" />
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, computed, onMounted } from 'vue';
  import { useWindowSize } from '@vueuse/core';
  import CenterIndex from '../center-index/index.vue';
  import CenterText from '../center-text/index.vue';
  // import { useMapState } from '@/utils/store'
  // import { getMinioFileUrl } from '../../../utils';
  import Player from 'xgplayer';
  import 'xgplayer/dist/index.min.css';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import 'swiper/css';

  const swiperOptions = {
    noSwiping: true,
    loop: false,
    autoplay: false,
    initialSlide: 0,
  };

  const emits = defineEmits(['prev', 'next']);
  const props = defineProps({
    src: {
      type: String,
    },
    dataList: {
      type: Array as any,
      default: () => [],
    },
  });
  onMounted(() => {
    const videoList = props.dataList.filter((item) => item.mediumType === '2');
    videoList.forEach((item) => {
      new Player({
        id: item.id,
        url: item.src,
        loop: true,
        autoplay: true, // 自动播放 无效？
        autoplayMuted: true,
        // fluid: true,
        fitVideoSize: 'fixWidth',
        // inactive: 0,
        leavePlayerTime: 0,
        controls: false,
      });
    });
  });
  const currentIndex = ref(0);
  const swiperRef = ref<any>(null);
  const centerModelRef = ref();

  const getBindPrevClass = computed(() => {
    return {
      prev: true,
      prev_disabled: currentIndex.value === 0,
    };
  });

  const getBindNextClass = computed(() => {
    return {
      next: true,
      next_disabled: currentIndex.value === props.dataList.length - 1,
    };
  });

  const getIndexStyle = computed(() => {
    // const val = props.dataList[currentIndex.value].index;
    // const a = 'repeat(3, 1fr)';
    // const b = 'repeat(2, 1fr)';
    return {
      // gridTemplateColumns: val.length > 4 ? a : b,
    };
  });
  const getIndexArr = computed(() => {
    const val: any[] = props.dataList[currentIndex.value].index;
    console.log('getIndexArr1', val);
    const step = val.length ? Math.ceil(val.length / 2) : 0;
    const IndexArr: any[] = [];
    console.log('getIndexArr-step', step);
    if (step > 0) {
      for (let i = 0; i < step; i++) {
        console.log('getIndexArr-for', val.slice(i * 2, (i + 1) * 2));
        IndexArr.push(val.slice(i * 2, (i + 1) * 2));
      }
    }
    console.log('IndexArr', IndexArr);
    return IndexArr;
  });

  function onSwiper(swiper) {
    swiperRef.value = swiper;
  }

  function handleSideChange() {
    currentIndex.value = swiperRef.value.activeIndex;
  }

  function handlePrev() {
    if (currentIndex.value === 0) {
      return;
    }
    swiperRef.value.slidePrev();
    emits('prev');
  }

  function handleNext() {
    if (currentIndex.value === props.dataList.length - 1) {
      return;
    }
    swiperRef.value.slideNext();
    emits('next');
  }

  function setSwiperWrapper() {
    if (currentIndex.value !== 0) {
      const swiperWrapper = document.getElementsByClassName('swiper-wrapper')[0];
      swiperWrapper.style.transform = `translate3d(-${currentIndex.value * 100}%, 0px, 0px)`;
    }
  }

  const { width } = useWindowSize();
  watch(
    () => width.value,
    () => {
      setTimeout(() => {
        setSwiperWrapper();
      }, 300);
    },
    { immediate: true },
  );
</script>

<style lang="less" scoped>
  .center-model {
    height: 100%;
    overflow: hidden;

    .index-text {
      // height: 4.5rem;
      padding-top: 2rem;
      padding-bottom: 1.5rem;
      display: flex;
      justify-content: center;
    }

    .model-outer {
      width: 100%;
      // 2.1875rem
      height: ~'calc(100% - 13.1875rem)';
      display: flex;
      justify-content: space-between;
      align-items: center;
      // padding: 0 4rem;
      // padding: 0 1.5rem;

      .swiper {
        width: 100%;
        height: 100%;

        .swiper-no-swiping {
          width: 100% !important;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .prev,
      .next {
        // width: 2rem;
        // height: 1.75rem;
        width: 32px;
        height: 28px;
        cursor: pointer;
        background-size: 100% 100%;
      }

      .prev {
        background: url('../../../assets/images/pager-prev-light.png') 0 0 no-repeat;

        &_disabled {
          background: url('../../../assets/images/pager-prev-dark.png') 0 0 no-repeat;
        }
      }

      .next {
        background: url('../../../assets/images/pager-next-light.png') 0 0 no-repeat;

        &_disabled {
          background: url('../../../assets/images/pager-next-dark.png') 0 0 no-repeat;
        }
      }

      .center-model {
        position: relative;
        width: ~'calc(100% - 64px)';
        height: 100%;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;

        .bg {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;

          .video_box {
            background-color: transparent !important;
            pointer-events: none;

            .xgplayer-controls {
              display: none;
            }
          }
          // @media screen and (min-width: 3201px) {
          //   .img {
          //     transform: scale(2.64);
          //   }
          // }

          // @media screen and (min-width: 1921px) {
          //   .img {
          //     transform: scale(1.4);
          //   }
          // }

          // @media screen and (max-width: 1600px) {
          //   .img {
          //     transform: scale(0.72);
          //   }
          // }

          // @media screen and (max-width: 1440px) {
          //   .img {
          //     transform: scale(0.65);
          //   }
          // }
        }
      }
    }

    .index-container {
      padding: 2.1875rem 0 0 0;
      height: 7.1875rem;
      // display: grid;
      // grid-template-columns: repeat(3, 1fr);
      gap: 0 1rem;
      display: flex;
      // grid-template-columns: repeat(3, 1fr);
      justify-content: space-between;
      flex-wrap: nowrap;

      .ciitem {
        display: flex;
        flex-direction: column;
        justify-content: center;
        box-sizing: border-box;
        padding: 0 1rem;
        // width: 33.3%;
        gap: 10px 0;
        width: calc(33.33% - 0.6666rem);
        // max-width: 297px;
        background: linear-gradient(180deg, rgba(3, 36, 85, 0.8) 0%, rgba(1, 32, 70, 0.56) 100%);
        box-shadow: inset 0px 0px 16px 0px rgba(0, 96, 235, 0.8);
        border-radius: 8px 8px 8px 8px;
        border: 1px solid rgba(255, 255, 255, 0.4);

        .center-index {
          // flex: 1;
        }
      }
      // @media screen and (min-width: 1921px) {
      //   justify-content: center;
      //   grid-template-columns: repeat(3, 16.5rem);
      // }
    }
  }
</style>
