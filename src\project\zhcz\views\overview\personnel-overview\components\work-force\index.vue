<template>
  <div class="work-force">
    <div class="title">
      <img :src="getIcon(parentIndex)" alt="" class="icon" />
      <div class="info">
        <div class="name">{{ data.name }}</div>
        <div class="sum">
          <span class="number">{{ data.persons?.length }}</span>
          <span class="unit">人</span>
        </div>
      </div>
    </div>
    <div class="content" ref="contentRef">
      <!-- :slides-per-view="4.7" -->
      <Swiper
        class="swiper"
        :modules="modules"
        :pagination="{ clickable: true }"
        @swiper="onSwiper"
        @slideChange="onSlideChange"
        :space-between="39"
        :slides-per-view="widthWind > 1280 ? 6.7 : 4.7"
        navigation
      >
        <template v-if="data.persons?.length">
          <SwiperSlide v-for="(item, index) in data.persons" :key="index">
            <PersonnelItem
              :data="item"
              :type="data.name"
              :avatar="item.avatar ? getEevReturnDomain(item.avatar) : ''"
              :index="parentIndex"
              :container="swiperRef"
            />
          </SwiperSlide>
        </template>
        <template v-else>
          <SwiperSlide :key="0">
            <PersonnelItem empty :type="data.name" :index="parentIndex" :container="swiperRef" />
          </SwiperSlide>
        </template>
      </Swiper>
      <div class="swiper-scroll"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  // import { useWindowSize } from '@vueuse/core';
  import { getEevReturnDomain } from '/@zhcz/utils/file/url';
  import PersonnelItem from './PersonnelItem.vue';
  import workTypeBlue from '../../../assets/images/work-type-blue.png';
  import workTypeYellow from '../../../assets/images/work-type-yellow.png';
  import workTypeGreen from '../../../assets/images/work-type-green.png';
  import workTypeOrange from '../../../assets/images/work-type-orange.png';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Navigation } from 'swiper/modules';
  import 'swiper/css';
  // import 'swiper/css/scrollbar';
  import 'swiper/css/navigation';

  defineProps({
    parentIndex: {
      type: Number,
      default: 0,
    },
    data: {
      type: Object,
      default: () => ({
        name: '',
        description: '',
        sort: 1,
        factoryId: 2001,
        creationTime: '',
        personInCharge: '',
        userIds: [],
        id: 1,
      }),
    },
  });

  // const swiperOptions = {
  //   slidesPerView: 4.7,
  //   spaceBetween: 57,
  // };

  // const { width } = useWindowSize();

  const swiperRef = ref<any>(null);
  const contentRef = ref(null);
  // const options = ref({
  //   slidesPerView: 4.5,
  //   spaceBetween: 58,
  // });
  const modules = ref([Navigation]);
  const onSlideChange = () => {
    console.log('slide change');
  };
  const widthWind = computed(() => {
    console.log('width', window.innerWidth);
    return window.innerWidth;
  });
  // watch(
  //   () => width.value,
  //   (val) => {
  //     updateSwiper(val);
  //   },
  // );

  function onSwiper(swiper) {
    swiperRef.value = swiper;
  }

  function getIcon(number) {
    const index = (number + 1) % 4;
    const iconMap = new Map([
      [1, workTypeBlue],
      [2, workTypeYellow],
      [3, workTypeGreen],
      [0, workTypeOrange],
    ]);

    return iconMap.get(index) || workTypeBlue;
  }

  // function updateSwiper(val) {
  //   test.value = Math.floor(Math.random() * 100);
  //   if (val <= 1852 && val > 1776) {
  //     swiperRef.value.params.spaceBetween = 45;
  //   } else if (val <= 1776 && val > 1646) {
  //     swiperRef.value.params.spaceBetween = 40;
  //   } else if (val <= 1646 && val > 1600) {
  //     swiperRef.value.params.spaceBetween = 35;
  //   } else if (val <= 1600 && val > 1530) {
  //     swiperRef.value.params.spaceBetween = 45;
  //   } else if (val <= 1530 && val > 1446) {
  //     swiperRef.value.params.spaceBetween = 40;
  //     nextTick(() => {
  //       contentRef.value.style.padding = '0 3rem';
  //     });
  //   } else if (val <= 1446 && val >= 1280) {
  //     swiperRef.value.params.spaceBetween = 35;
  //     nextTick(() => {
  //       contentRef.value.style.padding = '0 2.4rem';
  //     });
  //   }
  // }

  // onMounted(() => {
  //   updateSwiper(width.value);
  // });
</script>

<style lang="less" scoped>
  .work-force {
    // margin-bottom: 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: flex-start;

    .title {
      min-width: 6.6rem;
      display: flex;
      align-items: flex-end;
      flex-shrink: 0;

      .icon {
        width: 4rem;
      }

      .info {
        margin-bottom: -5px;

        .name {
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
        }

        .sum {
          .number {
            font-size: 20px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #2d82fe;
          }

          .unit {
            color: #515a6e;
            margin-left: 4px;
            font-size: 14px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
          }
        }
      }
    }

    .content {
      padding-left: 1.25rem;
      padding-right: 1rem;
      flex: 1;
      overflow: hidden;

      :deep(.swiper-container) {
        cursor: pointer;
      }

      :deep(.swiper) {
        cursor: grab;

        // .swiper-wrapper {
        //   padding-bottom: 8px;
        // }

        .swiper-scrollbar {
          bottom: 0;
        }
      }

      :deep(.swiper-slide) {
        width: 5rem !important;
      }

      :deep(.swiper-button-prev) {
        color: #ffffff;
        // top: 78px;
        left: 0;
        width: 24px;
        height: 32px;
        background: #0b62cb;
        border-radius: 4px 0px 0px 4px;

        &::after {
          font-size: 16px;
        }

        &.swiper-button-disabled {
          opacity: 1 !important;
          background: rgba(11, 98, 203, 0.56);
          backdrop-filter: blur(4px);
        }
      }

      :deep(.swiper-button-next) {
        color: #ffffff;
        width: 24px;
        height: 32px;
        right: 0rem;
        background: #0b62cb;
        border-radius: 0px 4px 4px 0px;

        &::after {
          font-size: 16px;
        }

        &.swiper-button-disabled {
          opacity: 1 !important;
          background: rgba(11, 98, 203, 0.56);
          backdrop-filter: blur(4px);
        }
      }
    }
  }
</style>
