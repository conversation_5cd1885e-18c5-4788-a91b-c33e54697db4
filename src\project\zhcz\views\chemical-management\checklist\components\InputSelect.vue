<template>
  <Dropdown :trigger="['click']">
    <InputNumber v-model:value="activeValue" class="w-full" :step="0.01" :min="0" />

    <div style="height: 0"></div>
    <template #overlay>
      <Menu :selectedKeys="[String(activeValue)]" style="width: 220px; max-height: 200px">
        <MenuItem :key="item.value" v-for="item in options" @click="() => handleClick(item)">
          <span>{{ item.label }}</span>
        </MenuItem>
      </Menu>
    </template>
  </Dropdown>
</template>

<script lang="ts" setup>
  import { defineProps, defineEmits } from 'vue';
  import { useVModel } from '@vueuse/core';
  import { Dropdown, InputNumber, MenuItem, Menu } from 'ant-design-vue';
  const props = defineProps(['options', 'modelValue']);
  const emit = defineEmits(['select', 'update:modelValue']);
  const activeValue = useVModel(props, 'modelValue', emit);
  // const show = ref(false);

  function handleClick(item) {
    activeValue.value = item.value;
  }
  // function handleBlur() {
  //   console.log('blur');
  //   show.value = false;
  // }
  // function handleFocus() {
  //   console.log('focus');
  //   show.value = true;
  // }
</script>

<style scoped></style>
