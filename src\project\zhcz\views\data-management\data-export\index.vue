<template>
  <PageWrapper contentFullHeight>
    <div class="search-form" ref="formRef">
      <BasicForm @register="registerForm" @submit="handleSubmit()" @reset="handleReset">
        <template #data-type-slot>
          <Select
            :allow-clear="false"
            :options="dataTypeOptions"
            v-model:value="searchForm.timeType"
            placeholder="请选择数据类型"
            @change="handleDataTypeChange"
          />
        </template>
        <template #date-slot>
          <RangePicker
            v-model:value="searchForm.dates"
            v-bind="actTimeTypeOpt"
            :allowClear="false"
          />
        </template>
        <template #data-set-slot>
          <div class="tag-container">
            <Tag
              v-for="item in dataset"
              :key="item.id"
              :class="{ dataset__active: item.id === activeDataset?.id }"
              closable
              @click="handleActiveDataset(item)"
              @close="handleDeleteDataset(item)"
            >
              <span class="text flex-1 overflow-hidden whitespace-nowrap text-ellipsis">
                {{ item.name }}
              </span>
            </Tag>
            <a-button
              :disabled="checkedList.length === 0"
              type="second"
              :icon="h(Icon, { icon: 'icon-park-outline:save' })"
              @click="openDatasetModal"
            >
              保存
            </a-button>
          </div>
        </template>
        <template #data-index-slot>
          <div class="tag-container indictor">
            <Tag
              v-for="item in checkedList"
              :key="item.code"
              closable
              @close="handleDeleteIndictor(item)"
            >
              <span class="text flex-1 overflow-hidden whitespace-nowrap text-ellipsis">
                {{ item.name }}
              </span>
            </Tag>
            <a-button
              type="second"
              :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
              @click="openIndexModal"
            >
              {{ checkedList.length ? '继续添加' : '添加指标' }}
            </a-button>
            <a-button
              :disabled="checkedList.length === 0"
              type="second"
              style="min-width: 72px"
              :icon="h(Icon, { icon: 'icon-park-outline:clear' })"
              @click="handleClear"
            >
              清空
            </a-button>
          </div>
        </template>
      </BasicForm>
    </div>
    <div class="flex-1 overflow-hidden">
      <BasicTable @register="registerTable" @change="handleChange">
        <template #tableTitle>
          <div class="flex" style="align-items: center">
            <a-button
              type="second"
              class="flex items-center"
              :disabled="Boolean(tableData.length) == false"
              :icon="h(Icon, { icon: 'icon-park-outline:upload' })"
              @click="exportExcel"
            >
              导出
            </a-button>
            <BasicTitle
              class="ml-3 basic-title"
              helpMessage="根据分、时、日、月导出厂站的生成、采集数据。单次最多支持导出15W个单元格数据，超出数据量请分多次导出"
            >
              说明
            </BasicTitle>
          </div>
        </template>
      </BasicTable>
      <!-- <div class="empty" v-show="!tableData.length">
        <img src="../../../assets/images/empty.png" alt="" />
        <div>暂无数据</div>
      </div> -->
    </div>
    <ChoseIndicatorModal
      :api="getExportIndexListApi"
      :queryParams="queryParams"
      v-model:checkedList="checkedList"
      @register="registerModal"
      @confirm="activeDataset = undefined"
    />
    <CreateDatasetModal
      :checkedList="checkedList"
      @register="registerDatasetModal"
      @success="handleAddDataSet"
    />
  </PageWrapper>
</template>
<script lang="ts">
  export default { name: 'DataManagementDataExport' };
</script>
<script setup lang="ts" name="DataExport">
  import { BasicTitle } from '/@/components/Basic/index';
  import { ref, computed, reactive, h } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { Select, Tag, RangePicker } from 'ant-design-vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { PageWrapper } from '/@/components/Page';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useModal } from '/@/components/Modal';
  import { ChoseIndicatorModal } from './ChoseIndicatorModal';
  import CreateDatasetModal from './CreateDatasetModal.vue';
  import { searchFormSchema, dataTypeOptions } from './data';
  import {
    getExportIndexListApi,
    getExportListApi,
    exportIndexExcelApi,
    getDatasetListApi,
    deleteDatasetListApi,
  } from '/@zhcz/api/data-management';
  import type { IIndicators, QueryIndexList, IDatasetList } from '/@zhcz/api/data-management/model';
  import { indicatorType } from '/@zhcz/enums/indicator';
  import { useDomain } from '/@/locales/useDomain';
  import { useMessage } from '/@/hooks/web/useMessage';
  import type { BasicColumn } from '/@/components/Table';
  import { downloadByData } from '/@/utils/file/download';

  const timeTypeMap = {
    [indicatorType.小时数据]: {
      unit: 'day',
      picker: 'date',
      'show-time': { format: 'HH' },
      format: 'YYYY-MM-DD HH',
      fileNameFormat: 'YYYY年MM月DD日HH时',
    },
    [indicatorType.天数据]: {
      unit: 'month',
      picker: 'date',
      'show-time': false,
      format: 'YYYY-MM-DD',
      fileNameFormat: 'YYYY年MM月DD日',
    },
    [indicatorType.月数据]: {
      unit: 'year',
      picker: 'month',
      'show-time': false,
      format: 'YYYY-MM',
      fileNameFormat: 'YYYY年MM月',
    },
  };

  interface SearchForm {
    timeType: number;
    dates: [Dayjs, Dayjs];
  }

  const { getTenantId } = useDomain();
  const { createMessage } = useMessage();

  const formRef = ref<HTMLDivElement>();

  const actTimeTypeOpt = computed(
    () => timeTypeMap[searchForm.timeType] || timeTypeMap[indicatorType.小时数据],
  );

  const [registerTable, { setColumns, setTableData, setPagination, setLoading }] = useTable({
    title: '数据表格',
    titleHelpMessage:
      '根据分、时、日、月导出厂站的生成、采集数据。单次最多支持导出15W个单元格数据，超出数据量请分多次导出',
    columns: [
      {
        title: '时间',
        dataIndex: 'time',
      },
      {
        title: '指标',
        dataIndex: 'name',
      },
    ],
    dataSource: [],
    clickToRowSelect: false,
    showIndexColumn: false,
    scroll: {
      // y: 435,
      x: 1800,
    },
  });

  const searchForm = reactive<SearchForm>({
    timeType: indicatorType.小时数据,
    dates: [dayjs().startOf('day'), dayjs().endOf('day')],
  });
  const [registerForm] = useForm({
    schemas: searchFormSchema,
    showActionButtonGroup: true,
    labelWidth: 68,
    actionColOptions: {
      span: 4,
      style: {
        textAlign: 'right',
      },
    },
  });

  async function handleDataTypeChange() {
    const { unit } = actTimeTypeOpt.value;
    searchForm.dates = [dayjs().startOf(unit), dayjs().endOf(unit)];
    await getDataSetData();
    // handleSubmit(true);
  }

  const dataset = ref<IDatasetList[]>([]);
  const activeDataset = ref<IDatasetList>();
  async function getDataSetData() {
    const data = await getDatasetListApi({
      timeType: searchForm.timeType,
      type: 1,
    });
    dataset.value = data;
    activeDataset.value = dataset.value[0];
    const firstCheckedList = JSON.parse(dataset.value[0]?.content || '[]');
    console.log('firstCheckedList', firstCheckedList);
    checkedList.value = firstCheckedList.map((item) => ({
      name: item.indexName,
      code: item.indexCode,
      unit: '',
      tag: '',
    }));
  }
  getDataSetData().then(() => {
    handleSubmit(true);
  });

  const [registerDatasetModal, { openModal: openDatasetModalFn }] = useModal();

  function openDatasetModal() {
    // 不能添加更多数据集
    if (dataset.value.length >= 5) {
      createMessage.info('最多只能添加6个数据集');
      return;
    }
    openDatasetModalFn(true, {
      timeType: searchForm.timeType,
      indecatorCodes: checkedList.value,
    });
  }

  function handleAddDataSet() {
    getDataSetData();
  }

  function handleActiveDataset(record: IDatasetList) {
    if (activeDataset.value === record) {
      activeDataset.value = undefined;
      checkedList.value = [];
    } else {
      activeDataset.value = record;
      const firstCheckedList = JSON.parse(record.content || '[]');
      checkedList.value = firstCheckedList.map((item) => ({
        name: item.indexName,
        code: item.indexCode,
        unit: '',
        tag: '',
      }));
    }
  }

  async function handleDeleteDataset(record: IDatasetList) {
    await deleteDatasetListApi(record.id);
    createMessage.success('删除成功');
    getDataSetData();
  }

  const [registerModal, { openModal }] = useModal();
  const queryParams = computed(() => ({
    tenantId: getTenantId.value,
    timeType: searchForm.timeType,
  }));
  async function openIndexModal() {
    try {
      // const data = await getExportIndexListApi({
      //   tenantId: getTenantId.value,
      //   timeType: searchForm.timeType,
      // });
      // openModal(true, data || []);
      openModal(true, []);
    } catch (error) {
      openModal(true, []);
    }
  }

  function handleClear() {
    checkedList.value = [];
    activeDataset.value = undefined;
  }

  const checkedList = ref<IIndicators[]>([]);
  function handleDeleteIndictor(record: IIndicators) {
    checkedList.value = checkedList.value.filter((item) => item.code !== record.code);
    activeDataset.value = undefined;
  }

  function validate(isFirst?: boolean) {
    if (!checkedList.value.length) {
      if (!isFirst) createMessage.info('请选择数据指标');
      return false;
    }

    return true;
  }

  const tableData = ref<Recordable[]>([]);

  const page = ref({
    pageIndex: 1,
    pageSize: 10,
    total: 0,
  });
  function getQueryParams() {
    const { unit } = actTimeTypeOpt.value;
    const {
      timeType,
      dates: [startDateTime, endDateTime],
    } = searchForm;

    const params: QueryIndexList = {
      tenantId: getTenantId.value,
      startDateTime: dayjs(startDateTime).startOf(unit).valueOf(),
      endDateTime: dayjs(endDateTime).endOf(unit).valueOf(),
      indecatorCodes: checkedList.value.map((item) => ({
        indexCode: item.code,
        indexName: item.name,
        unit: item.unit,
      })),
      timeType,
      resourceInterfaceId: 3,
    };

    return params;
  }

  function handleChange(pagination: { pageSize: number; current: number }) {
    page.value.pageIndex = pagination.current;
    page.value.pageSize = pagination.pageSize;
    handleSubmit();
  }

  async function handleSubmit(isFirst?: boolean) {
    const bol = validate(isFirst);
    if (!bol) return;
    try {
      setLoading(true);
      const baseParams = getQueryParams();
      const params = {
        ...baseParams,
        pageIndex: page.value.pageIndex,
        pageSize: page.value.pageSize,
      };
      const res = await getExportListApi(params);
      const data = res.records;

      const codeRecord = (data && data[0]?.codeRecord) || [];
      const columns: BasicColumn[] = [
        {
          title: '时间',
          dataIndex: 'provideTime',
          width: 130,
          fixed: 'left',
          customRender: ({ record }) => {
            return h('span', {}, dayjs(record.provideTime).format('YYYY-MM-DD HH:mm'));
          },
        },
        ...codeRecord.map((item) => ({
          title: item.indexTitie,
          dataIndex: item.indicatorCode,
          width: 200,
          ellipsis: true,
        })),
      ];
      setColumns(columns);
      setLoading(false);

      const _data_ = data
        ? data.map((item) => {
            const indexArr = item.codeRecord.map((j) => ({ key: j.indicatorCode, value: j.val }));
            const indexObj = {};
            indexArr.forEach((k) => {
              indexObj[k.key] = k.value;
            });
            return {
              ...item,
              ...indexObj,
            };
          })
        : [];
      setTableData(_data_);
      tableData.value = _data_;
      setPagination({
        total: res.total,
        current: res.current,
        pageSize: res.size,
      });
    } catch {
      setLoading(false);
    }
  }

  async function handleReset() {
    searchForm.timeType = indicatorType.小时数据;
    searchForm.dates = [dayjs().startOf('day'), dayjs().endOf('day')];
    handleDataTypeChange();
  }

  async function exportExcel() {
    const params = getQueryParams();
    const blob = await exportIndexExcelApi(params);
    const { unit, fileNameFormat } = actTimeTypeOpt.value;
    const { dates } = searchForm;

    const startDateTime = dayjs(dates[0]).startOf(unit).format(fileNameFormat);
    const endDateTime = dayjs(dates[1]).endOf(unit).format(fileNameFormat);

    downloadByData(blob, `${startDateTime}-${endDateTime}指标数据.xlsx`);
  }
</script>

<style lang="less" scoped>
  .vben-page-wrapper {
    :deep(.vben-page-wrapper-content) {
      display: flex;
      flex-direction: column;
      // gap: 16px;
      background-color: #ffffff;
      border-radius: 4px;
    }

    .search-form {
      flex-shrink: 0;
      padding: 16px 16px 0 16px;
      background-color: #ffffff;
      border-bottom: #e9e9e9 1px solid;

      :deep(.ant-row) {
        .ant-col {
          .ant-form-item {
            margin-bottom: 16px !important;
          }
        }
      }

      :deep(.ant-form) {
        .tag-container {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 16px;

          &.indictor .ant-tag {
            cursor: default;

            .ant-tag-close-icon {
              cursor: pointer;
            }
          }

          .ant-tag {
            height: 32px;
            padding: 0 12px;
            // color: #333;
            color: @theme-color;
            cursor: pointer;
            background-color: @theme-color-5p;
            margin-inline-end: 0;
            display: flex;
            align-items: center;
            max-width: 100%;
            border: @theme-color-12p 1px solid;
            border-radius: 4px;
            font-size: 14px;

            &.dataset__active {
              border-color: @theme-color;
            }

            .text {
              font-size: 14px;
            }

            .ant-tag-close-icon {
              font-size: 12px;
              color: @theme-color;
              margin-left: 12px;
            }
          }
        }
      }
    }

    .empty {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      color: rgba(0, 0, 0, 0.25);
      background-color: #fff;
    }

    .vben-basic-table {
      :deep(.ant-table-wrapper) {
        margin: 0;
      }

      :deep(.basic-title) {
        font-size: 14px;
        color: #333;
      }
    }
  }
</style>
