<template>
  <Card
    :bordered="false"
    dis-hover
    :data-resource-code="dataType === 1 ? dayGroupCode : monthGroupCode"
  >
    <template #title>
      <div data-index-name="RankingDrugConsumption">{{ title }}</div>
    </template>
    <template #extra>
      <div class="flex extra-wrap">
        <Select v-model:value="dataType">
          <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
        <DatePicker
          v-if="dataType === 2"
          style="width: 200px"
          v-model:value="monthDate"
          picker="month"
          valueFormat="YYYY-MM"
          :disabledDate="disabledDateMonth"
        />
        <DatePicker
          v-if="dataType === 1"
          valueFormat="YYYY-MM-DD"
          format="YYYY-MM-DD"
          v-model:value="dayDate"
          placeholder="请选择"
          style="width: 200px"
          :allowClear="false"
          :disabledDate="disabledDate"
        />
      </div>
    </template>
    <div
      class="box"
      :class="props.column ? 'column' : ''"
      v-loading="loading"
      v-if="indexList.length"
    >
      <ProgressBar
        :index="index"
        v-for="(item, index) in indexList"
        :key="index"
        :data="item"
        :max="indexList[0].value"
        :column="props.column"
      />
    </div>
    <div v-else class="empty-box">
      <HEmpty />
    </div>
  </Card>
</template>

<script setup lang="ts">
  import { DatePicker, Card, Select, SelectOption } from 'ant-design-vue';
  import ProgressBar from './ProgressBar.vue';
  import dayjs from 'dayjs';
  import { ref, watch } from 'vue';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  // import { useIntervalFn } from '@vueuse/core';
  // import { useUserStore } from '/@/store/modules/user';

  import HEmpty from '/@/components/HEmpty/index.vue';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    column: {
      type: Boolean,
      default: false,
    },
    dayResourceInterfaceId: {
      type: String,
      default: '',
    },
    dayGroupCode: {
      type: String,
      default: '',
    },
    monthResourceInterfaceId: {
      type: String,
      default: '',
    },
    monthGroupCode: {
      type: String,
      default: '',
    },
  });

  const dataList = [
    {
      label: '日',
      value: 1,
    },
    {
      label: '月',
      value: 2,
    },
  ];
  const dataType = ref(1);
  const monthDate = ref(dayjs().subtract(1, 'month').format('YYYY-MM'));
  const dayDate = ref(dayjs().format('YYYY-MM-DD'));

  const loading = ref(false);
  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);

  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }

  function disabledDateMonth(current) {
    return current && current > dayjs().subtract(1, 'month').endOf('day');
  }

  const getData = async () => {
    const startDateTime =
      dataType.value === 1
        ? dayjs(dayDate.value).format('YYYY-MM-DD 00:00:00')
        : dayjs(monthDate.value).startOf('month').format('YYYY-MM-DD 00:00:00');
    const endDateTime =
      dataType.value === 1
        ? dayjs(dayDate.value).format('YYYY-MM-DD 23:59:59')
        : dayjs(monthDate.value).endOf('month').format('YYYY-MM-DD 23:59:59');
    const params = {
      startDateTime: startDateTime,
      endDateTime: endDateTime,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      resourceInterfaceId:
        dataType.value === 1 ? props.dayResourceInterfaceId : props.monthResourceInterfaceId,
      groupCode: dataType.value === 1 ? props.dayGroupCode : props.monthGroupCode,
      paramsData: JSON.stringify(params),
    };

    const { data } = await callResourceFunction(paramData);
    console.log(`${props.title}`, data);
    if (data && data.length) {
      const newData = data.map((item) => {
        return {
          indexName: item.indexName,
          indexCode: item.indexCode,
          unitName: item.unitName,
          value: item.data
            .map((i) => i.value)
            .reduce((prev, cur) => {
              return prev + cur;
            }, 0),
        };
      });
      indexList.value = BubbleSort(newData);
    }
  };

  function BubbleSort(ary) {
    for (var i = 0; i < ary.length - 1; i++) {
      for (var j = i + 1; j < ary.length; j++) {
        var current = ary[i];
        if (Number(current.value) < Number(ary[j].value)) {
          var tmp = ary[j];
          ary[j] = current;
          ary[i] = tmp;
        }
      }
    }
    return ary;
  }

  getData();

  // const { pause, resume } = useIntervalFn(getData, 60 * 1000);
  // const userStore = useUserStore();
  // const token = computed(() => userStore.getToken);
  // watch(
  //   () => token.value,
  //   (newVal) => {
  //     if (!newVal) {
  //       pause();
  //     }
  //   },
  // );

  watch(
    () => dayDate.value,
    async (newVal) => {
      if (dataType.value === 1) {
        console.log('newValArr=>日', newVal);
        // pause();
        await getData();
        // resume();
      }
    },
  );

  watch(
    () => monthDate.value,
    async (newVal) => {
      if (dataType.value === 2) {
        console.log('newVal=>月', newVal);
        // pause();
        await getData();
        // resume();
      }
    },
  );

  watch(
    () => dataType.value,
    async (newVal) => {
      console.log('newVal=>日期类型', newVal);
      monthDate.value = dayjs().subtract(1, 'month').format('YYYY-MM');
      dayDate.value = dayjs().format('YYYY-MM-DD');
      // pause();
      await getData();
      // resume();
    },
  );

  // onMounted(() => {
  //   resume();
  // });

  // onActivated(() => {
  //   resume();
  // });

  // onUnmounted(() => {
  //   pause();
  // });

  // onDeactivated(() => {
  //   pause();
  // });
</script>

<style lang="less" scoped>
  .ant-card {
    height: 100%;

    :deep(.ant-card-head) {
      padding: 0 16px;
      font-size: 16px;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      min-height: 48px;
      border-bottom: 1px solid #e9e9e9;

      .ant-card-head-title {
        min-width: 120px;
      }
    }

    :deep(.ant-card-body) {
      padding: 20px 16px;
      height: 328px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px;
        height: 9px;
      }
    }
  }

  .extra-wrap {
    :deep(.ant-select) {
      margin-right: 16px;

      .ant-select-selector {
        width: 80px;
      }
    }
  }

  .box {
    height: 100%;
    // overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 42px;

    &.column {
      gap: 14px;
    }
  }

  .empty-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #999;
  }
</style>
