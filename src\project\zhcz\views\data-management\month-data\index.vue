<template>
  <PageWrapper contentFullHeight fixed-height contentClass="data-wrapper">
    <div class="left-wrapper" :class="{ 'left-wrapper_hide': !isExpansion }" ref="leftWrapperRef">
      <div class="header">
        <div class="title">{{ isExpansion ? '指标分类' : '' }}</div>
      </div>
      <div class="content">
        <!-- <Select
          style="width: 100%"
          :options="factoryInfoList"
          :field-names="{ label: 'factoryName', value: 'factoryId' }"
          v-model:value="factoryId"
          @change="handleChangeFactory"
        /> -->
        <div
          class="type-list"
          :style="{
            display: typeList.length ? '' : 'flex',
            justifyContent: typeList.length ? '' : 'center',
            alignItems: typeList.length ? '' : 'center',
            height: typeList.length ? '' : 'calc(100% - 48px)',
          }"
        >
          <template v-if="typeList.length">
            <div
              :class="['item', { item_active: item.id === activeTypeItem?.id }]"
              v-for="(item, index) in typeList"
              :key="item.id"
              @click="handleSwitchType(item)"
            >
              <span class="icon-wrapper">
                <Icon v-if="index === 0" icon="icon-park-outline:ripple" />
                <Icon v-if="index === 1" icon="icon-park-outline:experiment" />
                <Icon v-if="index === 2" icon="icon-park-outline:lightning" />
                <Icon v-if="index === 3" icon="icon-park-outline:water-level" />
                <Icon v-if="index === 4" icon="icon-park-outline:speed" />
              </span>
              <span>{{ item.name }}</span>
            </div>
          </template>
          <HEmpty v-else />
        </div>
      </div>
    </div>
    <div class="resize" @mousedown="startDrag"></div>
    <div
      class="toggle-wrapper"
      @click="handleSwitchExpansion"
      :style="{
        width: isExpansion ? '0' : '24px',
        borderRight: isExpansion ? 'none' : '1px solid #e9e9e9',
      }"
    >
      <div
        class="toggle"
        :style="{
          left: isExpansion ? '-24px' : '0',
          borderRadius: isExpansion ? '4px 0px 0px 4px' : '0px 4px 4px 0px',
          background: isExpansion ? '' : 'var(--theme-color)',
          borderColor: isExpansion ? '' : 'var(--theme-color)',
        }"
      >
        <LeftOutlined v-show="isExpansion" />
        <RightOutlined v-show="!isExpansion" style="color: #fff" />
      </div>
    </div>
    <div class="right-wrapper">
      <div class="search-form">
        <div class="form-wrapper">
          <BasicForm @register="registerForm" @submit="handleSubmit" @reset="handleReset" />
        </div>
        <div class="action-wrapper">
          <div class="flex items-center">
            <a-button
              :icon="h(Icon, { icon: 'icon-park-outline:save' })"
              @click="handleSave"
              :disabled="!dataManagementStore.getModifyIndexData.length"
              v-show="false"
              class="mr-4"
            >
              保存
            </a-button>
            <BasicImport
              fileName="标签批量导入模板.xlsx"
              @success="handleSubmit"
              :api="importDataExcelApi"
              :tempApi="() => tempDataExcelApi(tempParams)"
            >
              <a-button :icon="h(Icon, { icon: 'icon-park-outline:download' })"> 导入 </a-button>
            </BasicImport>
            <a-button
              :icon="h(Icon, { icon: 'icon-park-outline:upload' })"
              class="ml-4"
              @click="handleExport"
            >
              导出
            </a-button>

            <BasicTitle
              class="ml-3"
              :helpMessage="['橙色是已修改数据', '上箭头是超限数据', '下箭头是低限数据']"
            >
              说明
            </BasicTitle>
          </div>
          <div>
            <autoSet
              :timeType="timeType"
              :indexList="indexList"
              @updataList="handleSubmit"
              style="margin-right: 16px"
            />
            <ColumnSetting
              v-if="columnSet"
              @columns-change="columnsChange"
              :columns="columnsCopy"
            />
          </div>
        </div>
      </div>
      <div class="content-wrapper">
        <DataTable
          :dataTimeType="timeType"
          :columns="columnsCopy2"
          :data="tableData"
          :mergeRow="mergeRow"
          v-loading="spinning"
        />
      </div>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts" name="monthData">
  import { ref, onMounted, nextTick, computed, provide, h, onBeforeUnmount } from 'vue';
  // import { Select } from 'ant-design-vue';
  import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
  import autoSet from '../components/data-table/autoInOut.vue';
  import dayjs from 'dayjs';
  import { Icon } from '/@/components/Icon';
  import { PageWrapper } from '/@/components/Page';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicTitle } from '/@/components/Basic/index';
  import DataTable from '../components/data-table/index.vue';
  import { useDomain } from '/@/locales/useDomain';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDataManagementStore } from '/@zhcz/store/modules/data-management';
  import { searchFormSchema } from './data';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import {
    getIndexListApi,
    updateIndexDataApi,
    getGroupTreeApi,
    tempDataExcelApi,
    importDataExcelApi,
    exportApi,
  } from '/@zhcz/api/data-management';
  import { indicatorType } from '/@zhcz/enums/indicator';
  import { BasicImport } from '/@zhcz/components/ImportButton';
  import type {
    QueryIndexList,
    IIndexList,
    IIndexData,
    IDataClassificationItem,
  } from '/@zhcz/api/data-management/model';
  import type { ITableData } from './types';
  import onTimeImg from '../assets/images/on-month.png';
  import noonTimeImg from '../assets/images/noon-month.png';
  import { BasicQueryParamsSymbol } from '../injectionSymbols';
  // import { useUserStore } from '/@/store/modules/user';
  // import { createLocalStorage } from '/@/utils/cache';
  // import { FACTORY_KEY } from '/@/enums/cacheEnum';
  import { map, keyBy } from 'lodash-es';
  import { useEmitt } from '/@/hooks/web/useEmitt';
  import { SAVE_REPORT_DATA } from '/@zhcz/views/data-management/constant';
  import { downloadByData } from '/@/utils/file/download';
  import ColumnSetting from '../components/data-table/ColumnSetting.vue';
  import { cloneDeep } from 'lodash-es';

  const columnsCopy2 = ref<any[]>([]);
  const columnsCopy = ref<any[]>([]);
  const columnsChange = (data) => {
    console.log('columnsChangedata', data);
    const dataC: any[] = [];
    if (data.length) {
      data.forEach((val) => {
        columnsCopy.value.forEach((item) => {
          if (val.dataIndex === item.title && val.visible) {
            dataC.push(item);
          }
        });
      });
      columnsCopy2.value = cloneDeep([
        {
          title: '月',
          type: 'time',
          unit: '',
        },
        ...dataC,
      ]);
    }
    console.log('columnsCopy2.value', columnsCopy2.value);
  };
  const columnSet = ref(true);
  const timeType: number = indicatorType['月数据'];
  const spinning = ref(false);

  const { getTenantId } = useDomain();

  const dataManagementStore = useDataManagementStore();
  const { createMessage } = useMessage();
  dataManagementStore.setModifyIndexData([]);
  // const userStore = useUserStore();
  // const userInfo = computed(() => userStore.getUserInfo);
  // const ls = createLocalStorage();
  // const factoryInfoList = computed(() => userInfo.value.factoryInfoList || []);
  // const factoryId = ref(ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId);

  const typeList = ref<IDataClassificationItem[]>([]);
  const activeTypeItem = ref<IDataClassificationItem | null>(null);

  const tempParams = computed(() => ({
    timeType,
    // factoryId: factoryId.value,
    groupId: activeTypeItem.value?.id,
  }));

  // async function getTypeListData() {
  //   try {
  //     const params = {
  //       factoryId: factoryId.value,
  //       timeType,
  //     };
  //     if (!params.factoryId) {
  //       throw new Error(`水厂id为${params.factoryId}`);
  //     }
  //     const data = await getGroupTreeApi(params);
  //     typeList.value = data?.children || [];
  //     activeTypeItem.value = data?.children[0] || null;
  //   } catch (_) {
  //     typeList.value = [];
  //     activeTypeItem.value = null;
  //   }
  // }

  function handleSwitchType(item: IDataClassificationItem) {
    activeTypeItem.value = item;
    handleSubmit();
  }

  // async function handleChangeFactory() {
  //   await getTypeListData();
  //   if (activeTypeItem.value) {
  //     handleSwitchType(activeTypeItem.value);
  //   } else {
  //     indexData.value = null;
  //     indexList.value = [];
  //   }
  // }

  const leftWrapperRef = ref<HTMLDivElement | null>(null);
  const startX = ref(0);
  const startWidth = ref(0);

  function startDrag(e) {
    startX.value = e.clientX;
    const leftWrapper = leftWrapperRef.value as HTMLDivElement;
    leftWrapper.style.transition = 'none';
    startWidth.value = leftWrapper.offsetWidth;
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);

    function drag(e) {
      const moveX = e.clientX - startX.value;
      if (startWidth.value + moveX <= 248) {
        return;
      }
      const leftWrapper = document.getElementsByClassName('left-wrapper')[0] as HTMLDivElement;
      leftWrapper.style.width = startWidth.value + moveX + 'px';
      const resize = document.getElementsByClassName('resize')[0] as HTMLDivElement;
      resize.style.left = startWidth.value + moveX + 10 + 'px';
    }

    function stopDrag() {
      leftWrapper.style.transition = 'all 0.2s';
      document.removeEventListener('mousemove', drag);
      document.removeEventListener('mouseup', stopDrag);
    }
  }
  const isExpansion = ref(true);

  function handleSwitchExpansion() {
    isExpansion.value = !isExpansion.value;
  }

  const sourceType = ref<Recordable[]>([]);
  async function getInitData() {
    async function getTypeData() {
      const params = {
        // factoryId: factoryId.value,
        timeType,
      };
      // if (!params.factoryId) {
      //   throw new Error(`水厂id为${params.factoryId}`);
      // }
      const data = await getGroupTreeApi(params);

      return data;
    }

    async function getSourceData() {
      const data = await getDictTypeListApi({ type: 'monitoring_point_data_source' });

      return data;
    }

    const promises = [getTypeData(), getSourceData()];

    const result = {
      typeList: [] as IDataClassificationItem[],
      sourceType: [] as Recordable[],
    };

    await Promise.allSettled(promises).then((results: Recordable[]) => {
      const values = results.map((i) => i.value);
      const [typeList, sourceType] = values;
      result.typeList = typeList?.children || [];
      result.sourceType = sourceType;
    });

    return result;
  }

  const basicQueryParams = ref({
    tenantId: getTenantId.value,
    sourceType: '',
    timeType: timeType,
    date: '',
  });
  provide(BasicQueryParamsSymbol, basicQueryParams);
  const [registerForm, { updateSchema, getFieldsValue, setFieldsValue }] = useForm({
    schemas: searchFormSchema,
    showActionButtonGroup: true,
    labelWidth: 68,
    actionColOptions: {
      span: 12,
      style: {
        textAlign: 'right',
      },
    },
  });

  // let isFirstRequest = true;
  async function updateFormData(data: Recordable, formData: Recordable) {
    updateSchema([
      {
        field: 'sourceType',
        componentProps: {
          allowClear: false,
          options: data.sourceType,
          async onChange(e) {
            await nextTick();
            basicQueryParams.value.sourceType = e;
            // if (!isFirstRequest) handleSubmit();
          },
        },
      },
      {
        field: 'date',
        componentProps: {
          async onChange(e) {
            await nextTick();
            basicQueryParams.value.date = e;
            // if (!isFirstRequest) handleSubmit();
          },
        },
      },
    ]);
    await nextTick();
    setFieldsValue(formData);
  }

  function getIndexListQueryParams(formData: Recordable) {
    const result: QueryIndexList = {
      tenantId: getTenantId.value,
      startDateTime: dayjs(
        `${dayjs(formData.date).startOf('years').format('YYYY-MM-DD')} 00:00:00`,
      ).valueOf(),
      endDateTime: dayjs(
        `${dayjs(formData.date).endOf('years').format('YYYY-MM-DD')} 23:59:59`,
      ).valueOf(),
      sourceType: formData.sourceType as string,
      timeType: timeType,
      resourceInterfaceId: 3,
      groupId: activeTypeItem.value?.id,
      // factoryId: factoryId.value,
    };

    return result;
  }

  const indexList = ref<IIndexList[]>([]);
  const indexData = ref<IIndexData | null>(null);

  // const columns = computed(() => {
  //   const timeColumn = [
  //     {
  //       title: '月',
  //       type: 'time',
  //       unit: '',
  //     },
  //   ];
  //   const indexData = indexList.value[0]?.codeRecord || [];
  //   const indexColumn = indexData.map((item) => {
  //     return {
  //       type: 'index',
  //       title: item.indexTitie,
  //       unit: item.unit,
  //     };
  //   });
  //   return timeColumn.concat(indexColumn);
  // });

  const mergeRow = computed(() => {
    let codeRecord: any[] = indexList.value[0]?.codeRecord || [];
    codeRecord = columnsCopy2.value
      .map((item) => {
        let row = {};
        codeRecord.forEach((val) => {
          if (val.indexTitie === item.title) {
            row = val;
          }
        });
        if (item.title === '月') {
          row = item;
        }
        return row;
      })
      .filter((item: any) => item.title !== '月');
    const indicatorValueMergerList = indexData.value?.indicatorValueMergerList ?? [];

    const codeRecordMap = keyBy(codeRecord, 'indicatorCode');
    const indicatorValueMergerListMap = keyBy(indicatorValueMergerList, 'indicatorCode');

    const sortedAndFilledIndicatorValueMergerList = map(codeRecordMap, (record) => {
      return (
        indicatorValueMergerListMap[record.indicatorCode] || {
          indicatorCode: record.indicatorCode,
          val: '',
          mergerName: '未定义',
        }
      );
    });

    return sortedAndFilledIndicatorValueMergerList;
  });

  // 数字转中文
  function numToChinese(num: number) {
    const chinese = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'];
    return chinese[num - 1];
  }

  const tableData = computed(() => {
    let codeRecord: any[] = indexList.value[0]?.codeRecord || [];
    // console.log('tableData', codeRecord);
    codeRecord = columnsCopy2.value
      .map((item) => {
        let row = {};
        codeRecord.forEach((val) => {
          if (val.indexTitie === item.title) {
            row = val;
          }
        });
        if (item.title === '月') {
          row = item;
        }
        return row;
      })
      .filter((item: any) => item.title !== '月');
    const formData = getFieldsValue();
    const formDate = dayjs(formData?.date).format('YYYY');

    const timeData: ITableData = {
      title: '月',
      type: 'time',
      data: Array.from({ length: 12 }, (_, index) => {
        const num = index + 1;
        const date = `${formDate}-${num}-01 00:00:00`;
        const isBefore = dayjs(date).isBefore(dayjs(), 'months');
        // const currentCodeRecord = indexList.value[index]?.codeRecord || [];
        return {
          icon: isBefore && date ? onTimeImg : noonTimeImg,
          time: `${numToChinese(num)}月份`,
          date,
          isShowDuty: isBefore && date ? 1 : 0,
          isBefore,
          isEmpty: !codeRecord.length,
          // isHaveEmptyValue: currentCodeRecord.some((i) => !i.val),
        };
      }),
    };

    // const indicatorValueMergerList = indexData.value?.indicatorValueMergerList ?? [];
    const indicatorData: ITableData[] = codeRecord.map((item) => {
      const data = indexList.value.map((i) => {
        const indicator: any = i.codeRecord.find((val) => val.indexTitie === item.indexTitie);
        return {
          isShowDuty: dayjs().subtract(1, 'months').isBefore(i.provideTime) ? 0 : 1,
          value: indicator.val,
          status: 'blur',
          showHistory: false,
          isModify: indicator.isModify,
          oldValue: indicator.val,
          provideTime: i.provideTime,
          indicatorCode: indicator.indicatorCode,
          indexTitle: indicator.indexTitie,
          indexUnit: indicator.unit,
          rawData: indicator,
        };
      });

      // const mergeItem = indicatorValueMergerList.find(
      //   (item) => item.indicatorCode === data[0].indicatorCode,
      // );

      // const mergeValueItem = {
      //   isShowDuty: 0,
      //   value: '',
      //   status: 'blur',
      //   showHistory: false,
      //   isModify: '',
      //   oldValue: '',
      //   mergeValue: mergeItem?.val || '',
      //   mergeName: mergeItem?.mergerName || '',
      //   provideTime: '',
      //   indicatorCode: data[0].indicatorCode,
      // } as any;
      // data.push(mergeValueItem);

      return {
        title: `${item.indexTitie}${item.unit ? `(${item.unit})` : ''}`,
        type: 'index',
        data,
      };
    });

    return [timeData].concat(indicatorData);
  });

  async function handleSubmit() {
    try {
      spinning.value = true;
      columnSet.value = false;
      const formData = getFieldsValue();
      const queryParams = getIndexListQueryParams(formData);
      const data = await getIndexListApi(queryParams);
      indexData.value = data;
      indexList.value = data.indicatorDataList || [];
      const indexDatac = indexList.value[0]?.codeRecord || [];
      const indexColumn = indexDatac.map((item) => {
        return {
          type: 'index',
          title: item.indexTitie,
          unit: item.unit,
        };
      });
      columnsCopy.value = cloneDeep(indexColumn);
      columnsCopy2.value = cloneDeep([
        {
          title: '月',
          type: 'time',
          unit: '',
        },
        ...indexColumn,
      ]);
      columnSet.value = true;
    } catch (error) {
      console.log('error', error);
    } finally {
      columnSet.value = true;
      spinning.value = false;
    }
  }

  async function handleReset() {
    const formData = {
      sourceType: sourceType.value[0].value,
      date: dayjs().format('YYYY'),
    };
    await setFieldsValue(formData);
    handleSubmit();
  }

  async function handleSave() {
    const data = dataManagementStore.getModifyIndexData;
    if (!data.length) {
      createMessage.info('请修改数据');
      return;
    }

    const queryParams = {
      tenantId: getTenantId.value,
      // factoryId: factoryId.value,
      itemsList: data.map((item) => {
        return {
          provideTime: item.provideTime,
          indicatorCode: item.indicatorCode,
          indicatorValue: String(item.value),
          oldValue: String(item.oldValue),
        };
      }),
    };

    await updateIndexDataApi(queryParams);
    createMessage.info('保存成功');
    handleSubmit();
    dataManagementStore.setModifyIndexData([]);
  }

  async function handleExport() {
    const formData = getFieldsValue();
    const queryParams = getIndexListQueryParams(formData);
    const blob = await exportApi(queryParams);

    downloadByData(blob, `${activeTypeItem.value?.name}月数据.xlsx`);
  }

  const { emitter } = useEmitt();
  emitter.on(SAVE_REPORT_DATA + timeType, async (data: Recordable) => {
    console.log('month-data', data);
    const queryParams = {
      tenantId: getTenantId.value,
      // factoryId: factoryId.value,
      itemsList: [
        {
          provideTime: data.provideTime,
          indicatorCode: data.indicatorCode,
          indicatorValue: String(data.value),
          oldValue: String(data.oldValue),
        },
      ],
    };

    if (!data.indicatorCode || !data.value) {
      createMessage.info('请修改数据');
      return;
    } else {
      await updateIndexDataApi(queryParams);
      createMessage.info('保存成功');
    }

    const timeIndex = indexList.value.findIndex((item) => item.provideTime === data.provideTime);

    if (timeIndex > -1) {
      const timeItem = indexList.value[timeIndex];

      const recordIndex = timeItem.codeRecord.findIndex(
        (item) => item.indicatorCode === data.indicatorCode,
      );
      if (recordIndex > -1) {
        timeItem.codeRecord[recordIndex].isModify = '1';
        timeItem.codeRecord[recordIndex].val = data.value;
        // const list = [...indexList.value];
        // list[timeIndex] = timeItem;
        // indexList.value = list;
      }
    }

    const modifyData = dataManagementStore.getModifyIndexData;
    const newData = modifyData.filter(
      (item) => item.indicatorCode !== data.indicatorCode && item.provideTime === data.provideTime,
    );
    dataManagementStore.setModifyIndexData(newData);
  });

  onMounted(async () => {
    const data = await getInitData();
    typeList.value = data.typeList;
    activeTypeItem.value = data.typeList[0] || null;
    sourceType.value = data.sourceType;
    const formData = {
      sourceType: data.sourceType[0].value,
      date: dayjs().format('YYYY'),
    };
    updateFormData(data, formData);

    try {
      spinning.value = true;
      columnSet.value = false;
      const queryParams = getIndexListQueryParams(formData);
      const data = await getIndexListApi(queryParams);
      // isFirstRequest = false;
      indexData.value = data;
      indexList.value = data.indicatorDataList || [];
      const indexDatac = indexList.value[0]?.codeRecord || [];
      const indexColumn = indexDatac.map((item) => {
        return {
          type: 'index',
          title: item.indexTitie,
          unit: item.unit,
        };
      });
      columnsCopy.value = cloneDeep(indexColumn);
      columnsCopy2.value = cloneDeep([
        {
          title: '月',
          type: 'time',
          unit: '',
        },
        ...indexColumn,
      ]);
      columnSet.value = true;
      // console.log('columnsCopy.value', columnsCopy.value);
    } catch (error) {
      console.log('error', error);
    } finally {
      columnSet.value = true;
      spinning.value = false;
    }
  });

  onBeforeUnmount(() => {
    emitter.off(SAVE_REPORT_DATA + timeType);
  });
  defineOptions({
    name: 'DataManagementMonthData',
  });
</script>
<script lang="ts">
  export default { name: 'MonthData' };
</script>
<style lang="less" scoped>
  .vben-page-wrapper {
    :deep(.data-wrapper) {
      position: reactive;
      display: flex;
      border-radius: 4px;
      overflow: hidden;

      .left-wrapper {
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        width: 248px;
        background-color: #ffffff;
        border-right: 1px solid #e9e9e9;
        overflow: hidden;
        transition: all 0.2s;

        &_hide {
          width: 0 !important;
          border-right: none;

          .header,
          .content {
            display: none;
          }
        }

        .header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 48px;
          padding: 0 16px;
          border-bottom: 1px solid #e9e9e9;

          .title {
            font-size: 16px;
            font-weight: 600;
          }
        }

        .content {
          padding: 12px;
          flex: 1;
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 0;
            height: 0;
            background-color: transparent;
          }

          .type-list {
            // margin-top: 12px;

            .item {
              padding: 0 14px;
              display: flex;
              align-items: center;
              gap: 6px;
              line-height: 40px;
              font-size: 14px;
              border-radius: 4px;
              color: @text-color-bold;
              cursor: pointer;
              transition: background-color 0.3s;

              &:not(.item_active):hover {
                background-color: #f0f0f0;
              }

              .icon-wrapper {
                color: @text-color-call-out;
              }

              &_active {
                color: @theme-color;
                background-color: @theme-color-12p;

                .icon-wrapper {
                  color: @theme-color;
                }
              }
            }
          }
        }
      }

      .toggle-wrapper {
        position: relative;
        width: 24px;
        height: 100%;
        background: #ffffff;
        cursor: pointer;

        .toggle {
          position: absolute;
          top: 50%;
          width: 24px;
          height: 32px;
          border-radius: 4px 0px 0px 4px;
          border: 1px solid #e9e9e9;
          transform: translateY(-50%);
          cursor: pointer;
          text-align: center;
          line-height: 30px;
          z-index: 999;
          background: #fff;
          transition: all 0.2s;
        }
      }

      .resize {
        position: absolute;
        top: 0;
        bottom: 16px;
        left: 262px;
        width: 8px;
        cursor: col-resize;
        // background: red;
      }

      .right-wrapper {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        background-color: #ffffff;

        .search-form {
          // padding: 16px 16px 0 16px;
          border-radius: 4px;
          background-color: #ffffff;

          .form-wrapper {
            padding: 16px 16px 0 16px;
            border-bottom: 1px solid #e9e9e9;

            .ant-row {
              .ant-col {
                &:last-child {
                  .ant-btn {
                    margin-right: 0 !important;
                  }
                }

                .ant-form-item {
                  margin-bottom: 16px;
                }
              }
            }
          }

          .action-wrapper {
            padding: 16px 16px 12px 16px;
            // border-top: 1px solid #f5f6f7;
            display: flex;
            justify-content: space-between;

            .vben-basic-title {
              font-size: 14px;
              font-weight: 400;
            }
          }
        }

        .content-wrapper {
          position: relative;
          flex: 1;
          overflow: hidden;
          background-color: #ffffff;
        }
      }
    }

    :deep(.ant-spin-nested-loading) {
      height: 100%;
      overflow: hidden;

      .ant-spin-container {
        height: 100%;
      }
    }
  }
</style>
