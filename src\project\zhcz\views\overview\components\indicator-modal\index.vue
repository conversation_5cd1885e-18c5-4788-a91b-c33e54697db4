<template>
  <div>
    <IndicatorModal
      v-model:open="indicatorOpen"
      width="1272px"
      :bodyStyle="{ height: '716px' }"
      :destroyOnClose="true"
      :groupInfo="groupInfo"
      :multiple="multiple"
      :factoryId="factoryId"
      title="指标详情"
      :requestHeader="requestHeader"
      :bgImage="bgImage"
      :footer="' '"
      :base-url="baseUrl"
      wrapClassName="bi-screen-modal"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { getFactoryId } from '/@zhcz/utils/factory';
  import { getToken } from '/@/utils/auth';
  import { useDomain } from '/@/locales/useDomain';
  import { IndicatorModal } from 'hlxb-business-ui';

  import headerBg from './assets/images/header.png';
  import bodyBg from './assets/images/body.png';
  import footerBg from './assets/images/footer.png';
  import { getAppEnvConfig } from '/@/utils/env';

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
  });

  const bgImage = { header: headerBg, body: bodyBg, footer: footerBg };

  const indicatorOpen = ref(false);
  const groupInfo = ref({
    groupCode: 'zhinengbi-shengchanshuju',
    resourceInterfaceId: '3',
    jsConvert: false,
    indexCodes: 'Smart.M580.FROM_CS.PH',
  });
  const multiple = ref(false);

  const baseUrl = computed(() => getAppEnvConfig().VITE_GLOB_API_URL);
  const factoryId = getFactoryId();

  const { getTenantId } = useDomain();
  const requestHeader: any = {
    Authorization: getToken(),
    'Tenant-Id': getTenantId.value,
  };

  watch(
    () => props.show,
    (newVal) => {
      console.log('newVal', newVal);
      indicatorOpen.value = newVal;
    },
    {
      immediate: true,
    },
  );
</script>

<style lang="less">
  .hlxb-modal-wrap {
    .ant-modal {
      @media (max-width: 1024px) {
        margin: 0;
        max-width: 100vw;
        padding-bottom: 0;
      }

      .ant-modal-close {
        width: 22px;
        height: 22px;

        .ant-modal-close-x {
          width: auto;
          height: auto;
          display: block;
          font-size: 16px;
          font-style: normal;
          line-height: 22px;
          text-align: center;
          text-transform: none;
          text-rendering: auto;
        }
      }

      .ant-modal-content {
        .ant-modal-body {
          padding: 0 24px 24px !important;

          @media (max-width: 1024px) {
            padding: 0 !important;
          }
        }
      }
    }
  }

  .hlxb-modal-wrap--dark {
    .ant-modal {
      @media (max-width: 1024px) {
        .ant-modal-content {
          .ant-modal-header {
            background-color: #041946;
          }
        }
      }

      .ant-modal-close-x {
        color: #fff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }

      .ant-tree {
        .ant-tree-title {
          color: inherit;
        }
      }

      .ant-select-selection-item {
        color: inherit;
      }

      span.anticon:not(.app-iconify) {
        font-size: inherit;
        margin-right: 0;
        vertical-align: inherit;
      }
    }
  }

  .bi-screen-moda {
    .ant-modal {
      @media (max-width: 1024px) {
        .ant-modal-content {
          .ant-modal-header {
            background-color: inherit;
          }
        }
      }

      .ant-spin-nested-loading {
        .ant-spin-container::after {
          display: none;
        }
      }

      .ant-table-thead {
        & > tr {
          :where(.ant-table-cell) {
            color: #fff !important;
          }
        }
      }

      .ant-table .ant-table-row .ant-table-cell {
        color: #fff !important;
      }

      .ant-pagination {
        .ant-pagination-item {
          border-color: transparent !important;

          &.ant-pagination-item-active {
            border: 1px solid #2d82fe !important;
          }

          a {
            color: #fff !important;
          }

          &:hover {
            background-color: transparent !important;

            a {
              color: #fff !important;
            }
          }
        }

        .ant-pagination-jump-next,
        .ant-pagination-jump-prev,
        .ant-pagination-next,
        .ant-pagination-prev {
          border-color: transparent !important;

          .ant-pagination-item-ellipsis,
          .anticon {
            color: #fff !important;
          }

          &:hover {
            background-color: transparent !important;

            .ant-pagination-item-ellipsis,
            .anticon {
              color: #fff !important;
            }
          }
        }
      }
    }
  }
</style>

<style lang="less" scoped>
  @font-face {
    font-family: YouSheBiaoTiHei;
    src: url('/@/assets/fonts/YouSheBiaoTiHei/YouSheBiaoTiHei-2.ttf');
  }

  @font-face {
    font-family: D-DIN-PRO;
    src: url('/@/assets/fonts/D-DIN-PRO/D-DIN-PRO-400-Regular.otf');
  }

  .topology-main {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .spin-wrapper {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      z-index: 100;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  #diagram-wrap {
    height: 100%;
    width: 100%;
  }
</style>
