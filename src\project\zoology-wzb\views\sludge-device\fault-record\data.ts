import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getDictTypeListApi } from '/@/api/admin/dict';
import { DICT } from '../enums';
import { getDeviceModelListApi } from '/@zoology-wzb/api/device';
import { disable } from 'ol/rotationconstraint';

let emergencyLevelOptions = [];
let faultReasonOptions = [];
async function getEmergencyLevelOptions() {
  const res = await getDictTypeListApi({ type: DICT.EQUIPMENT_FAILURE_URGENCY_LEVEL });
  emergencyLevelOptions = res;
}

const reasonMap = {};

async function getFaultReasonOptions() {
  const res = await getDictTypeListApi({ type: DICT.EQUIPMENT_FAILURE_REASON });
  res.forEach((item) => {
    reasonMap[item.value] = item.label;
  });
  faultReasonOptions = res;
}

await Promise.all([getEmergencyLevelOptions(), getFaultReasonOptions()]);

export const columns: BasicColumn[] = [
  {
    title: '设备编号',
    dataIndex: 'id',
    width: 120,
  },
  {
    title: '设备名称',
    dataIndex: 'name',
    width: 150,
  },
  {
    title: '故障时间',
    dataIndex: 'faultDate',
    width: 180,
  },
  {
    title: '完成时间',
    dataIndex: 'finishedDate',
    width: 180,
  },
  {
    title: '故障现象',
    dataIndex: 'faultPhenomenon',
    key: 'faultPhenomenon',
    width: 200,
  },
  {
    title: '故障原因',
    dataIndex: 'faultReasonValue',
    width: 150,
    customRender: ({ record }) => {
      return reasonMap[record.faultReasonValue] || '未知';
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'deviceName',
    label: '设备名称',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请输入设备名称',
      api: async () => {
        const res = await getDeviceModelListApi({});
        return res;
      },
    },
    colProps: { span: 6 },
  },
  {
    field: 'faultPhenomenon',
    label: '故障现象',
    component: 'Input',
    componentProps: {
      placeholder: '请输入故障现象',
    },
    colProps: { span: 6 },
  },
];

// 故障设备表单配置
export function getFaultDeviceFormSchema(isUpdate, type?): FormSchema[] {
  return [
    {
      field: 'name',
      label: '设备名称',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入设备名称',
        disabled: isUpdate,
      },
      colProps: { span: 12 },
    },
    {
      field: 'emergencyLevelValue',
      label: '紧急程度',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择紧急程度',
        options: emergencyLevelOptions,

        // api: async () => {
        //   const res = await getDictTypeListApi({ type: DICT.EQUIPMENT_FAILURE_URGENCY_LEVEL });
        //   return res;
        // },
      },
      colProps: { span: 12 },
    },

    {
      field: 'faultDate',
      label: '故障时间',
      component: 'DatePicker',
      required: true,
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择故障时间',
      },
      colProps: { span: 12 },
    },
    {
      field: 'finishedDate',
      label: '完成时间',
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择完成时间',
      },
      colProps: { span: 12 },
    },

    {
      field: 'faultReasonValue',
      label: '故障原因',
      component: 'Select',
      required: true,
      componentProps: {
        placeholder: '请选择故障原因',
        options: faultReasonOptions,
        // api: async () => {
        //   const res = await getDictTypeListApi({ type: DICT.EQUIPMENT_FAILURE_REASON });
        //   return res;
        // },
      },
      colProps: { span: 12 },
    },

    {
      field: 'faultPhenomenon',
      label: '故障现象',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入故障现象描述',
        rows: 3,
      },
      colProps: { span: 24 },
    },
  ];
}
