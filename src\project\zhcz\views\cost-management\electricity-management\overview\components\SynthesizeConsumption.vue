<template>
  <Card
    :bordered="false"
    dis-hover
    :data-resource-code="dataType === 1 ? dayGroupCode : monthGroupCode"
  >
    <template #title>
      <div data-index-name="ProportionDrugConsumption">电量统计</div>
    </template>
    <template #extra>
      <div class="flex extra-wrap">
        <div>
          <Select v-model:value="typeType" style="width: 150px">
            <SelectOption v-for="item in typeList" :key="item.value" :value="item.value">
              {{ item.label }}
            </SelectOption>
          </Select>
        </div>
        <div>
          <Select v-model:value="dataType" style="width: 80px">
            <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
              {{ item.label }}
            </SelectOption>
          </Select>
        </div>
        <DatePicker
          v-if="dataType === 2"
          style="width: 200px"
          v-model:value="monthDate"
          picker="month"
          valueFormat="YYYY-MM"
          :disabledDate="disabledDateMonth"
        />
        <DatePicker
          v-if="dataType === 1"
          valueFormat="YYYY-MM-DD"
          format="YYYY-MM-DD"
          v-model:value="dayDate"
          placeholder="请选择"
          style="width: 200px"
          :allowClear="false"
          :disabledDate="disabledDate"
        />
      </div>
    </template>
    <div
      style="
        padding-top: 1px;
        width: 100%;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;
      "
      v-loading="loading"
      v-if="indexList.length"
    >
      <div ref="chartRef" style="height: 100%; min-height: 140px"></div>
    </div>
    <div v-else class="empty-box">
      <HEmpty />
    </div>
  </Card>
</template>

<script setup lang="ts">
  import { Card, DatePicker, Select, SelectOption } from 'ant-design-vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { ref, watch } from 'vue';
  import dayjs from 'dayjs';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { EChartsOption } from 'echarts';
  // import { useIntervalFn } from '@vueuse/core';
  // import { useUserStore } from '/@/store/modules/user';
  import { roundAndConvert } from '/@zhcz/utils/number';
  import { useWindowSize } from '@vueuse/core';

  import HEmpty from '/@/components/HEmpty/index.vue';

  const props = defineProps({
    dayResourceInterfaceId: {
      type: String,
      default: '',
    },
    dayGroupCode: {
      type: String,
      default: '',
    },
    monthResourceInterfaceId: {
      type: String,
      default: '',
    },
    monthGroupCode: {
      type: String,
      default: '',
    },
  });
  const colorList = [
    'rgba(46, 140, 255, 1)',
    'rgba(46, 196, 255, 1)',
    'rgba(129, 67, 255, 1)',
    'rgba(34, 205, 128, 1)',
    'rgba(118, 195, 31, 1)',
    'rgba(255, 140, 46, 1)',
  ];
  const loading = ref(false);
  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);
  const typeList = [
    {
      label: '工艺段',
      value: 1,
    },
    {
      label: '分类',
      value: 2,
    },
    {
      label: '设备',
      value: 3,
    },
  ];
  const dataList = [
    {
      label: '日',
      value: 1,
    },
    {
      label: '月',
      value: 2,
    },
  ];
  const typeType = ref(1);
  const dataType = ref(1);
  const monthDate = ref(dayjs().subtract(1, 'month').format('YYYY-MM'));
  const dayDate = ref(dayjs().format('YYYY-MM-DD'));
  const chartRef = ref<any>(null);
  const { setOptions } = useECharts(chartRef);
  const { width } = useWindowSize();

  function handleSetVisitChart() {
    const sum = indexList.value
      .map((item) =>
        item.value == null || !item.value ? 0 : Number(roundAndConvert(item.value, 0)),
      )
      .reduce((prev, cur) => {
        return prev + cur;
      }, 0);

    let commonLeft = 15;
    const option: EChartsOption = {
      title: [
        {
          text: `${sum}`,
          left: commonLeft + '%',

          top: '34%',
          textAlign: 'center',
          textStyle: {
            fontSize: 26,
            fontWeight: 600,
            height: 50,
            color: '#333333',
          },
        },
        {
          text: `总用电(${indexList.value[0]?.unitName ?? ''})`,
          left: commonLeft + '%',

          top: '58%',
          textAlign: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 400,
            color: '#999999',
          },
        },
      ],
      tooltip: {
        trigger: 'item',
        formatter: (data) => {
          return `${data.marker} <span>${data.name}</span> <span>${
            data.data.value === null ? '-' : roundAndConvert(Number(data.data.value), 2)
          }</span><span>${data.data.unit}</span> <span>${data.percent}%</span>`;
        },
        backgroundColor: 'rgba(50, 50, 50, 0.7)',
        textStyle: {
          color: '#fff',
          fontSize: 14,
          lineHeight: 28,
          height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
      },
      legend: {
        // orient: 'vertical',
        // type: 'scroll',
        // scroll: true,
        icon: 'circle',
        left: '30%',
        top: 'middle',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 15,
        textStyle: {
          color: '#77899c',
          fontFamily: 'PingFang SC, PingFang SC',
          overflow: 'truncate',
          rich: {
            title: {
              width: width.value <= 1366 ? 65 : width.value <= 1440 ? 100 : 150,
              color: '#666666',
              fontSize: 14,
              fontWeight: 400,
              lineHeight: 26,
            },
            num: {
              width: dataType.value === 1 ? 75 : 85,
              padding: [0, 0, 0, 0],
              fontFamily: 'PingFang SC-Semibold, PingFang SC',
              color: '#333',
              fontSize: 20,
              lineHeight: 20,
              align: 'right',
            },
            unit: {
              padding: [3, 16, 0, 4],
              color: '#666',
              fontSize: 14,
              lineHeight: 14,
            },
            tag: {
              width: 72,
              height: 26,
              backgroundColor: 'rgba(32, 118, 212, 0.1)',
              borderRadius: 13,
              align: 'center',
              fontWeight: 500,
              fontSize: 14,
              color: '#2076d4',
              lineHeight: 26,
            },
          },
        },
        formatter: (name) => {
          // let percentage;
          let value = '';
          let unit = '';
          const data = indexList.value.map((item) => ({
            ...item,
            value: Number(roundAndConvert(Number(item.value), 0)),
          }));
          // const values = data.map((i) => i.value);
          // const percentageResult = calculatePercentages(values, 1);

          data.forEach((item) => {
            if (name === item.indexName) {
              value = item.value === null || item.value === undefined ? '-' : `${item.value}`;
              unit = item.value === null || item.value === undefined ? '' : `${item.unitName}`;
              // const index = percentageResult.findIndex((i) => i.value == item.value);
              // percentage = percentageResult[index].percentage;
            }
          });

          // return `{title|${name}}`;
          return `{title|${name}}{num|${value}}{unit|${unit}}`;
          // {tag|${percentage}%}
        },
      },
      series: {
        type: 'pie',
        radius: [60, 76],
        center: [commonLeft + '%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emptyCircleStyle: {
          color: 'lightgray',
        },
        labelLine: {
          show: false,
        },
        itemStyle: {
          color: function (colors) {
            return colorList[colors.dataIndex % colorList.length];
          },
        },
        data: indexList.value.map((i) => ({
          value: i.value !== null ? (i.value ? roundAndConvert(i.value, 0) : '0.0000001') : '-',
          name: i.indexName,
          originValue: i.value,
          unit: i.unitName,
        })),
      },
      animation: false,
    };
    setOptions(option);
  }
  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }

  function disabledDateMonth(current) {
    return current && current > dayjs().subtract(1, 'month').endOf('day');
  }

  const getData = async () => {
    const startDateTime =
      dataType.value === 1
        ? dayjs(dayDate.value).format('YYYY-MM-DD 00:00:00')
        : dayjs(monthDate.value).startOf('month').format('YYYY-MM-DD 00:00:00');
    const endDateTime =
      dataType.value === 1
        ? dayjs(dayDate.value).format('YYYY-MM-DD 23:59:59')
        : dayjs(monthDate.value).endOf('month').format('YYYY-MM-DD 23:59:59');
    const params = {
      startDateTime: startDateTime,
      endDateTime: endDateTime,
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      resourceInterfaceId:
        dataType.value === 1 ? props.dayResourceInterfaceId : props.monthResourceInterfaceId,
      groupCode: dataType.value === 1 ? props.dayGroupCode : props.monthGroupCode,
      paramsData: JSON.stringify(params),
    };
    if (typeType.value === 1) {
      paramData.groupCode = dataType.value === 1 ? 'dlph_r' : 'dlph_y';
    } else if (typeType.value === 2) {
      paramData.groupCode = dataType.value === 1 ? props.dayGroupCode : props.monthGroupCode;
    } else if (typeType.value === 3) {
      paramData.groupCode = dataType.value === 1 ? props.dayGroupCode : props.monthGroupCode;
    }

    const { data } = await callResourceFunction(paramData);
    console.log('电量统计', data);
    if (data && data.length) {
      let newData = data.map((item, index) => {
        return {
          indexName: item.indexName,
          indexCode: item.indexCode,
          unitName: item.unitName,
          value: item.data
            .map((i) => Number(i.value))
            .reduce((prev, cur) => {
              return prev + cur;
            }, 0),
          color: colorList[index % 8],
          ratio: 0,
        };
      });
      const total = newData
        .map((i) => i.value)
        .reduce((prev, cur) => {
          return prev + cur;
        }, 0);
      if (total) {
        newData = newData.map((item) => {
          item.ratio = ((item.value / total) * 100).toFixed(2);
          return item;
        });
      }
      indexList.value = newData;
    } else {
      indexList.value = [];
    }
    handleSetVisitChart();
  };

  getData();

  // const jumpConfig = JSON.stringify({
  //   code: 'custom',
  //   params: {
  //     workItemID: '193',
  //     moduleId: '792355641648877747',
  //     modelShowType: 1,
  //     entryID: '3',
  //     extendOpenParam: { userId: '' },
  //   },
  // });
  // router.push({
  //   path: '/module/792355641648877747',
  //   query: {
  //     jumpConfig: jumpConfig,
  //   },
  // });

  // const { pause, resume } = useIntervalFn(getData, 60 * 1000);
  // const userStore = useUserStore();
  // const token = computed(() => userStore.getToken);
  // watch(
  //   () => token.value,
  //   (newVal) => {
  //     if (!newVal) {
  //       pause();
  //     }
  //   },
  // );

  watch(
    () => dayDate.value,
    async () => {
      if (dataType.value === 1) {
        // pause();
        await getData();
        // resume();
      }
    },
  );

  watch(
    () => monthDate.value,
    async (newVal) => {
      if (dataType.value === 2) {
        console.log('newVal=>月', newVal);
        // pause();
        await getData();
        // resume();
      }
    },
  );

  watch(
    () => dataType.value,
    async (newVal) => {
      console.log('newVal=>日期类型', newVal);
      monthDate.value = dayjs().subtract(1, 'month').format('YYYY-MM');
      dayDate.value = dayjs().format('YYYY-MM-DD');
      // pause();
      await getData();
      // resume();
    },
  );
  watch(
    () => typeType.value,
    async () => {
      monthDate.value = dayjs().subtract(1, 'month').format('YYYY-MM');
      dayDate.value = dayjs().format('YYYY-MM-DD');
      // pause();
      await getData();
      // resume();
    },
  );

  // onMounted(() => {
  //   resume();
  // });

  // onActivated(() => {
  //   resume();
  // });

  // onUnmounted(() => {
  //   pause();
  // });

  // onDeactivated(() => {
  //   pause();
  // });
</script>

<style lang="less" scoped>
  .ant-card {
    height: 100%;

    :deep(.ant-card-head) {
      padding: 0 16px;
      font-size: 16px;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      min-height: 48px;
      border-bottom: 1px solid #e9e9e9;
    }

    :deep(.ant-card-body) {
      padding: 16px;
      height: calc(100% - 50px);
      overflow: auto;
    }
  }

  .extra-wrap {
    :deep(.ant-select) {
      margin-right: 16px;

      // .ant-select-selector {
      //   width: 80px;
      // }
    }
  }

  .legend-container {
    position: absolute;
    width: 50%;
    height: 100%;
    right: 0;
    top: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    font-family: 'PingFang SC, PingFang SC';
    gap: 16px;

    .item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      cursor: pointer;

      .point {
        width: 12px;
        height: 12px;
        border-radius: 50%;
      }

      .name {
        padding-left: 8px;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 14px;
        width: 160px;
        white-space: nowrap; /* 让文本不换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 使用省略号代替超出部分 */
      }

      .value {
        margin-left: 8px;
        font-size: 20px;
        font-weight: 600;
        color: #333333;
        display: flex;
        align-items: flex-end;

        .number-value {
          display: inline-block;
          max-width: 120px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .unit {
          display: inline-block;
          margin-left: 4px;
          font-size: 14px;
          font-weight: 400;
          color: #666;
          margin-bottom: 4px;
        }
      }

      .tag {
        margin-left: 12px;
        width: 72px;
        height: 26px;
        background: rgba(32, 118, 212, 0.1);
        border-radius: 13px;
        text-align: center;
        font-weight: 500;
        font-size: 14px;
        color: #2076d4;
        line-height: 26px;
      }
    }
  }

  .empty-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #999;
  }
</style>
