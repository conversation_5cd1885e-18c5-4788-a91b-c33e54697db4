import type { AppRouteRecordRaw, AppRouteModule } from '/@/router/types';

import { getAppEnvConfig } from '/@/utils/env';

import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';

import {
  parentBasicRoutes, //  未经许可的基本路由
  ParentRootRoute, // 根路由
  parentRouteModuleList, // 公共菜单路由 系统管理
  treeList, // 将路由转为菜单所需要的结构
} from '/@/router/routes';
import { t } from '/@/hooks/web/useI18n';

// project  aoa import.meta.glob() 直接引入所有的模块 Vite 独有的功能
const modules: Record<string, any> = import.meta.glob('./modules/**/*.ts', { eager: true });

const routeModuleList: AppRouteModule[] = [];

// 加入到路由集合中
Object.keys(modules).forEach((key) => {
  const mod = modules[key].default || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  routeModuleList.push(...modList);
});

export const asyncRoutes = [PAGE_NOT_FOUND_ROUTE, ...routeModuleList, ...parentRouteModuleList];

export const RootRoute = ParentRootRoute;

export const LoginRoute: AppRouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: () => import('/@/views/sys/login/Login.vue'),
  meta: {
    title: t('routes.basic.login'),
  },
};

export const routeMenuList = treeList([...routeModuleList, ...parentRouteModuleList]);
// === 服务启动动态替换禁止删除注释 start===
const mainoutPages: Record<string, any> = import.meta.glob(
  ['./project/zhcz.ts', './project/zoology-wzb.ts'],
  {
    eager: true,
  },
);
// === 服务启动动态替换禁止删除注释 end ===

function setMainOutPage() {
  const ret = [...parentBasicRoutes, LoginRoute];

  const { VITE_GLOB_PROJECT = [] } = getAppEnvConfig();

  Object.entries(mainoutPages).forEach(([key, value]) => {
    const isProject = VITE_GLOB_PROJECT.find((name) => key.includes(name));

    if (isProject) {
      ret.push(...value.default);
    }
  });

  return ret;
}
// Basic routing without permission
// 未经许可的基本路由
export const basicRoutes = setMainOutPage();
