# Whether to open mock
VITE_USE_MOCK = false

# public path
VITE_PUBLIC_PATH = /

# Cross-domain proxy, you can configure multiple
# Please note that no line breaks
# 
# VITE_PROXY = [["/office","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/hloa", "http://************:9999"],["/jl", "http://************:9999"]] #// 测试
# VITE_PROXY = [["/gis","http://************:9999"],["/mail","http://************:9999"],["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"], ["/factory","http://************:9999"]] #// 开发
# VITE_PROXY = [["/gis","http://************:9999"],["/mail","http://************:9999"],["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"], ["/factory","http://************:9999"]] #// 开发
# VITE_PROXY = [["/office","http://*************:9999"],["/zkrq","http://*************:9999"],["/images","http://*************"],["/admin","http://*************:9999"], ["/auth","http://*************:9999"],["/model","http://*************:9999"],["/workflow","http://*************:9999"],["/msg","http://*************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://*************:9999"],["/cz","http://*************:9999"],["/portal", "http://*************:9999"],["/data-sence", "http://*************:9999"],["/data", "http://*************:9999"],["/api/admin","http://************:15000"], ["/factory","http://*************:9999"],["/jl","http://*************:9999"]] #// 开发
# VITE_PROXY = [["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"],["/assay","http://************:9999"],["/event-center","http://************:9999"], ["/factory","http://************:9999"], ["/process-configuration","http://************:9999"]] #// 开发
# VITE_PROXY = [["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"],["/assay","http://************:9999"],["/event-center","http://************:9999"], ["/factory","http://************:9999"], ["/process-configuration","http://************:9999"]] #// 开发
# VITE_PROXY = [["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/msg","http://************:9999"],["/jd","http://************:9999"],["/portal", "http://************:9999"], ["/data", "http://************:9999"], ["/factory","http://************:9999"]] #// 线上
# VITE_PROXY = [["/office","http://*************:9999"],["/zkrq","http://*************:9999"],["/images","http://*************"],["/admin","http://*************:9999"], ["/auth","http://*************:9999"],["/model","http://*************:9999"],["/workflow","http://*************:9999"],["/msg","http://*************:9999"],["/jd","http://*************:9999"]] #// 线上
# VITE_PROXY = [["/zkrq","http://19***********:9999"],["/images","http://19***********"],["/admin","http://19***********:9999"], ["/auth","http://19***********:9999"],["/model","http://19***********:9999"],["/workflow","http://19***********:9999"],["/msg","http://19***********:9999"],["/jd","http://19***********:9999"]] #// 
# VITE_PROXY = [["/yjpt","http://************:9999"],["/pm","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://************:9999"],["/api/v1/SIndustryInspect","http://************:15029"],["/api/v1/SIndustryWarehouse","http://************:15025"],['/api/fs','http://************:15005'],['/fs','http://************'],['/api/admin','http://************:15000'],['/api/v1/SIndustryCamera','http://************:15030'],['/api/GroupRelationShip','http://************:15019'],['/industryflowapi','http://*************:14444']] #// 开发
# VITE_PROXY = [["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/eq","http://************:9999"],["/schedule","http://************:9999"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"],["/assay","http://************:9999"],["/event-center","http://************:9999"], ["/factory","http://************:9999"], ["/process-configuration","http://************:9999"],["/inspect","http://************:9999"], ["/camera","http://************:9999"],["/HlxbZhczBc/api","http://************/basic-api"],["/schedule","http://************:9999"]] #// 
# VITE_PROXY = [["/office","http://*************:9999"],["/zkrq","http://*************:9999"],["/images","http://*************"],["/admin","http://*************:9999"], ["/auth","http://*************:9999"],["/model","http://*************:9999"],["/workflow","http://*************:9999"],["/msg","http://*************:9999"],["/eq","http://*************:9999"],["/yjpt","http://*************:9999"],["/schedule","http://*************:9999"],["/jd","http://*************:9999"],["/cz","http://*************:9999"],["/pm","http://*************:9999"],["/waterbalance","http://*************:9999"],["/portal", "http://*************:9999"],["/data-sence", "http://*************:9999"],["/data", "http://*************:9999"],["/api/admin","http://*************:15000"],["/assay","http://*************:9999"],["/event-center","http://*************:9999"], ["/factory","http://*************:9999"],["/factory1","http://*************:9999"],["/diagnosis","http://*************:9999"],["/process-configuration","http://*************:9999"],["/inspect","http://*************:9999"], ["/camera","http://*************:9999"],["/HlxbZhczBc/api","http://************/basic-api"],["/schedule","http://*************:9999"],["/workorder","http://*************:9999"],["/safe","http://*************:9999"],["/hloa", "http://*************:9999"]] #// 
# VITE_PROXY = [["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/eq","http://************:9999"],["/schedule","http://************:9999"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"],["/assay","http://************:9999"],["/event-center","http://************:9999"], ["/factory","http://************:9999"], ["/process-configuration","http://************:9999"],["/inspect","http://************:9999"], ["/camera","http://************:9999"],["/HlxbZhczBc/api","http://************/basic-api"],["/schedule","http://************:9999"],["/waterbalance","http://*************:9999"]] #// 
# VITE_PROXY = [["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/eq","http://************:9999"],["/schedule","http://************:9999"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"],["/assay","http://************:9999"],["/event-center","http://************:9999"], ["/factory","http://************:9999"], ["/process-configuration","http://************:9999"],["/inspect","http://************:9999"], ["/camera","http://************:9999" ]] #// 
# VITE_PROXY = [["/yjpt","http://************:9999"],["/pm","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://************:9999"],["/api/v1/SIndustryInspect","http://************:15029"],["/api/v1/SIndustryWarehouse","http://************:15025"],['/api/fs','http://************:15005'],['/fs','http://************'],['/api/admin','http://************:15000'],['/api/v1/SIndustryCamera','http://************:15030'],['/api/GroupRelationShip','http://************:15019'],['/industryflowapi','http://*************:14444']] #// 开发
# VITE_PROXY = [["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"],["/assay","http://************:9999"],["/event-center","http://************:9999"], ["/factory","http://************:9999"], ["/process-configuration","http://************:9999"],["/diagnosis","http://************:9999"]] #// 开发
# VITE_PROXY = [["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"],["/assay","http://************:9999"],["/event-center","http://************:9999"], ["/factory","http://************:9999"], ["/process-configuration","http://************:9999"],["/diagnosis","http://************:9999"]] #// 开发
# VITE_PROXY = [["/yjpt","http://************:9999"],["/pm","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://************:9999"],["/api/v1/SIndustryInspect","http://************:15029"],["/api/v1/SIndustryWarehouse","http://************:15025"],['/api/fs','http://************:15005'],['/fs','http://************'],['/api/admin','http://************:15000'],['/api/v1/SIndustryCamera','http://************:15030'],['/api/GroupRelationShip','http://************:15019'],['/industryflowapi','http://*************:14444']] #// 开发
# VITE_PROXY = [["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/api/v1/SIndustryEquipment","http://************:15027"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"],["/assay","http://************:9999"],["/event-center","http://************:9999"], ["/factory","http://************:9999"], ["/process-configuration","http://************:9999"],["/waterbalance","http://************:9999"]] 
VITE_PROXY = [["/office","http://************:9999"],["/zkrq","http://************:9999"],["/images","http://************"],["/admin","http://************:9999"], ["/auth","http://************:9999"],["/model","http://************:9999"],["/workflow","http://************:9999"],["/msg","http://************:9999"],["/eq","http://************:9999"],["/yjpt","http://************:9999"],["/schedule","http://************:9999"],["/jd","http://************:9999"],["/cz","http://************:9999"],["/pm","http://************:9999"],["/waterbalance","http://************:9999"],["/portal", "http://************:9999"],["/data-sence", "http://************:9999"],["/data", "http://************:9999"],["/api/admin","http://************:15000"],["/assay","http://************:9999"],["/event-center","http://************:9999"], ["/factory","http://************:9999"],["/factory1","http://************:9999"],["/diagnosis","http://************:9999"],["/process-configuration","http://************:9999"],["/inspect","http://************:9999"], ["/camera","http://************:9999"],["/HlxbZhczBc/api","http://************/basic-api"],["/schedule","http://************:9999"],["/workorder","http://************:9999"],["/safe","http://************:9999"],["/hloa", "http://************:9999"],["/hlxb", "http://************:9999"]] #// 
# // 开发 /images
# http://*************/
# VITE_PROXY = [["/basic-api","http://**************:3000"]]
# VITE_PROXY=[["/api","https://vvbin.cn/test"]]
# ["/basic-api","http://localhost:3000"]
# Delete console
VITE_DROP_CONSOLE = false

# Basic interface address SPA 
VITE_GLOB_API_URL=

# 是否单体服务模式
VITE_GLOB_MONOCASE = true

# 单体服务名称
VITE_GLOB_MONOCASE_NAME = '/hlxb'

# 工艺流程编辑器服务
VITE_GLOB_EDITOR_API_URL = 'http://*************:14444'

# 厂站摄像头服务
VITE_GLOB_CAMERA_API_URL = 'http://************:15030'

# 天气服务
VITE_GLOB_WEATHER_API_URL = https://devapi.qweather.com

# 工艺流程上传文件地址
VITE_GLOB_UPLOAD_EQUIPMENT_FIRE_PATH = /data-sence/resourceIndex/uploadFile

# 工艺流程文件地址
VITE_GLOB_EQUIPMENT_FIRE_PATH = /industryflowapi/File/GetFile/

# 地理位置服务
VITE_GLOB_GEO_API_URL = https://geoapi.qweather.com/v2/city/lookup

# File upload address， optional
VITE_GLOB_UPLOAD_URL=

# Interface prefix
VITE_GLOB_API_URL_PREFIX=

# 开发环境使用的域名
# VITE_DEV_DOMAIN = 'http://************'
# VITE_DEV_DOMAIN = 'http://************'
# VITE_DEV_DOMAIN = 'http://************'
# VITE_DEV_DOMAIN = 'http://************'
# VITE_DEV_DOMAIN = 'http://************'
# VITE_DEV_DOMAIN = 'http://************'

# VITE_DEV_DOMAIN = 'http://*************'
# VITE_DEV_DOMAIN = 'http://************'
# VITE_DEV_DOMAIN = 'http://************'
# VITE_DEV_DOMAIN = 'http://************'
# VITE_DEV_DOMAIN = 'http://************'
# VITE_DEV_DOMAIN = 'http://************'
VITE_DEV_DOMAIN = 'http://************'

# 项目启动与打包的项目
VITE_GLOB_PROJECT = 'zhcz,zoology-wzb'
# VITE_GLOB_PROJECT = 'gis,metering'
# VITE_GLOB_PROJECT = 'jd,metering,hlxb-zhoukougas'
# VITE_GLOB_PROJECT = 'door'
# VITE_GLOB_PROJECT = 'yjpt'
# VITE_GLOB_PROJECT = 'aoa,zhcz'
# VITE_GLOB_PROJECT = 'water-balance' 
# VITE_GLOB_PROJECT = 'equipment-maintenance' 
