<!-- 中心预警图 -->
<template>
  <div class="center-image">
    <div class="title-section">
      <h1 class="main-title">朱坳水厂- 原水2-MIB监测预警大师</h1>
    </div>
    <div class="center-image__container">
      <!-- 左上指标 -->
      <div class="indicator indicator--top-left" @click="openDetail('chloramine')">
        <div class="indicator__label"
          >游离氯<img src="../assets/images/goto.png" class="indicator__img" alt="" srcset=""
        /></div>
        <div class="indicator__value">{{ centerData.chloramine || '--' }}</div>
      </div>

      <!-- 右上指标 -->
      <div class="indicator indicator--top-right" @click="openDetail('temperature')">
        <div class="indicator__label"
          >温度<img src="../assets/images/goto.png" class="indicator__img" alt="" srcset=""
        /></div>
        <div class="indicator__value">{{ centerData.temperature || '--' }}°C</div>
      </div>

      <!-- 左侧指标 -->
      <div class="indicator indicator--left" @click="openDetail('turbidity')">
        <div class="indicator__label"
          >浊度<img src="../assets/images/goto.png" class="indicator__img" alt="" srcset=""
        /></div>
        <div class="indicator__value">{{ centerData.turbidity || '--' }}</div>
      </div>

      <!-- 右侧指标 -->
      <div class="indicator indicator--right" @click="openDetail('ph')">
        <div class="indicator__label"
          >ph<img src="../assets/images/goto.png" class="indicator__img" alt="" srcset=""
        /></div>
        <div class="indicator__value">{{ centerData.ph || '--' }}</div>
      </div>

      <!-- 左下指标 -->
      <div class="indicator indicator--bottom-left" @click="openDetail('chlorophyll')">
        <div class="indicator__label"
          >叶绿体<img src="../assets/images/goto.png" class="indicator__img" alt="" srcset=""
        /></div>
        <div class="indicator__value">{{ centerData.chlorophyll || '--' }}</div>
      </div>

      <!-- 中央主要指标 -->
      <div class="center-main" :style="{ backgroundColor: circleBox[centerData.level].bgColor }">
        <div
          class="center-main__lid-img"
          :style="{ bottom: `${centerData.mibConcentration * 0.01 * 404 - 96}px` }"
        >
          <img :src="circleBox[centerData.level].lid" alt="" srcset="" />
        </div>
        <div
          class="center-main__bg"
          :style="{
            backgroundImage: `url(${circleBox[centerData.level].bottle})`,
            height: `${
              centerData.mibConcentration ? centerData.mibConcentration * 0.01 * 404 - 94 : 0
            }px`,
          }"
        >
        </div>
        <div class="center-main__content">
          <div class="center-main__warning">{{ getAlert(centerData.level) || '--' }}</div>
          <div class="center-main__value">{{ centerData.mibConcentration || '--' }}</div>
          <div class="center-main__label" @click="openDetail('2-MIB')">
            <span>2-MIB</span>
            <img
              src="../assets/images/center-img/center-goto.png"
              style="width: 40px"
              alt=""
              srcset=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  // 中心预警图组件，数据从props或API获取
  import { ref, watch, onMounted } from 'vue';
  import { brominePredictions } from '/@zhcz/api/mib-early-warning';
  import dayjs from 'dayjs';

  import centerBottleGreen from '../assets/images/center-img/center-bottle-green.png';
  import centerBottleYellow from '../assets/images/center-img/center-bottle-yellow.png';
  import centerBottleRed from '../assets/images/center-img/center-bottle-red.png';
  import centerBottleOrange from '../assets/images/center-img/center-bottle-orange.png';

  import centerLidGreen from '../assets/images/center-img/center-lid-green.png';
  import centerLidYellow from '../assets/images/center-img/center-lid-yellow.png';
  import centerLidRed from '../assets/images/center-img/center-lid-red.png';
  import centerLidOrange from '../assets/images/center-img/center-lid-orange.png';

  const centerData = ref({
    warningLevel: '二级预警',
    level: 0, // 0: 绿色, 1: 黄色, 2: 红色, 3: 橙色
    // 监测值
    mibConcentration: 67.2,
    chloramine: 2.4,
    temperature: '31',
    turbidity: 85,
    ph: 7.4,
    chlorophyll: 7.5,
  });
  const circleBox = [
    { bottle: centerBottleGreen, lid: centerLidGreen, bgColor: '#EEFFF7' },
    { bottle: centerBottleRed, lid: centerLidRed, bgColor: '#FFECEC' },
    { bottle: centerBottleOrange, lid: centerLidOrange, bgColor: '#FFF5ED' },
    { bottle: centerBottleYellow, lid: centerLidYellow, bgColor: '#FFFBDF' },
  ];
  const openDetail = (type: string) => {
    // 打开详情页面或执行其他操作
    console.log(`打开 ${type} 详情`);
    // 这里可以使用路由跳转或其他方式打开详情
  };
  const getAlert = (type: number) => {
    if (type === 0) return '暂无预警';
    if (type === 1) return '一级预警';
    if (type === 2) return '二级预警';
    if (type === 3) return '三级预警';
  };
  const getData = async () => {
    const res = await brominePredictions({
      date: dayjs().format('YYYY-MM-DD'),
      deviceId: '002',
    });
    console.log('center-image res', res);
    if (res) {
      centerData.value = Object.assign(centerData.value, res);
    }
  };
  onMounted(() => {
    getData();
  });
  const props = defineProps({
    updata: {
      type: Number,
      default: 1,
    },
  });
  watch(
    () => props.updata,
    (newVal, oldVal) => {
      if (newVal !== oldVal) {
        getData();
      }
    },
  );
</script>

<style lang="less" scoped>
  .center-image {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 0 47px 0;

    .main-title {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 40px;
      color: #000000;
      line-height: 40px;
    }

    .center-image__container {
      position: relative;
      width: 800px;
      height: 500px;

      .indicator__img {
        width: 8px;
        height: 14px;
      }
    }

    .indicator {
      position: absolute;
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      backdrop-filter: blur(10px);
      width: 126px;
      height: 126px;
      background: #f5f9fe;
      box-shadow: inset -4px 4px 8px 0px #d5e5ee, inset 4px -4px 8px 0px #ffffff;
      border: 2px solid #ffffff;
      cursor: pointer;

      &--top-left {
        top: 3%;
        right: 20%;
      }

      &--top-right {
        top: 30%;
        right: 2%;
      }

      &--left {
        top: 30%;
        left: 5%;
        transform: translateY(-50%);
      }

      &--right {
        top: 76%;
        right: 0;
        transform: translateY(-50%);
      }

      &--bottom-left {
        bottom: 10%;
        left: 3%;
      }

      .indicator__label {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #000000;
        line-height: 24px;
        display: flex;
        align-items: center;
        gap: 0 8px;
      }

      .indicator__value {
        margin-top: 12px;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 40px;
        color: #000000;
        line-height: 40px;
      }
    }

    .center-main {
      position: absolute;
      top: 60%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      justify-content: center;
      align-items: center;
      width: 404px;
      height: 404px;
      border-radius: 50%;
      border: 2px solid #fff;
      overflow: hidden;
      cursor: pointer;

      .center-main__lid-img {
        position: absolute;
        // top: 0;
        left: 0;
        width: 100%;
        z-index: 1;
      }

      .center-main__bg {
        position: absolute;
        width: 100%;
        // height: 100%;
        bottom: 0;
        // border-radius: 50%;
        // border: 2px solid #fff;
      }

      .center-main__content {
        position: relative;
        z-index: 2;
        text-align: center;
        // color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 36px 0;

        .center-main__warning {
          font-family: PingFang SC;
          // font-weight: 600;
          font-size: 40px;
          color: #000000;
          line-height: 40px;
          // text-stroke: 2px #ffffff;
          text-align: left;
          font-style: normal;
          text-transform: none;
          // -webkit-text-stroke: 2px #ffffff;
        }

        .center-main__value {
          font-family: PingFang SC;
          font-weight: 600;
          font-size: 88px;
          color: #000000;
          line-height: 88px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          -webkit-text-stroke: 2px #ffffff;
        }

        .center-main__label {
          display: flex;
          align-items: center;
          gap: 0 4px;
          font-family: PingFang SC;
          font-weight: 600;
          height: 40px;
          font-size: 40px;
          line-height: 40px;
          color: #000000;
          text-align: left;
          font-style: normal;
          text-transform: none;
          -webkit-text-stroke: 2px #ffffff;
        }
      }
    }
  }

  // @media (max-width: 1200px) {
  //   .center-image {
  //     padding: 20px 0;

  //     .center-image__container {
  //       width: 400px;
  //       height: 350px;
  //     }

  //     .indicator {
  //       width: 80px;
  //       height: 80px;

  //       &--top-left {
  //         top: 10px;
  //         left: 20px;
  //       }

  //       &--top-right {
  //         top: 10px;
  //         right: 20px;
  //       }

  //       &--left {
  //         left: -10px;
  //       }

  //       &--right {
  //         right: -10px;
  //       }

  //       &--bottom-left {
  //         bottom: 10px;
  //         left: 20px;
  //       }

  //       .indicator__label {
  //         font-size: 12px;
  //         margin-bottom: 4px;
  //       }

  //       .indicator__value {
  //         font-size: 18px;
  //       }
  //     }

  //     .center-main {
  //       width: 200px;
  //       height: 200px;

  //       .center-main__content {
  //         .center-main__warning {
  //           font-size: 14px;
  //           margin-bottom: 4px;
  //         }

  //         .center-main__value {
  //           font-size: 32px;
  //           margin-bottom: 2px;
  //         }

  //         .center-main__label {
  //           font-size: 16px;
  //         }
  //       }
  //     }
  //   }
  // }
</style>
