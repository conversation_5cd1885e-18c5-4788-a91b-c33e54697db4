<template>
  <!-- 头部详情数据和大图 -->
  <div class="img-box">
    <!-- 头部详情数据 -->
    <div class="header">
      <div class="item">
        <span class="label">设备名称：</span>
        <span class="value">{{ detailInfo?.equipmentName }}</span>
      </div>
      <div class="item">
        <span class="label">设备标识：</span>
        <span class="value">{{ detailInfo?.equipmentExternalId }}</span>
      </div>
      <div class="item">
        <span class="label">设备类型：</span>
        <span class="value">{{ detailInfo?.equipmentTypeName }}</span>
      </div>
      <div class="item">
        <span class="label">设备型号：</span>
        <span class="value">{{ detailInfo?.equipmentModelName }}</span>
      </div>
      <div class="item">
        <span class="label">使用单位：</span>
        <span class="value">{{ detailInfo?.usingOrganizationName }}</span>
      </div>
    </div>
    <!-- 图片 -->
    <div
      class="flex-1 flex items-center justify-center relative"
      style="overflow: hidden; background: #fff"
    >
      <img
        v-if="mainImg?.[0]?.sourceUniqueKey"
        class="img"
        :src="getEevReturnDomain(mainImg?.[0]?.sourceUniqueKey)"
      />
      <div v-else class="img images-empty">
        <Icon icon="icon-park-outline:pic" :size="100" color="#999" />
      </div>
      <WarningLayer :list="warningList" @itemClick="toWarningPage" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { Icon } from '/@/components/Icon';
  import WarningLayer from '../components/WarningLayer.vue';
  import { ref, watch } from 'vue';
  import { searchEqarApi } from '/@zoology-wzb/api/device';
  import { getGroupImageApi } from '/@zhcz/api/scenes-group';
  import { getEevReturnDomain } from '/@/utils/file/url';
  import { eventJumpAddress } from '../../../hooks';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    detailInfo: {
      type: Object,
      default: () => {},
    },
  });
  watch(
    () => props.detailInfo,
    () => {
      initData();
    },
  );

  const router = useRouter();
  const toWarningPage = (v) => {
    router.push({
      path: eventJumpAddress.value,
      query: { detailParam: JSON.stringify({ id: v.id }) },
    });
  };
  /* 获取大图 */
  const mainImg = ref([]);
  const getMainImg = async () => {
    mainImg.value = await getGroupImageApi({ groupCode: props.detailInfo.equipmentTypeCode });
  };
  /* 获取报警列表 */
  const warningList = ref([]);
  const getEqarApi = () => {
    searchEqarApi(props.detailInfo.id).then((res) => {
      warningList.value = res;
    });
  };

  const initData = () => {
    getMainImg();
    getEqarApi();
  };
</script>
<style lang="less" scoped>
  .img-box {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-right: 12px;

    .header {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      margin-bottom: 12px;
      background-color: #fff;
      border-radius: 4px;

      .item {
        margin-right: 44px;
        white-space: nowrap;

        .label {
          color: #999999;
          white-space: nowrap;
        }

        .value {
          color: #333333;
          // flex: 1;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }
    }

    .img {
      height: 100%;
      width: 80%;
      border-radius: 4px;

      &.images-empty {
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f2f3f5;
      }
    }
  }
</style>
