<template>
  <Card :bordered="false" dis-hover title="设备完好率">
    <template #extra>
      <div class="extra-wrap">
        <Select
          placeholder="请选择"
          @change="handleOneChange"
          v-model:value="formData.argOne"
          class="mr-16px"
          :dropdownMatchSelectWidth="false"
        >
          <SelectOption v-for="item in optionsOne" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
        <DatePicker
          v-model:value="formData.dayDate"
          :picker="picker"
          :valueFormat="valueFormat"
          :allowClear="false"
          class="!w-[auto]"
          placeholder="请选择"
          :disabledDate="disabledDate"
          @change="handleChangeDayDate"
        />
      </div>
    </template>
    <div
      class="h-full grid gird-cols-3 grid-rows-[56px_1fr] gap-x-17px gap-y-12px"
      v-show="!isEmpty"
    >
      <div v-for="item in list" :key="item.name" class="item"
        ><span>{{ item.name || '' }}</span
        ><span class="value">{{ item.value ?? '' }}%</span></div
      >
      <div class="col-span-3" ref="chartRef" style="height: 100%"></div>
    </div>

    <div v-show="isEmpty" class="empty-box">
      <HEmpty />
    </div>
  </Card>
</template>

<script setup>
  import { Card, Select, SelectOption, DatePicker } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { ref, watch, reactive, onMounted, nextTick, computed, onBeforeMount } from 'vue';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { roundAndConvert } from '/@zhcz/utils/number';
  import { getEqpIntactApi } from '/@zhcz/api/device-management';
  import { getParamKeyApi } from '/@/api/admin/param';
  import { DICT } from '/@zhcz/enums/sceneResource';

  const isSceneApi = ref(true);

  const timeTypeMap = {
    0: {
      type: 'date',
      format: 'YYYY-MM-DD',
    },
    1: {
      type: 'month',
      format: 'YYYY-MM',
    },
    2: {
      type: 'year',
      format: 'YYYY',
    },
  };

  // 根据返回的后缀确定，eg: xxxx_month
  const picker = computed(() => {
    let type = isSceneApi.value
      ? formData.argOne && formData.argOne.split('_')[1]
      : timeTypeMap[Number(formData.argOne)].type;
    return type ?? 'month';
  });
  const valueFormat = computed(() => {
    let map = {
      month: 'YYYY-MM',
      day: 'YYYY-MM-DD',
      year: 'YYYY',
    };
    return map[picker.value] || map.month;
  });
  const xAxisFormat = computed(() => {
    let map = {
      month: 'DD日',
      day: 'YYYY-MM-DD',
      year: 'MM月',
    };
    return map[picker.value] || map.month;
  });

  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }

  let formData = reactive({
    dayDate: dayjs().format('YYYY-MM'),
    argOne: '1',
    argTwo: null,
  });

  let optionsOne = ref([
    { label: '月', value: '1' },
    { label: '年', value: '2' },
  ]);
  const chartRef = ref(null);

  const props = defineProps({
    sourceData: { type: Array, default: () => [] },
  });

  const getIsSceneApi = async () => {
    const data = await getParamKeyApi(DICT.IS_SCENE_API);
    isSceneApi.value = data === '1';
  };

  onBeforeMount(() => {
    getIsSceneApi();
  });

  // 更新选项一
  function updateOptions(newValue) {
    if (newValue.length < 1) return;
    if (isSceneApi.value) {
      let res = newValue.find((i) => i.groupCode === 'sbzl_sbwhl')?.children || [];
      if (res.length < 1) return;
      optionsOne.value = res.map(({ groupCode, name }) => ({
        value: groupCode,
        label: name,
      }));
      formData.argOne = res[0].groupCode;
    }
    getData();
  }

  let getParams = () => {
    let paramsData = {
      startDateTime: dayjs(formData.dayDate).startOf(picker.value).format('YYYY-MM-DD 00:00:00'),
      endDateTime: dayjs(formData.dayDate).endOf(picker.value).format('YYYY-MM-DD 23:59:59'),
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    let params = {
      resourceInterfaceId: '3',
      groupCode: formData.argOne,
      paramsData: JSON.stringify(paramsData),
    };
    return params;
  };

  const getApiParams = () => {
    return {
      type: Number(formData.argOne),
      time: formData.dayDate,
    };
  };

  async function getData() {
    let params = getParams();
    const apiParams = getApiParams();
    const res = isSceneApi.value
      ? await callResourceFunction(params)
      : await getEqpIntactApi(apiParams);
    const data = isSceneApi.value ? res.data : res;
    renderChart(data);
  }

  let isEmpty = ref(true);
  let list = ref([[], [], []]);
  function renderChart(data) {
    if (!data?.length) {
      isEmpty.value = true;
      return;
    }
    for (let item of data) {
      if (!item.data?.length) {
        isEmpty.value = true;
        return;
      }
    }
    isEmpty.value = false;
    list.value = data.map((i) => {
      let value = i.data.at(-1) ? i.data.at(-1).value : 0;
      return {
        name: i.indexName,
        value: value ? roundAndConvert(value, 0) : 0,
      };
    });
    let color = ['#22CD80', '#2E8CFF', '#FF8C2E'];
    let areaColor = [
      ['rgba(34, 205, 128, 0.16)', 'rgba(34, 205, 128, 0)'],
      ['rgba(46, 123, 255, 0.16)', 'rgba(46, 123, 255, 0)'],
      ['rgba(255, 140, 46, 0.16)', 'rgba(255, 140, 46, 0)'],
    ];
    const option = {
      animation: false,
      color,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(255, 255, 255, 1)',
        textStyle: {
          color: '#999',
          fontSize: 14,
          lineHeight: 28,
          height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
        formatter: (params) => {
          let str = `<div style='display: grid; grid-template-columns: 15px 1fr auto;'>`;
          params.forEach((item) => {
            str += `
                        <span>${item.marker}</span>
                        <span style='color: #333;'>${item.seriesName}</span>
                        <div style='color: #333; margin-left: 15px; font-weight: 600; justify-self: end;'>
                           <span>${item.value ?? '-'}</span><span>${'%'}</span>
                        </div>`;
          });
          str = `${str}</div>`;
          return str;
        },
      },
      legend: {
        icon: 'circle',
        itemWidth: 8,
        itemHeight: 8,
        itemGap: 24,
        textStyle: {
          fontSize: 14,
          color: '#333333',
        },
      },
      grid: {
        top: '15%',
        left: 0,
        right: 20,
        bottom: 0,
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: data[0].data.map((i) => i.collectDateTime),
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#666',
              fontSize: 14,
            },
            formatter(value) {
              return dayjs(value).format(xAxisFormat.value);
            },
          },
          axisPointer: {
            type: 'shadow',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: `单位（%）`,
          nameTextStyle: {
            color: '#666',
            fontSize: 14,
            align: 'left',
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            align: 'right',
            textStyle: {
              color: '#666',
              fontSize: 14,
            },
          },
        },
      ],
      series: data.map((i, index) => {
        return {
          name: i.indexName,
          type: 'line',
          symbol: 'none',
          barWidth: 12,
          color: color[index % 3],
          data: i.data.map((i) => (i.value == null ? null : roundAndConvert(Number(i.value), 0))),
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: areaColor[index % 3][0] },
              { offset: 1, color: areaColor[index % 3][1] },
            ]),
          },
        };
      }),
    };
    setOptions(option);
  }

  watch(() => props.sourceData, updateOptions, { immediate: true });

  function handleOneChange() {
    nextTick(() => {
      formData.dayDate = dayjs().format(valueFormat.value);
      getData();
    });
  }
  function handleChangeDayDate() {
    getData();
  }
  let setOptions;
  let echarts;
  onMounted(() => {
    ({ setOptions, echarts } = useECharts(chartRef));
  });
</script>

<style lang="less" scoped>
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(11, 98, 203, 0.08);
    border-radius: 4px 4px 4px 4px;
    padding: 0 16px;

    .value {
      font-size: 16px;
      font-weight: 600;
    }
  }
</style>
