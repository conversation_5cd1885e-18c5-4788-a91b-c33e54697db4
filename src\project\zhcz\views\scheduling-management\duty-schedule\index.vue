<!-- 值班排班 -->
<template>
  <div class="page-container">
    <!-- <div class="search-form">
      <BasicForm @register="registerForm" />
    </div> -->
    <div class="month_l">
      <MonthLeft
        v-if="activeType === 'month'"
        :calendar-value="calendarValue"
        :schedule-data="scheduleData"
        :planType="1"
        :updataFlag="updataFlag"
      />
    </div>
    <div class="content-container">
      <div class="oper-container relative">
        <div class="left_button">
          <a-button
            v-show="!isCanEdit"
            type="primary"
            :icon="h(Icon, { icon: 'icon-park-outline:edit-two' })"
            @click="handleChangeEdit(true)"
          >
            调整排班
          </a-button>
          <a-button
            v-show="isCanEdit"
            :icon="h(Icon, { icon: 'icon-park-outline:return' })"
            type="primary"
            ghost
            @click="handleChangeEdit(false)"
            >返回</a-button
          >
          <Button
            :loading="exportLoading"
            @click="handleExport"
            style="margin-left: 16px"
            :icon="h(Icon, { icon: 'icon-park-outline:upload' })"
            >导出</Button
          >
          <Tooltip placement="right">
            <template #title>仅调整今天及往后排班，例如可调整后天排班。</template>
            <span style="padding: 0 4px 0 12px">说明</span>
            <QuestionCircleFilled style="color: #d8d8d8" />
          </Tooltip>
        </div>
        <div class="title-container">
          <MonthPicker
            style="width: 258px !important; height: 32px"
            v-model:value="calendarValue"
            :format="'YYYY-MM'"
            :allow-clear="false"
            @change="getPlantData()"
          />
          <div class="button-box">
            <a-button
              :icon="h(Icon, { icon: 'ant-design:left-outlined' })"
              @click="handleChangeMonth(-1)"
            />
            <div class="title" @click="goToday">{{ '回到今天' }}</div>
            <a-button
              :icon="h(Icon, { icon: 'ant-design:right-outlined' })"
              @click="handleChangeMonth(1)"
            />
          </div>
        </div>
        <!-- <div class="title-container">
          <a-button
            :icon="h(Icon, { icon: 'ant-design:left-outlined' })"
            @click="handleChangeMonth(-1)"
          />
          <span class="title">{{ calendarTitle }}</span>
          <a-button
            :icon="h(Icon, { icon: 'ant-design:right-outlined' })"
            @click="handleChangeMonth(1)"
          />
        </div> -->
        <!-- <div class="type-container">
          <div
            v-for="item in typeData"
            :key="item.value"
            :class="['type-item', { active: activeType === item.value }]"
            @click="handleTypeClick(item.value)"
            >{{ item.label }}</div
          >
        </div> -->
      </div>
      <DayCalendar
        v-if="activeType === 'day'"
        :calendar-value="calendarValue"
        :schedule-data="scheduleData"
        :isCanEdit="isCanEdit"
      />
      <WeekCalendar
        v-else-if="activeType === 'week'"
        :calendar-value="calendarValue"
        :schedule-data="scheduleData"
      />
      <MonthCalendar
        v-else
        :calendar-value="calendarValue"
        :schedule-data="scheduleData"
        :isCanEdit="isCanEdit"
        :type="1"
        :dataNextDay="dataNextDay"
        @handleChangeDate="handleChangeDate"
      />
    </div>
  </div>
</template>
<script lang="ts">
  export default { name: 'SchedulingManagementDutySchedule' };
</script>
<script lang="ts" setup name="SchedulingManagementDutySchedule">
  import { ref, provide, h } from 'vue';
  import { Icon } from '/@/components/Icon';
  import dayjs from 'dayjs';
  import updateLocale from 'dayjs/plugin/updateLocale';
  import weekOfYear from 'dayjs/plugin/weekOfYear';
  import { Tooltip, Button, MonthPicker } from 'ant-design-vue';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import { getPlantByDate, getGainPlanDay, exportScheduleTime } from '/@zhcz/api/scheduling';
  import { downLoadFile } from '/@/utils/file/download';
  import DayCalendar from '../components/DayCalendar/index.vue';
  import WeekCalendar from '../components/WeekCalendar/index.vue';
  import MonthCalendar from '../components/MonthCalendar/index.vue';
  import MonthLeft from '../components/MonthCalendar/MonthLeft.vue';
  // import * as XLSX from 'xlsx';
  // function showTooltip(e) {
  //   if (e.target.clientWidth >= e.target.scrollWidth) {
  //     e.target.style.pointerEvents = 'none';
  //   }
  // }
  dayjs.extend(updateLocale);
  dayjs.extend(weekOfYear);

  dayjs.updateLocale('en', {
    weekdays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
  });

  // const typeData = ref([
  //   {
  //     label: '日',
  //     value: 'day',
  //   },
  //   // {
  //   //   label: '周',
  //   //   value: 'week',
  //   // },
  //   {
  //     label: '月',
  //     value: 'month',
  //   },
  // ]);
  const calendarTitle = ref(dayjs().format('YYYY年MM月'));
  const activeType = ref<any>('month');
  const calendarValue = ref(dayjs());
  const scheduleData = ref({});
  const isCanEdit = ref(false);
  const dataNextDay = ref('');
  const updataFlag = ref(false);
  const handleChangeDate = (date) => {
    // if (date.format('YYYY-MM') === calendarValue.value.format('YYYY-MM')) {
    //   return;
    // }
    if (date) {
      calendarValue.value = dayjs(date);
      handleTypeClick('month');
      updataFlag.value = !updataFlag.value;
      getPlantData();
    }
  };

  const handleChangeEdit = (flag) => {
    isCanEdit.value = flag;
  };

  const handleTypeClick = (type) => {
    if (type === 'day') {
      calendarTitle.value = calendarValue.value.format('YYYY年MM月DD日 dd');
    } else if (type === 'week') {
      calendarTitle.value = calendarValue.value.format(
        `YYYY年MM月 第${calendarValue.value.week()}周`,
      );
    } else if (type === 'month') {
      calendarTitle.value = dayjs(calendarValue.value).format('YYYY年MM月');
    }
    activeType.value = type;
  };

  const handleChangeMonth = (num) => {
    calendarValue.value = calendarValue.value.add(num, activeType.value);
    handleTypeClick(activeType.value);
    getPlantData();
  };

  const getNextPlanDay = async () => {
    const params = { type: 1 };
    dataNextDay.value = await getGainPlanDay(params);
  };
  getNextPlanDay();

  const getPlantData = async () => {
    const startTime = dayjs(calendarValue.value).startOf('month').format('YYYY-MM-DD');
    const endTime = dayjs(calendarValue.value).endOf('month').format('YYYY-MM-DD');
    const params = {
      type: 1,
      startTime,
      endTime,
    };

    const data = await getPlantByDate(params);
    if (!data || !data.scheduleDayInfoList) {
      scheduleData.value = {};
      return;
    }
    data.type = 1;
    data.scheduleDayInfoList.forEach((item) => {
      item.schedulePersonInfoList.forEach((detail) => {
        detail.scheduleMemberList = detail.scheduleMemberList
          ? detail.scheduleMemberList.filter((person) => person.memberName !== '')
          : [];
      });
    });
    scheduleData.value = data;
  };
  const exportLoading = ref(false);
  const handleExport = async () => {
    exportLoading.value = true;
    try {
      const startTime = dayjs(calendarValue.value).startOf('month').format('YYYY-MM-DD');
      const endTime = dayjs(calendarValue.value).endOf('month').format('YYYY-MM-DD');
      const params = {
        type: 1,
        startTime,
        endTime,
      };

      const ret = await exportScheduleTime(params);
      downLoadFile(ret, `值班排班.xlsx`);
      exportLoading.value = false;
    } finally {
      exportLoading.value = false;
    }
  };
  const goToday = () => {
    calendarValue.value = dayjs();
    handleTypeClick(activeType.value);
    getPlantData();
  };
  getPlantData();
  provide('getPlantData', getPlantData);
</script>

<style scoped lang="less">
  .page-container {
    margin: 0 16px 16px;
    height: 100%;
    overflow: auto;
    display: flex;
    border-radius: 4px;
    background-color: #ffffff;

    .month_l {
      width: 248px;
      height: 100%;
      overflow: hidden;
    }

    .search-form {
      padding: 0 16px;
      height: 64px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      background-color: #ffffff;

      :deep(.ant-form) {
        width: 100%;

        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }

    .form-container {
      padding: 12px 10px 6px;
      border-radius: 4px;
      margin-bottom: 8px;
      background-color: #ffffff;

      .ant-form-item {
        margin-bottom: 8px;
      }
    }

    .content-container {
      flex: 1;
      padding: 16px;
      background: #ffffff;
      overflow-y: hidden;
      overflow-x: auto;
      display: flex;
      flex-direction: column;

      .oper-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .left_button {
          display: flex;
          align-items: center;
        }

        .title-container {
          display: flex;
          // margin: auto;
          .button-box {
            margin-left: 16px;
            display: flex;
            align-items: center;

            .title {
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 32px;
              line-height: 32px;
              font-size: 14px;
              // padding: 0 12px;
              border-top: 1px solid #d9d9d9;
              border-bottom: 1px solid #d9d9d9;
              font-family: PingFang SC-Semibold, PingFang SC;
              // font-weight: 600;
              color: #333333;
              width: 78px;
            }
          }

          .ant-btn:first-child {
            width: 40px;
            height: 32px;
            padding: 0;
            color: #999999;
            border-radius: 4px 0px 0px 4px;
            border: 1px solid #d9d9d9;

            &:hover {
              background: #f8f8f8;
              color: #333;
              border: 1px solid #e9e9e9;
            }
          }

          .ant-btn:last-child {
            width: 40px;
            height: 32px;
            padding: 0;
            color: #999999;
            border-radius: 0 4px 4px 0px;
            border: 1px solid #d9d9d9;

            &:hover {
              background: #f8f8f8;
              color: #333;
              border: 1px solid #e9e9e9;
            }
          }
        }

        .type-container {
          display: flex;
          cursor: pointer;
          background: rgba(43, 99, 161, 0.05);
          padding: 4px;

          .type-item {
            flex: 1;
            padding: 1px 18px;
            border-radius: 2px;
          }

          .active {
            background: @theme-color;
            color: white;
          }
        }
      }

      .calendar-container {
        flex: 1;
        overflow: hidden;
      }
    }
  }
</style>
