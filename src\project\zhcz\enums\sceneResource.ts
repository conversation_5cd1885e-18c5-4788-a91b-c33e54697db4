export enum DICT {
  INDEX_SOURCE_TYPE = 'index_source_type', // 接口类型
  TOKEN_TYPE_DICT = 'token_type', // token类型
  TOKEN_LOCATION = 'token_location', // token位置
  RESOURCE_TYPE = 'resource_type', // 资源类型
  DATABASE_TYPE = 'database_type', // 数据库类型
  DATA_SENCE_GROUP_PURPOSE = 'data_sence_group_purpose', //分组用途
  IS_SCENE_API = 'IS_SCENE_API', //是否走场景分组
}

export enum TOKEN_TYPE {
  NONE = 1,
  STATIC = 2,
  DYNAMIC = 3,
}

export enum RESOURCE_TYPE {
  API = 0,
  DATABASE = 1,
}
