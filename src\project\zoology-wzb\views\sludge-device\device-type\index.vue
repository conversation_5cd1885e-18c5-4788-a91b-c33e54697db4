<template>
  <div :class="prefixCls" class="h-full">
    <BasicTable @register="registerList" @selection-change="handleChangeSelection">
      <template #tableTitle>
        <div>
          <a-button
            :icon="h(Icon, { icon: 'icon-park-outline:plus' })"
            @click="handleEdit(null)"
            type="primary"
            >新增</a-button
          >
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '编辑',
                onClick: handleEdit.bind(null, record.id),
              },

              {
                label: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record.id),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <EditModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import EditModal from './components/EditModal/EditModal.vue';
  import { ref, h } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Icon } from '/@/components/Icon';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns } from './data';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { searchEquipmentTypeListApi, deleteEquipmentTypeApi } from '/@zoology-wzb/api/device';

  const { createMessage } = useMessage();
  const { prefixCls } = useDesign('sludge-device-type');

  const [registerList, { reload }] = useTable({
    api: searchEquipmentTypeListApi,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'size',
    },
    beforeFetch: (params) => {
      return {
        ...params,
        name: params.name || undefined,
      };
    },
    clickToRowSelect: false,
    columns: columns,
    showIndexColumn: false,
    showTableSetting: false,
    bordered: false,
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });
  const [registerModal, { openModal: openModal }] = useModal();
  function handleSuccess() {
    reload();
  }

  const selectRowKeys = ref<string[]>([]);
  const handleChangeSelection = ({ keys }) => {
    selectRowKeys.value = keys;
  };
  function handleEdit(id) {
    openModal(true, {
      id,
    });
  }
  async function handleDelete(id) {
    await deleteEquipmentTypeApi([id]);
    reload();
    createMessage.success('删除成功');
  }
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-sludge-device-type';
</style>
