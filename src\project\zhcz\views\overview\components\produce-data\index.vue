<template>
  <div class="produce-data-top">
    <div class="content">
      <div
        v-for="(item, i) in dataList.slice(0, sumLength())"
        :key="i"
        :style="{
          width: 100 / sumLength() + '%',
          backgroundImage: item.src ? `url(${getEevReturnDomain(item.src)})` : null,
          backgroundSize: 'contain',
        }"
        :class="['item', `head-bg${dataList.length === 2 ? i + 1 : i}`]"
      >
        <div class="item-head" @click="openDataCurveModal(item.indexCode)">
          <span :class="['num', getValClass(item.indexName, item.index ?? i)]">
            {{ item.value }}
          </span>
          <!-- <span :class="['level', getLevelClass(item.indexName, i)]">
            {{ getLevel(item.value) }}
          </span> -->
        </div>
        <div class="item-footer" :title="item.indexName">
          {{ item.indexName
          }}<span class="unit" v-if="item.value && item.unitName">（{{ item.unitName }}）</span>
        </div>
      </div>
    </div>
    <IndicatorModal ref="dataCurveRef" :groupInfo="groupInfo" :multiple="true" />
  </div>
</template>

<script lang="ts" setup>
  import { PropType, ref, reactive } from 'vue';
  import { getEevReturnDomain } from '/@zhcz/utils/file/url';
  import { IndicatorModal } from '/@zhcz/components/IndicatorModal';

  interface DataItem {
    indexName: string;
    value: string;
    unitName: string;
    src?: string;
    [key: string]: any;
  }

  const props = defineProps({
    length: {
      type: Number,
      default: 3,
    },
    dataList: {
      type: Array as PropType<Array<DataItem>>,
      default: () => [
        {
          indexName: '设备完好率',
          value: '9.99',
          unitName: '%',
        },
        {
          indexName: '设备故障率',
          value: '99.99',
          unitName: '%',
        },
        {
          indexName: '维修及时率',
          value: '99.99',
          unitName: '%',
        },
      ],
    },
    dataResourceCode: {
      type: String,
      default: '',
    },
  });

  // function getBgImg(val, index) {
  //   const imgMap = new Map([
  //     ['进水量', 'head-bg0'],
  //     ['供水量', 'head-bg1'],
  //     ['总用电', 'head-bg2'],
  //     ['设备完好率', 'head-bg0'],
  //     ['设备故障率', 'head-bg1'],
  //     ['维修及时率', 'head-bg2'],
  //     ['总用量', 'head-bg1'],
  //     ['年累计药耗', 'head-bg0'],
  //     ['年计划药耗', 'head-bg1'],
  //     ['年日均药耗', 'head-bg2'],
  //     [0, 'head-bg0'],
  //     [1, 'head-bg1'],
  //     [2, 'head-bg2'],
  //   ]);
  //   return imgMap.get(index) || imgMap.get(val) || 'head-bg0';
  // }

  function getValClass(val, index) {
    const indexMap = new Map([
      [0, 'num0'],
      [1, 'num1'],
      [2, 'num2'],
    ]);

    const imgMap = new Map([
      ['进水量', 'num1'],
      ['供水量', 'num2'],
      ['总用电', 'num0'],
      ['进水累计量', 'num1'],
      ['出水累计量', 'num2'],
      ['供水累计量', 'num2'],
      ['设备完好率', 'num0'],
      ['设备故障率', 'num2'],
      ['维修及时率', 'num1'],
      ['总用量', 'num1'],
      ['年累计药耗', 'num0'],
      ['年计划药耗', 'num1'],
      ['年日均药耗', 'num2'],
    ]);

    return imgMap.get(val) || indexMap.get(index) || 'num0';
  }

  // function getLevelClass(val, index) {
  //   const levelMap = new Map([
  //     ['进水量', 'level0'],
  //     ['供水量', 'level1'],
  //     ['总用电', 'level2'],
  //     ['设备完好率', 'level0'],
  //     ['设备故障率', 'level1'],
  //     ['维修及时率', 'level2'],
  //     ['总用量', 'level1'],
  //     ['年累计药耗', 'level0'],
  //     ['年计划药耗', 'level1'],
  //     ['年日均药耗', 'level2'],
  //     [0, 'level0'],
  //     [1, 'level1'],
  //     [2, 'level2'],
  //   ]);
  //   return levelMap.get(index) || levelMap.get(val) || 'level0';
  // }

  function sumLength() {
    if (props.length > 3) {
      return 3;
    } else if (props.length < 1) {
      return 1;
    } else {
      return props.length;
    }
  }

  // function getValue(val) {
  //   return formatValueV2(val, 'value');
  // }

  // function getLevel(val) {
  //   return formatValueV2(val, 'level');
  // }
  const dataCurveRef = ref(null);
  const groupInfo = reactive({
    groupCode: '',
    indexCodes: '',
  });
  const openDataCurveModal = (indexCode) => {
    console.log('indexCode', indexCode);
    groupInfo.groupCode = props.dataResourceCode;
    groupInfo.indexCodes = indexCode;
    return;
    // dataCurveRef.value?.openCurve();
  };
</script>

<style lang="less" scoped>
  .produce-data-top {
    .content {
      display: flex;
      justify-content: space-around;

      .item {
        text-align: center;
        position: relative;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;

        .item-head {
          font-size: 0;
          // height: 5.875rem;
          height: 6.375rem;
          padding-top: 1rem;
          // cursor: pointer;

          // 1920x125% 适配
          @media screen and (max-width: 1536px) {
            height: 4.5rem;
            padding-top: 1rem;
          }

          > span {
            display: inline-block;
          }

          .num {
            font-size: 1.25rem;
            font-family: DIN Alternate-Bold, DIN Alternate;
            font-weight: 700;
          }

          .level {
            font-size: 1rem;
            font-family: DIN Alternate-Bold, DIN Alternate;
            font-weight: 400;
          }

          .num0,
          .level0 {
            background-image: linear-gradient(181deg, #ffffff 0%, #2d82fe 98%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .num1,
          .level1 {
            background-image: linear-gradient(181deg, #ffffff 0%, #1fc3a4 99%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .num2,
          .level2 {
            background-image: linear-gradient(181deg, #ffffff 0%, #fec52d 98%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .unit {
            // font-size: 0.875rem;
            font-size: 0.75rem;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.6);
          }
        }

        .item-footer {
          width: 100%;
          margin-top: 0.5rem;
          font-size: 0.875rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 1.375rem;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .head-bg0 {
        background: url(../../assets/images/data0.png) center center no-repeat;
        // background-size: 7.5rem 5.625rem;
      }

      .head-bg1 {
        background: url(../../assets/images/data1.png) center center no-repeat;
        // background-size: 7.5rem 5.625rem;
      }

      .head-bg2 {
        background: url(../../assets/images/data2.png) center center no-repeat;
        // background-size: 7.5rem 5.625rem;
      }
    }
  }
</style>
