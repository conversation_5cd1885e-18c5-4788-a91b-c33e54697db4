<template>
  <div class="device-tree-wrapper" :class="{ 'hide-tree-wrapper': !isShowTree }">
    <div class="tree-container" :class="{ 'hide-tree-container': !isShowTree }">
      <div class="tit-content">
        <div class="tit">设备类型</div>
      </div>
      <div class="tree-content">
        <BasicTree
          combo
          defaultExpandAll
          placeholder="请输入"
          treeWrapperClassName="h-[calc(100%-52px)] overflow-auto"
          :clickRowToExpand="false"
          :treeData="treeData"
          :fieldNames="treeFieldNames"
          :loading="loading"
          @select="handleSelectNode"
          ref="treeRef"
          emptyClass="mt-[50%]"
        >
          <template #title="node">
            <div class="flex items-center justify-between flex-1">
              <div style="overflow: hidden; text-overflow: ellipsis">
                <!-- name值内部重新赋值，取值错误 -->
                <Tooltip :title="node.showName">
                  <span>{{ node.showName }}</span>
                </Tooltip>
              </div>
            </div>
          </template>
        </BasicTree>
      </div>
    </div>
    <div
      class="toggle-tree"
      @click="handleChangeTreeShow"
      :style="{
        width: isShowTree ? '0' : '24px',
        borderRight: isShowTree ? 'none' : '1px solid #e9e9e9',
      }"
    >
      <div
        class="toggle-tree-btn"
        :style="{
          left: isShowTree ? '-24px' : '0',
          borderRadius: isShowTree ? '4px 0px 0px 4px' : '0px 4px 4px 0px',
          background: isShowTree ? '' : 'var(--theme-color)',
          borderColor: isShowTree ? '' : 'var(--theme-color)',
        }"
      >
        <Icon
          :icon="isShowTree ? 'icon-park-outline:left' : 'icon-park-outline:right'"
          :color="isShowTree ? 'rgba(51, 51, 51, 0.65)' : 'rgb(255,255,255)'"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="DeviceTree">
  import { ref, onMounted, watch } from 'vue';
  import { BasicTree, TreeActionType } from '/@/components/Tree';
  import { Tooltip } from 'ant-design-vue';
  import Icon from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getFaultDeviceTreeApi } from '/@zoology-wzb/api/device';
  import type { faultDeviceTreeType, deviceTreeItem } from '/@zoology-wzb/api/device/type';

  defineOptions({
    name: 'DeviceTree',
  });

  const { createMessage } = useMessage();

  // Props
  interface Props {
    queryParams?: Record<string, any>;
  }

  const props = withDefaults(defineProps<Props>(), {
    queryParams: () => ({}),
  });

  // Emits
  const emit = defineEmits<{
    select: [node: any, selectedKeys: string[]];
    'toggle-change': [isShow: boolean];
  }>();

  // 树节点类型定义
  interface TreeNode extends deviceTreeItem {
    key: string;
    children?: TreeNode[];
  }

  // 树组件配置
  const treeFieldNames = {
    title: 'name',
    key: 'key',
    children: 'children',
  };

  // 响应式数据
  const treeData = ref<TreeNode[]>([]);
  const loading = ref(false);
  const selectedKeys = ref<string[]>([]);
  const treeRef = ref<Nullable<TreeActionType>>(null);
  const isShowTree = ref(true);

  // 获取树数据
  const getTreeData = async () => {
    try {
      loading.value = true;
      const params = {};
      let res: faultDeviceTreeType = [];
      try {
        res = await getFaultDeviceTreeApi(params);
      } catch (error) {}
      // wqtest
      res = [
        {
          id: '1',
          name: '设备1',
          type: 2,
          children: [
            {
              id: '1-1',
              name: '设备1-1',
              type: 2,
            },
            {
              id: '1-2',
              name: '设备1-2',
              type: 2,
            },
          ],
        },
        {
          id: '2',
          name: '设备2',
          type: 2,
        },
      ];
      // 处理数据，添加必要的key和title属性
      const processTreeData = (nodes: any[], prefix?: number | string | undefined): TreeNode[] => {
        if (!nodes || !Array.isArray(nodes)) return [];
        return nodes.map((node) => {
          let children = node.children;
          if (Array.isArray(children)) {
            children = processTreeData(children, node.id);
          }
          console.log('-----------> node', node);
          return {
            ...node,
            key: prefix === undefined ? String(node.id) : `${prefix}-${node.id}`,
            showName: node.name,
            children,
          };
        });
      };
      treeData.value = res ? processTreeData(res) : [];
      // 展开第一个
      let firstKey = treeData.value?.[0].key;
      firstKey && treeRef.value?.setExpandedKeys([firstKey]);

      let firstChildrenKey = treeData.value?.[0]?.children?.[0]?.key;
      if (firstChildrenKey) {
        // 选中第一个
        treeRef.value?.setSelectedKeys([firstChildrenKey]);
        // 触发选择事件
        handleSelectNode([firstChildrenKey], { node: treeData.value?.[0]?.children?.[0] });
      }
      console.log('----------> treeData', firstKey, firstChildrenKey, treeData.value);
    } catch (error) {
      console.error('获取设备树数据失败:', error);
      createMessage.error('获取设备数据失败');
      treeData.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 处理节点选择
  const handleSelectNode = (selectedKeys: string[], { node }: any) => {
    emit('select', node, selectedKeys);
  };

  // 切换树的显示/隐藏
  const handleChangeTreeShow = () => {
    isShowTree.value = !isShowTree.value;
    emit('toggle-change', isShowTree.value);
  };

  // 暴露方法供父组件调用
  const refresh = () => {
    getTreeData();
  };

  const toggleTree = () => {
    handleChangeTreeShow();
  };

  defineExpose({
    refresh,
    toggleTree,
    isShowTree,
  });

  // 组件挂载时获取数据
  onMounted(() => {
    getTreeData();
  });
</script>

<style lang="less" scoped>
  .device-tree-wrapper {
    height: 100%;
    display: flex;
    position: relative;

    &.hide-tree-wrapper {
      width: 24px;
    }

    .tree-container {
      width: 248px;
      height: 100%;
      background: #fff;
      border-radius: 4px;
      border-right: 1px solid #e9e9e9;
      margin-right: 0;
      transition: all 0.3s;

      &.hide-tree-container {
        width: 0;
        padding: 0;
        border-right: none;
        margin-right: 0;

        .tit-content,
        .tree-content {
          display: none;
        }
      }

      .tit-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        padding: 0 12px;
        border-bottom: 1px solid #e9e9e9;
        white-space: nowrap;

        .tit {
          font-size: 16px;
          font-weight: 600;
        }
      }

      .tree-content {
        height: calc(100% - 48px);
        overflow-x: hidden;
        overflow-y: hidden;

        :deep(.vben-tree) {
          .vben-tree-header {
            border-bottom: none;
          }
        }
      }
    }

    .toggle-tree {
      position: relative;
      width: 24px;
      height: 100%;
      background: #fff;
      border-right: 1px solid #e9e9e9;
      cursor: pointer;

      .toggle-tree-btn {
        position: absolute;
        top: 50%;
        width: 24px;
        height: 32px;
        border-radius: 4px 0px 0px 4px;
        border: 1px solid #e9e9e9;
        transform: translateY(-50%);
        cursor: pointer;
        text-align: center;
        line-height: 30px;
        z-index: 999;
        background: white;
        transition: all 0.5s;

        .anticon {
          margin: 0;
        }
      }
    }
  }
</style>
