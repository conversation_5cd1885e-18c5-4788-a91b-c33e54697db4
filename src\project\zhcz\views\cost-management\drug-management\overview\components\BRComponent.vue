<template>
  <div class="brc">
    <HlxbRankingSimpleCard
      v-bind="{ bottomList: indexList, empty, loading, indexType: Boolean(true) }"
      :title="'用药排行'"
    >
      <template #headerLeftAfter>
        <div class="button_ai" v-if="!empty && indexList.length" @click="handleCreate()"
          ><img :src="aiImg" alt="" srcset="" /><span class="text">AI分析</span></div
        ></template
      >

      <template #headerRight>
        <div class="header-right">
          <div>
            <Select
              v-model:value="dataType"
              @change="getData"
              style="width: 80px"
              v-if="dataList.length"
            >
              <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
                {{ item.label }}
              </SelectOption>
            </Select>
          </div>

          <DatePicker
            v-if="dataList.find((item) => item.value === dataType)?.label === '月'"
            @change="getData"
            v-model:value="date"
            picker="month"
            valueFormat="YYYY-MM"
            :disabledDate="disabledMonthDate"
          />
          <DatePicker
            v-if="dataList.find((item) => item.value === dataType)?.label === '日'"
            @change="getData"
            valueFormat="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model:value="date1"
            placeholder="请选择"
            :allowClear="false"
            :disabledDate="disabledDate"
          />
        </div>
      </template>
    </HlxbRankingSimpleCard>
    <HAiDrawer @register="registerDrawer" :aiQuestion="aiDataCopy[0].aiQuestion" />
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  // import { RankingSimpleCard } from '/@zhcz/components/HLCardComponent';
  import { HlxbRankingSimpleCard } from 'hlxb-ui';
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';
  import aiImg from '../AI.png';
  import { HAiDrawer } from '/@zhcz/components/HAiDrawer/index';
  import { useDrawer } from '/@/components/Drawer';

  const [registerDrawer, { openDrawer }] = useDrawer();
  function handleCreate() {
    openDrawer(true, {
      record: {},
      isUpdate: false,
    });
  }
  const aiDataCopy = ref([
    {
      aiQuestion:
        '自来水厂2025年2月份，用药量从高到低排行为：碱铝_kg，石灰_kg，次氯酸钠_kg，高锰酸钾_kg，PAM_kg。分析当前情况。',
      deepAnalysis:
        '好的，用户问用药量的原因，我需要详细分析。首先，我得回忆一下浊度的定义，素全面分析.\n可能需要进一步的信息来确定具体原因，但先列出这些可能性。\n帮助用户排查。',
      explain:
        '用药量通常由多种因素引起，具体原因需结合环境、人为活动和水处理流程综合分析。以下是常见原因分类：',
      resultTitle: '总览分析',
      resultDes: '',
      summaryTitle: '总结',
      summary:
        '若用药量持续超标，可能影响水质安全（如隐藏病原微生物），建议及时联系水务部门或环保机构介入调查。',
    },
  ]);
  const loading = ref(false);
  const empty = ref(false);
  type OptionItem = {
    label: string;
    value: string;
  };
  const dataList = ref<OptionItem[]>([
    // {
    //   label: '日',
    //   value: 1,
    // },
    // {
    //   label: '月',
    //   value: 2,
    // },
  ]);
  const dataType = ref<any>(null);
  const date = ref(dayjs().subtract(1, 'month').format('YYYY-MM'));
  const date1 = ref(dayjs().subtract(1, 'day').format('YYYY-MM-DD'));

  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);
  async function getTimeList() {
    try {
      loading.value = true;
      const res = await listSenceGroupByParent({
        // dhzl2_dlph
        groupCode: 'yhzl2_yypx',
        factoryId: 1,
        platformld: 1,
      });
      // console.log('res.data', res);
      if (Object.keys(res).length) {
        empty.value = false;
        dataType.value = Object.keys(res)[0];
        dataList.value = Object.keys(res).map((item) => ({
          value: item,
          label: res[item],
        }));
        getData();
      } else {
        empty.value = true;
        loading.value = false;
      }
    } catch (error) {
      empty.value = true;
      loading.value = false;
      console.log('error', error);
    }
  }
  function disabledDate(current) {
    return current && current > dayjs().subtract(0, 'day').endOf('day');
  }
  function disabledMonthDate(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('day');
  }

  const getData = async () => {
    try {
      loading.value = true;
      empty.value = false;
      const startDateTime =
        dataList.value.find((item) => item.value === dataType.value)?.label === '日'
          ? dayjs(date1.value).format('YYYY-MM-DD 00:00:00')
          : dayjs(date.value).startOf('month').format('YYYY-MM-DD 00:00:00');
      const endDateTime =
        dataList.value.find((item) => item.value === dataType.value)?.label === '日'
          ? dayjs(date1.value).format('YYYY-MM-DD 23:59:59')
          : dayjs(date.value).endOf('month').format('YYYY-MM-DD 23:59:59');
      const time =
        dataList.value.find((item) => item.value === dataType.value)?.label === '日'
          ? `${date1.value}日`
          : `${date.value}月`;
      const params = {
        startDateTime: startDateTime,
        endDateTime: endDateTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        resourceInterfaceId: '5',
        jsConvert: true,
        groupCode: dataType.value,
        paramsData: JSON.stringify(params),
      };
      indexList.value = [];
      const { data } = await callResourceFunction(paramData);
      // console.log('用药排行', data);
      if (data && data.length) {
        loading.value = false;
        empty.value = false;
        const newData = data.map((item) => {
          return {
            indexName: item.indexName,
            indexCode: item.indexCode,
            unitName: item.unitName,
            value: Number(item.total) ? Number(item.total) : '-',
            // item.data
            //   .map((i) => i.value)
            //   .reduce((prev, cur) => {
            //     return prev + cur;
            //   }, 0),
          };
        });
        const copyData = newData.map((item) => {
          return item.indexName + item.value + item.unitName + '\n';
        });
        aiDataCopy.value[0].aiQuestion = `自来水厂${time}，用药量从高到低排行为：${copyData.join(
          ',',
        )}。分析当前情况。`;
        indexList.value = BubbleSort(newData);
      } else {
        empty.value = true;
        loading.value = false;
      }
    } catch (err) {
      empty.value = true;
      loading.value = false;
      console.log(err);
    }
  };

  function BubbleSort(ary) {
    for (var i = 0; i < ary.length - 1; i++) {
      for (var j = i + 1; j < ary.length; j++) {
        var current = ary[i];
        if (Number(current.value) < Number(ary[j].value)) {
          var tmp = ary[j];
          ary[j] = current;
          ary[i] = tmp;
        }
      }
    }
    return ary;
  }
  getTimeList();
</script>
<style lang="less" scoped>
  .brc {
    background-color: #fcfcfc;
    height: 100%;
    overflow: hidden;
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
    border-right: 1px solid #d8d8d8;

    .header-right {
      display: flex;
      gap: 0 16px;
    }

    .button_ai {
      margin-left: 8px;
      padding: 5px 8px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      cursor: pointer;

      img {
        width: 16px;
      }

      .text {
        font-size: 14px;
        color: #0b62cb;
        line-height: 14px;
        padding-left: 4px;
        font-weight: 600;
      }

      &:hover {
        background: rgba(11, 98, 203, 0.12);
      }
    }
  }
</style>
