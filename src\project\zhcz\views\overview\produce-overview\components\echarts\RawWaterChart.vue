<template>
  <div style="width: 100%; height: 100%">
    <div ref="chartRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
  import { defineComponent, ref, onMounted, watch } from 'vue';
  import { useECharts } from '/@zhcz/hooks/useECharts';
  import { getEchartFontSize } from '../../../utils';

  export default defineComponent({
    props: {
      data: {
        type: Object,
        default: () => ({
          title: '昨天数据曲线',
          chartOptions: {
            xAxis: {
              data: [
                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23,
                24,
              ],
            },
            series: [
              {
                name: '余氯',
                color: '#1FC3A4',
                data: [0],
                itemColor: ['rgba(31, 195, 164, 1)', 'rgba(31, 195, 164, 0)'],
              },
              {
                name: '浊度',
                color: '#FEC52D',
                data: [0],
                areaColor: ['rgba(254, 197, 45, 0.3)', 'transparent'],
              },
            ],
          },
        }),
      },
    },
    setup(props) {
      const chartRef = ref(null);

      const { setOptions, echarts } = useECharts(chartRef);

      // 计算最大值
      function calMax(arr) {
        let max = 0;
        arr.forEach((el) => {
          el.forEach((el1) => {
            if (!(el1 === undefined || el1 === '')) {
              if (max < el1) {
                max = el1;
              }
            }
          });
        });
        const maxint = Math.ceil(max / 9.5); // 不让最高的值超过最上面的刻度
        const maxval = maxint * 10; // 让显示的刻度是整数
        return maxval;
      }

      // 计算最小值
      function calMin(arr) {
        let min = 0;
        arr.forEach((el) => {
          el.forEach((el1) => {
            if (!(el1 === undefined || el1 === '')) {
              if (min > el1) {
                min = el1;
              }
            }
          });
        });
        const minint = Math.floor(min / 10);
        const minval = minint * 10; // 让显示的刻度是整数
        return minval;
      }

      function renderChart() {
        const { title, chartOptions } = props.data;
        const { xAxis, series } = chartOptions;

        const Min1 = calMin([series[0].data, series[1].data]);
        const Max1 = calMax([series[0].data, series[1].data]);

        const option = {
          title: {
            top: 0,
            text: title,
            textStyle: {
              color: '#ffffff',
              fontWeight: '400',
              fontSize: getEchartFontSize(14),
            },
            padding: [
              5, // 上
              10, // 右
              5, // 下
              0, // 左
            ],
          },
          grid: {
            left: 0,
            right: 0,
            top: 65,
            // top: fontSize(0.4),
            bottom: 10,
            containLabel: true,
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
          },
          legend: {
            show: true,
            icon: 'circle',
            top: -5,
            right: 0,
            itemGap: 16,
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
              fontSize: getEchartFontSize(14),
              color: '#fff',
              padding: [3, 5],
            },
            // formatter(name) {
            //   const legendName = new Map([
            //     ['原水（mg/L）', '原水'],
            //     ['出厂水（mg/L）', '出厂水']
            //   ])

            //   return legendName.get(name) || '原水'
            // }
          },
          xAxis: [
            {
              type: 'category',
              data: xAxis.data,
              boundaryGap: true,
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.8)',
                  type: 'solid',
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  color: '#ffffff',
                  fontSize: getEchartFontSize(14),
                },
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              name: series[0].name,
              // min: series[0].data.length || series[1].data.length ? null : 0,
              // max: series[0].data.length || series[1].data.length ? null : 10,
              // min: 0,
              // max: max1,
              min: Min1,
              max: Max1,
              splitNumber: 5,
              interval: (Max1 - Min1) / 5,

              position: 'left',
              // interval: 2,
              nameTextStyle: {
                color: '#ffffff',
                fontSize: getEchartFontSize(14),
                padding: [0, 0, 4, -40],
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(255,255,255,0.2)',
                  type: 'dashed',
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#fff',
                  fontSize: getEchartFontSize(14),
                },
              },
            },
            {
              type: 'value',
              name: series[1].name,
              // min: series[0].data.length || series[1].data.length ? null : 0,
              // max: series[0].data.length || series[1].data.length ? null : 10,
              // min: 0,
              // max: max2,
              min: Min1,
              max: Max1,
              splitNumber: 5,
              interval: (Max1 - Min1) / 5,
              position: 'right',
              // interval: 2,
              nameTextStyle: {
                color: '#ffffff',
                fontSize: getEchartFontSize(14),
                padding: [0, 54, 4, 0],
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(255,255,255,0.2)',
                  type: 'dashed',
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#fff',
                  fontSize: getEchartFontSize(14),
                },
              },
            },
          ],
          series: [
            {
              name: series[0].name,
              type: 'bar',
              data: series[0].data,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(31, 195, 164, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(31, 195, 164, 0)',
                  },
                ]),
              },
              label: {
                normal: {
                  show: true,
                  position: [-1.5, 0],
                  // distance: 1,
                  padding: 0,
                  formatter: ['{b|}'].join(''),
                  rich: {
                    b: {
                      width: getEchartFontSize(2),
                      height: 2,
                      backgroundColor: '#C4FFF4',
                      borderRadius: 8,
                    },
                  },
                },
              },
              barWidth: getEchartFontSize(16),
            },
            {
              name: series[1].name,
              color: '#FEC52D',
              data: series[1].data,
              type: 'line',
              smooth: true,
              symbol: 'none',
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(254, 197, 45, 0.3)',
                  },
                  {
                    offset: 1,
                    color: 'transparent',
                  },
                ]),
              },
              yAxisIndex: 1,
            },
          ],
        };

        setOptions(option);
      }

      watch(
        () => props.data,
        () => {
          renderChart();
        },
      );

      onMounted(() => {
        renderChart();
      });
      return {
        chartRef,
      };
    },
  });
</script>

<style lang="less" scoped></style>
