<template>
  <Card :bordered="false" dis-hover title="设备运行情况">
    <template #extra>
      <div class="extra-wrap">
        <Select
          placeholder="请选择"
          @change="handleOneChange"
          v-model:value="formData.argOne"
          class="mr-16px"
          :dropdownMatchSelectWidth="false"
        >
          <SelectOption v-for="item in optionsOne" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
        <DatePicker
          v-model:value="formData.dayDate"
          :picker="picker"
          :valueFormat="valueFormat"
          :allowClear="false"
          class="!w-[auto]"
          placeholder="请选择"
          :disabledDate="disabledDate"
          @change="handleChangeDayDate"
        />
      </div>
    </template>
    <div ref="chartRef" style="height: 100%" v-show="!isEmpty"></div>

    <div v-show="isEmpty" class="empty-box">
      <HEmpty />
    </div>
  </Card>
</template>

<script setup>
  import { Card, Select, SelectOption, DatePicker } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { ref, watch, reactive, onMounted, nextTick, computed, onBeforeMount } from 'vue';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { roundAndConvert } from '/@zhcz/utils/number';
  import { getBiOperatingStatistic } from '/@zhcz/api/overview';
  import { getParamKeyApi } from '/@/api/admin/param';
  import { DICT } from '/@zhcz/enums/sceneResource';

  const timeTypeMap = {
    0: {
      type: 'date',
      format: 'YYYY-MM-DD',
    },
    1: {
      type: 'month',
      format: 'YYYY-MM',
    },
    2: {
      type: 'year',
      format: 'YYYY',
    },
  };

  // 根据返回的后缀确定，eg: xxxx_month
  const picker = computed(() => {
    let type = isSceneApi.value
      ? formData.argOne && formData.argOne.split('_')[1]
      : timeTypeMap[Number(formData.argOne)].type;
    return type || 'month';
  });
  const valueFormat = computed(() => {
    let map = {
      month: 'YYYY-MM',
      day: 'YYYY-MM-DD',
      year: 'YYYY',
    };
    return map[picker.value] || map.month;
  });

  const xAxisFormat = computed(() => {
    let map = {
      month: 'DD日',
      year: 'MM月',
    };
    return map[picker.value] || map.month;
  });

  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }

  let formData = reactive({
    dayDate: dayjs().format('YYYY-MM'),
    argOne: '1',
    argTwo: null,
  });

  let optionsOne = ref([
    { label: '月', value: '1' },
    { label: '年', value: '2' },
  ]);
  const chartRef = ref(null);

  const props = defineProps({
    sourceData: { type: Array, default: () => [] },
  });

  const isSceneApi = ref(true);

  const getIsSceneApi = async () => {
    const data = await getParamKeyApi(DICT.IS_SCENE_API);
    isSceneApi.value = data === '1';
  };

  onBeforeMount(() => {
    getIsSceneApi();
  });

  // 更新选项一
  function updateOptions(newValue) {
    if (newValue.length < 1) return;
    if (isSceneApi.value) {
      // sbzl_sbyxqk
      let res = newValue.find((i) => i.groupCode === 'sbzl_sbyxqk')?.children || [];
      if (res.length < 1) return;
      optionsOne.value = res.map(({ groupCode, name }) => ({
        value: groupCode,
        label: name,
      }));
      formData.argOne = res[0].groupCode;
    }

    getData();
  }

  let getParams = () => {
    let paramsData = {
      startDateTime: dayjs(formData.dayDate).startOf(picker.value).format('YYYY-MM-DD 00:00:00'),
      endDateTime: dayjs(formData.dayDate).endOf(picker.value).format('YYYY-MM-DD 23:59:59'),
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };

    let params = {
      resourceInterfaceId: '3',
      groupCode: formData.argOne,
      paramsData: JSON.stringify(paramsData),
    };
    return params;
  };

  const getApiParams = () => {
    return {
      type: Number(formData.argOne),
      time: formData.dayDate,
    };
  };

  async function getData() {
    // let params = getParams();
    // let { data } = await callResourceFunction(params);
    let params = getParams();
    const apiParams = getApiParams();
    const res = isSceneApi.value
      ? await callResourceFunction(params)
      : await getBiOperatingStatistic(apiParams);
    const data = isSceneApi.value ? res.data : res;
    renderChart(data);
  }

  let isEmpty = ref(true);
  function renderChart(data) {
    if (!data?.length) {
      isEmpty.value = true;
      return;
    }
    for (let item of data) {
      if (!item.data?.length) {
        isEmpty.value = true;
        return;
      }
    }
    isEmpty.value = false;

    let isZoom = data[0].data.length > 6 ? true : false;
    let color = ['#22CD80', '#2E8CFF', '#FF8C2E'];
    const option = {
      animation: false,
      color,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(255, 255, 255, 1)',
        textStyle: {
          color: '#999',
          fontSize: 14,
          lineHeight: 28,
          height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
        formatter: (params) => {
          let str = `<div style='display: grid; grid-template-columns: 15px 1fr auto;'>`;
          params.forEach((item) => {
            str += `
                        <span>${item.marker}</span>
                        <span style='color: #333;'>${item.seriesName}</span>
                        <div style='color: #333; margin-left: 15px; font-weight: 600; justify-self: end;'>
                           <span>${item.value ?? '-'}</span><span>${'个'}</span>
                        </div>`;
          });

          str = `${str}</div>`;
          return str;
        },
      },
      legend: {
        icon: 'circle',
        itemWidth: 8,
        itemHeight: 8,
        itemGap: 24,
        textStyle: {
          fontSize: 14,
          color: '#333333',
        },
      },
      grid: {
        top: '15%',
        left: 0,
        right: '1%',
        bottom: isZoom ? 30 : 0,
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          data: data[0].data.map((i) => i.collectDateTime),
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#666',
              fontSize: 14,
            },
            formatter(value) {
              return dayjs(value).format(xAxisFormat.value);
            },
          },
          axisPointer: {
            type: 'shadow',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          nameTextStyle: {
            color: '#666',
            fontSize: 14,
            align: 'left',
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            align: 'right',
            textStyle: {
              color: '#666',
              fontSize: 14,
            },
          },
        },
      ],

      series: data.map((i, index) => {
        return {
          name: i.indexName,
          type: 'bar',
          barMaxWidth: 12,
          color: color[index % 3],
          data: i.data.map((i) => (i.value == null ? null : roundAndConvert(Number(i.value), 0))),
        };
      }),
    };
    if (isZoom) {
      option.dataZoom = [
        {
          type: 'inside',
          start: 0,
          end: 60,
        },
        {
          start: 0,
          height: 20,
          bottom: 10,
          end: 20,
        },
      ];
    }
    let instance = getInstance();
    instance?.clear();
    setOptions(option);
  }

  watch(() => props.sourceData, updateOptions, { immediate: true });

  function handleOneChange() {
    nextTick(() => {
      formData.dayDate = dayjs().format(valueFormat.value);
      getData();
    });
  }
  function handleChangeDayDate() {
    getData();
  }
  let setOptions;
  let getInstance;
  onMounted(() => {
    ({ setOptions, getInstance } = useECharts(chartRef));
  });
</script>

<style lang="less" scoped></style>
