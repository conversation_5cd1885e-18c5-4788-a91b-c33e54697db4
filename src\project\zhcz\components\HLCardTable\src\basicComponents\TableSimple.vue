<template>
  <div :class="['card-table', themeColor]">
    <BasicTable
      ref="basicTable"
      class="basic-table"
      :title="tableTitle"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :showIndexColumn="showIndexColumn"
    >
      <template #bodyCell="{ column, record }">
        <slot name="tableAction" :column="column" :record="record"></slot>
        <!-- <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: '编辑',
                    onClick: handleEdit.bind(null, record),
                  },
                  {
                    label: '删除',
                    popConfirm: {
                      title: '是否确认删除',
                      placement: 'left',
                      confirm: handleDelete.bind(null, record),
                    },
                  },
                ]"
              />
            </template> -->
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup name="table">
  import { BasicTable, BasicColumn } from '/@/components/Table';
  import { defineProps, PropType } from 'vue';

  defineProps({
    // 表格列配置，类型为 BasicColumn 数组，默认值为空数组
    columns: {
      type: Array as PropType<BasicColumn[]>,
      default: () => [],
    },
    // 表格数据源，类型为任意类型数组，默认值为空数组
    dataSource: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    // 表格分页配置，类型为任意对象，默认每页显示 20 条数据
    pagination: {
      type: Object as PropType<any>,
      default: () => {
        return { pageSize: 20 };
      },
    },
    // 表格标题，类型为字符串，默认值为空字符串
    tableTitle: {
      type: String as PropType<string>,
      default: '',
    },
    // 是否显示序号列
    showIndexColumn: {
      type: Boolean,
      default: true,
    },
    themeColor: {
      type: String,
      default: 'light', // Dark, light, screenColor
    },
  });
</script>
<style lang="less" scoped>
  .card-table {
    color: #999999;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .Dark {
    color: #ffffff;
    // padding: 16px 8px 16px 16px;
    // height: calc(100% - 50px);
    // overflow-y: auto;
    // box-sizing: border-box;
  }

  .light {
    color: #333333;
  }

  .screenColor {
    color: #ffffff;
    background: transparent;
  }
</style>
