<template>
  <div class="card-indicator flex flex-col justify-between h-full">
    <div class="indicator-wrapper h-[calc(100%-0px)]" v-if="list.length">
      <div
        class="indictor flex items-center cursor-pointer"
        v-for="(item, index) in data"
        :key="index"
        :style="getDefaultBgColor(item.selected)"
        @click="onClick(item)"
      >
        <img :src="getImageUrl()" alt="" class="flex-shrink-0 w-48px h-48px mr-12px" />
        <div
          class="left flex flex-col justify-between"
          style="height: 40px; width: calc(100% - 60px)"
        >
          <Tooltip @mouseenter="showToolTip">
            <template #title>
              {{ item.indicatorName }}
            </template>
            <div class="name">{{ item.indicatorName }}</div>
          </Tooltip>
          <div class="flex items-end">
            <Tooltip @mouseenter="showToolTip">
              <template #title>
                {{ item.val ? roundAndConvertCheckNullAndUnDef(item.val, decimalPlaces) : '--' }}
              </template>
              <span class="value">
                {{ item.val ? roundAndConvertCheckNullAndUnDef(item.val, decimalPlaces) : '--' }}
              </span>
            </Tooltip>
            <span v-if="item.val" class="unit flex-shrink-0">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="pagination flex justify-center mt-12px" v-if="list.length && isShowPage">
      <Pagination
        v-model:current="pageIndex"
        :pageSize="pageSize"
        :total="total"
        size="small"
        :show-less-items="false"
        @change="handleChangePage"
      />
    </div>
    <HEmpty v-if="list.length === 0" />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, PropType, watch } from 'vue';
  import { Tooltip, Pagination } from 'ant-design-vue';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import type { IIndicatorStatistics } from '/@zhcz/api/process-production/production-history/model';
  import { roundAndConvertCheckNullAndUnDef } from '/@zhcz/utils/number';

  const bgColors = ['46, 123, 255', '34, 205, 128', '46, 123, 255'];

  const props = defineProps({
    list: {
      type: Array as PropType<IIndicatorStatistics[]>,
      default: () => [],
    },
    weekAndMonth: {
      type: String,
      default: '天',
    },
    index: {
      type: Number,
      default: 0,
    },
    decimalPlaces: {
      type: Number,
      default: 0,
    },
    selectedIndictor: {
      type: String,
      default: '',
    },
    isShowPage: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['view-chart']);

  watch(() => props.selectedIndictor, updateSelected);

  function updateSelected() {
    if (props.selectedIndictor) {
      props.list.map((item) => {
        item.selected = false;
      });
      for (const item of data.value) {
        item.selected = item.indicatorCode === props.selectedIndictor;
      }
    }
  }

  function showToolTip(e) {
    if (!e?.target) return;
    const { clientWidth, scrollWidth } = e.target;
    if (clientWidth >= scrollWidth) {
      e.target.style.pointerEvents = 'none'; // 阻止鼠标事件
    }
  }

  watch(
    () => props.list,
    () => {
      console.log('props.list', props.list);

      let currentSelectedIndex = props.list.findIndex(
        (item) => item.indicatorCode === props.selectedIndictor,
      );
      let currentPage =
        currentSelectedIndex === -1 ? 0 : Math.floor(currentSelectedIndex / pageSize);

      data.value = props.list
        .slice(currentPage * pageSize, (currentPage + 1) * pageSize)
        .map((item) => {
          return {
            ...item,
            selected: item.indicatorCode === props.selectedIndictor,
          };
        });
    },
  );

  function getDefaultBgColor(selected: boolean) {
    return {
      backgroundColor: `rgba(${bgColors[props.index % 3]}, 0.04)`,
      border: `1px solid rgba(${bgColors[props.index % 3]}, ${selected ? '1' : '0.16'})`,
    };
  }

  function getImageUrl() {
    return new URL(`../assets/images/index-${props.index}.png`, import.meta.url).href;
  }

  const pageSize = 4;
  const data = ref<IIndicatorStatistics[]>(props.list.slice(0, pageSize));
  const total = computed(() => props.list.length);
  const pageIndex = ref(1);

  function handleChangePage(page: number) {
    pageIndex.value = page;
    data.value = props.list.slice((pageIndex.value - 1) * pageSize, pageIndex.value * pageSize);
  }

  function onClick(item: IIndicatorStatistics) {
    emit('view-chart', item);
  }
</script>

<style lang="less" scoped>
  .card-indicator {
    .indicator-wrapper {
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr));
      grid-template-rows: repeat(2, minmax(72px, 1fr));
      gap: 16px;
      align-content: start;

      .indictor {
        align-items: center;
        padding: 0px 16px;
        border: 1px solid #f5f6f7;
        border-radius: 8px;
        color: #666666;
        font-size: 14px;
        font-weight: 400;
        line-height: 1;

        .left {
          .name {
            // width: 75%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .value {
            // max-width: 65%;
            margin-right: 8px;
            font-weight: 600;
            font-size: 20px;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .pagination {
      :deep(.ant-pagination) {
        margin: 0 !important;

        .ant-pagination-item,
        .ant-pagination-prev,
        .ant-pagination-next,
        .ant-pagination-jump-prev,
        .ant-pagination-jump-next {
          width: 24px !important;
          height: 24px !important;
          min-width: 24px !important;
          border: none !important;
        }

        .ant-pagination-mini .ant-pagination-item {
          margin: 0 4px;
        }

        &-disabled {
          display: block !important;
        }

        .ant-pagination-item-active {
          background-color: var(--theme-color-8p);

          a {
            color: var(--theme-color) !important;
          }
        }
      }
    }
  }
</style>
