import type { UserConfig, ConfigEnv } from 'vite';
import pkg from './package.json';
import dayjs from 'dayjs';
import { loadEnv, createLogger } from 'vite';
import { resolve } from 'path';
import { generateModifyVars } from './build/generate/generateModifyVars';
import { createProxy } from './build/vite/proxy';
import { wrapperEnv, getCurrentRoot } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

const { dependencies, devDependencies, name, version } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
};

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();

  const env = loadEnv(mode, root);
  const { projectRoot } = getCurrentRoot(env);

  // The boolean type read by loadEnv is a string. This function can be converted to boolean type
  const viteEnv = wrapperEnv(env);

  const {
    VITE_PORT,
    VITE_PUBLIC_PATH,
    VITE_PROXY,
    VITE_DROP_CONSOLE,
    VITE_GLOB_MONOCASE,
    VITE_GLOB_MONOCASE_NAME,
  } = viteEnv;

  const isBuild = command === 'build';

  const projectAlia = {
    find: /\/@@\//,
    replacement: projectRoot + '/',
  };

  const logger = createLogger();
  const loggerWarn = logger.warn;

  logger.warn = (msg, options) => {
    // 忽略空 警告
    if (
      msg.includes('[plugin:vite:reporter]') &&
      msg.includes(' dynamic import will not move module into another chunk.')
    )
      return;
    loggerWarn(msg, options);
  };

  return {
    customLogger: logger,
    base: VITE_PUBLIC_PATH,
    root: projectRoot,
    resolve: {
      alias: [
        projectAlia,
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: /\/@project\//,
          replacement: pathResolve('src/project') + '/',
        },
        {
          find: /\/@aoa\//,
          replacement: pathResolve('src/project/aoa') + '/',
        },

        {
          find: /\/@metering\//,
          replacement: pathResolve('src/project/metering') + '/',
        },
        {
          find: /\/@yjpt\//,
          replacement: pathResolve('src/project/yjpt') + '/',
        },
        {
          find: /\/@zhcz\//,
          replacement: pathResolve('src/project/zhcz') + '/',
        },
        {
          find: /\/@zhoukou\//,
          replacement: pathResolve('src/project/hlxb-zhoukougas') + '/',
        },
        {
          find: /\/@equipment-maintenance\//,
          replacement: pathResolve('src/project/equipment-maintenance') + '/',
        },
        {
          find: /\/@door\//,
          replacement: pathResolve('src/project/door') + '/',
        },
        {
          find: /\/@hlxb-mail\//,
          replacement: pathResolve('src/project/hlxb-mail') + '/',
        },
        {
          find: /\/@yjpt-yt\//,
          replacement: pathResolve('src/project/yjpt-yt') + '/',
        },
        {
          find: /\/@gis\//,
          replacement: pathResolve('src/project/gis') + '/',
        },
        {
          find: /\/@water-balance\//,
          replacement: pathResolve('src/project/water-balance') + '/',
        },
        {
          find: /\/@zoology-wzb\//,
          replacement: pathResolve('src/project/zoology-wzb') + '/',
        },
        {
          find: /\/@process-editor\//,
          replacement: pathResolve('src/project/process-editor') + '/',
        },
        {
          find: /\/@im\//,
          replacement: pathResolve('src/project/im') + '/',
        },
        {
          find: /\/@zoology-wzb\//,
          replacement: pathResolve('src/project/zoology-wzb') + '/',
        },
        // 运行时编译vue文件
        {
          find: 'vue',
          replacement: 'vue/dist/vue.esm-bundler.js',
        },
      ],
    },
    server: {
      https: false,
      // Listening on all local IPs
      host: true,
      port: 9003,
      // Load proxy configuration from .env
      proxy: createProxy(VITE_PROXY, VITE_GLOB_MONOCASE ? VITE_GLOB_MONOCASE_NAME : ''),
    },
    esbuild: {
      drop: VITE_DROP_CONSOLE ? ['console', 'debugger'] : [],
    },
    build: {
      target: 'es2015',
      cssTarget: 'chrome80',
      outDir: OUTPUT_DIR,
      // minify: 'terser',
      /**
       * 当 minify=“minify:'terser'” 解开注释
       * Uncomment when minify="minify:'terser'"
       */
      // terserOptions: {
      //   compress: {
      //     keep_infinity: true,
      //     drop_console: VITE_DROP_CONSOLE,
      //   },
      // },
      assetsDir: 'resource', // 静态文件目录
      emptyOutDir: true,
      reportCompressedSize: false,
      chunkSizeWarningLimit: 2000,

      rollupOptions: {
        output: {
          chunkFileNames: 'resource/js/[name]-[hash].js',
          entryFileNames: 'resource/js/[name]-[hash].js',
          assetFileNames: 'resource/[ext]/[name]-[hash].[ext]',
          // manualChunks(id) {
          //   if (id.includes('echarts')) return 'echarts';
          //   if (id.includes('diagram-js')) return 'diagram';
          //   if (id.includes('bpmn-js')) return 'bpmn';
          //   if (id.includes('monaco-editor')) return 'monaco-editor';
          //   if (id.includes('vue') || id.includes('pinia')) return 'vue';
          //   if (id.includes('ant-design')) return 'ant-design';
          //   if (id.includes('node_modules')) return 'vendor';
          // },
        },
        // external: ['ant-design-vue/es/autoComplete'],
      },
    },
    define: {
      __APP_INFO__: JSON.stringify(__APP_INFO__),
    },

    css: {
      devSourcemap: true,
      preprocessorOptions: {
        less: {
          modifyVars: generateModifyVars(),
          javascriptEnabled: true,
        },
      },
    },

    // The vite plugin used by the project. The quantity is large, so it is separately extracted and managed
    plugins: createVitePlugins(viteEnv, isBuild),

    optimizeDeps: {
      // @iconify/iconify: The dependency is dynamically and virtually loaded by @purge-icons/generated, so it needs to be specified explicitly
      include: [
        'echarts/core',
        'echarts/charts',
        'echarts/components',
        'echarts/renderers',
        'qrcode',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
      ],
    },
  };
};
