<template>
  <BasicModal
    v-bind="$attrs"
    :canFullscreen="false"
    title="展示设置"
    width="750px"
    wrapClassName="chart-settings-modal"
    @cancel="handleCancel"
    @register="registerModal"
  >
    <BasicTable
      ref="tableRef"
      :columns="columns"
      :dataSource="tableData"
      :showIndexColumn="false"
      :pagination="false"
      :canResize="false"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'label'">
          <span>
            {{ record.label + (record.unit ? '(' + record.unit + ')' : '') }}
          </span>
        </template>
        <template v-else-if="column.key === 'chartType'">
          <RadioGroup
            v-model:value="record.chartType"
            :options="chartOptions"
            @change="(e) => handleChangeType(e, index)"
          />
        </template>
        <template v-else-if="column.key === 'color'">
          <div style="display: flex; align-items: center">
            <!-- <a-button
              type="link"
              @click="openColorModal(record.color, index)"
              style="padding-left: 0"
            >
              选择
            </a-button> -->
            <!-- <div :style="{ width: '20px', height: '20px', background: record.color }"></div> -->
            <Input type="color" class="color-input" v-model:value="record.color" />
          </div>
        </template>
      </template>
    </BasicTable>
    <!-- <ColorModal @success="handleChooseColor" @register="registerColorModal" /> -->
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">保存</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup name="ChartSettingsModal">
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicTable } from '/@/components/Table';
  import { changeAssayTemplateChartConfig } from '/@zhcz/api/chemical';
  import { RadioGroup, Input } from 'ant-design-vue';
  import { cloneDeep } from 'lodash-es';
  // import { useModal } from '/@/components/Modal';
  // import ColorModal from './ColorModal.vue';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  // const [registerColorModal, { openModal }] = useModal();

  const okLoading = ref(false);
  const recordId = ref('');
  const columns = ref([
    {
      title: '化验项',
      dataIndex: 'label',
    },
    {
      title: '展示样式',
      dataIndex: 'chartType',
    },
    {
      title: '展示颜色',
      dataIndex: 'color',
    },
  ]);
  const tableData: any = ref([]);
  const tableRef: any = ref(null);
  const chartOptions = ref([
    {
      label: '折线图',
      value: 'line',
    },
    {
      label: '柱状图',
      value: 'bar',
    },
  ]);
  // const selectIndex = ref(0);

  const [registerModal, { closeModal }] = useModalInner((data) => {
    const record = cloneDeep(data.record);
    recordId.value = record.id;
    if (data.record.chartConfig) {
      tableData.value = JSON.parse(record.chartConfig);
    } else {
      tableData.value = record.config
        .filter((i) => i.projectId)
        .map((i) => {
          return {
            label: i.cells[0].value,
            unit: i.cells[1].value,
            chartType: 'line',
            color: '',
          };
        });
    }
  });

  function handleChangeType(e, index) {
    tableData.value[index].chartType = e.target.value;
  }

  // function handleChooseColor(color) {
  //   tableData.value[selectIndex.value].color = color;
  //   tableRef.value!.updateTableData(selectIndex.value, 'color', color);
  // }

  // function openColorModal(color, index) {
  //   selectIndex.value = index;
  //   openModal(true, {
  //     color,
  //   });
  // }

  async function handleSubmit() {
    try {
      okLoading.value = true;
      const data = {
        id: recordId.value,
        chartConfig: JSON.stringify(tableData.value),
      };
      await changeAssayTemplateChartConfig(data);
      createMessage.success('编辑成功');
      handleCancel();
      emit('success');
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    closeModal();
  }
</script>

<style lang="less" scoped>
  :deep(.ant-table-wrapper) {
    margin: 0 !important;
    padding: 0 !important;
  }

  .color-input {
    padding: 0 2px;
    width: 32px;
    cursor: pointer;
  }
</style>
