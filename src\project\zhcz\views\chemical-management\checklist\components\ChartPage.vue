<template>
  <BasicDetail class="checklist-detail" v-model:visible="visible">
    <div class="px-2">
      <div class="title">
        <div class="mark"></div>
        <span class="title-text">曲线</span>
      </div>
      <div class="py-4 text-base font-semibold text-center">{{ chartTitle }}</div>
      <Chart v-if="chartData.length" :data="chartData" height="378px" />
      <div class="title">
        <div class="mark"></div>
        <span class="title-text">表格</span>
      </div>
      <BasicTable
        :columns="tableColumns"
        :dataSource="tableData"
        :showIndexColumn="false"
        :pagination="false"
        :scroll="{ y: 250 }"
      />
    </div>

    <template #footer>
      <HActionBar
        @handle-methods="handleMethods"
        :btnList="[{ title: '返回', type: 'default', btntype: 'back' }]"
      />
    </template>
  </BasicDetail>
</template>

<script setup lang="ts">
  import { onMounted, ref, computed } from 'vue';
  import { BasicDetail } from '/@/components/Detail';
  import { HActionBar } from '/@/components/HActionBar';
  import Chart from './Chart.vue';
  import { BasicTable } from '/@/components/Table';
  import { roundAndConvert } from '/@zhcz/utils/number';

  type Item = {
    indexName: string;
    indexCode: string;
    unitName: string;
    data: { collectDateTime: string; value: number }[];
  };

  const props = defineProps({
    show: {
      type: Boolean,
    },
    chartTitle: {
      type: String,
      default: '',
    },
    chartData: {
      type: Array as PropType<Item[]>,
      default: () => [],
    },
    configData: {
      type: Object,
      default: () => {},
    },
    allProjects: {
      type: Array,
      default: () => [],
    },
  });

  const tableColumns = ref([]);
  const tableData = ref([]);
  // const showType = ref('table');

  const emits = defineEmits(['update:show', 'success']);

  const visible = computed({
    get() {
      return props.show;
    },
    set(val) {
      emits('update:show', val);
    },
  });

  function goBack() {
    emits('update:show', false);
  }

  const handleMethods = (type) => {
    switch (type) {
      case 'back': //返回
        goBack();
        break;
    }
  };

  onMounted(() => {
    tableColumns.value = props.configData.colsData.map((item, index) => {
      return {
        title: item.value,
        dataIndex: 'key' + index,
      };
    });
    tableData.value = props.configData.tableData.map((row) => {
      const res = {};
      if (!row.cells) {
        return null;
      }
      let projectId = row.projectId;
      let project = props.allProjects.find((proj) => proj.id === projectId);
      let decimalDigits = project ? project.decimalDigits : null;
      row.cells.forEach((item, index) => {
        let { value, unit } = item;
        if (index === 0) {
          res['key' + index] = unit ? value + '(' + unit + ')' : value;
        } else {
          res['key' + index] =
            decimalDigits === null
              ? !value && value !== 0
                ? '-'
                : value
              : roundAndConvert(value ? Number(value) : 0, decimalDigits);
        }
      });
      return res;
    });
    if (props.configData.rowDenormalized) {
      tableData.value.shift();
    }
  });
</script>

<style lang="less" scoped>
  .chart-page {
    padding: 0 16px 16px 16px;
    height: 100%;
    // display: flex;
    // flex-direction: column;
    // overflow-x: hidden;
    // overflow-y: auto;
    overflow: hidden;

    :deep(.vben-basic-table) {
      .ant-table-wrapper {
        margin: 0;
      }

      .ant-table-wrapper .ant-table-body::-webkit-scrollbar {
        width: 8px;
      }
    }

    .page-inner {
      width: 100%;
      height: 100%;
      background: #fff;
    }
  }
</style>

<style lang="less">
  .checklist-detail {
    .title {
      padding: 24px 0 16px 0;
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 1px solid #e9e9e9;
      color: #1f2329;

      .mark {
        width: 4px;
        height: 16px;
        background: @theme-color;
        display: inline-block;
        margin-right: 8px;
      }
    }

    .vben-basic-table .ant-table-wrapper {
      margin: 16px 0 0 0;
      padding: 0;
    }
  }
</style>
