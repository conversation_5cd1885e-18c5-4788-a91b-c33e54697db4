<template>
  <div class="page relative w-full h-full" :style="{ ...useWrapperStyle() }" v-loading="loading">
    <div class="search-form">
      <BasicForm @register="registerForm">
        <!-- <template #category="{ model, field }">
            <TreeSelect
              :tree-data="categoryList"
              show-search
              allow-clear
              placeholder="请选择分类"
              v-model:value="model[field]"
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              treeNodeFilterProp="name"
              @change="handleChangeCate"
            />
          </template>
          <template #device="{ model, field }">
            <Select
              show-search
              allow-clear
              v-model:value="model[field]"
              :options="deviceList"
              placeholder="请选择设备"
              optionFilterProp="label"
            />
          </template> -->
      </BasicForm>
      <!-- <div class="search-button">
        <a-button :icon="h(Icon, { icon: 'icon-park-outline:search' })" type="primary"
          >查询</a-button
        >
        <a-button type="default" :icon="h(Icon, { icon: 'icon-park-outline:refresh' })"
          >重置</a-button
        >
      </div> -->
    </div>
    <div class="content">
      <div class="bottom">
        <Row :gutter="16" style="height: 100%">
          <Col :span="8">
            <Card title="重点设备健康指数" :bordered="false">
              <KeyNote :keyNoteData="keyNoteData" :scoreList="scoreList" />
            </Card>
          </Col>
          <Col :span="8">
            <div class="mb-4" style="height: 395px">
              <Card :bordered="false">
                <template #title>
                  <div class="flex">
                    <span>设备健康分项得分情况</span>
                    <Tooltip placement="right">
                      <template #title>
                        <div
                          >核心指标：异常一次减20分<br />
                          故障次数：发生一次减50分<br />
                          保养情况：未按计划保养减50分<br />
                          使用情况：1年减5分<br />
                          总分=核心指标*0.4+故障次数*0.4+保养次数*0.1+使用年限*0.1</div
                        >
                      </template>
                      <img
                        style="width: 16px; height: 16px; margin: 0 4px 0 8px"
                        :src="tip"
                        alt=""
                      />
                    </Tooltip>
                  </div>
                </template>

                <ComprehensiveScore
                  :scoreLoading="scoreLoading"
                  :equipmentScoreList="equipmentScoreList"
                >
                  <template #page>
                    <div class="page-score" v-if="equipmentScoreList.length > 4">
                      <div
                        class="page-item page-left"
                        :class="pageIndex === 1 ? 'disabled' : 'active'"
                        @click="handlePrev"
                      >
                        <LeftOutlined />
                      </div>
                      <div
                        class="page-item page-right ml-4"
                        :class="pageIndex === Math.ceil(total / 4) ? 'disabled' : 'active'"
                        @click="handleNext"
                      >
                        <RightOutlined />
                      </div>
                    </div>
                  </template>
                </ComprehensiveScore>
              </Card>
            </div>
            <div style="height: calc(100% - 410px)">
              <Card title="设备类型详细分数" :bordered="false" style="padding-bottom: 16px">
                <DetailedScore ref="detailedScore" :equipmentDetailList="equipmentDetailList" />
              </Card>
            </div>
          </Col>
          <Col :span="8">
            <Card title="设备数量统计" :bordered="false">
              <Statistics
                v-if="equipmentStatus.length"
                :equipmentStatus="equipmentStatus"
                :equipmentTotal="equipmentTotal"
                :size="'large'"
              />
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="">
  import { Row, Col, Card, Tooltip } from 'ant-design-vue';
  // import { PageWrapper } from '/@/components/Page';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useWrapperStyle } from '/@zhcz/hooks/useWrapperStyle';
  import { schemas, scoreList } from './health.data';
  import Statistics from './components/Statistics.vue';
  import ComprehensiveScore from './components/ComprehensiveScore.vue';
  import DetailedScore from './components/DetailedScore.vue';
  import KeyNote from './components/KeyNote.vue';
  import { ref, onMounted } from 'vue';
  import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
  //import { getEquipmentList } from '/@zhcz/api/equipment-ledger/table';
  // import dayjs from 'dayjs';
  import tip from '/@zhcz/assets/images/tip.png';

  import {
    //GetEquipmentCategoryTreeListApi,
    getEquipmentHealth,
    getEquipmentTotalCount,
    getEquipmentStatusCount,
    getEquipmentCategoryScore,
    getEquipmentCategoryDetailScore,
  } from '/@zhcz/api/device-category/table';

  const [registerForm, { getFieldsValue }] = useForm({
    schemas: schemas,
    showActionButtonGroup: true,
    actionColOptions: {
      span: 20,
    },
    submitFunc: handleSearch,
    resetFunc: init,
  });
  // const categoryList = ref<any>([]);
  // async function getCateList() {
  //   const res = await GetEquipmentCategoryTreeListApi();
  //   categoryList.value = res;
  // }
  // const deviceList = ref<any>([]);
  // async function getDeviceList(equipmentCategoryId) {
  //   const res = await getEquipmentList({ equipmentCategoryId });
  //   deviceList.value = res.map((i) => ({ ...i, label: i.name, value: i.id }));
  // }

  // function handleChangeCate(e) {
  //   nextTick(() => {
  //     setFieldsValue({
  //       equipmentId: null,
  //     });
  //     getDeviceList(e);
  //   });
  // }

  const loading = ref(false);
  // const isUpdate = ref(true);
  async function handleSearch() {
    loading.value = true;
    try {
      const value = getFieldsValue();

      await Promise.all([
        getKeyNoteData(value),
        getEquipmentTotal(value),
        getEquipmentStatus(value),
        getEquipmentScoreList(value),
        getEquipmentDetailScore(value),
      ]);
    } finally {
      loading.value = false;
    }
  }
  // 重点设备健康指数
  const keyNoteData = ref(0);
  // const isGetData = ref(false);
  async function getKeyNoteData(value) {
    // const value = getFieldsValue();
    const res = await getEquipmentHealth(value);
    keyNoteData.value = res;
    // isGetData.value = true;
  }
  // 设备统计
  const equipmentTotal = ref(0);
  const equipmentStatus = ref([]);
  async function getEquipmentTotal(value) {
    const res = await getEquipmentTotalCount(value);
    equipmentTotal.value = res;
  }
  async function getEquipmentStatus(value) {
    const res = await getEquipmentStatusCount(value);
    equipmentStatus.value = res || [];
  }

  // 设备类型综合分数
  const pageIndex = ref(1);
  const total = ref(0);
  const equipmentScoreList = ref([]);
  const scoreLoading = ref(false);
  function handlePrev() {
    if (pageIndex.value === 1) {
      return;
    }
    pageIndex.value--;
    getEquipmentScoreList();
  }
  function handleNext() {
    if (pageIndex.value === Math.ceil(total.value / 4)) {
      return;
    }
    pageIndex.value++;
    getEquipmentScoreList();
  }
  async function getEquipmentScoreList(value = {}) {
    scoreLoading.value = true;
    try {
      const res = await getEquipmentCategoryScore({
        current: pageIndex.value,
        ...value,
        size: 4,
      });
      equipmentScoreList.value = res.slice(0, 4);
      total.value = res.length;
    } finally {
      scoreLoading.value = false;
    }
  }
  // 设备类型详细分数
  const equipmentDetailList = ref([]);
  const detailedScore = ref<any>(null);
  async function getEquipmentDetailScore(value) {
    const res = await getEquipmentCategoryDetailScore(value);
    equipmentDetailList.value = res;

    detailedScore.value.setTableData(res);
  }

  async function init() {
    // await getCateList();
    // let id = null;
    // if (categoryList.value && categoryList.value.length) {
    //   id = categoryList.value[0].id;
    //   setFieldsValue({
    //     equipmentCategoryId: id,
    //   });
    // }
    // setFieldsValue({
    //   equipmentCategoryId: id,
    //   dateTime: `${dayjs().format('YYYY-MM-DD')}`,
    // });
    // await getDeviceList(id);
    handleSearch();
  }

  onMounted(async () => {
    init();
  });
</script>

<style lang="less" scoped>
  // @media screen and (min-width: 1680px) {
  //   .page {
  //     overflow-y: auto !important;
  //   }
  // }

  .page {
    overflow-y: hidden;
    // display: flex;
    // flex-direction: column;
    // justify-content: flex-start;
  }

  .page-score {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 12px;

    .page-item {
      width: 32px;
      height: 32px;
      line-height: 32px;
      background: #ffffff;
      border-radius: 50%;
      opacity: 1;
      border: 1px solid rgba(217, 217, 217, 0.5);
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #333;

      .anticon {
        margin-right: 0;
      }

      img {
        width: 12px;
        height: 12px;
      }
    }

    .active {
      &:hover {
        background: rgba(45, 130, 254, 0.05);
        border: 1px solid @theme-color;
        color: @theme-color;
      }
    }

    .disabled {
      background: #ffffff;
      color: rgba(0, 0, 0, 0.351);
      border: 1px solid rgba(217, 217, 217, 0.5);
      cursor: not-allowed;
    }
  }

  .search-form {
    position: relative;
    padding: 0 16px;
    height: 64px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    background-color: #fff;

    :deep(.ant-form) {
      width: 100%;

      .ant-form-item {
        margin-bottom: 0;
      }

      .ant-form-item-control-input-content {
        .ant-btn-default {
          &.mr-2 {
            margin-right: 0;
          }
        }
      }
    }

    .search-button {
      position: absolute;
      display: flex;
      gap: 0 16px;
      top: 16px;
      right: 16px;
    }
  }

  .content {
    margin-top: 12px;
    height: calc(100% - 76px);
    // display: flex;
    // flex-direction: column;
    // gap: 16px;
    // flex: 1;

    .bottom {
      height: 100%;
      overflow: hidden;
      background: rgb(240, 242, 245) !important;

      :deep(.ant-col) {
        overflow: hidden;
        height: 100%;
      }
    }

    :deep(.ant-card) {
      height: 100%;
      line-height: 1;
      background: #fff;
      box-shadow: none;
      // border-bottom-left-radius: 4px;
      // border-bottom-right-radius: 4px;

      .ant-card-head {
        font-weight: 600;
        color: #333;
        border-bottom: 1px solid #e9e9e9;
      }

      .ant-card-body {
        padding: 16px;
        height: calc(100% - 49px);
        overflow: hidden;
      }
    }
  }
</style>
