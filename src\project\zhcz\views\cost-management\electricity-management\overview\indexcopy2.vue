<template>
  <div class="overview-box h-full overflow-y overflow-x" style="overflow-y: auto">
    <div class="content">
      <Row :gutter="12" style="height: calc(50% - 6px)">
        <Col :span="8" style="height: 100%">
          <div class="content-item content-item-t">
            <EnergySummary
              v-if="indexCodeDataList[0].resourceInterfaceId"
              :dayResourceInterfaceId="indexCodeDataList[0].resourceInterfaceId"
              :dayGroupCode="indexCodeDataList[0].groupCode"
              :monthResourceInterfaceId="indexCodeDataList[1].resourceInterfaceId"
              :monthGroupCode="indexCodeDataList[1].groupCode"
            />
          </div>
        </Col>
        <Col :span="16" style="height: 100%">
          <div class="content-item content-item-t">
            <RingRatio
              v-if="indexCodeDataList[2].resourceInterfaceId"
              :dayResourceInterfaceId="indexCodeDataList[2].resourceInterfaceId"
              :dayGroupCode="indexCodeDataList[2].groupCode"
              :monthResourceInterfaceId="indexCodeDataList[3].resourceInterfaceId"
              :monthGroupCode="indexCodeDataList[3].groupCode"
            />
          </div>
        </Col>
      </Row>
      <Row :gutter="0" style="height: calc(50% - 6px); margin-top: 12px">
        <Col :span="8" style="height: 100%">
          <div class="content-item content-item-b">
            <powerRanking
              @setItem="setItem"
              v-if="indexCodeDataList[6].resourceInterfaceId"
              :dayResourceInterfaceId="indexCodeDataList[6].resourceInterfaceId"
              :dayGroupCode="indexCodeDataList[6].groupCode"
              :monthResourceInterfaceId="indexCodeDataList[7].resourceInterfaceId"
              :monthGroupCode="indexCodeDataList[7].groupCode"
          /></div>
        </Col>
        <Col :span="16" style="height: 100%">
          <div class="content-item content-item-c">
            <PeakFlatValley :itemVal="itemVal" />
          </div>
        </Col>
      </Row>
    </div>
  </div>
</template>

<script setup lang="ts" name="ElectricityManagementOverview">
  import { onMounted, ref } from 'vue';
  import { Row, Col } from 'ant-design-vue';

  import EnergySummary from './components/EnergySummary.vue';
  import RingRatio from './components/RingRatio.vue';
  import PeakFlatValley from './components/PeakFlatValley.vue';
  import powerRanking from './components/powerRanking.vue';
  import { getSenceGroupTree } from '/@zhcz/api/scenes-group';

  const indexCodeDataList = ref<{ resourceInterfaceId: string; groupCode: string }[]>([]);

  const initIndexCOdeDataList = () => {
    const initData = Array.from({ length: 10 }, () => {
      return {
        resourceInterfaceId: '',
        groupCode: '',
      };
    });
    indexCodeDataList.value = initData;
  };
  initIndexCOdeDataList();
  const itemVal = ref<{ value: string; date: string }>();
  function setItem(val) {
    itemVal.value = val;
  }
  const setGroupCode = async () => {
    // 总电量统计-日，月环比
    indexCodeDataList.value[0] = {
      resourceInterfaceId: '3',
      groupCode: 'zdltj_rhb',
    };
    indexCodeDataList.value[1] = {
      resourceInterfaceId: '3',
      groupCode: 'zdltj_yhb',
    };
    // 电耗统计-日，月
    indexCodeDataList.value[2] = {
      resourceInterfaceId: '3',
      groupCode: 'dhtj_r',
    };
    indexCodeDataList.value[3] = {
      resourceInterfaceId: '3',
      groupCode: 'dhtj_y',
    };
    // 电量排行-日，月
    indexCodeDataList.value[4] = {
      resourceInterfaceId: '3',
      groupCode: 'dlph_r',
    };
    indexCodeDataList.value[5] = {
      resourceInterfaceId: '3',
      groupCode: 'dlph_y',
    };
    // 分类用电统计-日，月
    indexCodeDataList.value[6] = {
      resourceInterfaceId: '3',
      groupCode: 'flydtj_r',
    };
    indexCodeDataList.value[7] = {
      resourceInterfaceId: '3',
      groupCode: 'flydtj_y',
    };
    // 峰平谷用电
    indexCodeDataList.value[8] = {
      resourceInterfaceId: '3',
      groupCode: 'nhzl_fpgyd',
    };
    // 环境监测
    indexCodeDataList.value[9] = {
      resourceInterfaceId: '3',
      groupCode: 'hjjc_shj',
    };
  };

  // 查找温度监测下面配置的选项
  const EnvironmentOptions = ref([]);

  function flattenChildren(nodes) {
    if (!Array.isArray(nodes)) return [];
    const result = [];
    nodes.forEach((node) => {
      if (!node || typeof node !== 'object') return;
      result.push(node);
      if (Array.isArray(node.children)) {
        result.push(...flattenChildren(node.children));
      }
    });

    return result;
  }

  async function getSenceGroupTreeFn() {
    let data = await getSenceGroupTree();
    data = data || [];
    const flattenData = flattenChildren(data);
    EnvironmentOptions.value = flattenData.find((i) => i.groupCode === 'nhzl_hjjc')?.children || [];
    console.log('EnvironmentOptions.value', EnvironmentOptions.value);
  }

  onMounted(() => {
    setGroupCode();
    getSenceGroupTreeFn();
  });
</script>
<script lang="ts">
  export default { name: 'CostManagementElectricityManagementOverview' };
</script>
<style lang="less" scoped>
  .overview-box {
    padding: 0 16px 16px;
    height: 100%;
    overflow: auto;

    .content {
      height: 100%;

      :deep(.ant-col) {
        height: 100%;
      }

      .content-item {
        background-color: #fcfcfc;
        height: 100%;

        overflow: hidden;

        :deep(.ant-card) {
          box-shadow: none;
        }
      }

      .content-item-t {
        border-radius: 4px;
      }

      .content-item-c {
        border-bottom-right-radius: 4px;
        border-top-right-radius: 4px;
      }

      .content-item-b {
        border-bottom-left-radius: 4px;
        border-top-left-radius: 4px;
        border-right: 1px solid #d8d8d8;
      }
    }
  }
</style>
