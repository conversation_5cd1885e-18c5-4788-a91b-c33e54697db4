<template>
  <Container title="待办事项" height="350px" :class="prefixCls">
    <template #title-after>
      <a-button type="link" v-if="morePath" @click="go(morePath)" style="padding: 0">更多</a-button>
    </template>
    <div class="relative h-[100%] px-20px" v-loading="loading">
      <Empty desc="暂无待办事项" v-if="!flowList.length && !loading" />
      <Table :columns="columns" :data-source="flowList" :pagination="false" v-if="flowList.length">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'WorkItemName'">
            <div
              style="color: var(--theme-color); cursor: pointer"
              class="truncate"
              @click="handleCheck(record)"
            >
              {{ record.WorkItemName }}
            </div>
          </template>
        </template>
      </Table>
    </div>
    <FlowListModal ref="flowListModalRef" @success="handleFlowSuccess" />
  </Container>
</template>

<script lang="ts" setup>
  import { Table } from 'ant-design-vue';
  import Empty from '../components/Empty.vue';
  import Container from '../components/Container.vue';
  import { ref, onActivated } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { searchHomeFlowList } from '/@/api/flow/flow';
  import FlowListModal from './FlowListModal.vue';
  import { ParamsKeyEnum } from '../enum';
  import { getParamKeyApi } from '/@/api/admin/param';
  import { useGo } from '/@/hooks/web/usePage';

  defineOptions({
    name: 'FlowList',
  });

  const { prefixCls } = useDesign('FlowList-Box');
  const loading = ref(true);
  const flowList = ref<any[]>([]);
  const flowListModalRef = ref();
  const morePath = ref();
  const go = useGo();

  function handleCheck(record: any) {
    flowListModalRef.value?.openFlowDetail(record);
  }

  function handleFlowSuccess() {
    getFlowList();
  }

  const columns = [
    {
      title: '文件名称',
      dataIndex: 'WorkItemName',
      key: 'WorkItemName',
      ellipsis: { showTitle: false },
    },
    {
      title: '拟稿人',
      dataIndex: 'Creator',
      key: 'Creator',
      ellipsis: { showTitle: false },
    },
    {
      title: '接收时间',
      dataIndex: 'InceptDate',
      key: 'InceptDate',
      width: 280,
      ellipsis: { showTitle: false },
    },
  ];

  const getFlowList = async () => {
    try {
      const res = await searchHomeFlowList({
        current: 1,
        type: 0,
        size: 5,
      });
      flowList.value = res.records;
    } finally {
      loading.value = false;
    }
  };

  const getMorePath = async () => {
    morePath.value = await getParamKeyApi(ParamsKeyEnum.FLOW_BACKLOG_PATH);
  };

  getMorePath();
  getFlowList();

  onActivated(() => {
    getFlowList();
  });
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-FlowList-Box';

  .@{prefix-cls} {
    :deep(.ant-table-wrapper) {
      height: 100%;

      .ant-table-cell {
        padding: 0 24px 0 11px;
        height: 46px;
      }

      .ant-table-thead {
        .ant-table-cell {
          height: 48px;
        }
      }

      .ant-table-row {
        .ant-table-cell {
          transition: all 0.3s;
          background-color: transparent !important;
        }

        &:hover .ant-table-cell {
          background-color: #f0f0f0 !important;
        }

        &:last-of-type {
          .ant-table-cell {
            border-bottom: none;
          }
        }
      }
    }
  }
</style>
