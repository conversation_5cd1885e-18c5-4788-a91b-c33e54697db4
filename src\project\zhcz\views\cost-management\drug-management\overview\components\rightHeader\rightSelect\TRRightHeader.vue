<template>
  <div class="flex-center">
    <div class="button_ai" v-if="indexList.length > 1" @click="handleCreate()"
      ><img :src="aiImg" alt="" srcset="" /><span class="text">AI分析</span></div
    >
    <Select
      v-if="false"
      style="width: 80px"
      v-model:value="dataType"
      @change="handleChangeIndicator"
    >
      <SelectOption v-for="item in dataList" :key="item.value" :value="item.value">
        {{ item.label }}
      </SelectOption>
    </Select>
  </div>
  <HAiDrawer @register="registerDrawer" :aiQuestion="aiDataCopy[0].aiQuestion" />
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { SelectOption, Select } from 'ant-design-vue';
  import { mockEnergyAllStatisticsData } from '../../../data';
  import { zyytjData } from '/@zhcz/api/cost-management';
  import aiImg from '../../../AI.png';
  import { HAiDrawer } from '/@zhcz/components/HAiDrawer/index';
  import { useDrawer } from '/@/components/Drawer';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';
  const [registerDrawer, { openDrawer }] = useDrawer();
  function handleCreate() {
    openDrawer(true, {
      record: {},
      isUpdate: false,
    });
  }
  const aiDataCopy = ref([
    {
      aiQuestion:
        '自来水厂药耗总览中，昨日用药量为_kg，前日用药量为_kg；近七日用药量为_kg，前七日用药量为_kg；\n上月用药量为_kg，上上月用药量为_kg；今年用药量为_kg，去年用药量为_kg。\n综合以上对比分析药耗情况。',
      deepAnalysis:
        '好的，用户问对比分析药耗情况的原因，我需要详细分析。/n首先，我得回忆一下浊度的定义，素全面分析.\n可能需要进一步的信息来确定具体原因，但先列出这些可能性。\n帮助用户排查。',
      explain:
        '对比分析药耗情况通常由多种因素引起，具体原因需结合环境、人为活动和水处理流程综合分析。以下是常见原因分类：',
      resultTitle: '总览分析',
      resultDes: '',
      summaryTitle: '总结',
      summary:
        '若药耗持续超标，可能影响水质安全（如隐藏病原微生物），建议及时联系水务部门或环保机构介入调查。',
    },
  ]);

  const dataType = ref<any>(null);
  type OptionItem = {
    label: string;
    value: string;
  };
  const dataList = ref<OptionItem[]>([
    // {
    //   label: '全部',
    //   value: 1,
    // },
    // {
    //   label: '固体',
    //   value: 2,
    // },
    // {
    //   label: '液体',
    //   value: 3,
    // },
  ]);

  // const loading = ref(false);
  const indexList = ref<any[]>([mockEnergyAllStatisticsData]);

  async function getTimeList() {
    const res = await listSenceGroupByParent({
      // dhzl2_dlph
      groupCode: 'yhzl2_zyytj',
      factoryId: 1,
      platformld: 1,
    });
    console.log('res.data', res);
    if (Object.keys(res).length) {
      dataType.value = Object.keys(res)[0];
      dataList.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      getData();
    }
  }
  const emit = defineEmits(['setsummarydata']);
  const getData = async () => {
    try {
      const res = await zyytjData({ factoryId: 1 });
      console.log('res', res);
      indexList.value = res;
      const copyData = indexList.value.map((item) => {
        const { value, preVal, indexName, indexPreName, unitName } = item;
        return (
          indexName +
          (value ? value : '') +
          unitName +
          indexPreName +
          (preVal ? preVal : '') +
          unitName +
          '\n'
        );
      });
      aiDataCopy.value[0].aiQuestion = `自来水厂药耗总览中，${copyData.join(
        ',',
      )}。综合以上对比分析药耗情况。`;
      emit('setsummarydata', { dataList: indexList.value });
    } catch (err) {
      emit('setsummarydata', { dataList: [] });
      console.log(err);
    }
  };
  getTimeList();
  const handleChangeIndicator = () => {
    getData();
  };
</script>
<style lang="less" scoped>
  .flex-center {
    display: flex;
    align-items: center;
    justify-self: start;

    .button_ai {
      margin-left: 8px;
      padding: 5px 8px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      cursor: pointer;

      img {
        width: 16px;
      }

      .text {
        font-size: 14px;
        color: #0b62cb;
        line-height: 14px;
        padding-left: 4px;
        font-weight: 600;
      }

      &:hover {
        background: rgba(11, 98, 203, 0.12);
      }
    }
  }
</style>
