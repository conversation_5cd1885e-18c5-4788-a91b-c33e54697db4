// mib
import { defZhczHttp } from '/@/utils/http/axios';

enum Api {
  AlgaeOnlineMonitoring = '/mib/algaeOnlineMonitoring/data', // 获取藻类在线监测数据
  HistoryMonitoringChartData = '/mib/historyMonitoring/chart', // 获取历史监测数据柱状图
  BrominePredictions = '/mib/warn/main/bromine-predictions', // 主页接口 ?date=&deviceId=002
  GetLastRecord = '/mib/warn/main/get-last-record', // 获取最新一条告警信息?deviceId=002
  WaterMonitorList = '/mib/warn/main/water-monitor-list', // 主页指标点击其他指标返回的柱状图接口?startDate=&endDate=&deviceId=002&monitorType= 1-PH值 2-温度  3-游离氧 4-浑浊度 5-叶绿体
  BrominePredictionsList = '/mib/warn/main/bromine-predictions-list', //  未来预测?startDate=&endDate=&deviceId=002
  Main2MibPredictList = '/mib/warn/main/2-mib-predict-list', // 点击未来预测数据返回柱状图接口 ?startDate=&endDate=&deviceId=002
  PageCollect = '/mib/warn/main/page-collect', // 预测人工录入列表?startDate=2023-03-01&endDate=2025-03-01&deviceId=002
  UpdateCollect = '/mib/warn/main/update-collect', // 预测人工录入更新
  PageWarnRecord = '/mib/warn/main/page-warn-record', // 告警记录列表分页 ?pageNum=1&pageSize=10&deviceId=002&startDate=2023-03-01&endDate=2025-08-01
}

export const algaeOnlineMonitoring = (data) =>
  defZhczHttp.get<any>({
    url: Api.AlgaeOnlineMonitoring,
    params: data,
  });

export const historyMonitoringChartData = (data) =>
  defZhczHttp.get<any>({
    url: Api.HistoryMonitoringChartData,
    params: data,
  });

export const brominePredictions = (data) =>
  defZhczHttp.get<any>({
    url: Api.BrominePredictions,
    params: data,
  });

export const getLastRecord = (data) =>
  defZhczHttp.get<any>({
    url: Api.GetLastRecord,
    params: data,
  });

export const waterMonitorList = (data) =>
  defZhczHttp.get<any>({
    url: Api.WaterMonitorList,
    params: data,
  });
export const updateCollect = (data) =>
  defZhczHttp.post<any>({
    url: Api.UpdateCollect,
    data,
  });
export const brominePredictionsList = (data) =>
  defZhczHttp.get<any>({
    url: Api.BrominePredictionsList,
    params: data,
  });
export const main2MibPredictList = (data) =>
  defZhczHttp.get<any>(
    {
      url: Api.Main2MibPredictList,
      params: data,
      // responseType: 'blob',
    },
    // { isTransformResponse: false },
  );
export const pageCollect = (data) =>
  defZhczHttp.get<any>(
    {
      url: Api.PageCollect,
      params: data,
      // responseType: 'blob',
    },
    // { isTransformResponse: false },
  );

export const pageWarnRecord = (data) =>
  defZhczHttp.get<any>(
    {
      url: Api.PageWarnRecord,
      params: data,
      // responseType: 'blob',
    },
    // { isTransformResponse: false },
  );
// export const getMaintenancePlanDetail = (data: { id: number }) =>
//   defZhczHttp.get<any>({
//     url: Api.GetMaintenancePlanDetail,
//     params: data,
//   });

// export const multipleDeleteMaintenancePlan = (data: { ids: number[] }) =>
//   defZhczHttp.post<any>({
//     url: Api.MultipleDeleteMaintenancePlan,
//     data,
//   });
