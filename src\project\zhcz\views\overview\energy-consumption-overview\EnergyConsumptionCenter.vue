<template>
  <div class="energy-center">
    <CenterBoxContainer>
      <template #info>
        <CenterInfo :data-resource-code="indexList?.index_CT[0]?.groupCode">
          <CenterInfoItem
            v-for="(item, index) in state.topData"
            :key="index"
            :data="item"
            :index="index"
            type="能耗总览"
            :empty="state.topData.every((i) => i.value === null || i.value === undefined)"
          />
        </CenterInfo>
      </template>
      <template #model>
        <CenterModel
          v-if="state.bottomData.length"
          :dataList="state.bottomData"
          :data-resource-code="indexList?.index_CB[0]?.groupCode"
        />
      </template>
    </CenterBoxContainer>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, watch, onUnmounted } from 'vue';
  import dayjs from 'dayjs';
  import { getEevReturnDomain } from '/@zhcz/utils/file/url';
  import { useIndexList } from '../hooks';
  import { energyCenterEmptyData } from './data';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { useIntervalFn30S } from '/@zhcz/utils/useInterval';

  import CenterBoxContainer from '../components/box-container/CenterBoxContainer.vue';
  import CenterInfo from '../components/center/center-info/index.vue';
  import CenterInfoItem from '../components/center/center-info/CenterInfoItem.vue';
  import CenterModel from '../components/center/center-model/index.vue';

  const { indexList } = useIndexList();

  const state = reactive({
    topData: JSON.parse(JSON.stringify(energyCenterEmptyData)),
    bottomData: [],
  });

  async function getTopData() {
    const tempParams = indexList.value.index_CT[0];
    const params = {
      // startDateTime: dayjs().format('YYYY-MM-DD 00:00:00'),
      // endDateTime: null,
      startDateTime: dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'),
      endDateTime: dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD 00:00:00'),
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      ...tempParams,
      paramsData: JSON.stringify(params),
    };

    const { data } = await callResourceFunction(paramData);
    if (data && data.length) {
      state.topData = data.map((i, index) => {
        return {
          indexName: i.indicatorsByTimeIntervalResp.length
            ? i.indicatorsByTimeIntervalResp[0]?.indexName
            : energyCenterEmptyData[index].indexName,
          value: i.indicatorsByTimeIntervalResp.length
            ? i.indicatorsByTimeIntervalResp[0]?.data.reduce((prev, cur) => {
                return prev + cur.value;
              }, 0)
            : 0,
          unitName: i.indicatorsByTimeIntervalResp.length
            ? i.indicatorsByTimeIntervalResp[0]?.unitName
            : energyCenterEmptyData[index].unitName,
          src: i.imgByTimeIntervalResps[0]?.sourceUniqueKey,
        };
      });
    } else {
      state.topData = energyCenterEmptyData;
    }
  }

  async function getBottomData() {
    const tempParams = indexList.value.index_CB[0];
    const params = {
      // startDateTime: dayjs().format('YYYY-MM-DD 00:00:00'),
      // endDateTime: null,
      // indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    const paramData = {
      ...tempParams,
      paramsData: JSON.stringify(params),
    };

    const { data } = await callResourceFunction(paramData);
    if (data && data.length) {
      const newData = data.map((i) => {
        const index = i.indicatorsByTimeIntervalResp.map((j) => {
          return {
            indexName: j.indexName,
            unitName: j.unitName,
            value: j.data instanceof Array ? Number(j.data[0]?.value) || 0 : 0,
          };
        });
        return {
          id: i.imgByTimeIntervalResps[0].id,
          text: i.imgByTimeIntervalResps[0].displayName,
          src: getEevReturnDomain(i.imgByTimeIntervalResps[0].sourceUniqueKey),
          mediumType: i.imgByTimeIntervalResps[0].mediumType,
          index,
        };
      });
      state.bottomData = newData;
    } else {
      state.bottomData = [];
    }
  }

  const getData = () => {
    getTopData();
    getBottomData();
  };

  watch(
    () => indexList.value,
    () => {
      getData();
    },
  );

  const { pause } = useIntervalFn30S(getData);

  onUnmounted(() => {
    pause();
  });
</script>

<style lang="less" scoped>
  .energy-center {
    width: 100%;
    height: 100%;
  }
</style>
