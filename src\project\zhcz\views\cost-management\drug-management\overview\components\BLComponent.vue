<template>
  <div class="blc">
    <HlxbPieSimpleCard
      v-bind="{
        bottomList: indexList,
        priceList,
        empty,
        loading,
        tagFlag: true,
        titleText: `总用药(${indexList[0]?.unitName ?? ''})`,
      }"
      :title="'药耗占比'"
    >
      <template #headerLeftAfter>
        <div class="button_ai" v-if="!empty" @click="handleCreate()"
          ><img :src="aiImg" alt="" srcset="" /><span class="text">AI分析</span></div
        ></template
      >

      <template #headerRight>
        <div class="header-right">
          <DatePicker
            style="width: 200px"
            v-model:value="monthDate"
            picker="month"
            :allowClear="false"
            valueFormat="YYYY-MM"
            :disabledDate="disabledDateMonth"
          />
        </div>
      </template>
    </HlxbPieSimpleCard>
    <HAiDrawer @register="registerDrawer" :aiQuestion="aiDataCopy[0].aiQuestion" />
  </div>
</template>
<script lang="ts" setup>
  // import { PieSimpleCard } from '/@zhcz/components/HLCardComponent';
  import { HlxbPieSimpleCard } from 'hlxb-ui';
  import { DatePicker } from 'ant-design-vue';
  import { ref, watch } from 'vue';
  import dayjs from 'dayjs';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import aiImg from '../AI.png';
  import { HAiDrawer } from '/@zhcz/components/HAiDrawer/index';
  import { useDrawer } from '/@/components/Drawer';
  const [registerDrawer, { openDrawer }] = useDrawer();
  function handleCreate() {
    openDrawer(true, {
      record: {},
      isUpdate: false,
    });
  }
  const aiDataCopy = ref([
    {
      aiQuestion:
        '自来水厂药耗占比中，2025年3月，碱铝_kg占比_%预估_元、次氯酸钠_kg占比_%预估_元、石灰_kg占比_%预估_元、高锰酸钾_kg占比_%预估_元、PAM_kg占比_%预估_元，综合以上内容分析当前情况。',
      deepAnalysis:
        '好的，用户问药耗占比的原因，我需要详细分析。首先，我得回忆一下浊度的定义，素全面分析.\n可能需要进一步的信息来确定具体原因，但先列出这些可能性。\n帮助用户排查。',
      explain:
        '药耗占比通常由多种因素引起，具体原因需结合环境、人为活动和水处理流程综合分析。以下是常见原因分类：',
      resultTitle: '总览分析',
      resultDes: '',
      summaryTitle: '总结',
      summary:
        '若药耗占比不合理，可能影响水质安全（如隐藏病原微生物），建议及时联系水务部门或环保机构介入调查。',
    },
  ]);
  const colorList = [
    'rgba(46, 140, 255, 1)',
    'rgba(46, 196, 255, 1)',
    'rgba(129, 67, 255, 1)',
    'rgba(34, 205, 128, 1)',
    'rgba(118, 195, 31, 1)',
    'rgba(255, 140, 46, 1)',
  ];
  const loading = ref(false);
  const empty = ref(false);
  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      value: number;
    }[]
  >([]);
  const dataType = ref(2);
  const monthDate = ref(dayjs().subtract(1, 'month').format('YYYY-MM'));
  const dayDate = ref(dayjs().format('YYYY-MM-DD'));
  const priceList = ref<{ label: string; value: number; remarks: number; unit: string }[]>([]);
  const getprice = async () => {
    try {
      const data = await getDictTypeListApi({
        type: 'yhdj',
      });
      if (data.length) {
        priceList.value = data.map((item) => {
          const { label, value, remarks } = item;
          if (Number(label) < 10) {
            return {
              value: Number(value),
              label,
              unit: '元',
              remarks: Number(remarks),
            };
          } else {
            return {
              value: Number(value),
              label,
              unit: '元',
              remarks: Number(remarks),
            };
          }
        });
      }
    } catch (err) {}
  };

  function disabledDateMonth(current) {
    return current && current > dayjs().subtract(0, 'month').endOf('day');
  }

  const getData = async () => {
    try {
      empty.value = false;
      loading.value = true;
      const startDateTime =
        dataType.value === 1
          ? dayjs(dayDate.value).format('YYYY-MM-DD 00:00:00')
          : dayjs(monthDate.value).startOf('month').format('YYYY-MM-DD 00:00:00');
      const endDateTime =
        dataType.value === 1
          ? dayjs(dayDate.value).format('YYYY-MM-DD 23:59:59')
          : dayjs(monthDate.value).endOf('month').format('YYYY-MM-DD 23:59:59');
      const params = {
        startDateTime: startDateTime,
        endDateTime: endDateTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        resourceInterfaceId: '3',
        groupCode: 'yhzl2_yhzb_y',
        paramsData: JSON.stringify(params),
      };

      const { data } = await callResourceFunction(paramData);
      loading.value = false;
      console.log('药耗占比data', data);
      if (data && data.length) {
        empty.value = false;
        let newData = data.map((item, index) => {
          const price = priceList.value.find((i) => i.label === item.indexCode);
          return {
            indexName: item.indexName,
            indexCode: item.indexCode,
            unitName: item.unitName,
            price: price?.value,
            value: item.data
              .map((i) => Number(i.value))
              .reduce((prev, cur) => {
                return prev + cur;
              }, 0),
            color: colorList[index % 8],
            ratio: 0,
          };
        });
        const total = newData
          .map((i) => i.value)
          .reduce((prev, cur) => {
            return prev + cur;
          }, 0);
        if (total) {
          newData = newData.map((item) => {
            item.ratio = ((item.value / total) * 100).toFixed(2);
            return item;
          });
        }
        indexList.value = newData;
        console.log('药耗占比indexList', indexList.value);
        const copyData = newData.map((item) => {
          const { indexName, price, unitName, value, ratio } = item;
          return (
            indexName + value + unitName + '占比' + ratio + '%预估' + price * value + '元' + '\n'
          );
        });
        aiDataCopy.value[0].aiQuestion = `自来水厂药耗占比中，${dayjs(monthDate.value).format(
          'YYYY年MM月',
        )}，${copyData.join(',')}综合以上内容分析当前情况。`;
      } else {
        indexList.value = [];
        empty.value = true;
      }
    } catch (err) {
      empty.value = true;
      loading.value = false;
    }
  };

  // const { pause, resume } = useIntervalFn(getData, 60 * 1000);
  // const userStore = useUserStore();
  // const token = computed(() => userStore.getToken);
  // watch(
  //   () => token.value,
  //   (newVal) => {
  //     if (!newVal) {
  //       pause();
  //     }
  //   },
  // );

  // watch(
  //   () => dayDate.value,
  //   async () => {
  //     if (dataType.value === 1) {
  //       pause();
  //       await getData();
  //       resume();
  //     }
  //   },
  // );

  watch(
    () => monthDate.value,
    async (newVal) => {
      if (dataType.value === 2) {
        console.log('newVal=>月', newVal);
        // pause();
        await getData();
        // resume();
      }
    },
  );

  watch(
    () => dataType.value,
    async (newVal) => {
      console.log('newVal=>日期类型', newVal);
      monthDate.value = dayjs().subtract(1, 'month').format('YYYY-MM');
      dayDate.value = dayjs().format('YYYY-MM-DD');
      // pause();
      await getData();
      // resume();
    },
  );

  getprice();
  getData();
</script>
<style lang="less" scoped>
  .blc {
    background-color: #fcfcfc;
    height: 100%;
    overflow: hidden;
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
    border-right: 1px solid #d8d8d8;

    .header-right {
      display: flex;
      gap: 0 16px;
    }

    .button_ai {
      margin-left: 8px;
      padding: 5px 8px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      cursor: pointer;

      img {
        width: 16px;
      }

      .text {
        font-size: 14px;
        color: #0b62cb;
        line-height: 14px;
        padding-left: 4px;
        font-weight: 600;
      }

      &:hover {
        background: rgba(11, 98, 203, 0.12);
      }
    }
  }
</style>
