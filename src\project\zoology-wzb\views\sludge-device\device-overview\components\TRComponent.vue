<template>
  <div class="trc">
    <SummarySimpleCard
      :title="'实时数据'"
      v-bind="{ bottomList: indexList, empty: empty, loading, roseType: 'area' }"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { SummarySimpleCard } from '/@zhcz/components/HLCardComponent';
  import { mockEnergyAllStatisticsData } from '../data';
  import { searchEqrtdApi } from '/@zoology-wzb/api/device';

  const empty = ref(false);
  const loading = ref(false);
  const indexList = ref<any[]>([mockEnergyAllStatisticsData]);

  const getData = async () => {
    try {
      loading.value = true;
      empty.value = false;
      const res = await searchEqrtdApi();
      loading.value = false;
      indexList.value = res;
      if (indexList.value.length) {
        empty.value = false;
      } else {
        empty.value = true;
      }
    } catch (err) {
      empty.value = true;
      loading.value = false;
      console.log(err);
    }
  };
  getData();
</script>
<style lang="less" scoped>
  .trc {
    background-color: #fcfcfc;
    height: 100%;
    overflow: hidden;
    border-radius: 4px;
  }
</style>
