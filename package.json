{"name": "vben-admin", "version": "2.8.2", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "scripts": {"commit": "czg", "bootstrap": "pnpm install", "serve": "vite --", "dev": "vite --", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=1024000 pnpm vite build && esno ./build/script/postBuild.ts", "build:prefix": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=1024000 pnpm vite build && esno ./build/script/postBuild.ts --VITE_GLOB_UPLOAD_URL=/hlxb-api --VITE_GLOB_API_URL=/hlxb-api", "build:test": "cross-env vite build --mode test && esno ./build/script/postBuild.ts", "build:config": "esno ./build/script/postBuild.ts  --VITE_GLOB_UPLOAD_URL=/hlxb-api   --VITE_GLOB_API_URL=/hlxb-api", "build:no-cache": "pnpm clean:cache && npm run build", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "test:unit": "jest", "test:gzip": "npx http-server dist --cors --gzip -c-1", "test:br": "npx http-server dist --cors --brotli -c-1", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "prepare": "husky install", "gen:icon": "esno ./build/generate/icon/index.ts", "gen:project": "esno ./build/generate/project/index.ts --", "preinstall": "only-allow pnpm"}, "dependencies": {"@ag-grid-community/locale": "^32.1.0", "@ant-design/colors": "^6.0.0", "@ant-design/icons-vue": "^6.1.0", "@antv/g2plot": "^2.4.32", "@antv/l7": "^2.22.0", "@antv/l7plot": "^0.5.11", "@antv/s2": "^1.55.8", "@bpmn-io/add-exporter": "^0.2.0", "@bpmn-io/element-template-chooser": "^1.0.0", "@bpmn-io/properties-panel": "^1.8.2", "@fortaine/fetch-event-source": "^3.0.6", "@iconify/iconify": "^2.2.1", "@logicflow/core": "^1.2.15", "@logicflow/extension": "^1.2.16", "@meta2d/activity-diagram": "1.0.0", "@meta2d/chart-diagram": "1.0.7", "@meta2d/class-diagram": "1.0.1", "@meta2d/core": "1.0.46", "@meta2d/flow-diagram": "1.0.0", "@meta2d/form-diagram": "1.0.11", "@meta2d/sequence-diagram": "^1.0.0", "@meta2d/svg": "1.0.4", "@tinymce/tinymce-vue": "^6.0.1", "@vue/runtime-core": "^3.3.4", "@vue/shared": "^3.3.4", "@vueuse/core": "^8.9.4", "@vueuse/shared": "^8.9.4", "@zxcvbn-ts/core": "^2.2.1", "ag-grid-community": "^33.0.3", "ag-grid-enterprise": "^33.0.3", "ag-grid-vue3": "^33.0.3", "ant-design-vue": "^4.2.3", "autofit.js": "^3.1.0", "await-to-js": "^3.0.0", "axios": "^0.26.1", "bpmn-js": "^11.5.0", "bpmn-js-bpmnlint": "^0.20.1", "bpmn-js-connectors-extension": "^0.4.6", "bpmn-js-properties-panel": "^1.26.0", "bpmn-js-token-simulation": "^0.31.1", "bpmn-moddle": "^8.0.1", "bpmnlint": "^8.3.2", "camunda-bpmn-moddle": "^7.0.1", "cropperjs": "^1.6.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "diagram-js": "^11.13.1", "diagram-js-grid": "^0.2.0", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "element-resize-detector": "^1.2.4", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "handsontable": "^13.0.0", "hlxb-business-ui": "^0.2.35", "hlxb-chart-diagram": "^1.0.18", "hlxb-meta2d-core": "^1.0.80", "hlxb-ui": "^0.3.16", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "ids": "^1.0.5", "intro.js": "^5.1.0", "jquery": "^3.7.1", "js-base64": "^3.7.5", "js-pinyin": "^0.2.7", "jspdf": "^2.5.1", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "mathjs": "^13.0.3", "min-dash": "^4.1.1", "min-dom": "^4.1.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "monaco-editor": "^0.41.0", "nprogress": "^0.2.0", "ol": "^10.1.0", "ol-contextmenu": "^5.4.0", "ol-ext": "^4.0.23", "path-to-regexp": "^6.2.1", "pinia": "2.0.12", "print-js": "^1.6.0", "proj4": "^2.14.0", "qrcode": "^1.5.3", "qs": "^6.11.2", "resize-observer-polyfill": "^1.5.1", "shimo-js-sdk": "^1.2.1", "showdown": "^2.1.0", "snowflake-id": "^1.1.0", "sortablejs": "^1.15.0", "swiper": "^10.3.1", "tiny-svg": "^3.0.1", "tinymce": "^5.10.8", "vditor": "^3.9.6", "vue": "^3.5.13", "vue-color-kit": "^1.0.6", "vue-i18n": "^9.5.0", "vue-json-pretty": "^2.2.4", "vue-pdf-embed": "^1.2.1", "vue-router": "4.2.5", "vue-types": "^4.2.1", "vue3-openlayers": "^10.0.0", "vue3-pdfjs": "^0.1.6", "vue3-seamless-scroll": "^2.0.1", "vuedraggable": "^4.1.0", "vxe-pc-ui": "^4.1.19", "vxe-table": "^4.7.77", "vxe-table-plugin-export-xlsx": "^3.1.0", "web-storage-cache": "^1.1.1", "wujie": "^1.0.22", "wujie-vue3": "^1.0.22", "xe-utils": "^3.5.13", "xgplayer": "^3.0.11", "xgplayer-flv": "^3.0.11", "xgplayer-flv.js": "^3.0.16", "xlsx": "^0.18.5", "xlsx-style-vite": "^0.0.2"}, "devDependencies": {"@commitlint/cli": "^16.3.0", "@commitlint/config-conventional": "^16.2.4", "@handsontable/vue3": "^13.1.0", "@iconify/json": "^2.2.127", "@purge-icons/generated": "^0.8.1", "@types/codemirror": "^5.60.10", "@types/crypto-js": "^4.1.2", "@types/file-saver": "^2.0.7", "@types/fs-extra": "^9.0.13", "@types/inquirer": "^8.2.7", "@types/intro.js": "^3.0.2", "@types/lodash-es": "^4.17.9", "@types/mockjs": "^1.0.8", "@types/node": "^17.0.45", "@types/nprogress": "^0.2.1", "@types/ol-ext": "npm:@siedlerchr/types-ol-ext@^3.5.0", "@types/qrcode": "^1.5.2", "@types/qs": "^6.9.8", "@types/showdown": "^1.9.4", "@types/sortablejs": "^1.15.3", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.4.0", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vue-leaflet/vue-leaflet": "^0.10.1", "@vue/compiler-sfc": "^3.3.4", "@vue/test-utils": "^2.4.1", "autoprefixer": "^10.4.16", "conventional-changelog-cli": "^2.2.2", "cross-env": "^7.0.3", "cz-git": "^1.7.1", "czg": "^1.7.1", "dom-to-image-more": "^3.5.0", "dotenv": "^16.3.1", "eslint": "^8.51.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^8.7.1", "esno": "^0.14.1", "esri-leaflet": "^3.0.12", "fs-extra": "^10.1.0", "husky": "^7.0.4", "inquirer": "^8.2.6", "leaflet": "^1.9.4", "less": "^4.2.0", "lint-staged": "12.3.7", "npm-run-all": "^4.1.5", "only-allow": "^1.2.1", "picocolors": "^1.0.0", "postcss": "^8.4.31", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.8.8", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.69.2", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^7.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^25.0.0", "stylelint-order": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^4.9.5", "vite": "^4.4.11", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-mkcert": "^1.16.0", "vite-plugin-mock": "^2.9.8", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-pwa": "^0.16.5", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-theme": "^0.8.6", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-plugin-windicss": "^1.9.1", "vue-eslint-parser": "^8.3.0", "vue-tsc": "^1.8.18"}, "repository": {"type": "git", "url": "git+https://github.com/anncwb/vue-vben-admin.git"}, "license": "MIT", "bugs": {"url": "https://github.com/anncwb/vue-vben-admin/issues"}, "homepage": "https://github.com/anncwb/vue-vben-admin", "engines": {"node": "^12 || >=14"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --max-warnings 0", "eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "pnpm": {"overrides": {}}}