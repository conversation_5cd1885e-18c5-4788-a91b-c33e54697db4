<template>
  <div class="on-gard">
    <!-- <BoxHeader :title="title" /> -->
    <div class="on-gard-content">
      <div class="gard-item-content" v-for="(item, index) in list" :key="index">
        <div class="title">
          <img :src="getIcon(item.classType)" alt="" class="icon" />
          <div class="info">
            <!-- <div class="name">{{ item.typeName }}({{ item.dateOrder }})</div> -->
            <div class="name">{{ item.typeName || '-' }}</div>
            <div class="time"></div>
            <div class="sum">
              <span class="number">{{ item.persons?.length || 0 }}</span>
              <span class="unit">人</span>
            </div>
          </div>
        </div>
        <div class="content">
          <template v-if="item?.persons.length">
            <Swiper
              class="swiper"
              :modules="modules"
              :pagination="{ clickable: true }"
              @swiper="onSwiper"
              @slideChange="onSlideChange"
              :space-between="24"
              :slides-per-view="widthWind > 1280 ? 4 : 3"
              navigation
            >
              <SwiperSlide v-for="(person, idx) in item.persons" :key="idx">
                <PersonnelItem :data="person" :classType="item.classType" :container="swiperRef" />
              </SwiperSlide>
            </Swiper>
            <!-- <div class="button button_l" @click="goPrev">
              <img :src="person_l" alt="" srcset="" />
            </div>
            <div class="button button_r" @click="goNext">
              <img :src="person_r" alt="" srcset="" />
            </div> -->
          </template>
          <template v-else>
            <PersonnelItem :empty="true" :classType="item.classType" :container="swiperRef" />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  // import BoxHeader from '../../../components/box-container/BoxHeader.vue';
  import PersonnelItem from './PersonnelItem.vue';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Navigation } from 'swiper/modules';
  // import { Pagination, Scrollbar, A11y } from 'swiper/modules';
  // import 'swiper/css/pagination';
  // 如果需要导航按钮
  import 'swiper/css/navigation';
  import 'swiper/css';
  import workTypeBlue from '../../../assets/images/work-type-blue.png';
  import workTypeYellow from '../../../assets/images/work-type-yellow.png';
  import workTypeGreen from '../../../assets/images/work-type-green.png';
  import workTypeOrange from '../../../assets/images/work-type-orange.png';
  // import person_l from './person_left.png';
  // import person_r from './person_right.png';

  const swiperInstance = ref<typeof Swiper | null>(null);
  const widthWind = computed(() => {
    return window.innerWidth;
  });
  // Pagination Scrollbar, A11y
  const modules = [Navigation];
  const onSwiper = (swiper) => {
    swiperInstance.value = swiper;
    console.log('swiper', swiper);
  };
  const onSlideChange = () => {
    console.log('slide change');
  };
  // const goPrev = () => {
  //   console.log('swipergoPrev', swiperInstance.value);
  //   if (swiperInstance.value) {
  //     swiperInstance.value.slidePrev();
  //   }
  // };
  // const goNext = () => {
  //   console.log('swipergoNext', swiperInstance.value);
  //   if (swiperInstance.value) {
  //     swiperInstance.value.slideNext();
  //   }
  // };
  const props = defineProps({
    data: {
      type: Object,
      // default: () => [
      //   {
      //     title: '值班排班',
      //     list: [
      //       {
      //         classType: '',
      //         leaderId: '',
      //         leaderName: '',
      //         typeName: '早班',
      //         dateOrder: '10:00-20:00',
      //         persons: [],
      //       },
      //     ],
      //   },
      // ],
    },
    title: {
      type: String,
      default: '',
    },
  });

  // const swiperOptions = {
  //   slidesPerView: 4.3,
  //   spaceBetween: 40,
  // };
  function getIcon(number) {
    const index = number % 4;
    const iconMap = new Map([
      [1, workTypeGreen],
      [2, workTypeBlue],
      [3, workTypeYellow],
      [0, workTypeOrange],
    ]);

    return iconMap.get(index) || workTypeBlue;
  }

  const swiperRef = ref<any>(null);
  // const contentRef = ref(null);
  const list = ref<
    {
      classType: number;
      leaderId: string;
      leaderName: string;
      leaderAvatar: string;
      typeName: string;
      dateOrder: string;
      persons: any[];
    }[]
  >([]);

  watch(
    () => props.data,
    (newVal) => {
      const newData = JSON.parse(JSON.stringify(newVal));
      newData.list = newData.list.map((item) => {
        const persons = item.persons.map((person) => ({
          leader: false,
          id: person.id,
          name: person.name,
          avatar: person.avatar,
          identity: person.identity,
        }));

        const index = persons.findIndex((person) => item.leaderId === person.id);
        if (index !== -1 && item.leaderName) {
          persons.splice(index, 1);
        }

        if (item.leaderName) {
          persons.unshift({
            leader: true,
            id: item.leaderId,
            name: item.leaderName,
            avatar: item.leaderAvatar,
            identity: item.identity,
          });
        }

        return {
          leader: true,
          typeName: item.typeName,
          classType: item.classType,
          dateOrder: item.dateOrder,
          id: item.leaderId,
          name: item.leaderName,
          avatar: item.leaderAvatar,
          identity: item.identity,
          persons,
        };
      });
      list.value = newData.list;
    },
    {
      immediate: true,
    },
  );

  // function updateSwiper(val) {
  //   if (val <= 1852 && val > 1776) {
  //     swiperRef.value.params.spaceBetween = 36;
  //   } else if (val <= 1776 && val > 1646) {
  //     swiperRef.value.params.spaceBetween = 32;
  //   } else if (val <= 1646 && val > 1600) {
  //     swiperRef.value.params.spaceBetween = 28;
  //   } else if (val <= 1600) {
  //     swiperRef.value.params.spaceBetween = 28;
  //   }
  // }

  // onMounted(() => {
  // updateSwiper(width.value)
  // });
</script>

<style lang="less" scoped>
  .on-gard {
    height: calc(100% - 3rem);
    overflow: auto;

    &::-webkit-scrollbar {
      width: 0px;
    }

    .on-gard-content {
      padding: 1.5rem 0 0 0rem;
    }

    .gard-item-content {
      padding-right: 1rem;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0rem;
      }

      .title {
        min-width: 6.6rem;
        display: flex;
        align-items: flex-end;
        flex-shrink: 0;

        .icon {
          width: 4rem;
        }

        .info {
          margin-bottom: -5px;

          .name {
            font-size: 14px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #ffffff;
          }

          .sum {
            .number {
              font-size: 20px;
              font-family: PingFang SC-Semibold, PingFang SC;
              font-weight: 600;
              color: #2d82fe;
            }

            .unit {
              color: #515a6e;
              margin-left: 4px;
              font-size: 14px;
              font-family: PingFang SC-Regular, PingFang SC;
              font-weight: 400;
            }
          }
        }
      }

      .content {
        padding-left: 1.25rem;
        flex: 1;
        overflow: hidden;

        :deep(.swiper-container) {
          cursor: pointer;
        }
      }
    }

    .content {
      position: relative;

      .button {
        width: 24px;
        height: 32px;
        padding: 8px 4px;
        display: flex;
        align-items: center;
        background: #0b62cb;
        cursor: pointer;

        & > img {
          width: 16px;
        }
      }

      .button_l {
        position: absolute;
        left: 20px;
        top: 78px;
        border-radius: 4px 0px 0px 4px;
      }

      .button_r {
        position: absolute;
        right: 20px;
        top: 78px;
        border-radius: 0px 4px 4px 0px;
      }

      :deep(.swiper-slide) {
        width: 120px !important;
      }

      :deep(.swiper-container) {
        cursor: pointer;
      }

      :deep(.swiper-button-prev) {
        color: #ffffff;
        // top: 78px;
        left: 0;
        width: 24px;
        height: 32px;
        background: #0b62cb;
        border-radius: 4px 0px 0px 4px;

        &::after {
          font-size: 16px;
        }

        &.swiper-button-disabled {
          opacity: 1 !important;
          background: rgba(11, 98, 203, 0.56);
          backdrop-filter: blur(4px);
        }
      }

      :deep(.swiper-button-next) {
        color: #ffffff;
        width: 24px;
        height: 32px;
        right: 0rem;
        background: #0b62cb;
        border-radius: 0px 4px 4px 0px;

        &::after {
          font-size: 16px;
        }

        &.swiper-button-disabled {
          opacity: 1 !important;
          background: rgba(11, 98, 203, 0.56);
          backdrop-filter: blur(4px);
        }
      }
    }
  }
</style>
