<template>
  <div class="index-column" @mousedown="handleNoDown">
    <div class="list">
      <div
        class="item"
        :class="{ item_hover: index === hoverIndex, pointer: item.isShowDuty }"
        v-for="(item, index) in newData"
        :key="index"
        @click="(e) => changeStatus(e, item, index)"
        @mouseenter="handleMouseEnter(item, index)"
        @mouseleave="handleMouseLeave(item)"
      >
        <!-- <template v-if="item.hasOwnProperty('mergeValue') == false"> -->
        <div class="item-content" v-if="item.status === 'blur' && item.isShowDuty">
          <div class="item-value">
            <div
              :style="{
                color: getValColor(item),
              }"
              class="value"
              @click="(e) => changeStatus(e, item, index)"
            >
              <Tooltip placement="bottom" @getPopupContainer="(e) => e.target.parentNode">
                <template #title>{{ getVal(item.value) }}</template>
                <span>{{ getVal(item.value) }}</span>
              </Tooltip>
              <Tooltip
                placement="bottom"
                @getPopupContainer="(e) => e.target.parentNode"
                v-if="isWarning(item)"
              >
                <template #title>{{
                  Number(item.value) >= Number(item.rawData.maxVal)
                    ? `上限：${item.rawData.maxVal}`
                    : `下限：${item.rawData.minVal}`
                }}</template>
                <img
                  :src="Number(item.value) >= Number(item.rawData.maxVal) ? up_data : down_data"
                  style="margin-left: 4px; font-size: 14px !important"
                />
              </Tooltip>
            </div>
            <div class="his" @click="(e) => changeHistory(e, item, index)"> </div>
            <div
              v-show="item.showHistory && item.isModify == '1'"
              class="box-remark"
              @click.stop="changeRemark(item)"
            >
              <Icon icon="icon-park-outline:history-query" color="inherit" />
              <div class="remark-text">历史</div>
            </div>
          </div>
        </div>
        <div
          class="input-box"
          v-if="item.status === 'focus' && item.isShowDuty"
          @click="(e) => handleClickBox(e)"
        >
          <InputNumber
            @blur="handleBlur(item)"
            v-model:value="item.value"
            placeholder="请输入"
            style="width: 134px"
          />
        </div>
        <!-- </template> -->
        <!-- <template v-else>{{ getMergeValue(item) }}</template> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, Ref, watch, onMounted, onBeforeUnmount, inject, defineExpose } from 'vue';
  import { Tooltip, InputNumber } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { useDataManagementStore } from '/@zhcz/store/modules/data-management';
  import { truncateAndConvert } from '/@zhcz/utils/number';
  import { useEmitt } from '/@/hooks/web/useEmitt';
  import { SAVE_REPORT_DATA } from '/@zhcz/views/data-management/constant';
  import { BasicQueryParamsSymbol } from '/@zhcz/views/data-management/injectionSymbols';
  import down_data from '../../../assets/images/down_data.svg';
  import up_data from '../../../assets/images/up_data.svg';

  const props = defineProps({
    timeColumnWidth: {
      type: Number,
      default: 201,
    },
    indexVal: {
      type: Number,
      default: -1,
    },
    indexKey: {
      type: Number,
    },
    data: {
      type: Array as PropType<Recordable[]>,
      default: () => [
        {
          value: '1234.00',
          status: 'focus', // blur
          showHistory: false,
        },
      ],
    },
  });

  const emit = defineEmits(['defaultIndex', 'indexLog', 'show', 'setIndexVal']);

  const dataManagementStore = useDataManagementStore();

  const basicQueryParams = inject(BasicQueryParamsSymbol) as Ref<Recordable>;

  const newData = ref<Recordable[]>([]);

  watch(
    () => props.data,
    (val) => {
      const data = JSON.parse(JSON.stringify(val));
      newData.value = data;
    },
    {
      immediate: true,
      deep: true,
    },
  );

  function getVal(val: string) {
    return isNaN(Number(val)) || val === '' || val === null ? '-' : truncateAndConvert(val, 2);
  }

  function cleanWord(value) {
    return String(value).trim();
  }

  function getValColor(record: Recordable) {
    const value = cleanWord(record.value);
    const oldValue = cleanWord(record.oldValue);

    if (value !== oldValue || record.isModify == '1') {
      // 新值不等于老值且修改了
      return '#FC7C22';
    } else {
      return '';
    }
  }
  // 判断是否报警
  function isWarning(record: Recordable) {
    const rawData = record.rawData;
    const value = cleanWord(record.value);

    if (
      value &&
      rawData.openLimit &&
      (Number(value) >= Number(rawData.maxVal) || Number(value) <= Number(rawData.minVal))
    ) {
      // if (Number(value) >= Number(rawData.maxVal)) {
      //   // 最大值上限
      //   upFalg.value = true;
      // } else if (Number(value) <= Number(rawData.minVal)) {
      //   // 最小值下限
      //   upFalg.value = false;
      // }
      // 开启限制且当前值大于等于最大值或者小于等于最小值
      return true;
    }
    return false;
  }

  function changeRemark(item) {
    emit('indexLog', item);
  }

  function changeStatus(e, item, index) {
    if (item.isShowDuty) {
      emit('defaultIndex');

      e.stopPropagation();

      const status = newData.value[index].status;
      // 收起其他input
      newData.value.forEach((item) => {
        item.status = 'blur';
      });
      newData.value[index].status = status === 'focus' ? 'blur' : 'focus';
      emit('setIndexVal', props.indexKey);
    }
  }

  const hoverIndex = ref(Infinity);
  const { emitter } = useEmitt();
  emitter.on('index-table-hover', (index: number) => {
    hoverIndex.value = index;
  });

  function handleMouseEnter(item, index: number) {
    item.showHistory = true;
    emitter.emit('index-table-hover', index);
  }

  function handleMouseLeave(item) {
    item.showHistory = false;
    hoverIndex.value = Infinity;
    emitter.emit('index-table-hover', Infinity);
  }

  function changeHistory(e, item, index) {
    e.stopPropagation();
    if (item.isShowDuty) {
      newData.value[index].showHistory = !item.showHistory;
    }
  }

  function hideInput() {
    newData.value.forEach((item) => {
      item.status = 'blur';
      item.showHistory = false;
    });
  }

  function handleBlur(data: Recordable) {
    if (data.value !== data.oldValue) {
      const modifyData = dataManagementStore.getModifyIndexData;
      const index = modifyData.findIndex(
        (item) =>
          item.indicatorCode === data.indicatorCode && item.provideTime === data.provideTime,
      );
      if (index > -1) {
        modifyData[index].value = data.value;
      } else {
        modifyData.push(data);
      }

      dataManagementStore.setModifyIndexData(modifyData);
      emitter.emit(SAVE_REPORT_DATA + basicQueryParams.value.timeType, data);
    }
  }

  function handleClickBox(e) {
    e.stopPropagation();
  }

  function handleNoDown(e) {
    e.stopPropagation();
  }

  onMounted(() => {
    document.addEventListener('mousedown', hideInput);
  });

  onBeforeUnmount(() => {
    document.removeEventListener('click', hideInput);
  });
  defineExpose({
    newData,
  });
  watch(
    () => props.indexVal,
    (val, oldVal) => {
      if (oldVal !== -1 && props.indexKey !== val) {
        // 收起其他input
        newData.value.forEach((item) => {
          item.status = 'blur';
        });
      }
    },
  );
</script>

<style lang="less" scoped>
  .index-column {
    overflow: hidden;
    position: relative;
    z-index: 1;

    .list {
      display: flex;
      flex-direction: column;

      .item {
        padding: 0 16px 0 24px;
        width: 100%;
        height: 49px;
        display: flex;
        align-items: center;
        gap: 8px;
        border-bottom: 1px solid #f1f2f3;
        border-right: 1px solid #e9e9e9;

        &.pointer {
          cursor: pointer;
        }

        &:last-child {
          height: 49px;
          line-height: 60px;
          font-size: 14px;
          color: #666666;
        }

        .item-content {
          width: 100%;
          height: 32px;
          line-height: 32px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          position: relative;

          .item-value {
            display: flex;
            height: 32px;
            line-height: 32px;

            .value {
              font-size: 14px;
              padding-right: 14px;
              font-weight: 400;
              color: #666666;
              width: 125px;
              height: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              display: flex;
              align-items: center;

              & > span {
                display: inline-block;
                max-width: 95px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              &:hover {
                color: @theme-color;
              }
            }

            .his {
              width: 50px;
              height: 100%;
            }

            .box-remark {
              font-size: 14px;
              font-weight: 400;
              color: #999999;
              display: flex;
              gap: 2px;
              align-items: center;
              justify-content: flex-end;
              position: absolute;
              top: 1px;
              right: 0;
              width: 50px;
              z-index: 1;
              background: inherit;

              &:hover {
                color: @theme-color;
              }

              // .remark-text {
              //   margin-top: -2px;
              // }
            }
          }

          .item-img {
            position: absolute;
            right: 0px;
            top: 3px;
          }
        }

        &_hover {
          background-color: #f0f0f0;
        }

        &:hover {
          background-color: #f0f0f0;
        }

        .input-box {
          display: flex;
          align-items: center;
        }
      }
    }
  }
</style>
