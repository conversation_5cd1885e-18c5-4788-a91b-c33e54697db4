import { defZhczHttp, getBaseZhczApiParams } from '/@/utils/http/axios';
// import type {} from './model';

enum Api {
  GetList = '/schedule/api/BigScreenOption/GetList',
  DeleteItem = '/schedule/api/BigScreenOption/Delete',
  AddOrUpdateItem = '/schedule/api/BigScreenOption/AddOrUpdate',
  GetDetail = '/schedule/api/BigScreenOption/GetDetail',
  Copy = '/schedule/api/BigScreenOption/Copy',
  GetFactoryInfo = '/factory/factory/getFactoryInfo',
}

const baseParams = getBaseZhczApiParams();

export const getListApi = (params) =>
  defZhczHttp.get<Recordable[]>({
    url: Api.GetList,
    params,
  });

export const deleteItemApi = (id: number) =>
  defZhczHttp.post({
    url: `${Api.DeleteItem}?id=${id}&orgId=${baseParams.orgId}&appId=${baseParams.appId}&renterId=${baseParams.renterId}`,
    // params: { id },
  });

export const addOrUpdateItemApi = (params: Recordable) =>
  defZhczHttp.post({
    url: `${Api.AddOrUpdateItem}`,
    params,
  });

export const getDetailApi = (id: string, screenType: string) =>
  defZhczHttp.get({
    url: `${Api.GetDetail}?id=${id}&screenType=${screenType}`,
    // params: { id, screenType },
  });

export const copyApi = (id: number, screenType: string) => {
  return defZhczHttp.post({
    url: `${Api.Copy}?id=${id}&newScreenType=${screenType}&newOrgId=${baseParams.orgId}&newAppId=${baseParams.appId}&newRenterId=${baseParams.renterId}`,
    // params: { id },
  });
};

export const getFactoryInfoApi = () =>
  defZhczHttp.get({
    url: Api.GetFactoryInfo,
  });
