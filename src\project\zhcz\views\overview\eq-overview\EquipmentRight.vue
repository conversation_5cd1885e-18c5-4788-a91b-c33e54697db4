<template>
  <div class="equipment-right">
    <BigBoxContainer>
      <BoxContainer style="height: calc(50% - 4px)">
        <template #header>
          <BoxHeader title="故障统计">
            <template #right>
              <!-- 能耗数据，变更为能耗趋势数据，近30天下拉框，变更为近一周、近一月、近一年过滤 -->
              <Select
                v-model:value="dateType"
                style="width: 120px"
                @change="handleEnergyDateChange"
                class="big-screen-select"
                popupClassName="big-screen-select-dropdown"
              >
                <SelectOption v-for="item in timeList" :value="item.value" :key="item.value">
                  {{ item.label }}
                </SelectOption>
              </Select>
            </template>
          </BoxHeader>
        </template>
        <!-- 故障统计（近30天），变更为故障统计，同时增加近一周、近一月、近一年过滤 -->
        <template #content>
          <div style="width: 100%; height: 100%; padding-top: 1rem; position: relative">
            <!-- <template v-if="faultLoad">
              <div style="background: unset"></div>
            </template> -->
            <template v-if="faultLoad">
              <ProduceDataBottom :data="faultData" />
            </template>
            <DataEmpty v-else />
          </div>
        </template>
      </BoxContainer>
      <BoxContainer style="height: calc(50% - 4px)">
        <template #header>
          <!-- 维修维保（近半年），变更为设备维修排行 柱状图 -->
          <BoxHeader title="设备维修排行">
            <template #right>
              <div class="select-box">
                <Select
                  v-model:value="sludgeTime2"
                  style="width: 5rem"
                  @change="handleSludgeChange2"
                  class="big-screen-select"
                  popupClassName="big-screen-select-dropdown"
                >
                  <SelectOption v-for="item in dataList2" :value="item.value" :key="item.value">
                    {{ item.label }}
                  </SelectOption>
                </Select>
                <DatePicker
                  v-model:value="otherDate"
                  :picker="getPicker2"
                  class="date-picker big-screen-date-picker"
                  popupClassName="big-screen-date-picker-dropdown"
                  style="width: 126px !important"
                  placeholder="选择日期"
                  :allowClear="false"
                  :showToday="false"
                  @change="handleOtherDateChange"
                  :disabledDate="disabledDate2"
                />
              </div>
            </template>
          </BoxHeader>
        </template>
        <template #content>
          <div style="width: 100%; height: 100%; position: relative">
            <!-- <template v-if="costLoad">
              <div style="background: unset"></div>
            </template> -->
            <template v-if="costLoad">
              <UnitConsumption :data-list="costList" />
            </template>
            <DataEmpty v-else />
          </div>
          <!-- <div class="electricity container">
            <EnergyConsumptionChart :data="costList" />
          </div> -->
        </template>
      </BoxContainer>
    </BigBoxContainer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onUnmounted, computed } from 'vue';
  import BigBoxContainer from '../components/box-container/BigBoxContainer.vue';
  import BoxContainer from '../components/box-container/index.vue';
  import BoxHeader from '../components/box-container/BoxHeader.vue';
  import UnitConsumption from '../components/unit-consumotion/index.vue';
  import DataEmpty from '../components/data-empty/index.vue';
  // import EnergyConsumptionChart from '../components/echarts/EnergyConsumptionChart.vue';
  // import { mockData } from './data.js';
  import { toDecimalFloor } from '../utils';
  import { useEmitt } from '/@/hooks/web/useEmitt';
  import dayjs from 'dayjs';
  import { Select, SelectOption, DatePicker } from 'ant-design-vue';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  // import { getRepairStatistics, getMaintenanceAndRepair } from '/@zhcz/api/overview';
  import ProduceDataBottom from './components/echarts/ProduceDataBottom.vue';
  import { listSenceGroupByParent } from '/@zhcz/api/cost-management';
  import { getRepairStatistics, getBiRepairRank } from '/@zhcz/api/overview';

  const props = defineProps({
    isScene: {
      type: Boolean,
      default: true,
    },
  });

  const otherDate = ref(dayjs().subtract(1, 'day'));
  const sludgeTime2 = ref<null | string>('0');
  const dataList2 = ref<{ label: string; value: string }[]>([
    { label: '日', value: '0' },
    { label: '月', value: '1' },
    { label: '年', value: '2' },
  ]);
  const costList = ref([]);
  const costFlag = ref(true);

  // 故障统计
  const faultData = ref<any>({});
  const faultLoad = ref<Boolean>(true);
  const costLoad = ref<Boolean>(true);
  const dateType = ref(props.isScene ? '30' : '0');
  const timeListScene = [
    // {
    //   value: '7',
    //   label: '近一周',
    // },
    {
      value: '30',
      label: '近一月',
    },
    {
      value: '365',
      label: '近一年',
    },
  ];
  const timeListApi = [
    {
      value: '0',
      label: '近一周',
    },
    {
      value: '1',
      label: '近一月',
    },
  ];

  const timeTypeMap = {
    date: {
      type: 0,
      format: 'YYYY-MM-DD',
    },
    month: {
      type: 1,
      format: 'YYYY-MM',
    },
    year: {
      type: 2,
      format: 'YYYY',
    },
  };

  enum pickerType {
    '日' = 'date',
    '月' = 'month',
    '年' = 'year',
  }

  const timeList = computed(() => {
    return props.isScene ? timeListScene : timeListApi;
  });

  const getPicker2 = computed(() => {
    if (sludgeTime2.value) {
      return pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    } else {
      return pickerType['日'];
    }
  });
  const handleSludgeChange2 = () => {
    const picker =
      pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    if (picker === 'date') {
      otherDate.value = dayjs().subtract(1, 'day');
    } else if (picker === 'month') {
      otherDate.value = dayjs().subtract(1, picker);
    } else {
      otherDate.value = dayjs().subtract(0, picker);
    }
    // otherDate.value = dayjs().subtract(1, picker);
    handleOtherDateChange();
  };
  function disabledDate2(current) {
    // 禁止选择今天以后的日期
    // const picker =
    //   pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
    return current && current > dayjs().subtract(0, 'day');
  }
  const handleOtherDateChange = () => {
    getConsumptionData();
  };
  function getConsumptionData() {
    getCostData();
  }

  async function getTimeList2() {
    if (!props.isScene) {
      getCostData();
      return;
    }
    const res = await listSenceGroupByParent({
      groupCode: 'sbzldpcj_sbwxph',
      factoryId: 1,
      platformld: 1,
    });
    if (Object.keys(res).length) {
      sludgeTime2.value = Object.keys(res)[0];
      dataList2.value = Object.keys(res).map((item) => ({
        value: item,
        label: res[item],
      }));
      const picker =
        pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
      if (picker === 'date') {
        otherDate.value = dayjs().subtract(1, 'day');
      } else if (picker === 'month') {
        otherDate.value = dayjs().subtract(1, picker);
      } else {
        otherDate.value = dayjs().subtract(0, picker);
      }
      handleOtherDateChange();
    }
  }
  getTimeList2();
  function handleEnergyDateChange() {
    getFaultData();
  }
  const getFaultData = async () => {
    try {
      const collDate = Number(dateType.value) - 1;
      const endDataTime = dayjs().subtract(1, 'd').format('YYYY-MM-DD 23:59:59');
      const startDateTime = dayjs(endDataTime)
        .subtract(collDate, 'd')
        .format('YYYY-MM-DD 00:00:00');
      const tempParams = { resourceInterfaceId: '3', groupCode: 'sbzldpcj_gztj' };
      const params = {
        startDateTime: startDateTime,
        endDateTime: endDataTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        paramsData: JSON.stringify(params),
      };
      const paramDataApi = {
        type: Number(dateType.value),
      };
      // const { data } = await callResourceFunction(paramData);
      const res = props.isScene
        ? await callResourceFunction(paramData)
        : await getRepairStatistics(paramDataApi);
      const data = props.isScene ? res.data : res;

      const colorBox = [
        'rgba(222, 171, 34, 1)',
        'rgba(196, 196, 196, 1)',
        'rgba(45, 130, 254, 1)',
        'rgba(31, 195, 164, 1)',
        'rgba(196, 196, 196, 1)',
        'rgba(253, 165, 78, 1)',
      ];
      if (data && data.length) {
        faultLoad.value = true;
        const xAxis = data[0]?.data.map((item) => {
          let row;
          if (dateType.value === '0' || dateType.value === '1' || dateType.value === '7') {
            row = dayjs(item.collectDateTime).format('DD') + '日';
          } else if (dateType.value === '30') {
            row = dayjs(item.collectDateTime).format('DD') + '日';
          } else if (dateType.value === '365') {
            row = dayjs(item.collectDateTime).format('MM-DD');
          }
          return row;
        });
        // const xAxis = data[0]?.data.map((i) => dayjs(i.collectDateTime).format('YYYY-MM-DD'));
        faultData.value.chartOptions = {
          xAxis: {
            data: xAxis,
          },
          series: data.map((item, index) => {
            return {
              name: item.indexName,
              color: colorBox[colorBox.length % (index + 1)],
              // symbol: costList.chartOptions.series[index % costList.chartOptions.series.length].symbol,
              data: item.data.map((i) => i.value),
              unitName: item.unitName,
              // areaColor:
              //   costList.chartOptions.series[index % costList.chartOptions.series.length].areaColor,
            };
          }),
        };
        // costList.chartOptions.xAxis.data = xAxis;
        // costList.chartOptions.series =
      } else {
        faultLoad.value = false;
      }
    } catch (_) {
      faultLoad.value = false;
    }
  };

  function getApiParams() {
    const { type, format } =
      timeTypeMap[
        pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日']
      ];
    return {
      type,
      time: dayjs(otherDate.value).format(format),
    };
  }

  // 设备维修排行
  async function getCostData() {
    try {
      costFlag.value = true;
      const picker =
        pickerType[dataList2.value.find((i) => i.value === sludgeTime2.value)?.label || '日'];
      const startDate = dayjs(otherDate.value).startOf(picker).format('YYYY-MM-DD 00:00:00');
      const endDataTime = dayjs(otherDate.value).endOf(picker).format('YYYY-MM-DD 23:59:59');
      const tempParams = { resourceInterfaceId: '5', groupCode: sludgeTime2.value };
      const params = {
        startDateTime: startDate,
        endDateTime: endDataTime,
        indexCodes: '@￥Resource',
        tenantId: '@￥TenantId',
      };
      const paramData = {
        ...tempParams,
        jsConvert: true,
        paramsData: JSON.stringify(params),
      };
      // const { data } = await callResourceFunction(paramData);

      const paramApiData = getApiParams();

      const res = props.isScene
        ? await callResourceFunction(paramData)
        : await getBiRepairRank(paramApiData);
      const data = props.isScene ? res.data : res;
      if (data && data.length) {
        costLoad.value = true;
        const lsit = data;
        const max = Math.max(...lsit.map((item) => item.total));
        costList.value = lsit
          .map((i) => {
            return {
              indexName: i.indexName,
              unitName: i.unitName,
              value: i.total,
              percentage: `${toDecimalFloor((i.total / max) * 100)}%`,
            };
          })
          .sort((a, b) => b.value - a.value);
      } else {
        costLoad.value = false;
        costFlag.value = false;
        // costList.chartOptions = mockData.chartOptions;
      }
    } catch (_) {
      costLoad.value = false;
    }
  }

  getFaultData();
  // getCostData();

  const { emitter } = useEmitt();
  emitter.on('bi:change-factory', () => {
    getFaultData();
    getCostData();
  });

  onUnmounted(() => {
    emitter.off('bi:change-factory');
  });
</script>

<style lang="less" scoped>
  .equipment-right {
    width: 100%;
    height: 100%;

    .select-box {
      flex: 1;
      display: flex;
      justify-content: end;
      gap: 0 12px;
    }

    :deep(.ant-select-selection-item) {
      color: #fff;
    }

    :deep(.ant-picker) {
      color: #fff;

      .ant-picker-input > input {
        color: #fff;
      }
    }

    .container {
      padding-top: 1rem;
      height: 100%;
    }

    .electricity {
      @media screen and (max-height: 900px) {
        padding: 1rem 0.5rem 0 1rem;
      }
    }
  }
</style>
