<template>
  <Card :bordered="false" dis-hover :title="title">
    <template #extra>
      <div class="extra-wrap">
        <Select
          v-model:value="formData.dayDate"
          @change="handleChangeDayDate"
          class="ml-16px"
          placeholder="请选择"
          :dropdownMatchSelectWidth="false"
        >
          <SelectOption v-for="item in dateOptions" :key="item.label" :value="item.label">
            {{ item.label }}
          </SelectOption>
        </Select>

        <Select
          placeholder="请选择"
          @change="handleOneChange"
          v-model:value="formData.argOne"
          class="ml-16px"
          :dropdownMatchSelectWidth="false"
        >
          <SelectOption v-for="item in optionsOne" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>

        <Select
          placeholder="请选择"
          :value="formData.argTwo || null"
          @update:value="val = formData.argTwo = val"
          @change="handleTwoChange"
          class="ml-16px"
          :dropdownMatchSelectWidth="false"
        >
          <SelectOption v-for="item in optionsTwo" :key="item.value" :value="item.value">
            {{ item.label }}
          </SelectOption>
        </Select>
      </div>
    </template>
    <div ref="chartRef" style="height: 100%" v-show="!isEmpty"></div>

    <div v-show="isEmpty" class="empty-box">
      <HEmpty />
    </div>
  </Card>
</template>

<script setup>
  import { Card, Select, SelectOption } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { ref, watch, reactive, onMounted } from 'vue';
  import HEmpty from '/@/components/HEmpty/index.vue';
  import { callResourceFunction } from '/@zhcz/api/scenes-group';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { roundAndConvert } from '/@zhcz/utils/number';

  let dateOptions = [];
  const currentYear = dayjs().year();
  function initDateOptions() {
    dateOptions = [
      {
        label: '第一季度',
        value: [
          dayjs(`${currentYear}-01`).format('YYYY-MM'),
          dayjs(`${currentYear}-03`).format('YYYY-MM'),
        ],
      },
      {
        label: '第二季度',
        value: [
          dayjs(`${currentYear}-04`).format('YYYY-MM'),
          dayjs(`${currentYear}-06`).format('YYYY-MM'),
        ],
      },
      {
        label: '第三季度',
        value: [
          dayjs(`${currentYear}-07`).format('YYYY-MM'),
          dayjs(`${currentYear}-09`).format('YYYY-MM'),
        ],
      },
      {
        label: '第四季度',
        value: [
          dayjs(`${currentYear}-10`).format('YYYY-MM'),
          dayjs(`${currentYear}-12`).format('YYYY-MM'),
        ],
      },
      {
        label: '上半年',
        value: [
          dayjs(`${currentYear}-01`).format('YYYY-MM'),
          dayjs(`${currentYear}-06`).format('YYYY-MM'),
        ],
      },
      {
        label: '下半年',
        value: [
          dayjs(`${currentYear}-07`).format('YYYY-MM'),
          dayjs(`${currentYear}-12`).format('YYYY-MM'),
        ],
      },
    ];
  }
  initDateOptions();

  let formData = reactive({
    dayDate: dateOptions[0].label,
    argOne: null,
    argTwo: null,
  });

  let optionsOne = ref([]);
  let optionsTwo = ref([]);
  let cacheList = [];
  let preCacheList = [];
  const chartRef = ref(null);

  const props = defineProps({
    title: { type: String, required: true },
    sourceData: { type: Array, default: () => [] },
  });

  onMounted(() => {});

  // 更新选项一
  function updateOptionsOne(newValue) {
    if (newValue.length < 1) return;
    let res = newValue.find((i) => i.groupCode === 'hytj_szdbt')?.children || [];
    if (res.length < 1) return;
    optionsOne.value = res.map(({ groupCode, name }) => ({
      value: groupCode,
      label: name,
    }));
    formData.argOne = res[0].groupCode;
    getData();
  }
  function updateOptionsTwo(list) {
    // 如果没找到，默认选中第一个
    !list.find((i) => i.indexCode === formData.argTwo) &&
      (formData.argTwo = list[0] ? list[0].indexCode : '');

    optionsTwo.value = list.map(({ indexCode, indexName }) => ({
      value: indexCode,
      label: indexName,
    }));
  }

  let getParams = (pre) => {
    let dateValue = dateOptions.find((i) => i.label === formData.dayDate)?.value;
    let startDateTime = pre
      ? dayjs(dateValue[0]).subtract(1, 'year').format('YYYY-MM')
      : dateValue[0];
    let endDateTime = pre
      ? dayjs(dateValue[1]).subtract(1, 'year').format('YYYY-MM')
      : dateValue[1];
    let paramsData = {
      startDateTime: dayjs(startDateTime).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      endDateTime: dayjs(endDateTime).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
      indexCodes: '@￥Resource',
      tenantId: '@￥TenantId',
    };
    let params = {
      resourceInterfaceId: '3',
      groupCode: formData.argOne,
      paramsData: JSON.stringify(paramsData),
    };
    return params;
  };

  async function getData() {
    let params = getParams();
    let { data } = await callResourceFunction(params);
    let { data: preDate } = await callResourceFunction(getParams(true));

    cacheList = data = data || [];
    preCacheList = preDate = preDate || [];
    updateOptionsTwo(data);
    renderChart();
  }

  let isEmpty = ref(true);
  function renderChart() {
    let data = cacheList.find((i) => i.indexCode === formData.argTwo);
    let preData = preCacheList.find((i) => i.indexCode === formData.argTwo) || {};
    if (!data?.data?.length) {
      isEmpty.value = true;
      return;
    } else {
      isEmpty.value = false;
    }

    let { unitName, data: list } = data;
    let { data: preList = [] } = preData;
    let legendName = [formData.dayDate, `去年${formData.dayDate}`];
    const option = {
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(255, 255, 255, 1)',

        textStyle: {
          color: '#999',
          fontSize: 14,
          lineHeight: 28,
          height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
        formatter: (params) => {
          let str = `<div style='display: grid; grid-template-columns: 15px 1fr auto;'>`;
          params.forEach((item) => {
            str += `
                      <span>${item.marker}</span>
                      <span style='color: #333;'>${item.seriesName}</span>
                      <div style='color: #333; margin-left: 15px; font-weight: 600; justify-self: end;'>
                         <span>${item.value ?? '-'}</span><span>${unitName}</span>
                      </div>`;
          });

          str = `${str}</div>`;
          return str;
        },
      },
      legend: {
        icon: 'circle',
        itemWidth: 8,
        itemHeight: 8,
        itemGap: 24,
        data: legendName,
        textStyle: {
          fontSize: 14,
          color: '#333333',
        },
      },
      grid: {
        top: '15%',
        left: '0%',
        right: '1%',
        bottom: 20,
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          data: list.map((i) => i.collectDateTime),
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            // show: false,
            textStyle: {
              color: '#666',
              fontSize: 14,
            },
            formatter(value) {
              return '';
              return dayjs(value).format('MM月');
            },
          },
          axisPointer: {
            type: 'shadow',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: unitName ? `单位（${unitName}）` : '',
          nameTextStyle: {
            color: '#666',
            fontSize: 14,
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#E9E9E9',
              type: 'solid',
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9E9E9',
              type: 'dashed',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#666',
              fontSize: 14,
            },
          },
        },
      ],
      series: [
        {
          color: '#2E8CFFCC',
          name: legendName[0],
          type: 'bar',
          barWidth: 24,
          label: {
            show: true,
            color: '#666',
            position: 'bottom',
            fontSize: 14,

            formatter: (params) => {
              let { name } = params;
              return dayjs(name).format('YYYY-MM');
            },
          },
          data: list.map((i) => (i.value == null ? null : roundAndConvert(Number(i.value), 2))),
        },
        {
          color: '#FF8C2ECC',
          name: legendName[1],
          type: 'bar',
          barWidth: 24,
          barGap: '160%',
          label: {
            show: true,
            color: '#666',
            position: 'bottom',
            fontSize: 14,
            formatter: (params) => {
              let { name } = params;
              return dayjs(name).subtract(1, 'year').format('YYYY-MM');
            },
          },
          data: preList.map((i) => (i.value == null ? null : roundAndConvert(Number(i.value), 2))),
        },
      ],
    };
    let instance = getInstance();
    instance?.clear();
    console.log('--------------> options', option);
    setOptions(option);
  }

  watch(() => props.sourceData, updateOptionsOne);

  function handleOneChange() {
    getData();
  }
  function handleTwoChange() {
    renderChart();
  }
  function handleChangeDayDate() {
    getData();
  }
  let setOptions;
  let getInstance;
  onMounted(() => {
    ({ setOptions, getInstance } = useECharts(chartRef));
  });
</script>

<style lang="less" scoped></style>
