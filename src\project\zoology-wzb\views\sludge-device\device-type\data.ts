import { BasicColumn } from '/@/components/Table';
import { h } from 'vue';
import { Switch } from 'ant-design-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { updateEquipmentTypeStatusApi } from '/@zoology-wzb/api/device';

export const columns: BasicColumn[] = [
  {
    title: '类型名称',
    dataIndex: 'equipmentTypeName',
    width: 220,
  },
  {
    title: '设备型号',
    dataIndex: 'equipmentModelNameList',
    customRender: ({ record }) => record?.equipmentModelNameList?.join(','),
  },
  {
    title: '排序',
    dataIndex: 'equipmentTypeSort',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'equipmentTypeStatusKey',
    width: 100,
    customRender: ({ record }) => {
      return h(Switch, {
        checked: record.equipmentTypeStatusKey == '1',
        loading: record.pendingStatus,
        async onChange(checked: boolean) {
          record.pendingStatus = true;
          const newStatus = checked ? '1' : '0';
          const { createMessage } = useMessage();
          try {
            await updateEquipmentTypeStatusApi({
              id: record.id,
              equipmentTypeStatusKey: newStatus,
            });
            record.equipmentTypeStatusKey = newStatus;
            createMessage.success(`修改成功`);
          } catch {
            createMessage.error(`修改失败`);
          } finally {
            record.pendingStatus = false;
          }
        },
      });
    },
  },
  {
    title: '修改人',
    dataIndex: 'updateBy',
    width: 140,
  },
  {
    title: '修改时间',
    dataIndex: 'updateTimeStr',
    width: 140,
  },
];
