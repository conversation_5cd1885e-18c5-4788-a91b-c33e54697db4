import dayjs from 'dayjs';
import { iconTextMap } from '../data';
import consumptionBlueImg from '../assets/images/consumption-blue.png';
import consumptionYellowLightImg from '../assets/images/consumption-yellow-light.png';
import consumptionYellowDarkImg from '../assets/images/consumption-yellow-dark.png';
import consumptionGreenImg from '../assets/images/consumption-green.png';
import model2 from '../assets/images/model-produce2.png';
import model3 from '../assets/images/model-produce3.png';

export const symbolGreen =
  'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAAXNSR0IArs4c6QAAAKFJREFUKFNjZGBgYJDaP1+DhYWli/E/oxOI/5/x/74/f/6UPXNMvMEIkmRlYTv0n+l/M9Ov34tBCv6xscYy/mOs/f3nlx2j3OHFmxgZmXY9tImeApKEAbmjS3IZ/v13ZZQ/tOQL88+v0vdc0z/+////FEgBIyOjmcL++QL/mVmfEFYAsoKBiXH3I+uYyVitQHbkZ3a2RSBFvD9/xcEdScibAHmHYqeIC964AAAAAElFTkSuQmCC';

export const symbolYellow =
  'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAAXNSR0IArs4c6QAAAJxJREFUKFNjZGBgYPh/TFfj/7//XQwMjE4gPgPD/32MTIxljFaXbzCCJf8zHGJk+N/M8OvLYrA8G0/sfwbGWkZGBjvGf0d0NjEyMe5itLo8BaIbasZRndz//xlcGf8d0f3C+P2rNKPrvY////8/BZJmZGQ0+79fQeA/K+8TYhTobGJkZNjNaH1lMlYrUBzJ/m0RWNFPrji4Iwl5EwCu/FtDhtHa3QAAAABJRU5ErkJggg==';

export const innerCircleGraphic =
  'data:image/png;base64,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';

export const outerCircleGraphic =
  'data:image/png;base64,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';

export const circleGraphic =
  'data:image/png;base64,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';

export const otherIconData = [
  consumptionBlueImg,
  consumptionYellowLightImg,
  consumptionYellowDarkImg,
  consumptionGreenImg,
];

export const timeList = [
  // {
  //   value: '3',
  //   label: '近3天',
  // },
  {
    value: '7',
    label: '近一周',
  },
  // {
  //   value: '15',
  //   label: '近15天',
  // },
  {
    value: '30',
    label: '近一月',
  },
  {
    value: '365',
    label: '近一年',
  },
];

export const mockDataList = [
  {
    indexName: '总用电',
    value: 0,
    unitName: 'kW',
    totalvalue: 0,
  },
  {
    indexName: '生产用电',
    value: 0,
    unitName: 'kW·h',
    totalvalue: 0,
  },
  {
    indexName: '管理用电',
    value: 0,
    unitName: 'kW·h',
    totalvalue: 0,
  },
];

function getChartXAxisData(number) {
  return dayjs().subtract(number, 'day').format('MM-DD');
}

export const mockChartData = {
  title: '生产数据曲线',
  chartOptions: {
    xAxis: {
      data: [
        // getChartXAxisData(7),
        // getChartXAxisData(6),
        // getChartXAxisData(5),
        // getChartXAxisData(4),
        // getChartXAxisData(3),
        // getChartXAxisData(2),
        // getChartXAxisData(1),
      ],
    },
    series: [
      {
        // name: '总用电',
        color: '#2D82FE',
        data: [],
        unitName: 'kW',
        areaColor: ['rgba(45, 130, 254, 0.3) ', 'transparent'],
      },
      {
        // name: '生产用电',
        color: '#1FC3A4',
        data: [],
        unitName: 'kW·h',
        areaColor: ['rgba(31, 195, 164, .3)', 'transparent'],
      },
      {
        // name: '管理用电',
        color: '#FEC52D',
        data: [],
        unitName: 'kW·h',
        areaColor: ['rgba(254, 197, 45, .3)', 'transparent'],
      },
    ],
  },
};

export const mockEnergyChartData = {
  title: '',
  chartOptions: {
    xAxis: {
      data: [getChartXAxisData(3), getChartXAxisData(2), getChartXAxisData(1)],
    },
    yAxis: {
      offset: 60,
    },
    series: [
      {
        name: '余氯',
        color: '#2D82FE',
        data: [0, 0, 0, 0, 0, 0, 0],
        unitName: '',
        areaColor: ['rgba(45, 130, 254, 0.3) ', 'transparent'],
      },
      {
        name: '加药间',
        color: '#1FC3A4',
        data: [0, 0, 0, 0, 0, 0, 0],
        unitName: '',
        areaColor: ['rgba(31, 195, 164, .3)', 'transparent'],
      },
      {
        name: '紫外消毒',
        color: '#FEC52D',
        data: [0, 0, 0, 0, 0, 0, 0],
        unitName: '',
        areaColor: ['rgba(254, 197, 45, .3)', 'transparent'],
      },
      {
        name: '紫外消毒',
        color: '#37B9FF',
        data: [0, 0, 0],
        unitName: '',
        areaColor: ['rgba(55, 185, 255, 0.3)', 'transparent'],
      },
      {
        name: '曝气',
        color: '#2d82fe',
        data: [0, 0, 0],
        unitName: '',
        areaColor: ['rgba(45, 130, 254, 1)', 'rgba(31, 195, 164, 0)'],
      },
      // {
      //   name: '曝气',
      //   color: '#2CFFF1',
      //   data: [0, 0, 0],
      //   unitName: '',
      //   areaColor: ['rgba(44, 255, 241, 0.3)', 'transparent'],
      // },
      // {
      //   name: '提升泵房',
      //   color: '#D1F9FF',
      //   data: [0, 0, 0],
      //   unitName: '',
      //   areaColor: ['rgba(209, 249, 255, 0.3)', 'transparent'],
      // },
    ],
  },
};
// const lineColor = [
//   '#37B9FF',
//   '#2CFFF1',
//   '#D1F9FF',
//   '#8CFC9B',
//   '#F6EE60',
//   '#E551FF',
//   '#EBA4FF',
//   '#877CFF',
// ];
export const mockElectricityChartData = {
  series: [
    {
      data: [
        // { name: '送水泵房', value: 1048 },
        // { name: '炭滤提升', value: 735 },
        // { name: '臭氧间月用电', value: 580 },
        // { name: '其他用电', value: 484 },
        // { name: '回收水泵', value: 300 },
        // { name: '综合楼', value: 360 },
        // { name: '砂滤池月用电', value: 528 },
        // { name: '炭滤反冲', value: 622 },
        // { name: '调节池月用电', value: 345 },
        // { name: '加药间月用电', value: 469 },
      ],
    },
  ],
};

export const mockConsumptionData = [
  {
    indexName: '加药间',
    value: 0,
    unitName: 'kW·h',
    src: consumptionBlueImg,
  },
  {
    indexName: '紫外消毒',
    value: 0,
    unitName: 'kW·h',
    src: consumptionYellowLightImg,
  },
  {
    indexName: '曝气',
    value: 0,
    unitName: 'kW·h',
    src: consumptionYellowDarkImg,
  },
  {
    indexName: '提升泵房',
    value: 0,
    unitName: 'kW·h',
    src: consumptionGreenImg,
  },
];

function radomNumber(min, max) {
  return Number.parseInt(Math.floor(Math.random() * (max - min + 1) + min));
}

export const mockEnergyCenterData = [
  {
    indexName: iconTextMap.get('energy1'),
    value: radomNumber(500, 1000),
    unitName: 'kW·h',
  },
  {
    indexName: iconTextMap.get('energy2'),
    value: radomNumber(500, 1000),
    unitName: 'kW·h',
  },
  {
    indexName: iconTextMap.get('energy3'),
    value: radomNumber(500, 1000),
    unitName: 'kW·h',
  },
];

export const energyCenterEmptyData = [
  {
    indexName: iconTextMap.get('energy1'),
    value: '',
    unitName: 'kW·h',
  },
  {
    indexName: iconTextMap.get('energy2'),
    value: '',
    unitName: 'kW·h',
  },
  {
    indexName: iconTextMap.get('energy3'),
    value: '',
    unitName: 'kW·h',
  },
];

export const mockIndexData1 = [
  {
    indexName: '鼓风机瞬时风量',
    value: 0,
    unitName: 'm³/h',
  },
  {
    indexName: '鼓风机累计风量',
    value: 0,
    unitName: 'm³/h',
  },
  {
    indexName: '鼓风机实测压力',
    value: 0,
    unitName: 'kPa',
  },
  {
    indexName: '鼓风机设定压力',
    value: 0,
    unitName: 'kPa',
  },
];
export const mockIndexData2 = [
  {
    indexName: '宝安路泵站恒水位设置',
    value: 0,
    unitName: 'm',
  },
  {
    indexName: '生化池3#风机掉电',
    value: 0,
    unitName: 'm²',
  },
  {
    indexName: '生化池2#风机最高',
    value: 488,
    unitName: 'HZ',
  },
  {
    indexName: '生化池2#风机最低',
    value: 0,
    unitName: 'HZ',
  },
];

export const mockModelData = [
  {
    src: model2,
    text: '鼓风机房',
    index: mockIndexData1,
  },
  {
    src: model3,
    text: '生物池',
    index: mockIndexData2,
  },
];
