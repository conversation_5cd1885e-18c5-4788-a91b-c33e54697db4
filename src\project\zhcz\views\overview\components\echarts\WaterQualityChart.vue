<template>
  <div style="width: 100%; height: 100%">
    <div ref="chartRef" style="width: 100%; height: 100%; z-index: 999"></div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch } from 'vue';
  import { uniqBy } from 'lodash-es';
  import { useECharts } from '/@zhcz/hooks/useECharts';
  import { hasMax, toDecimalFloor, roundFun, getEchartFontSize } from '../../utils';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({
        title: '昨天数据曲线',
        chartOptions: {
          xAxis: {
            data: [1, 2, 3],
          },
          series: [
            {
              name: '余氯',
              color: '#2D82FE',
              data: [0, 0, 0, 0, 0, 0, 0],
              unitName: 'kW',
              areaColor: ['rgba(45, 130, 254, 0.3) ', 'transparent'],
            },
            {
              name: '浊度',
              color: '#1FC3A4',
              data: [0, 0, 0, 0, 0, 0, 0],
              unitName: 'kW·h',
              areaColor: ['rgba(31, 195, 164, .3)', 'transparent'],
            },
            {
              name: '曝气',
              color: '#FEC52D',
              data: [0, 0, 0, 0, 0, 0, 0],
              unitName: 'kW·h',
              areaColor: ['rgba(254, 197, 45, .3)', 'transparent'],
            },
            {
              name: '提升泵房',
              color: 'rgba(115, 45, 254, 1)',
              data: [0, 0, 0],
              areaColor: ['rgba(115, 45, 254, 0.3)', 'transparent'],
            },
            {
              name: 'pH',
              color: '#2D82FE',
              data: [0, 0, 0],
              areaColor: ['rgba(31, 195, 164, 0.3)', 'transparent'],
            },
            {
              name: 'COD',
              color: '#1FC3A4 ',
              data: [0, 0, 0],
              areaColor: ['rgba(31, 195, 164, 0.3)', 'transparent'],
            },
            {
              name: 'SS',
              color: '#FEC52D ',
              data: [0, 0, 0],
              areaColor: ['rgba(31, 195, 164, 0.3)', 'transparent'],
            },
          ],
        },
      }),
    },
    timeType: {
      type: String,
      default: '1d',
    },
    tooltipWidth: {
      type: Number,
      default: 0,
    },
  });

  const chartRef = ref(null);

  const { setOptions, echarts } = useECharts(chartRef);

  function getMaxValue(arr) {
    const max = Math.max(...arr);
    // 这样处理是为了不让最大值刚好到坐标轴最顶部
    return Math.ceil(max / 8) * 10;
  }

  function getMinValue(arr) {
    const min = Math.min(...arr);
    // 这样处理是为了不让最大值刚好到坐标轴最底部
    return Math.floor(min / 12) * 10;
  }

  // 返回字符串ascii码
  function getStrAscii(str) {
    let result = '';
    for (let i = 0; i < str.length; i++) {
      result += str.charCodeAt(i);
    }
    return Number(result);
  }

  function renderChart() {
    try {
      // const { title, chartOptions } = props.data
      const { chartOptions } = props.data;
      const { xAxis, series } = chartOptions;
      const isW = hasMax(chartOptions.series, 10000);
      let yAxis = {
        type: 'value',
        boundaryGap: [0, '100%'],
        nameTextStyle: {
          color: '#ffffff',
          fontSize: getEchartFontSize(14),
          padding: [0, 8, 0, 20],
          align: 'center',
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.2)',
            type: 'dashed',
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#fff',
            fontSize: getEchartFontSize(14),
          },
          formatter(value) {
            const val = isW ? `${roundFun(value / 10000, 1)}万` : value;
            return val;
          },
        },
      };

      const yAxisData = uniqBy(chartOptions.series, 'unitName').sort(
        (a, b) => getStrAscii(a.unitName) - getStrAscii(b.unitName),
      );
      let max1 = 0;
      let max2 = 0;
      if (yAxisData.length >= 2) {
        const min1 = getMinValue(yAxisData[0].data);
        max1 = getMaxValue(yAxisData[0].data) || 1;
        const min2 = getMinValue(yAxisData[1].data);
        max2 = getMaxValue(yAxisData[1].data) || 1;
        yAxis = [];
        const y1 = {
          type: 'value',
          // name: yAxisData[0].unitName ? `${yAxisData[0].unitName}` : '',
          nameTextStyle: {
            fontSize: getEchartFontSize(14),
            color: '#fff',
            // padding: [0, 8, 4, 0],
          },
          min: min1,
          max: max1,
          splitNumber: 5,
          interval: (max1 - min1) / 5,
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255,255,255,0.2)',
              type: 'dashed',
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: getEchartFontSize(14),
            },
            formatter(value) {
              const val = isW ? `${roundFun(value / 10000, 1)}万` : value;
              return val;
            },
          },
        };
        const y2 = {
          type: 'value',
          // name: yAxisData[1].unitName ? `${yAxisData[1].unitName}` : '',
          nameTextStyle: {
            fontSize: getEchartFontSize(14),
            color: '#fff',
            // padding: [0, 0, 4, 12],
          },
          splitNumber: 5,
          interval: (max2 - min2) / 5,
          min: min2,
          max: max2,
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255,255,255,0.2)',
              type: 'dashed',
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: getEchartFontSize(14),
            },
            formatter(value) {
              const val = isW ? `${roundFun(value / 10000, 1)}万` : value;
              return val;
            },
          },
        };

        yAxis.push(...[y1, y2]);
      } else {
        // yAxis.name = yAxisData[0].unitName ? `${yAxisData[0].unitName}` : '';
      }

      const seriesData = series.map((item) => {
        const index = yAxisData.findIndex((i) => i.unitName === item.unitName);
        const result = {
          name: item.name,
          color: item.color,
          data: item.data.map((j) => (j !== null ? toDecimalFloor(j) : null)),
          type: 'line',
          smooth: true,
          symbol: 'none',
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: item.areaColor[0],
              },
              {
                offset: 1,
                color: item.areaColor[1],
              },
            ]),
          },
        };

        if (index !== -1) {
          result.yAxisIndex = index;
        }
        return result;
      });

      const option = {
        // title: {
        //   top: 0,
        //   text: title,
        //   textStyle: {
        //     color: '#ffffff',
        //     fontWeight: '400',
        //     fontSize: fontSize(0.14)
        //   },
        //   padding: [
        //     5, // 上
        //     5, // 右
        //     5, // 下
        //     0 // 左
        //   ]
        // },
        grid: {
          left: 1,
          right: 20,
          top: 35,
          bottom: 0,
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#132b4e',
          borderColor: '#132b4e',
          textStyle: {
            color: '#fff',
          },
          formatter: (params) => {
            console.log('params', params);
            let item;
            if (Array.isArray(params)) {
              // 适用折线柱状堆叠的多条数据
              item = params.filter((item) => item.value !== undefined);
            } else {
              item = [params];
            }
            console.log('item', item);
            if (item.length) {
              let htmlStr = ``;
              item.forEach((val, index) => {
                const isW = hasMax(Number(val.value) ? Number(val.value) : 0);
                const value = isW ? `${roundFun(val.value / 10000, 2)}万` : val.value;
                if (Array.isArray(params)) {
                  // 适用折线柱状堆叠的多条数据
                  const unitName =
                    chartOptions.series.find((i) => val.seriesName === i.name)?.unitName || '';
                  htmlStr += `<div>${index === 0 ? val.name : ''}</div>
                    ${val.marker} <span style="display: inline-block; width: 30px;">${
                    val.seriesName
                  }</span><span  style="display: inline-block; width: ${
                    props.tooltipWidth ? props.tooltipWidth + 'px' : '110px'
                  }; font-weight: bold; text-align: right;">${value}</span><span  style="padding-left: 4px">${unitName}</span>`;
                } else {
                  // 饼图
                  const unitName =
                    chartOptions.series[0].data.find((i) => val.name === i.name)?.unitName || '';
                  htmlStr += `
                    ${val.marker} <span style="display: inline-block; width: 30px;">${
                    val.name
                  }</span><span  style="display: inline-block; width: ${
                    props.tooltipWidth ? props.tooltipWidth + 'px' : '110px'
                  }; font-weight: bold; text-align: right;">${value}</span><span  style="padding-left: 4px">${unitName}</span>`;
                }
              });
              return htmlStr;
            }
            return;
          },
          // formatter: (params) => {
          //   const item = params.filter((item) => item.value !== undefined);
          //   // console.log('item', item);
          //   if (item.length) {
          //     let htmlStr = ``;
          //     item.forEach((val, index) => {
          //       const unitName =
          //         chartOptions.series.find((i) => val.seriesName === i.name)?.unitName || '';
          //       htmlStr += `<div>${index === 0 ? val.name : ''}</div>
          //     ${val.marker} <span style="display: inline-block; width: 30px;">${
          //         val.seriesName
          //       }</span><span  style="display: inline-block; width: 110px; font-weight: bold; text-align: right;">${
          //         val.value
          //       }</span><span  style="padding-left: 4px">${unitName}</span>`;
          //     });
          //     return htmlStr;
          //   }
          //   return;
          // },
        },
        legend: {
          show: true,
          type: 'scroll',
          icon: 'circle',
          top: 0,
          left: Array.isArray(yAxis) ? 'center' : yAxis.name ? 100 : 'center',
          itemGap: 8,
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            fontSize: getEchartFontSize(14),
            color: '#fff',
            padding: [3, 5],
          },
          pageTextStyle: {
            color: '#fff',
          },
          pageIconColor: '#fff',
          pageIconInactiveColor: 'rgba(255,255,255, 0.56)',
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            data: xAxis.data,
            // boundaryGap: ['20%', '20%'],
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.8)',
                type: 'solid',
              },
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: '#ffffff',
                fontSize: getEchartFontSize(14),
              },
            },
          },
        ],
        yAxis: yAxis,
        series: seriesData,
      };
      setOptions(option);
    } catch (error) {
      console.log('error', error);
    }
  }

  watch(
    () => props.data,
    () => {
      renderChart();
    },
    {
      deep: true,
    },
  );

  onMounted(() => {
    renderChart();
  });
</script>

<style lang="less" scoped></style>
