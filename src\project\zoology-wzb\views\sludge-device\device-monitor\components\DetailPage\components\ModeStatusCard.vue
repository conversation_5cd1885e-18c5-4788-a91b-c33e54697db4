<template>
  <div class="model-status">
    <HLCard title="模式状态" :styleData="{ borderRadius: '4px' }">
      <template #defaultBody>
        <div class="flex flex1 flex-wrap" style="overflow-y: auto">
          <template v-if="list?.length">
            <div
              v-for="item in list"
              :key="item"
              :style="item?.style"
              :class="['item', { active: item.active }]"
              :title="item.indexName"
            >
              {{ item.indexName }}</div
            >
          </template>

          <template v-else>
            <HEmpty />
          </template>
        </div>
      </template>
    </HLCard>
  </div>
</template>
<script lang="ts" setup>
  import HEmpty from '/@/components/HEmpty';
  import { HLCard } from '/@/project/zhcz/components/HLCard';

  defineProps({
    list: {
      type: Array,
      default: () => [],
    },
  });
</script>
<style lang="less" scoped>
  .model-status {
    display: flex;
    flex-direction: column;
    width: 560px;
    height: 200px;
    background-color: #fff;
    border-radius: 4px;

    .item {
      margin: 8px 0 0 16px;
      padding: 0 4px;
      width: 120px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: #e9e9e9;
      color: #333;
      border-radius: 4px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;

      &.active {
        background: var(--theme-color);
        color: #fff;
      }
    }

    :deep(.card_in) {
      .card-body {
        padding: 0 0 8px !important;
      }
    }
  }
</style>
