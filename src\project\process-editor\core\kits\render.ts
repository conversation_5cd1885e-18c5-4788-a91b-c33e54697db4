import { type Meta2d, type Meta2dData } from 'hlxb-meta2d-core';
import { markRaw } from 'vue';
import { cloneDeep, groupBy, omit, pick } from 'lodash-es';
import {
  getProductionKitMeta2dData,
  getProductionControlKitMeta2dData,
  getVideoKitMeta2dData,
  getItemKitMeta2dData,
  getTableKitMeta2dData,
  getChartKitMeta2dData,
  getInputStatusImg,
  getLiquidLevelKitMeta2dData,
  processHoverCursor,
  updateByCommonKit,
} from './data';
import {
  KIT_INDEX_VALUE,
  PRODUCT_DATA_KIT,
  SMALL_SEMAPHORE,
  TABLE_KIT,
  CHART_KIT,
  TABLE_KIT_INDEX_VALUE,
  VIDEO_KIT,
  TABLE_KIT_FORM_ITEM,
  DISTRIBUTE_STATUS,
  LIQUID_LEVEL_KIT,
  COMMON_KIT,
  PRODUCT_CONTROL_KIT,
  CONTROL_INDEX_VALUE,
  CONTROL_INDEX_TEXT,
} from './constant';
import { addResourcePrefix } from '/@process-editor/utils/index';

// 渲染层
export class KitsV2 {
  meta2dInstance: Meta2d;
  data: Meta2dData | undefined;
  mode = 'edit';
  theme = 'light';

  constructor(instance, data, mode = 'edit', theme = 'light') {
    this.meta2dInstance = instance;
    this.data = data;
    this.mode = mode;
    this.theme = theme;
  }

  /**
   * 设置主题
   */
  setTheme(theme: string) {
    this.theme = theme;
  }

  /**
   * 删除pens(套件)
   */
  deletePens() {
    const pens = cloneDeep(this.meta2dInstance.store.data.pens);
    // pens = pens.filter((item) => item['category'] !== LIQUID_LEVEL_KIT);

    const delPens = Object.keys(pens)
      .map((key) => pens[key])
      .filter((item) => {
        return item?.category;
      });
    try {
      this.meta2dInstance.delete(delPens, true);
      this.deleteEcharts();
    } catch (error) {
      console.log('error', error);
    }
  }

  /**
   * pens排序,零像素设备层级最高
   */
  sortPens(pen) {
    this.meta2dInstance.active(pen);
    this.meta2dInstance.top();
    this.meta2dInstance.render();
  }

  /**
   * 更新Meta2d数据。
   *
   * 此函数用于更新当前实例中的Meta2d数据。它接受一个 Meta2dData 类型的对象，并将其赋值给实例的 data 属性。
   * @param data - 要更新的Meta2d数据。
   */
  updateMeta2dData(data: Meta2dData) {
    this.data = data;
  }

  /**
   * 删除所有echarts图表
   */
  deleteEcharts() {
    // 获取所有属性含有_echarts_instance_的元素
    const echarts = document.querySelectorAll('[_echarts_instance_]');
    echarts.forEach((item) => {
      item.parentElement?.removeChild(item);
    });
  }

  /**
   *  渲染画布
   * @param {Array} data 套件数据
   * @param {Number} scale 画布缩放
   */
  async render(data: Recordable[], scale: number) {
    const { production, productionControl, video, items, table, chart, liquidLevel, common } =
      this.getClassifiedKits(data);

    await new Promise((resolve) => {
      this.deletePens();
      resolve('delete success');
    });

    const itemKits = this.getItemKit(items, scale);
    // 小元件套件已经在获取流程图详情时渲染，这里只需要更新
    if (itemKits.itemImgPens.length > 0) {
      itemKits.itemImgPens.forEach((params) => {
        this.meta2dInstance.setValue(params);
      });
    }

    const currentPens = meta2d && meta2d.data().pens;
    if (Array.isArray(currentPens)) {
      currentPens.forEach((pen) => {
        updateByCommonKit(pen, common, null);
      });
    }

    console.log(
      '%c [  liquidLevel]-122',
      'font-size:13px; background:pink; color:#bf2c9f;',
      liquidLevel,
    );
    const liquidLevelKits = this.getLiquidLevelKit(liquidLevel);

    // this.data = undefined;
    const productionKits = await this.getProductionKit(production, scale);
    const productionControlKits = await this.getProductionControlKit(productionControl, scale);
    const videoKits = this.getVideoKit(video);
    const tableKits = await this.getTableKit(table, scale);
    const chartKit = await this.getChartKit(chart, scale);
    const pens = productionKits.concat(
      productionControlKits,
      videoKits,
      tableKits,
      chartKit,
      itemKits.itemOtherPens,
      liquidLevelKits,
    );
    console.log('addPens', pens);
    await this.meta2dInstance.addPens(pens);
    // this.sortPens(pen);
  }

  /**
   * 更新画布数据
   * @param {Array} data 套件数据
   * @param {Number} scale 画布缩放
   */
  async update(data: Recordable[], scale: number) {
    async function getUpdateData(pens: Recordable[], data: Recordable[], ins: KitsV2) {
      function findPen(newItem: Recordable, oldValues: Recordable[]) {
        let values = cloneDeep(oldValues);

        switch (newItem.categoryCN) {
          case KIT_INDEX_VALUE:
            values = oldValues.filter((i) => i.categoryCN === KIT_INDEX_VALUE);
            break;
          case TABLE_KIT_FORM_ITEM:
            values = oldValues.filter((i) => i.categoryCN === TABLE_KIT_FORM_ITEM);
            break;
          case DISTRIBUTE_STATUS:
            values = oldValues.filter((i) => i.categoryCN === DISTRIBUTE_STATUS);
            break;
          case LIQUID_LEVEL_KIT:
            values = oldValues.filter((i) => i.categoryCN === LIQUID_LEVEL_KIT);
            break;
          case CONTROL_INDEX_VALUE:
            values = oldValues.filter((i) => i.categoryCN === CONTROL_INDEX_VALUE);
            break;
          case CONTROL_INDEX_TEXT:
            values = oldValues.filter((i) => i.categoryCN === CONTROL_INDEX_TEXT);
            break;
          default:
            break;
        }

        // 当画布中有多个pen共用同一个指标，永远只会返回找到的第一个
        const oldPen = values.find((i) => i.rawData?.code === newItem.rawData?.code) || {};

        return oldPen;
      }

      function getUpdateParams(pen: Recordable, id?: string, oldPen?: Recordable) {
        let params = Object.create(null);
        params.id = id || '';
        oldPen = oldPen || {};

        const text = [KIT_INDEX_VALUE, TABLE_KIT_INDEX_VALUE, TABLE_KIT_FORM_ITEM];
        if (text.includes(pen.categoryCN)) {
          params.text = pen.text;
        } else if (pen.categoryCN == DISTRIBUTE_STATUS) {
          const status = pen.rawData.indexStatus;
          params.name = status === '1' ? 'gif' : 'image';
          params.image = getInputStatusImg(status);
        } else if (pen.categoryCN === LIQUID_LEVEL_KIT) {
          // function getRandom(min, max) {
          //   return Math.random() * (max - min) + min;
          // }
          // const test = getRandom(100, 200);
          console.log('pen', pen);
          console.log('oldPen', oldPen);
          params = {
            pen: oldPen,
            id: id || '',
            ...{
              name: pen.name,
              y: oldPen.y + ((oldPen?.height ?? 0) - pen.height),
              // width: pen.width,
              // height: pen.height,
              height: pen.height,
            },
          };
        }

        return params;
      }

      async function getProductionData(pens: Recordable[], data: Recordable[]) {
        async function getOldDataAndNewData() {
          const production = ins.getKitByType(data, PRODUCT_DATA_KIT);
          const _production_ = await ins.getProductionKit(production, scale);

          const oldValues = pens.filter((item) => item?.categoryCN === KIT_INDEX_VALUE);
          const newValues = _production_.filter((item) => item.categoryCN === KIT_INDEX_VALUE);

          return { oldValues, newValues };
        }

        const { oldValues, newValues } = await getOldDataAndNewData();
        const result = newValues.map((item) => {
          const pen = findPen(item, oldValues);
          const params = getUpdateParams(item, pen.id);

          return params;
        });

        return result;
      }

      // 生产下控套件，因为涉及样式，字段不确定，因此要全量更新
      async function getProductionControlData(pens: Recordable[], data: Recordable[]) {
        async function getOldDataAndNewData() {
          const productionControl = ins.getKitByType(data, PRODUCT_CONTROL_KIT);
          const _productionControl_ = await ins.getProductionControlKit(productionControl, scale);

          const oldValues = pens.filter((item) =>
            [CONTROL_INDEX_VALUE, KIT_INDEX_VALUE, CONTROL_INDEX_TEXT].includes(item?.categoryCN),
          );
          const newValues = _productionControl_.filter((item) =>
            [CONTROL_INDEX_VALUE, KIT_INDEX_VALUE, CONTROL_INDEX_TEXT].includes(item?.categoryCN),
          );

          return { oldValues, newValues };
        }

        const { oldValues, newValues } = await getOldDataAndNewData();
        const result = newValues.map((item) => {
          const pen = findPen(item, oldValues);
          const oldKeys = ['id', 'x', 'y', 'width', 'height'];
          const params = Object.assign(pick(pen, ['id']), omit(item, oldKeys));
          return params;
        });

        return result;
      }

      async function getTableData(pens: Recordable[], data: Recordable[]) {
        async function getOldDataAndNewData() {
          const table = ins.getKitByType(data, TABLE_KIT);
          const _table_ = await ins.getTableKit(table, scale);

          const types = [TABLE_KIT_INDEX_VALUE, TABLE_KIT_FORM_ITEM, DISTRIBUTE_STATUS];
          const oldValues = pens.filter((item) => types.includes(item?.categoryCN));
          const newValues = _table_.filter((item) => types.includes(item?.categoryCN));

          return { oldValues, newValues };
        }
        const { oldValues, newValues } = await getOldDataAndNewData();

        // 过滤出newValues中rawData的code一样的元素
        const codes = newValues.map((item) => item.rawData?.code);
        const noRepeatItem = newValues.filter(
          (item) => codes.filter((code) => code === item.rawData?.code).length === 1,
        );
        const result = noRepeatItem.map((item) => {
          const pen = findPen(item, oldValues);
          const params = getUpdateParams(item, pen.id);

          return params;
        });

        // 过滤出newValues中rawData的code不一样的元素
        const repeatItem = newValues.filter(
          (item) => codes.filter((code) => code === item.rawData?.code).length > 1,
        );
        const result2 = Object.entries(groupBy(repeatItem, 'rawData.code')).map(([key, value]) => {
          const pens = oldValues.filter((oldItem) => oldItem.rawData?.code === key);
          const paramsArr = pens.map((pen) => {
            return getUpdateParams(value[0], pen.id);
          });

          return paramsArr;
        });

        return result.concat(result2.flat());
      }

      function getItemData(data: Recordable[]) {
        const items = ins.getKitByType(data, SMALL_SEMAPHORE);
        const result = ins.getItemKit(items, scale);

        return result.itemImgPens;
      }

      async function getLiquidLevelData(pens: Recordable[], data: Recordable[]) {
        async function getOldDataAndNewData() {
          const liquidLevel = ins.getKitByType(data, LIQUID_LEVEL_KIT);
          const _liquidLevel_ = await ins.getLiquidLevelKit(liquidLevel);

          const oldValues = pens.filter((item) => item?.categoryCN === LIQUID_LEVEL_KIT);
          const newValues = _liquidLevel_.filter((item) => item.categoryCN === LIQUID_LEVEL_KIT);

          return { oldValues, newValues };
        }

        const { oldValues, newValues } = await getOldDataAndNewData();

        const result = newValues.map((item) => {
          const pen = findPen(item, oldValues);
          console.log('oldPen', pen);
          const params = getUpdateParams(item, pen.id, pen);

          return params;
        });

        return result;
      }

      const productionPens = await getProductionData(pens, data);
      const productionControlPens = await getProductionControlData(pens, data);
      const itemPens = getItemData(data);
      const tablePens = await getTableData(pens, data);
      const liquidLevelPens = await getLiquidLevelData(pens, data);

      return [productionPens, productionControlPens, itemPens, tablePens, liquidLevelPens].flat();
    }

    if (!this.meta2dInstance) return;
    const pens: Recordable[] = this.meta2dInstance.data().pens;
    pens.forEach((item) => {
      updateByCommonKit(item, data, this.mode);
    });
    const updateData = await getUpdateData(pens, data, this);
    // 更新套件数据
    updateData.forEach((params) => {
      if (params.id) {
        if (params.pen?.categoryCN === LIQUID_LEVEL_KIT) {
          this.meta2dInstance.setValue({
            id: params.id,
            y: params.y,
            height: params.height,
          });
          // this.meta2dInstance.setPenRect(
          //   toRaw(params.pen),
          //   {
          //     x: params.pen.x,
          //     y: params.y,
          //     width: params.pen.width,
          //     height: params.pen.height,
          //   },
          //   false,
          // );
          // this.meta2dInstance.canvas.calcActiveRect();
          // this.meta2dInstance.render();
        } else {
          this.meta2dInstance.setValue(params);
        }
      }
    });
  }

  /**
   * 根据套件类型获取套件数据
   * @param {Array} data 原生产套件数据
   * @param {String} type 套件类型
   * @returns {Array} data 套件数据
   */
  getKitByType(data: Recordable[], type: string) {
    switch (type) {
      case PRODUCT_DATA_KIT:
        return data?.filter((i) => i.kitTypeName === PRODUCT_DATA_KIT) || [];
      case PRODUCT_CONTROL_KIT:
        return data?.filter((i) => i.kitTypeName === PRODUCT_CONTROL_KIT) || [];
      case VIDEO_KIT:
        return data?.filter((i) => i.kitTypeName === VIDEO_KIT) || [];
      case SMALL_SEMAPHORE:
        return data?.filter((i) => i.kitTypeName === SMALL_SEMAPHORE) || [];
      case TABLE_KIT:
        return data?.filter((i) => i.kitTypeName === TABLE_KIT) || [];
      case CHART_KIT:
        return data?.filter((i) => i.kitTypeName === CHART_KIT) || [];
      case LIQUID_LEVEL_KIT:
        return data?.filter((i) => i.kitTypeName === LIQUID_LEVEL_KIT) || [];
      case COMMON_KIT:
        return data?.filter((i) => i.kitTypeName === COMMON_KIT) || [];
      default:
        return [];
    }
  }

  /**
   *  获取分类套件数据
   * @param data
   * @returns
   */
  getClassifiedKits(data: Recordable[]) {
    const production = this.getKitByType(data, PRODUCT_DATA_KIT);
    const video = this.getKitByType(data, VIDEO_KIT);
    const items = this.getKitByType(data, SMALL_SEMAPHORE);
    const table = this.getKitByType(data, TABLE_KIT);
    const chart = this.getKitByType(data, CHART_KIT);
    const liquidLevel = this.getKitByType(data, LIQUID_LEVEL_KIT);
    const productionControl = this.getKitByType(data, PRODUCT_CONTROL_KIT);
    const common = this.getKitByType(data, COMMON_KIT);

    return { production, video, items, table, chart, liquidLevel, productionControl, common };
  }

  /**
   * 将套件数据转化成Pen
   * @param {Array} data 套件数据
   * @param {Number} scale 画布缩放
   * @returns data 画布的Pen数据
   */
  async handleKitsToPens(data: Recordable[], scale: number) {
    const { production, productionControl, video, items, table, chart, liquidLevel } =
      this.getClassifiedKits(data);
    const videoKit = this.getVideoKit(video);
    const itemKit = this.getItemKit(items, scale);
    console.log(
      '%c [  liquidLevel]-122',
      'font-size:13px; background:pink; color:#bf2c9f;',
      liquidLevel,
    );
    const liquidLevelKits = this.getLiquidLevelKit(liquidLevel);
    console.log('LevelKits', liquidLevelKits);

    const asyncKits = { production: [], productionControl: [], table: [], chart: [] };
    const promises = [
      this.getProductionKit(production, scale),
      this.getProductionControlKit(productionControl, scale),
      this.getTableKit(table, scale),
      this.getChartKit(chart, scale),
    ];
    await Promise.allSettled(promises)
      .then((results: Recordable[]) => {
        console.log('results', results);
        const values = results.map((i) => i.value);
        const [production, productionControl, tableKit, chartKit] = values;
        asyncKits.production = production;
        asyncKits.productionControl = productionControl;
        asyncKits.table = tableKit;
        asyncKits.chart = chartKit;
      })
      .catch((error) => {
        console.log('error', error);
      });

    return {
      production: asyncKits.production,
      productionControl: asyncKits.productionControl,
      video: videoKit,
      items: itemKit,
      liquidLevel: liquidLevelKits,
      table: markRaw(asyncKits.table),
      chart: markRaw(asyncKits.chart),
    };
  }

  /**
   * TODO: 函数改名getAllPen
   * 获取画布上所有的图元
   * @param {Array} pens 模型
   * @param {Array} kits 套件
   * @param {Number} scale 画布缩放
   * @returns pens 图元
   */
  async getPens(pens: Recordable[], kits: Recordable[], scale: number) {
    const data = await this.handleKitsToPens(kits, scale);
    const commonKits = this.getKitByType(kits, COMMON_KIT);
    pens.forEach((item) => {
      processHoverCursor(item);
      updateByCommonKit(item, commonKits, this.mode);
      if (item.image) {
        item.image = addResourcePrefix(item.image);
      }
      if (item.video) {
        item.video = addResourcePrefix(item.video);
      }
    });
    if (data.items.itemImgPens.length > 0) {
      // 更新小元件的图片
      data.items.itemImgPens.forEach((item) => {
        const index = pens.findIndex((i) => i.id === item.id);
        if (index > -1) {
          pens[index].image = addResourcePrefix(item.image);
        }
      });
    }

    return pens.concat(
      data.production,
      data.video,
      data.table,
      data.chart,
      data.items.itemOtherPens,
      data.liquidLevel,
      data.productionControl,
    );
  }

  /**
   * 获取画布的生产套件数据
   * @param {Array} data 原生产套件数据
   * @param {Number} scale 画布缩放
   * @returns newData 画布的生产套件数据
   */
  async getProductionKit(data, scale) {
    const newData = await getProductionKitMeta2dData(data, scale, this.data);

    return newData;
  }

  /**
   * 获取画布的生产下控套件数据
   * @param {Array} data 原生产下控套件数据
   * @param {Number} scale 画布缩放
   * @returns newData 画布的生产下控套件数据
   */
  async getProductionControlKit(data, scale) {
    // 使用专门的函数处理生产下控套件数据
    const newData = await getProductionControlKitMeta2dData(data, scale, this.data);

    return newData;
  }

  /**
   * 获取画布的生产套件数据
   * @param {Array} data 原table套件数据
   * @param {Number} scale 画布缩放
   * @returns newData 画布的table数据
   */
  async getTableKit(data, scale) {
    const newData = await getTableKitMeta2dData(data, scale, this.theme, this.data);

    return newData;
  }

  /**
   * 获取画布的图表套件数据
   * @param {Array} data 原图表套件数据
   * @param {Number} scale 画布缩放
   * @returns newData 画布的图表数据
   */
  async getChartKit(data, scale) {
    const newData = await getChartKitMeta2dData(data, scale, this.data);

    return newData;
  }

  /**
   * 获取画布的视频套件数据
   * @param {Array} data 原视频套件数据
   * @param {Number} scale 画布缩放
   * @returns newData 画布的视频套件数据
   */
  getVideoKit(data: Recordable[]) {
    // app屏蔽视频套件
    const search = window.location.hash.slice(window.location.hash.indexOf('?'));
    if (search && new URLSearchParams(search).get('from')) return [];

    const newData = getVideoKitMeta2dData(data, this.data);

    return newData;
  }

  /**
   * 获取画布的小元件数据
   * @param {Array} data 原小元件数据
   * @param {Number} scale 画布缩放
   * @returns newData 画布的小元件数据
   */
  getItemKit(data: Recordable[], scale: number) {
    const newData = getItemKitMeta2dData(data, scale);

    return newData;
  }

  /**
   * 获取画布的液位数据
   * @param {Array} data 原小元件数据
   * @param {Number} scale 画布缩放
   * @returns newData 画布的小元件数据
   */
  getLiquidLevelKit(data: Recordable[]) {
    const newData = getLiquidLevelKitMeta2dData(data);

    return newData;
  }
}
