<template>
  <div class="brc">
    <HlxbLinePlusCard
      :pleftType="true"
      :classType="true"
      :nameBefore="true"
      :title="'峰平谷用电'"
      v-bind="{ bottomList: indexList, topList: pieIndexList, empty, loading }"
    >
      <template #headerRight>
        <div class="header-right">
          <Select
            v-model:value="indicator"
            @change="handleChangeIndicator"
            v-if="indicatorList.length"
          >
            <SelectOption v-for="item in indicatorList" :key="item.value" :value="item.value">
              {{ item.label }}
            </SelectOption>
          </Select>
          <DatePicker
            valueFormat="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model:value="date"
            placeholder="请选择"
            style="width: 200px"
            :allowClear="false"
            :disabledDate="disabledDate"
          />
        </div>
        <!-- <BRRightHeader v-if="BRRightHeader" :itemVal="itemVal" @setBRHData="setBRHData" /> -->
      </template>
    </HlxbLinePlusCard>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, ref, watch, reactive } from 'vue';
  // import { LinePlusCard } from '/@zhcz/components/HLCardComponent';
  import { HlxbLinePlusCard } from 'hlxb-ui';
  import { DatePicker, Select, SelectOption } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { getFpgIndicatorCodeList, getFpgDataList } from '/@zhcz/api/scenes-group';
  import { getDictTypeListApi } from '/@/api/admin/dict';

  const props = defineProps({
    itemVal: {
      type: Object,
      default: () => {},
    },
  });
  const empty = ref(false);
  const loading = ref(false);
  watch(
    () => props.itemVal,
    (val) => {
      if (val) {
        // console.log('val', val);
        // indicator.value = val;
        if (val.date && val.value) {
          date.value = val.date;
          if (indicatorList.value.find((item) => item.tag === val.value)?.value) {
            indicator.value = indicatorList.value.find((item) => item.tag === val.value)?.value;
          }
          getData();
        }
      }
    },
  );
  const indicatorList = ref<{ value: string; label: string; unit: string; tag: string }[]>([]);
  const indicator = ref<any>('');
  const date = ref(dayjs().format('YYYY-MM-DD'));
  const indexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      type?: string;
      data: any[];
    }[]
  >([]);
  const pieIndexList = ref<
    {
      indexName: string;
      indexCode: string;
      unitName: string;
      type?: string;
      value: number;
    }[]
  >([]);
  const priceList = ref<{ label: string; value: number; remarks: number }[]>([]);
  const getprice = async () => {
    try {
      const data = await getDictTypeListApi({
        type: 'dhdj',
      });
      if (data.length) {
        empty.value = false;
        priceList.value = data.map((item) => {
          const { label, value, remarks } = item;
          if (Number(label) < 10) {
            return {
              value: Number(value),
              label: `0${label}:00`,
              remarks: Number(remarks),
            };
          } else {
            return {
              value: Number(value),
              label: `${label}:00`,
              remarks: Number(remarks),
            };
          }
        });
      } else {
        empty.value = true;
      }
      // console.log('priceList.value', priceList.value);
    } catch (err) {
      empty.value = true;
    }
  };
  function disabledDate(current) {
    return current && current > dayjs().endOf('day');
  }

  const getIndicatorList = async () => {
    try {
      empty.value = false;
      const data = await getFpgIndicatorCodeList();
      if (data && data.length) {
        empty.value = false;
        indicatorList.value = data.map((item) => {
          return {
            label: item.name,
            value: item.code,
            unit: item.unit,
            tag: item.tag,
          };
        });
        indicator.value = indicatorList.value[0].value;
      } else {
        empty.value = true;
      }
    } catch (err) {
      empty.value = true;
    }
  };
  const dJata = reactive<any>({
    data: {},
  });
  const chartsData = ref<any>([]);
  const getData = async () => {
    try {
      if (!indicator.value) return;
      empty.value = false;
      loading.value = true;
      const params = {
        startDateTime: dayjs(date.value).startOf('day').valueOf(),
        endDateTime: dayjs(date.value).endOf('day').valueOf(),
        timeType: 2,
        indecatorCodeList: [
          {
            indexCode: indicator.value,
            indexName: indicatorList.value.find((i) => i.value === indicator.value)?.label,
            unit: indicatorList.value.find((i) => i.value === indicator.value)?.unit,
          },
        ],
      };
      const result = await getFpgDataList(params);
      loading.value = false;
      const data = priceList.value.map((item) => {
        const row = result.data.find((i) => {
          return item.label === dayjs(i.provideTime).format('HH:mm');
        });
        const { codeRecord, provideTime, stage } = row;
        return {
          codeRecord,
          provideTime,
          remarks: item.remarks, // 不查字典
          stage, // 接口直接返回
          value: codeRecord.length ? codeRecord[0].val : 0,
        };
      });
      chartsData.value = data;
      console.log('峰平谷电耗', data);
      if (data && data.length) {
        const newData: {
          indexName: string;
          indexCode: string;
          unitName: string;
          type?: string;
          data: any[];
        }[] = [];
        const getVal = (arr) => {
          const data = arr
            .map((item) => {
              return item.val === '' || item.val === null ? null : Number(item.val);
            })
            .reduce((prev, cur) => {
              return prev + cur;
            });
          return data !== null ? data.toFixed(2) : null;
        };
        // 峰
        const fD = data.filter((i) => i.stage === 1);
        if (fD.length) {
          const fData = {
            indexName: '峰',
            type: 'bar',
            indexCode: fD[0].codeRecord[0]?.indicatorCode,
            unitName: fD[0].codeRecord[0]?.unit,
            data: data.map((item) => {
              return {
                collectDateTime: item.provideTime.slice(11, 16),
                value:
                  item.stage === 1
                    ? item.codeRecord && item.codeRecord.length
                      ? getVal(item.codeRecord)
                      : null
                    : null,
              };
            }),
          };
          newData.push(fData);
        }
        // 峰尖
        const fJ = data.filter((i) => i.stage === 2);
        if (fJ.length) {
          const fData = {
            indexName: '峰尖',
            type: 'bar',
            indexCode: fJ[0].codeRecord[0]?.indicatorCode,
            unitName: fJ[0].codeRecord[0]?.unit,
            data: data.map((item) => {
              return {
                collectDateTime: item.provideTime.slice(11, 16),
                value:
                  item.stage === 2
                    ? item.codeRecord && item.codeRecord.length
                      ? getVal(item.codeRecord)
                      : null
                    : null,
              };
            }),
          };
          newData.push(fData);
        }

        // 平
        const pD = data.filter((i) => i.stage === 3);
        if (pD.length) {
          const pData = {
            indexName: '平',
            type: 'bar',
            indexCode: pD[0].codeRecord[0]?.indicatorCode,
            unitName: pD[0].codeRecord[0]?.unit,
            data: data.map((item) => {
              return {
                collectDateTime: item.provideTime.slice(11, 16),
                value:
                  item.stage === 3
                    ? item.codeRecord && item.codeRecord.length
                      ? getVal(item.codeRecord)
                      : null
                    : null,
              };
            }),
          };
          newData.push(pData);
        }

        // 谷
        const gD = data.filter((i) => i.stage === 4);
        if (gD.length) {
          const gData = {
            indexName: '谷',
            type: 'bar',
            indexCode: gD[0].codeRecord[0]?.indicatorCode,
            unitName: gD[0].codeRecord[0]?.unit,
            data: data.map((item) => {
              return {
                collectDateTime: item.provideTime.slice(11, 16),
                value:
                  item.stage === 4
                    ? item.codeRecord && item.codeRecord.length
                      ? getVal(item.codeRecord)
                      : null
                    : null,
              };
            }),
          };
          newData.push(gData);
        }
        // 谷底
        const gd = data.filter((i) => i.stage === 5);
        if (gd.length) {
          const gData = {
            indexName: '谷底',
            type: 'bar',
            indexCode: gd[0].codeRecord[0]?.indicatorCode,
            unitName: gd[0].codeRecord[0]?.unit,
            data: data.map((item) => {
              return {
                collectDateTime: item.provideTime.slice(11, 16),
                value:
                  item.stage === 5
                    ? item.codeRecord && item.codeRecord.length
                      ? getVal(item.codeRecord)
                      : null
                    : null,
              };
            }),
          };
          newData.push(gData);
        }
        // 电价
        if (data) {
          dJata.data = {
            indexName: '电价',
            indexCode: 'dj',
            unitName: '元',
            data: priceList.value.map((item) => {
              // const row = data.find((i) => {
              //   return item.label === dayjs(i.provideTime).format('HH:mm');
              // });
              // const { codeRecord } = row;
              return {
                collectDateTime: item.label,
                value: item.value ? item.value : null,
                // ? (Number(codeRecord[0].val) * item.value).toFixed(2)
                // : null,
              };
            }),
          };
          // console.log('总电价', dJata.data);
          newData.push(dJata.data);
        }
        // console.log('indexList.value', newData);
        indexList.value = newData;
        console.log('indexList.value ', indexList.value);
        pieIndexList.value = newData.map((item) => {
          let value;
          // const listArr = item.data.map((val) => val.value).filter((i) => i && i !== 0);
          if (item.indexName === '峰' || item.indexName === '平' || item.indexName === '谷') {
            if (item.indexName === '峰') {
              value = Number(result.fengzhi) ? Number(result.fengzhi) : '-';
            } else if (item.indexName === '平') {
              value = result.pingzhi || '-';
              value = Number(result.pingzhi) ? Number(result.pingzhi) : '-';
            } else if (item.indexName === '谷') {
              value = Number(result.guzhi) ? Number(result.guzhi) : '-';
            }
          } else if (item.indexName === '峰尖') {
            value = Number(result.fengjian) ? Number(result.fengjian) : '-';
          } else if (item.indexName === '谷底') {
            // 取极小值
            // if (listArr.length) {
            //   value = Math.min.apply(null, listArr);
            // }
            value = Number(result.gudizhi) ? Number(result.gudizhi) : '-';
          } else if (item.indexName === '电价') {
            // 取当前值
            value =
              item.data.find((val) => val.collectDateTime.slice(0, 2) === dayjs().format('HH'))
                ?.value || '-';
          }
          return {
            indexName: item.indexName === '电价' ? '当前电价' : item.indexName,
            indexCode: item.indexCode,
            unitName: item.unitName,
            value: value ? value : '-',
          };
        });
      }
    } catch (err) {
      empty.value = true;
      indexList.value = [];
      pieIndexList.value = [];
      loading.value = false;
    } finally {
    }
  };

  const handleChangeIndicator = async () => {
    // pause();
    await getData();
    // resume();
  };

  watch(
    () => date.value,
    async () => {
      await getData();
    },
  );

  onMounted(async () => {
    // 初始化
    await getprice();
    await getIndicatorList();
    await getData();
  });
</script>
<style lang="less" scoped>
  .brc {
    background-color: #fcfcfc;
    height: 100%;
    overflow: hidden;
    border-radius: 4px;

    .header-right {
      display: flex;
      gap: 0 16px;
    }
  }
</style>
